# Search Feature

## Overview

The Search feature provides comprehensive search functionality across the NSP platform, allowing users to find training courses, learning tracks, and other content efficiently. It includes search suggestions, filters, and result categorization for an optimal search experience.

## Architecture

### Feature Structure
```
features/search/
├── data/
│   ├── datasources/
│   │   └── search_data_source.dart
│   ├── models/
│   │   ├── search_result_model.dart
│   │   ├── search_suggestion_model.dart
│   │   └── search_filter_model.dart
│   └── repositories/
│       └── search_repository.dart
├── domain/
│   └── repositories/
│       └── search_repository.dart
└── presentation/
    ├── bloc/
    │   ├── search_bloc.dart
    │   ├── search_event.dart
    │   └── search_state.dart
    ├── pages/
    │   ├── search_page.dart
    │   └── search_results_page.dart
    └── widgets/
        ├── search_bar_widget.dart
        ├── search_suggestion_widget.dart
        ├── search_filter_widget.dart
        └── search_result_item_widget.dart
```

## Core Components

### 1. SearchBloc - State Management

The `SearchBloc` manages search queries, suggestions, results, and filter states.

**SearchBloc Implementation:**
The SearchBloc class is an injectable service that extends the BLoC pattern for managing search functionality. It handles four main event types: query changes for real-time suggestions, search submissions for executing searches, suggestion loading for dropdown display, and filter application for refining results. The bloc depends on a SearchRepository for data operations and uses event handlers to process each type of user interaction, maintaining clean separation between presentation logic and business logic.

**Key Features:**
- **Query Management**: Handles search input and query processing
- **Suggestion System**: Provides real-time search suggestions
- **Result Processing**: Manages search results and pagination
- **Filter Integration**: Applies and manages search filters

### 2. Search Events

Search events represent user interactions and system actions during the search process.

**Search Event Classes:**
The search feature defines three primary event classes that represent user interactions. SearchQueryChanged captures real-time input changes with a required query string parameter, enabling live search suggestions. SearchSubmitted represents the execution of a search operation, containing both the search query and optional filter parameters for refined results. LoadSuggestions specifically handles the request for search suggestions based on the current query input, supporting the autocomplete functionality that enhances user experience.

**Event Types:**
- **SearchQueryChanged**: Triggered as user types in search field
- **SearchSubmitted**: Executes search with current query and filters
- **LoadSuggestions**: Fetches search suggestions for current input
- **ApplyFilters**: Updates search results with applied filters

### 3. Search States

Search states represent the current status of search operations and drive UI updates.

**Search State Classes:**
The search feature implements two key state classes for managing UI updates. SearchLoaded represents a successful search operation, containing the search results list, the original query string, a pagination flag indicating if more results are available, and any applied filters. This state drives the main results display interface. SearchSuggestions represents the suggestion dropdown state, containing a list of SearchSuggestionModel objects that provide autocomplete options as users type their queries.

**State Types:**
- **SearchInitial**: Default state with empty search
- **SearchLoading**: Shows loading indicators during search
- **SearchLoaded**: Contains search results and metadata
- **SearchSuggestions**: Displays search suggestions dropdown

## Search Interface

### Search Bar Widget

The search bar provides an intuitive interface for entering search queries with suggestions.

**Search Bar Widget Implementation:**
The search bar widget creates a comprehensive input interface with a clean, modern design. It features a white container with rounded corners and a subtle border, containing a search icon, an expandable text field, and a conditional clear button. The text field includes event handlers for real-time input changes and search submission, with localized hint text for better user experience. The clear button appears dynamically when text is present, providing an easy way to reset the search. The entire component uses consistent spacing and color theming from the app's design system.

**Search Bar Features:**
- **Real-time Input**: Triggers suggestions as user types
- **Clear Functionality**: Easy way to clear current search
- **Visual Feedback**: Search icon and clear button for better UX
- **Responsive Design**: Adapts to different screen sizes

### Search Suggestions

**Search Suggestions Widget Structure:**

The search suggestions widget creates an elegant dropdown interface that displays search suggestions with proper styling and user interaction handling. This widget provides real-time search assistance to improve user experience.

**Visual Design:**
- **Container Styling**: Clean white background with 8px border radius for modern appearance
- **Shadow Effect**: Subtle shadow with 30% opacity grey color and 8px blur for depth
- **List Layout**: Scrollable list with dividers between suggestion items for clear separation
- **Responsive Design**: Shrink-wrap sizing to fit content without unnecessary space

**Interaction Features:**
- **Touch Targets**: Each suggestion item is individually tappable for easy selection
- **Visual Feedback**: Hover and tap states provide clear user feedback
- **Keyboard Navigation**: Supports keyboard navigation for accessibility
- **Dynamic Content**: Real-time updates based on search query changes

**Suggestion Item Builder Method:**

The suggestion item builder creates individual ListTile widgets for each search suggestion with the following structure:
- **Leading Icon**: Displays a type-specific icon using the _getSuggestionIcon helper method, styled with secondary grey color for visual consistency
- **Title Text**: Shows the suggestion text using medium text theme styling for optimal readability
- **Conditional Subtitle**: Displays the suggestion category when available, using small text with secondary grey styling for hierarchy
- **Tap Handler**: Implements onTap callback that triggers the _selectSuggestion method for user interaction

This method ensures consistent visual presentation and interaction behavior across all search suggestions.

**Suggestion Features:**
- **Categorized Suggestions**: Different types (courses, tracks, topics)
- **Visual Indicators**: Icons to distinguish suggestion types
- **Quick Selection**: Tap to select and search
- **Contextual Information**: Shows category or additional details

## Search Results

### Result Display

**Search Result Item Widget:**
The search result item widget creates an interactive card-based display for individual search results. Each result is presented as a Material Design card with proper margins and rounded corners, containing a tappable InkWell for navigation. The layout includes a header row with a result type chip and optional rating widget, followed by the result title with bold styling and ellipsis overflow handling. Optional description text appears below with secondary styling and truncation. The bottom section displays metadata through a dedicated helper method. The entire component uses consistent spacing and theming while providing clear visual hierarchy and touch feedback.

**Result Features:**
- **Rich Information**: Title, description, rating, and metadata
- **Type Indicators**: Visual chips showing content type
- **Truncated Content**: Ellipsis for long text with proper line limits
- **Interactive Cards**: Tap to navigate to detailed view

### Search Filters

**Search Filter Widget Implementation:**
The search filter widget creates an expandable interface using an ExpansionTile with a localized title and bold styling. The collapsible section contains multiple filter components for content type, duration, language, and sector selection. At the bottom, a row of action buttons provides clear and apply functionality - the clear button uses a light grey background to distinguish it from the primary apply button. The layout uses consistent spacing and the app's standard button components for a cohesive user experience. This design allows users to refine their search results while maintaining a clean, space-efficient interface.

**Filter Features:**
- **Multiple Filter Types**: Content type, duration, language, sector
- **Expandable Interface**: Collapsible filter section to save space
- **Clear and Apply**: Easy filter management with clear actions
- **Visual Feedback**: Shows active filter count and selections

## Search Analytics and Optimization

### Search Tracking

**Search Submission Handler:**
The search submission event handler implements a comprehensive search execution flow with analytics tracking and error handling. It begins by emitting a loading state to provide user feedback, then tracks the search operation through an analytics service, capturing the query, applied filters, and timestamp for usage analysis. The handler attempts to execute the search through the repository layer, passing the query and filter parameters. On success, it emits a SearchLoaded state containing the results, original query, and filters for UI rendering. Any errors during the search process are caught and converted to a SearchError state with an appropriate error message, ensuring robust error handling throughout the search flow.

**Analytics Features:**
- **Search Tracking**: Records search queries and patterns
- **Filter Usage**: Tracks which filters are most used
- **Result Interaction**: Monitors which results users select
- **Performance Metrics**: Measures search response times

### Search Optimization

**Search Repository with Caching:**
The SearchRepository class is an injectable service that implements intelligent caching for search operations. The search method accepts a required query string, optional filters, and pagination parameters. It implements a cache-first strategy by generating a unique cache key based on the search parameters and checking for existing cached results. If cached data exists, it returns immediately for optimal performance. For cache misses, it delegates to the data source layer to fetch fresh results from the API, then caches the response with a 5-minute expiration for future requests. This approach significantly improves search performance by reducing redundant API calls while ensuring data freshness through appropriate cache invalidation.

**Optimization Features:**
- **Result Caching**: Caches search results for improved performance
- **Debounced Suggestions**: Reduces API calls for suggestion requests
- **Pagination Support**: Loads results in chunks for better performance
- **Smart Caching**: Cache invalidation based on content updates

## Data Models

### SearchResultModel

**SearchResultModel Data Structure:**
The SearchResultModel is a Freezed-generated immutable data class that represents search results with comprehensive metadata. It includes required fields for unique identification (id), display title, and result type classification. Optional fields provide rich content information including description text, image URLs for visual representation, numerical ratings for quality indicators, duration in minutes, provider information, and tag lists for categorization. The model includes automatic JSON serialization through generated factory methods. The SearchResultType enum defines four distinct result categories: training courses, learning tracks, training providers, and industry sectors, enabling type-safe result handling and appropriate UI rendering for each content type.

**Model Features:**
- **Flexible Content**: Supports different types of search results
- **Rich Metadata**: Includes ratings, duration, provider information
- **Extensible Structure**: Easy to add new result types
- **Type Safety**: Enum-based type system for result categorization

## Integration with Other Features

### Catalog Integration

Search results seamlessly integrate with the catalog feature for detailed views.

**Result Navigation Handler:**
The navigation handler implements type-specific routing logic for search results using a switch statement on the result type. Training results navigate to the course details page with the training ID as a path parameter and training type specified in the extra data for proper page configuration. Learning track results follow a similar pattern but specify the learning track type for appropriate content rendering. Provider results navigate to the catalog page with the provider ID as a query parameter, enabling filtered catalog views. This approach ensures seamless integration between search and other app features while maintaining proper navigation context and parameter passing for each result type.

**Integration Features:**
- **Seamless Navigation**: Direct links to detailed content views
- **Context Preservation**: Maintains search context during navigation
- **Filter Propagation**: Passes search filters to catalog views
- **Back Navigation**: Proper back stack management

## Best Practices

### 1. User Experience
- Provide instant search suggestions
- Show clear "no results" states
- Implement proper loading states
- Use debouncing for search input

### 2. Performance
- Cache search results appropriately
- Implement pagination for large result sets
- Optimize suggestion queries
- Use efficient search algorithms

### 3. Accessibility
- Support keyboard navigation
- Provide proper screen reader labels
- Ensure sufficient color contrast
- Include alternative text for images

### 4. Search Quality
- Implement fuzzy search for typos
- Support multiple languages
- Provide relevant suggestions
- Track and improve search analytics

## Related Documentation

- [Catalog Feature](catalog.md)
- [Course Details](course-details.md)
- [State Management Guide](../architecture/state-management.md)
- [API Architecture](../api/api-architecture.md)
