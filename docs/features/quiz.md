# Quiz Feature

## Overview

The Quiz feature manages interactive quizzes embedded within training lessons. Unlike qualification tests, quizzes are typically shorter, lesson-specific assessments that help reinforce learning and provide immediate feedback to users.

## Architecture

### Feature Structure
```
features/quiz/
├── data/
│   ├── datasources/
│   │   └── quiz_data_source.dart
│   ├── models/
│   │   ├── quiz_model.dart
│   │   ├── quiz_question_model.dart
│   │   └── quiz_result_model.dart
│   └── repositories/
│       └── quiz_repository.dart
├── domain/
│   └── repositories/
│       └── quiz_repository.dart
└── presentation/
    ├── bloc/
    │   ├── quiz_bloc.dart
    │   ├── quiz_event.dart
    │   └── quiz_state.dart
    ├── pages/
    │   └── quiz_page.dart
    └── widgets/
        ├── quiz_question_widget.dart
        ├── quiz_answer_widget.dart
        └── quiz_progress_widget.dart
```

## Core Components

### 1. QuizBloc - State Management

The `QuizBloc` manages quiz flow, question progression, answer tracking, and immediate feedback display.

**QuizBloc Implementation:**
The QuizBloc is an injectable service that manages quiz flow, question progression, answer tracking, and immediate feedback display using the BLoC pattern. It handles four main event types: loading quiz data, answering questions, navigating to the next question, and completing the quiz. The bloc provides centralized state management for the entire quiz-taking experience, ensuring consistent behavior across all quiz interactions while maintaining proper separation between presentation logic and business logic. This approach enables features like immediate feedback, progress tracking, and seamless navigation through quiz questions.

**Key Features:**
- **Quiz Loading**: Fetches quiz questions and configuration
- **Answer Processing**: Handles user responses with immediate feedback
- **Progress Management**: Tracks completion through quiz questions
- **Result Calculation**: Provides instant scoring and feedback

### 2. Quiz Events

Quiz events represent user interactions during the quiz-taking process.

**Quiz Event Classes:**
The quiz feature defines three primary event classes that represent user interactions during the quiz-taking process. LoadQuiz initiates the quiz loading process and requires a quizId parameter to identify the specific quiz. AnswerQuizQuestion captures user responses with both questionId and selectedAnswer parameters, enabling immediate feedback and answer tracking. NextQuestion advances the quiz progression and requires no parameters since it operates on the current state context. These events provide a clean interface for triggering different quiz operations while maintaining proper identification and context for each interaction.

**Event Types:**
- **LoadQuiz**: Initializes quiz with questions and settings
- **AnswerQuizQuestion**: Records user's answer with immediate feedback
- **NextQuestion**: Advances to the next quiz question
- **CompleteQuiz**: Finalizes quiz and shows overall results

### 3. Quiz States

Quiz states represent the current status of the quiz and drive UI updates.

**Quiz State Classes:**
The quiz feature implements two key state classes for managing UI updates during quiz interactions. QuizLoaded represents the active quiz state, containing the complete quiz data, current question index for navigation, a map of user answers keyed by question ID, and a showFeedback flag for controlling immediate response display. QuizCompleted represents the final state after quiz submission, containing a QuizResultModel with scoring information, completion time, and performance metrics. These states drive reactive UI updates throughout the quiz experience, enabling proper progress tracking and result presentation.

**State Types:**
- **QuizInitial**: Default state before quiz loading
- **QuizLoading**: Shows loading indicators during quiz fetch
- **QuizLoaded**: Contains quiz data and current progress
- **QuizCompleted**: Displays final results and feedback

## Question Display and Interaction

### Quiz Question Widget

The quiz question widget provides an interactive interface for answering questions with immediate feedback.

**Quiz Question Widget Implementation:**
The quiz question widget provides an interactive interface for answering questions with immediate feedback using a Column layout with proper spacing. It includes a question header section, the main question text styled with bold H4 typography, and dynamically generated answer options using the map function. The widget conditionally displays a feedback section when showFeedback is true, providing immediate response information. This structure ensures clear presentation of quiz content with consistent spacing and typography while supporting interactive answer selection and real-time feedback display for enhanced learning experience.

**Question Features:**
- **Clear Presentation**: Well-formatted question text and options
- **Interactive Options**: Tap-to-select answer choices
- **Immediate Feedback**: Shows correct/incorrect feedback after selection
- **Progress Indication**: Visual progress through quiz questions

### Answer Feedback

**Answer Option Widget Implementation:**
The answer option widget creates sophisticated visual feedback for quiz interactions using dynamic styling based on selection and correctness states. It determines border colors through conditional logic: showing success green for correct answers, warning colors for incorrect selections, and accent green for current selections. The Container uses proper margins and padding with rounded corners, implementing a border system that provides immediate visual feedback. When feedback is shown and the answer is correct, it applies a light success background color. This approach ensures users receive clear, immediate visual confirmation of their choices while highlighting correct answers for educational value.

**Feedback Features:**
- **Visual Indicators**: Color-coded feedback for correct/incorrect answers
- **Correct Answer Highlighting**: Shows the right answer when user is wrong
- **Immediate Response**: Feedback appears instantly after selection
- **Clear Visual Hierarchy**: Distinguishes between selected, correct, and incorrect options

## Quiz Progress and Navigation

### Progress Tracking

**Quiz Progress Widget Implementation:**
The quiz progress widget provides clear visual indication of quiz completion status using a Column layout with a LinearProgressIndicator and descriptive text. It calculates progress as a percentage by dividing the current question index plus one by the total questions count. The progress bar uses the app's color scheme with grey background and green accent for the progress value. Below the progress bar, descriptive text shows the current position in "X of Y" format using secondary grey styling. This design provides users with clear awareness of their progress through the quiz while maintaining visual consistency with the app's design system.

**Progress Features:**
- **Visual Progress Bar**: Linear indicator showing completion percentage
- **Question Counter**: Clear indication of current position
- **Smooth Transitions**: Animated progress updates
- **Consistent Styling**: Matches app design system

### Navigation Controls

**Navigation Button Implementation:**
The navigation button widget implements intelligent conditional display and functionality based on quiz state. It returns an empty SizedBox when no answer has been provided, ensuring users must answer before proceeding. When an answer is given, it displays an AppButton with context-aware functionality: calling _completeQuiz for the final question or _nextQuestion for intermediate questions. The button text is localized and changes dynamically between "Complete Quiz" and "Next Question" based on the current position. The consistent top margin provides proper spacing within the quiz interface while maintaining the app's design standards.

**Navigation Features:**
- **Conditional Display**: Button appears only after answering
- **Context-Aware Text**: Different labels for next vs complete
- **Smooth Flow**: Seamless progression through questions
- **Completion Handling**: Automatic quiz finalization

## Quiz Results and Feedback

### Result Calculation

**Quiz Completion Handler:**
The quiz completion handler implements comprehensive result calculation and state management when users finish a quiz. It casts the current state to QuizLoaded to access quiz data and user answers, then calculates the number of correct answers using a helper method. The score percentage is computed by dividing correct answers by total questions and multiplying by 100. A QuizResultModel is constructed with the quiz ID, scoring metrics, and completion timestamp for record-keeping. Finally, it emits a QuizCompleted state with the result data, triggering the results display interface. This approach ensures accurate scoring and proper state transition for quiz completion.

**Result Features:**
- **Instant Scoring**: Immediate calculation of quiz performance
- **Detailed Metrics**: Correct answers, total questions, percentage
- **Completion Tracking**: Records completion time and date
- **Performance Feedback**: Contextual messages based on score

### Result Display

**Quiz Results Display Implementation:**
The quiz results widget creates a comprehensive visual representation of quiz performance using a Column layout with multiple information elements. It displays a large icon that changes based on performance: a green check circle for scores 70% and above, or a red cancel icon for lower scores. The main score is prominently displayed as a large percentage using H1 bold typography, followed by a detailed breakdown showing correct answers out of total questions in secondary grey text. The widget includes proper spacing between elements and concludes with a performance message that provides contextual feedback based on the score. This design ensures clear communication of results with appropriate visual hierarchy and motivational messaging.

**Display Features:**
- **Visual Score Representation**: Large percentage display with icon
- **Performance Indicators**: Success/warning colors based on score
- **Detailed Breakdown**: Shows correct vs total questions
- **Motivational Messaging**: Encouraging feedback based on performance

## Data Models

### QuizModel

**QuizModel Data Structure:**
The QuizModel is a Freezed-generated immutable data class that represents quiz configuration and content with comprehensive metadata. It includes required fields for unique identification, display title, and lesson association through lessonId. The questions field contains a list of QuizQuestionModel objects representing the quiz content. Optional configuration includes an isOptional boolean flag defaulting to false for required quizzes, and a passingScore integer defaulting to 70 percent for grade thresholds. The model includes automatic JSON serialization through generated factory methods, enabling seamless API integration while maintaining type safety and immutability throughout the quiz system.

**Model Features:**
- **Lesson Integration**: Links quiz to specific lesson content
- **Flexible Configuration**: Optional vs required quizzes
- **Scoring Thresholds**: Configurable passing scores
- **Immutable Data**: Uses Freezed for data integrity

### QuizQuestionModel

**QuizQuestionModel Data Structure:**
The QuizQuestionModel is a Freezed-generated immutable data class that represents individual quiz questions with comprehensive configuration options. It includes required fields for unique identification, question text content, and a list of QuizAnswerOption objects representing possible answers. Optional features include an explanation field for educational feedback and a type field defaulting to multiple choice for question format specification. The model supports automatic JSON serialization through generated factory methods, enabling seamless API integration. This flexible structure allows for various question types while maintaining consistent data handling and educational value through optional explanations.

**Question Features:**
- **Multiple Question Types**: Support for different quiz formats
- **Rich Content**: Text, images, and multimedia support
- **Educational Value**: Optional explanations for learning
- **Flexible Structure**: Extensible for future question types

## Integration with Training Consumption

### Lesson Integration

Quizzes are seamlessly integrated into the training consumption flow, appearing at strategic points within lessons.

**Lesson Quiz Integration Implementation:**
The lesson content integration demonstrates seamless quiz incorporation within the training consumption flow using conditional rendering. The _buildLessonContent method creates a Column layout containing lesson text, an optional quiz section, and navigation controls. The _buildQuizSection method creates a visually distinct Container with light accent background and rounded corners, containing localized text announcing quiz availability and an action button to start the quiz. The vertical margins and internal padding provide proper spacing within the lesson flow. This integration approach ensures quizzes appear contextually within lessons while maintaining clear visual separation and user control over quiz participation.

**Integration Features:**
- **Contextual Placement**: Quizzes appear at relevant lesson points
- **Optional Participation**: Users can choose when to take quizzes
- **Progress Tracking**: Quiz completion affects lesson progress
- **Seamless Flow**: Smooth transition between content and assessment

## Best Practices

### 1. User Experience
- Provide immediate feedback after each answer
- Use clear visual indicators for correct/incorrect responses
- Keep questions concise and focused
- Offer encouraging messages regardless of performance

### 2. Educational Value
- Include explanations for correct answers
- Design questions that reinforce key concepts
- Vary question difficulty appropriately
- Provide opportunities for learning from mistakes

### 3. Performance
- Load quiz data efficiently
- Cache questions for offline access
- Optimize for smooth animations
- Handle network interruptions gracefully

### 4. Accessibility
- Support screen readers with proper semantics
- Ensure sufficient color contrast for feedback
- Provide keyboard navigation options
- Include alternative text for visual elements

## Related Documentation

- [Qualification Test Feature](qualification-test.md)
- [Training Consumption](training-consumption.md)
- [State Management Guide](../architecture/state-management.md)
- [UI Components](../ui-components/shared-widgets.md)
