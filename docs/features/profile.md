# Profile Feature

## Overview

The Profile feature manages user account information, profile picture handling, and account settings. It provides a comprehensive interface for users to view and update their personal information within the NSP mobile application.

## Architecture

### Feature Structure
```
features/profile_page/
├── data/
│   ├── datasources/
│   │   └── user_data_source.dart
│   ├── models/
│   │   └── user_model.dart
│   └── repositories/
│       └── user_repository.dart
├── domain/
│   └── repositories/
│       └── user_repository.dart
└── presentation/
    ├── bloc/
    │   ├── user_bloc.dart
    │   ├── user_event.dart
    │   └── user_state.dart
    ├── pages/
    │   └── profile_page.dart
    └── widgets/
        ├── profile_header.dart
        ├── profile_info_section.dart
        └── profile_picture_widget.dart
```

## Core Components

### 1. UserBloc - State Management

The `UserBloc` manages user profile data, profile picture operations, and account settings.

**UserBloc Implementation:**
The UserBloc is a singleton service that manages user profile data, profile picture operations, and account settings using the BLoC pattern. It handles four main event types: getting user data, updating profile pictures, deleting profile pictures, and logout functionality. The bloc provides centralized state management for all profile-related operations while maintaining proper separation between presentation logic and business logic. This approach ensures consistent user data handling across the application while supporting features like profile picture management, secure logout, and state synchronization with authentication systems.

**Key Features:**
- **User Data Management**: Fetches and caches user profile information
- **Profile Picture Handling**: Upload, update, and delete profile images
- **Logout Functionality**: Handles secure user logout process
- **State Synchronization**: Coordinates with AuthBloc for authentication state

### 2. Profile Events

Profile events represent user actions and system triggers for profile operations.

**Profile Event Classes:**
The profile feature defines three primary event classes that represent user actions and system triggers for profile operations. GetUserData initiates the user profile data loading process and requires no parameters since it fetches the current user's information. UpdateProfilePicture handles profile picture upload operations and requires a File parameter containing the selected and processed image file. DeleteProfilePicture removes the current profile picture and requires no parameters since it operates on the current user's profile. These events provide a clean interface for triggering different profile operations while maintaining proper separation between user interactions and business logic.

**Event Types:**
- **GetUserData**: Fetches current user profile information
- **UpdateProfilePicture**: Uploads new profile picture from gallery
- **DeleteProfilePicture**: Removes current profile picture
- **Logout**: Initiates secure logout process

### 3. Profile States

Profile states represent the current status of profile operations and drive UI updates.

**Profile State Definitions:**

The user state classes represent different phases of profile data management:
- **UserLoading State**: Indicates profile data is being fetched or updated, triggers loading UI indicators
- **UserLoaded State**: Contains successfully retrieved UserModel data, enables profile information display
- **UserError State**: Holds error message string for failed operations, triggers error UI with specific message
- **Immutable Design**: All states use const constructors for performance and predictable state management
- **Required Parameters**: UserLoaded requires UserModel user, UserError requires String errorMsg for proper data handling
- **State Hierarchy**: All extend base UserState class for consistent BLoC state management

These states provide clear representation of profile operation status and enable appropriate UI responses.

**State Types:**
- **UserInitial**: Default state before any profile operations
- **UserLoading**: Shows loading indicators during operations
- **UserLoaded**: Contains user data for display
- **UserError**: Displays error messages for failed operations

## Profile Picture Management

### Image Selection and Cropping

The profile feature includes comprehensive image handling with selection, cropping, and upload capabilities.

**Image Selection and Cropping Process**

The image selection and cropping functionality provides a complete workflow for profile picture management:

**Image Selection**: Uses the ImagePicker plugin to access the device's photo gallery, with image quality set to 80% to balance file size and visual quality for optimal upload performance.

**File Processing**: After successful image selection, the picked file is converted to a File object and passed to a custom cropping function that allows users to adjust the image dimensions and positioning.

**State Management Integration**: Upon successful cropping, the processed image file is passed to the UserBloc through an UpdateProfilePicture event, triggering the upload process and UI state updates.

**Error Handling**: The implementation includes null checks at each step - if image selection fails or cropping is cancelled, the process gracefully terminates without triggering unnecessary state changes or API calls.

**Image Processing Features:**
- **Gallery Selection**: Uses `ImagePicker` for photo selection
- **Image Cropping**: Custom crop interface for profile pictures
- **Quality Optimization**: Compresses images for optimal upload size
- **Error Handling**: Manages permission and processing errors

### Profile Picture Display

**Profile Picture Display Widget**

The profile picture display component creates a layered interface for showing and editing user profile images:

**Base Avatar Structure**: Uses a CircleAvatar widget with 50-pixel radius to create a circular profile picture container that maintains consistent sizing across the application.

**Conditional Image Loading**: Implements smart image loading logic - when a profile picture URL exists, it uses CachedNetworkImageProvider for efficient network image caching and performance optimization. When no URL is available, displays a default person icon.

**Fallback UI Design**: For users without profile pictures, shows a large person icon (size 50) as a placeholder, maintaining visual consistency and providing clear indication of the profile picture area.

**Edit Button Overlay**: Uses a Stack layout with a Positioned widget to place an edit button at the bottom-right corner of the avatar, providing intuitive access to image editing functionality without cluttering the interface.

**Performance Optimization**: Leverages CachedNetworkImage for automatic image caching, reducing network requests and improving app performance for frequently viewed profile pictures.

**Display Features:**
- **Cached Images**: Uses `CachedNetworkImage` for performance
- **Fallback UI**: Shows default icon when no picture is set
- **Edit Overlay**: Floating action button for picture management
- **Circular Design**: Consistent with app design system

## User Information Display

### Profile Information Section

**Profile Information Tile Component**

The information tile component creates a consistent layout for displaying user profile data in label-value pairs:

**Layout Structure**: Uses a Row widget with start-aligned cross-axis alignment to create a two-column layout where labels and values align properly even when content spans multiple lines.

**Label Formatting**: The label section has a fixed width of 100 pixels and uses medium text style with secondary grey color from the app's theme system, ensuring consistent visual hierarchy and readability.

**Value Display**: The value section uses an Expanded widget to take remaining horizontal space, displaying content in bold medium text style for emphasis and improved readability.

**Null Safety Handling**: Implements safe null handling by displaying a dash ('-') placeholder when profile information is missing or null, maintaining layout consistency and providing clear visual feedback.

**Spacing Design**: Applies symmetric vertical padding of 8 pixels to each tile, creating consistent spacing between information rows and improving overall visual organization.

**Responsive Layout**: The Expanded widget ensures the value text adapts to different screen sizes and content lengths while maintaining the fixed label width for consistent alignment.

**Information Display:**
- **Structured Layout**: Consistent label-value pairs
- **Null Handling**: Shows placeholder for missing information
- **Responsive Design**: Adapts to different screen sizes
- **Theme Integration**: Uses app text theme for consistency

## Data Models

### UserModel

**UserModel Data Structure:**

The UserModel represents comprehensive user profile information with proper type safety:
- **@freezed Annotation**: Enables automatic code generation for immutable data class with equality, toString, and copyWith methods
- **Required Fields**: id, name, and email are mandatory for user identification and basic profile functionality
- **Optional Profile Data**: phoneNumber, profilePictureUrl, and lastLoginAt are nullable for incomplete or privacy-conscious profiles
- **Type Safety**: Uses appropriate types (String, DateTime?) for each field with null safety support
- **JSON Serialization**: Includes fromJson factory constructor using generated _$UserModelFromJson for API integration
- **Immutable Design**: Factory constructor pattern ensures data immutability and thread safety

This model provides a robust foundation for user profile management with comprehensive data representation and type safety.

**Model Features:**
- **Immutable Data**: Uses Freezed for immutable data classes
- **JSON Serialization**: Automatic JSON conversion
- **Null Safety**: Proper handling of optional fields
- **Type Safety**: Strong typing for all user properties

## Repository Implementation

### UserRepository

**User Repository Implementation**

The UserRepository serves as the domain layer's interface for user-related data operations, providing clean abstraction over data sources:

**Dependency Injection**: Marked with @injectable annotation for automatic registration in the dependency injection container, enabling easy testing and loose coupling between layers.

**Constructor Design**: Uses required named parameter for UserDataSource dependency, ensuring the repository cannot be instantiated without proper data source configuration.

**User Data Retrieval**: Provides getUserData method that returns a Future<UserModel>, abstracting the complexity of API calls and data transformation from the presentation layer.

**Profile Picture Upload**: Implements uploadProfilePicture method accepting a File parameter and returning the uploaded image URL as a String, handling the multipart upload process through the data source.

**Profile Picture Deletion**: Offers deleteProfilePicture method for removing user profile images, providing a clean interface for profile picture management operations.

**Async Operations**: All methods are asynchronous, properly handling network operations and potential delays while maintaining responsive UI through Future-based patterns.

**Repository Features:**
- **Data Abstraction**: Clean interface for profile operations
- **Error Handling**: Consistent error management across operations
- **Dependency Injection**: Injectable for easy testing and mocking
- **Single Responsibility**: Focused on user data operations

## Security Considerations

### Data Protection
- **Secure Storage**: Profile data cached securely
- **Image Upload**: Secure multipart upload with authentication
- **Privacy Controls**: User controls over profile visibility
- **Data Validation**: Server-side validation for all profile updates

### Authentication Integration
- **Token Management**: Automatic token inclusion in requests
- **Session Handling**: Proper logout and session cleanup
- **Permission Checks**: Validates user permissions for operations
- **Error Recovery**: Handles authentication failures gracefully

## Best Practices

### 1. State Management
- Use BLoC for profile state management
- Handle loading states appropriately
- Provide user feedback for all operations
- Implement proper error handling

### 2. Image Handling
- Compress images before upload
- Implement proper error handling for image operations
- Use cached network images for performance
- Provide fallback UI for missing images

### 3. User Experience
- Show loading indicators during operations
- Provide clear error messages
- Implement pull-to-refresh functionality
- Ensure responsive design across devices

### 4. Testing
- Test all profile operations
- Mock image picker and cropping
- Verify error handling scenarios
- Test authentication integration

## Related Documentation

- [Authentication Feature](auth.md)
- [State Management Guide](../architecture/state-management.md)
- [API Architecture](../api/api-architecture.md)
- [UI Components](../ui-components/shared-widgets.md)
