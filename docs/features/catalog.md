# Catalog Feature

## Overview

The Catalog feature enables users to browse, filter, and search through available training courses and learning tracks. It provides a comprehensive interface for course discovery with advanced filtering capabilities and seamless integration with the search functionality.

## Architecture

### Feature Structure
```
features/catalog/
├── data/
│   ├── datasources/
│   │   ├── trainings_data_source.dart
│   │   ├── learning_tracks_data_source.dart
│   │   └── filters_data_source.dart
│   ├── models/
│   │   ├── training_model.dart
│   │   ├── learning_track_model.dart
│   │   ├── sector_model.dart
│   │   └── training_provider_model.dart
│   └── repositories/
│       ├── trainings_repository.dart
│       ├── learning_tracks_repository.dart
│       └── filters_repository.dart
├── domain/
│   ├── entities/
│   │   ├── training_entity.dart
│   │   ├── learning_track_entity.dart
│   │   └── filter_entity.dart
│   └── repositories/
│       ├── trainings_repository.dart
│       └── learning_tracks_repository.dart
├── presentation/
│   ├── bloc/
│   │   ├── trainings_bloc.dart
│   │   ├── learning_tracks_bloc.dart
│   │   └── filters/
│   ├── pages/
│   │   ├── catalog_page.dart
│   │   ├── trainings_tab.dart
│   │   └── learning_tracks_tab.dart
│   └── widgets/
│       ├── course_card.dart
│       ├── filter_bottom_sheet.dart
│       └── sort_options.dart
└── utils/
    └── catalog_utils.dart
```

## Core Components

### 1. Catalog Page - Main Interface

The main catalog page provides tab-based navigation between Trainings and Learning Tracks with a clean, organized layout.

**Catalog Page Implementation:**
The CatalogPage implements a tab-based interface using a StatefulWidget with SingleTickerProviderStateMixin for animation support. It creates a TabController for managing tab navigation between Trainings and Learning Tracks. The build method constructs a Scaffold with the app's standard NspAppBar and a Column layout containing an AppTabBar component with localized tab labels and an expanded TabBarView displaying the appropriate content tabs. This structure provides a clean, intuitive interface for users to browse different types of learning content with smooth tab transitions and proper state management.

**Key Features:**
- **Tab Navigation**: Seamless switching between Trainings and Learning Tracks
- **Responsive Layout**: Adapts to different screen sizes and orientations
- **Localization Support**: Tab labels automatically translate based on user language
- **State Management**: Maintains tab state during navigation
```

### 2. Trainings BLoC - State Management

The `TrainingsBloc` manages the state of training course listings, including loading, filtering, and pagination functionality.

**TrainingsBloc Implementation:**
The TrainingsBloc is an injectable service that manages training course listings using the BLoC pattern. It handles four main event types: initial loading, refresh operations, pagination for loading more content, and filter application. The _onLoadTrainings method demonstrates the typical flow by emitting a loading state, calling the repository with pagination and filter parameters, then emitting a loaded state with the results. This pattern ensures consistent state management for training data while supporting features like infinite scroll, pull-to-refresh, and dynamic filtering throughout the catalog interface.

**BLoC Features:**
- **Event Handling**: Processes load, refresh, pagination, and filter events
- **State Emission**: Emits appropriate states for UI updates
- **Error Management**: Handles API errors with user-friendly messages
- **Pagination Support**: Manages infinite scroll and load-more functionality

### 3. Training Events and States

Training events and states provide a clear contract for catalog interactions and UI updates.

**Training Event Classes:**
The training feature defines three primary event classes for managing catalog interactions. LoadTrainings initiates the initial data loading with optional filter parameters for refined results. RefreshTrainings handles pull-to-refresh functionality, also accepting optional filters to maintain the current filter state during refresh operations. LoadMoreTrainings triggers pagination for infinite scroll behavior and requires no parameters since it uses the current state context. These events provide a clean interface for triggering different types of data operations while maintaining filter consistency across user interactions.

**TrainingsLoaded State Class:**
The TrainingsLoaded state represents successful data loading and contains comprehensive information for UI rendering. It includes the list of TrainingEntity objects for display, a hasReachedMax boolean flag to control pagination behavior, the currentPage number for tracking pagination state, and optional appliedFilters to maintain filter context. This state structure enables the UI to properly render training lists, manage infinite scroll behavior, and maintain filter state consistency across user interactions and navigation.

**Event Types:**
- **LoadTrainings**: Initial load with optional filters
- **RefreshTrainings**: Pull-to-refresh functionality
- **LoadMoreTrainings**: Infinite scroll pagination
- **ApplyFilters**: Filter application and clearing

**State Types:**
- **TrainingsInitial**: Default state before any operations
- **TrainingsLoading**: Loading indicator during API calls
- **TrainingsLoaded**: Contains training data and pagination info
- **TrainingsError**: Error state with user-friendly messages

## Filtering System

### 1. Filter Types

The catalog supports comprehensive filtering options to help users find relevant training content efficiently.

**TrainingFilters Data Model:**
The TrainingFilters class extends Equatable and provides a comprehensive filtering system for training content. It includes optional lists for sectors, providers, skill levels, languages, and duration ranges, along with sorting parameters for result ordering. The toQueryParameters method converts the filter object into API-compatible query parameters by joining list values with commas and building a dynamic parameter map. This design enables flexible filtering combinations while maintaining clean API integration and ensuring proper equality comparison for state management operations.

**Filter Categories:**
- **Sectors**: Industry sectors (IT, Healthcare, Finance, etc.)
- **Providers**: Training organizations and institutions
- **Skill Levels**: Beginner, Intermediate, Advanced
- **Languages**: Arabic, English, and other supported languages
- **Duration**: Short (< 2 hours), Medium (2-8 hours), Long (> 8 hours)
- **Sorting**: By popularity, rating, date, duration
```

### 2. Filter BLoCs

Each filter type has its own BLoC for independent state management:

**SectorFilterBloc Implementation:**
The SectorFilterBloc is an injectable BLoC that manages sector filtering state independently from other filters. It extends Bloc with SectorFilterEvent and SectorFilterState, initializing with SectorFilterInitial state and requiring a SectorRepository dependency. The constructor registers event handlers for LoadSectors, SelectSector, DeselectSector, and ClearSectorSelection events. The _onLoadSectors method emits SectorFilterLoading state, then calls the repository's getSectors method using the errorHandler pattern to emit either SectorFilterLoaded with sectors and empty selectedSectors list on success, or SectorFilterError with the error message on failure.

### 3. Filter Bottom Sheet

**Filter Bottom Sheet Implementation:**
The FilterBottomSheet is a StatefulWidget that provides a comprehensive filtering interface in a modal bottom sheet format. It accepts current filters and a callback function for applying filter changes. The component initializes with existing filters or defaults, then builds a responsive container taking 80% of screen height with proper padding. The layout includes a header section, an expanded scrollable area containing all filter components (sector, provider, skill level, duration, and language filters), and action buttons for applying or clearing filters. This design provides an intuitive, organized interface for users to refine their training search results.

## Course Display Components

### 1. Course Card Widget

**CourseCard Widget Implementation:**
The CourseCard is a StatelessWidget that displays training information in a card format. It requires a TrainingEntity and accepts an optional onTap callback. The widget builds a Card with symmetric margins, containing an InkWell with rounded corners for tap interactions. The main content is organized in a Column with CrossAxisAlignment.start, including an image section, title, description, metadata, and tags with appropriate spacing. The _buildImage method creates a ClipRRect with rounded corners containing a CachedNetworkImage that displays the training image with 120px height, full width, and cover fit. It includes a loading placeholder with CircularProgressIndicator and an error widget with image_not_supported icon. The _buildTitle method displays the training title using h4.bold text theme with 2-line limit and ellipsis overflow.

### 2. Course List with Pagination

**CourseList Widget Implementation:**
The CourseList is a StatefulWidget that displays a scrollable list of courses with pagination and pull-to-refresh functionality. It requires a courses list, hasReachedMax boolean, onLoadMore callback, and onRefresh function. The _CourseListState manages a ScrollController and implements scroll listening for pagination. In initState, it adds a scroll listener that triggers onLoadMore when reaching 90% of scroll extent and more data is available. The _isBottom getter calculates if the user has scrolled near the bottom by comparing current scroll offset to 90% of maximum scroll extent. The build method returns a RefreshIndicator wrapping a ListView.builder that displays CourseCard widgets for each training, with an additional loading indicator item when more data is available. Each CourseCard includes an onTap handler for navigation to course details.

## Search Integration

### 1. Search Bar Integration

**CatalogSearchBar Widget Implementation:**
The CatalogSearchBar is a StatelessWidget that provides a tappable search interface for the catalog. It requires an onSearchTap callback and creates a search bar appearance without actual input functionality. The widget uses Padding with 16px margins around a GestureDetector that handles tap events. The visual container has symmetric padding, greyExtraLight background color, 8px border radius, and greyTertiary border. The content is arranged in a Row with a search icon in greySecondary color, 12px spacing, and localized placeholder text using LocaleKeys.catalogPage_searchCourses with textMedium.greySecondary styling. This creates a consistent search bar appearance that navigates to the dedicated search page when tapped.

## Data Models

### 1. Training Model

**TrainingModel Data Structure:**
The TrainingModel is a freezed data class that represents training course information from the API. It includes required fields for id, title, and description, plus optional fields for imageUrl, durationHours (mapped from 'duration_hours'), provider (TrainingProviderModel), sectors list, skillLevel, language, location, enrollmentStatus, createdAt, and updatedAt timestamps. The class uses JsonKey annotations for proper API field mapping and includes a fromJson factory constructor generated by freezed for JSON deserialization. This model serves as the data transfer object between the API layer and domain entities.

### 2. Learning Track Model

**LearningTrackModel Data Structure:**
The LearningTrackModel is a freezed data class representing learning track information from the API. It includes required fields for id, title, and description, plus optional fields for imageUrl, totalTrainings count, completedTrainings count, estimatedDurationHours, trainings list (TrainingModel objects), sectors list, skillLevel, enrollmentStatus, createdAt, and updatedAt timestamps. The class uses JsonKey annotations for proper API field mapping with snake_case to camelCase conversion and includes a fromJson factory constructor for JSON deserialization. This model represents structured learning paths containing multiple related training courses.

## Repository Implementation

### Trainings Repository

**TrainingsRepository Implementation:**
The TrainingsRepository is an injectable service that manages training data retrieval with caching capabilities. It requires TrainingsDataSource and CacheManager dependencies for API communication and data caching. The getTrainings method implements a cache-first strategy with pagination and filtering support, accepting page (default 1), limit (default 20), and optional TrainingFilters parameters. The method builds a cache key using page, limit, and filter hash, checks for cached data first, and returns it if available. For cache misses, it fetches data from the API via the data source, transforms TrainingModel objects to TrainingEntity objects, caches the results, and returns the entities. The _buildCacheKey method creates unique cache identifiers by combining page, limit, and filter hash values.

## Testing

### BLoC Testing

**TrainingsBloc Testing Implementation:**
The TrainingsBloc tests use the bloc_test package to verify training loading behavior under different scenarios. The test group sets up MockTrainingsRepository and TrainingsBloc instances in setUp. The first blocTest verifies successful training loading by mocking the repository's getTrainings method to return mockTrainings, then expects the bloc to emit TrainingsLoading followed by TrainingsLoaded with the mock data, hasReachedMax false, and currentPage 1. The second blocTest validates error handling by mocking the repository to throw a 'Network error' exception, expecting the bloc to emit TrainingsLoading followed by TrainingsError with the error message. These tests ensure proper state transitions and error handling in the training loading flow.

### Widget Testing

**Widget Testing Implementation:**
The widget test verifies that CourseCard widgets are properly displayed when trainings are loaded. It creates a MockTrainingsBloc and sets its state to TrainingsLoaded with mockTrainings data, hasReachedMax false, and currentPage 1. The test pumps a MaterialApp containing a BlocProvider.value that provides the mock bloc to a TrainingsTab widget. The assertions verify that CourseCard widgets are found in the widget tree and that the first training's title text is displayed exactly once, confirming that the UI correctly renders training data when the bloc is in the loaded state.

## Performance Optimizations

### 1. Image Caching

**Optimized Image Caching Implementation:**
The OptimizedCourseCard implements performance-focused image loading using CachedNetworkImage with memory optimization. It sets memCacheHeight to 200 and memCacheWidth to 300 to limit memory cache size and prevent excessive memory usage. The widget uses DefaultCacheManager for consistent caching behavior across the app, includes a ShimmerPlaceholder for loading states to provide better user feedback, and displays a DefaultImagePlaceholder for error states. This optimization reduces memory footprint while maintaining smooth image loading performance in course lists.

### 2. List Optimization

**Optimized List Performance Implementation:**
The OptimizedCourseList implements ListView.builder with performance optimizations for smooth scrolling. It sets itemExtent to 280 for fixed item height, which improves scrolling performance by eliminating height calculations. The cacheExtent is increased to 1000 to cache more items off-screen, reducing rebuild frequency during scrolling. Each CourseCard is wrapped in RepaintBoundary to isolate repainting and prevent unnecessary redraws of neighboring items when individual cards update. These optimizations ensure smooth scrolling performance even with large course lists.

## Related Documentation

- [Search Feature](search.md)
- [Course Details Feature](course-details.md)
- [State Management Guide](../architecture/state-management.md)
- [API Architecture](../api/api-architecture.md)
