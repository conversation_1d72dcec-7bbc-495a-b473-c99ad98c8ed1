# Training Consumption Feature

This document covers the Training Consumption feature of the NSP Flutter application, which manages the core learning experience where users consume training content including videos, articles, slides, files, and quizzes.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [State Management](#state-management)
- [Lesson Types](#lesson-types)
- [Navigation System](#navigation-system)
- [Content Delivery](#content-delivery)
- [Progress Tracking](#progress-tracking)
- [Implementation Details](#implementation-details)
- [Best Practices](#best-practices)

## Overview

The Training Consumption feature provides the core learning experience for users taking courses. It handles different types of content delivery, progress tracking, and seamless navigation between lessons and sections.

### Key Responsibilities

- **Content Delivery**: Display various lesson types (video, article, slide, file, quiz)
- **Progress Tracking**: Mark lessons as completed and track user progress
- **Navigation**: Seamless navigation between lessons and sections
- **Offline Support**: Download and cache content for offline viewing
- **Assessment Integration**: Handle quizzes and qualification tests
- **Live Sessions**: Support for instructor-led live sessions

### Supported Content Types

1. **Video Lessons**: Streaming video content with custom controls
2. **Article Lessons**: HTML-based text content with rich formatting
3. **Slide Lessons**: PDF presentations with full-screen viewing
4. **File Lessons**: Downloadable resources and documents
5. **Quiz Lessons**: Interactive assessments and knowledge checks
6. **Live Sessions**: Real-time instructor-led sessions

## Architecture

### Directory Structure

```
lib/features/training_consumption/
├── data/
│   ├── data_sources/
│   │   └── training_consumption_datasource.dart
│   ├── models/
│   │   ├── content_item.dart
│   │   └── training_consumption_model.dart
│   └── params/
│       ├── lesson_params.dart
│       └── live_session_params.dart
├── domain/
│   └── repositories/
│       └── training_consumption_repository.dart
└── presentation/
    ├── bloc/
    │   ├── training_consumption_bloc.dart
    │   ├── training_consumption_event.dart
    │   └── training_consumption_state.dart
    ├── mixin/
    │   ├── slide_completion_mixin.dart
    │   └── unified_navigation_mixin.dart
    ├── pages/
    │   ├── lesson_pages/
    │   │   ├── article_lesson.dart
    │   │   ├── file_lesson.dart
    │   │   ├── slide_lesson.dart
    │   │   ├── video_lesson.dart
    │   │   └── widgets/
    │   ├── training_consumption_page.dart
    │   └── training_consumption_view.dart
    └── widgets/
        ├── training_overview.dart
        ├── training_section_card.dart
        └── lesson_bottom_navigation.dart
```

## State Management

### TrainingConsumptionBloc

The central state manager for the training consumption experience.

**TrainingConsumptionBloc Implementation:**
The TrainingConsumptionBloc is an injectable service that manages the training consumption experience using the BLoC pattern. It handles eight main event types: loading training consumption page data, opening lessons, opening live session lessons, marking lessons as completed, refreshing page data, getting video URLs for streaming, getting slide file paths for PDF viewing, and downloading files. The bloc depends on a TrainingConsumptionRepository for data operations and maintains the training consumption state throughout the learning experience. This centralized approach ensures consistent state management across all lesson types and navigation scenarios.

### Key Events

**TrainingConsumption Events Implementation:**
The training consumption feature defines comprehensive events for managing the learning experience. LoadTrainingConsumptionPageEvent loads training structure and progress data with a required trainingId parameter. OpenLessonEvent opens specific lessons using LessonParams containing lesson context and navigation information. MarkLessonAsCompletedEvent handles lesson completion tracking with a required Lesson parameter. GetVideoUrlEvent retrieves streaming URLs for video lessons with a Lesson parameter. GetSlideFilePathEvent downloads slide files for offline viewing with a Lesson parameter. These events provide complete coverage of training consumption operations while maintaining clear separation of concerns and proper parameter validation.

### Lesson Navigation Logic

**Lesson Navigation Logic:**
The _openLessonEvent method handles lesson navigation with sophisticated routing logic based on lesson type and navigation context. It defines a local navigate function that determines whether to use pushReplacement or pushNamed based on the navigation type (initial vs. subsequent navigation). The method then uses a switch statement to route to the appropriate lesson page based on lesson type: Article lessons navigate directly to the article page, Video lessons first trigger video URL retrieval then navigate to the video player, File and Slide lessons navigate to their respective viewers, and Quiz lessons include quiz result data for proper state restoration. After navigation, it refreshes the training consumption page to update progress and state information.

## Lesson Types

### 1. Video Lessons

Streaming video content with custom video player controls.

**VideoLesson Widget Implementation:**
The VideoLesson is a StatelessWidget that provides a comprehensive video learning interface with proper state management. It creates a Scaffold with a black AppBar (zero toolbar height for full-screen video experience) and a Column layout containing a VideoPlayerWrapper component and LessonHeader. The bottomNavigationBar uses a BlocSelector to reactively monitor lesson completion status from the TrainingConsumptionBloc, enabling or disabling next navigation based on whether the current lesson is completed. This design ensures users must complete video content before proceeding while providing a clean, distraction-free viewing experience with appropriate navigation controls.

**Features:**
- Custom video player with Chewie
- Automatic completion tracking
- Full-screen support
- Progress persistence

### 2. Article Lessons

HTML-based content with rich text formatting and link handling.

**ArticleLesson Widget Implementation:**
The ArticleLesson is a StatefulWidget that provides intelligent article reading with automatic completion tracking. It initializes a ScrollController and implements sophisticated auto-completion logic that varies based on training type. For non-instructor-led training, it adds a scroll listener that monitors reading progress and automatically marks the lesson as completed when the user scrolls to 90% of the content. The widget also handles edge cases where content is shorter than the viewport by immediately marking it as read. The _markAsRead method dispatches a MarkLessonAsCompletedEvent to the bloc and removes the scroll listener to prevent duplicate completion events. This approach provides seamless progress tracking without requiring explicit user action.

**Features:**
- HTML content rendering with flutter_html
- Automatic completion on scroll
- External link handling
- Responsive text sizing

### 3. Slide Lessons

PDF presentation viewer with full-screen support and download capability.

**SlideLesson Widget Implementation:**
The SlideLesson is a StatefulWidget that provides PDF presentation viewing with download progress tracking and automatic navigation. It requires LessonParams and uses BlocProvider.value to provide the trainingConsumptionBloc from the lesson parameters. The widget implements BlocListener to monitor state changes and automatically navigates to SlideViewerLandscape when slideFilePath becomes available. The Scaffold body uses BlocBuilder to render different views based on state: _DownloadProgressView when slideDownloadProgress is available, or _LoadingView as the default. This design provides seamless slide viewing with proper progress feedback during file download and automatic transition to the full-screen landscape viewer once the PDF is ready.

**Features:**
- PDF rendering with pdfrx
- Download progress tracking
- Offline viewing capability
- Full-screen landscape mode

### 4. File Lessons

Downloadable resources and documents with file management.

**FileLesson Widget Implementation:**
The FileLesson is a StatefulWidget that provides a comprehensive file download and management interface. It creates a Scaffold with a green-themed AppBar containing a white close button and lesson title, along with a LessonBottomNavigation for lesson progression. The body uses a ListView containing a LessonHeader and a white container that dynamically renders FileTile components for each resource in the lesson. The FileTile components are separated by AppDivider widgets for clean visual separation. This design provides an organized interface for users to access, download, and manage multiple file resources associated with a lesson while maintaining consistent navigation and styling throughout the learning experience.

**Features:**
- Multiple file downloads
- File type detection
- External app integration
- Download progress tracking

## Navigation System

### Unified Navigation Mixin

The `UnifiedNavigationMixin` provides sophisticated navigation logic for moving between lessons, sections, and meetings.

**UnifiedNavigationMixin Implementation:**
The UnifiedNavigationMixin provides sophisticated navigation logic for moving between lessons, sections, and meetings within the training consumption flow. The navigateToFirstLesson method implements robust error handling by checking for empty sections or lessons and gracefully exiting if no content is available. When valid content exists, it constructs comprehensive LessonParams containing the section, lesson, completion statistics, and navigation context. The method retrieves completed lesson IDs from the current state to determine completion status and calculates the number of completed lessons in the section. Finally, it dispatches an OpenLessonEvent with forward navigation type to the bloc, ensuring proper lesson opening with full context information for state management and UI rendering.

### Navigation Types

**LessonNavigationType Enum:**
The LessonNavigationType enum defines three navigation contexts for lesson transitions: initial for first-time lesson opening, forward for moving to the next lesson in sequence, and backward for moving to the previous lesson. This enum enables the navigation system to apply appropriate transition logic and state management based on the direction and context of lesson navigation.

### Content Navigation Result

**ContentNavigationResult Implementation:**
The ContentNavigationResult class represents the result of content navigation operations within the training consumption system. It contains a required ContentType (lesson, meeting, or test), dynamic content object for flexible content handling, and optional Section for context information. The ContentType enum defines the three main content categories that users can navigate to during training consumption. This structure enables the navigation system to handle different content types uniformly while maintaining type safety and providing necessary context for proper rendering and state management.

## Content Delivery

### Video Streaming

**Video URL Retrieval Implementation:**
The _getVideoUrl method handles video streaming URL retrieval for video lessons. It begins by emitting a loading state, then extracts the video key from the lesson's first resource. If no video key exists, it emits an error state with a localized error message and stops processing. For valid video keys, it calls the repository's getVideoUrl method using the errorHandler pattern, emitting success state with the retrieved video URL or error state with the error message. This approach ensures proper loading state management and error handling for video streaming operations while providing clear user feedback throughout the process.

### File Downloads

**File Download Implementation:**
The _downloadFile method handles file download operations with progress tracking and automatic file opening. It begins by emitting a loading state, then calls the repository's downloadFile method with the resource and a progress callback that emits state updates with download progress and clears the loading flag. The errorHandler pattern manages success and error scenarios: on success, it emits state with the downloaded file path, clears progress, and automatically opens the file using OpenFilex; on error, it emits error state with the error message and clears both progress and loading flags. This implementation provides comprehensive download management with real-time progress feedback and seamless file access upon completion.

## Progress Tracking

### Lesson Completion

**Lesson Completion Tracking Implementation:**
The _markLessonAsCompletedEvent method handles lesson completion with local state updates and API synchronization. It extracts the training ID from the current state and lesson ID from the event, then calls the repository's markLessonAsCompleted method using the errorHandler pattern. On success, it updates the local state by creating a new Set from existing completed lesson IDs, adding the current lesson ID, and creating an updated model with the new completion set. The updated model is then emitted to reflect the completion status immediately in the UI. On error, it emits an error state with the error message. This approach ensures immediate UI feedback while maintaining data consistency with the backend.

### Auto-Completion Logic

Different lesson types have different completion criteria:

- **Video**: Completed when video finishes playing
- **Article**: Completed when user scrolls to 90% of content
- **Slide**: Completed when user views all slides
- **File**: Completed immediately when opened
- **Quiz**: Completed when quiz is submitted with passing score

## Implementation Details

### Lesson Parameters

**LessonParams Data Structure:**
The LessonParams class encapsulates all necessary information for lesson navigation and rendering. It contains required fields for section context, lesson data, completedLessonsInSection count for progress tracking, isCompleted boolean for completion status, trainingConsumptionBloc for state management, and optional lessonNavigationType defaulting to initial. This comprehensive parameter object ensures that lesson pages have access to all necessary context for proper rendering, navigation, and state management while maintaining clean interfaces between navigation logic and lesson presentation components.

### Live Session Support

**LiveSessionParams Data Structure:**
The LiveSessionParams class provides parameters for live session functionality within training consumption. It contains required fields for meeting data and trainingConsumptionBloc for state management, plus an optional sublesson field for cases where live sessions contain sub-lessons. This parameter object enables proper integration of live sessions into the training consumption flow while maintaining access to necessary state management and meeting context information.

### Error Handling

**Centralized Error Handling Pattern:**
The training consumption feature implements a consistent error handling pattern using the errorHandler method for all operations. The pattern includes onSuccess callback that handles successful operations by emitting updated state with result data and clearing loading flags, and onError callback that handles failures by emitting error state with user-friendly error messages and clearing loading flags. This centralized approach ensures consistent error handling across all training consumption operations while providing proper loading state management and user feedback.

## Best Practices

### Performance Optimization

1. **Lazy Loading**: Load content only when needed
2. **Caching**: Cache downloaded files and video URLs
3. **Memory Management**: Dispose of video controllers properly
4. **Background Downloads**: Use background downloader for large files

### User Experience

1. **Progress Indicators**: Show download/loading progress
2. **Offline Support**: Enable offline viewing of downloaded content
3. **Seamless Navigation**: Smooth transitions between lessons
4. **Auto-Save**: Automatically save progress

### Error Handling

1. **Network Errors**: Graceful handling of connectivity issues
2. **File Errors**: Handle download failures and corrupted files
3. **Playback Errors**: Fallback options for video playback issues
4. **User Feedback**: Clear error messages and recovery options

### Testing

1. **Unit Tests**: Test navigation logic and state management
2. **Widget Tests**: Test lesson page rendering and interactions
3. **Integration Tests**: Test complete learning flows
4. **Performance Tests**: Test with large files and long videos

## Related Documentation

- [Quiz Feature](quiz.md) - Quiz and assessment functionality
- [Qualification Test](qualification-test.md) - Testing system
- [Course Details](course-details.md) - Course information display
- [State Management](../architecture/state-management.md) - BLoC patterns
- [Navigation](../navigation/routing-system.md) - App navigation system

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
