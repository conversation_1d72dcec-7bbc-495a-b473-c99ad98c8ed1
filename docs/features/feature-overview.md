# Feature Overview

The NSP app is organized into feature modules. This guide helps you understand what each feature does and how to work with them.

## Quick Feature Reference

| Feature | Purpose | Main Entry Point | Key BLoC |
|---------|---------|------------------|----------|
| **Auth** | Login/logout | `QiwaWebView` | `AuthBloc` |
| **Catalog** | Browse courses | `CatalogPage` | `TrainingsBloc` |
| **Course Details** | Course info & enrollment | `TrainingDetailsPage` | `TrainingDetailsBloc` |
| **Home** | Dashboard | `HomePage` | `HomePageBloc` |
| **My Learnings** | User's courses | `MyLearningsPage` | `MyLearningsBloc` |
| **Profile** | User settings | `ProfilePage` | `UserBloc` |
| **Training Consumption** | Taking courses | `TrainingConsumptionPage` | `TrainingConsumptionBloc` |
| **Quiz** | In-lesson quizzes | `QuizPage` | `QuizBloc` |
| **Search** | Find courses | `SearchPage` | `SearchBloc` |

## Feature Structure

Every feature follows this pattern:
```
feature_name/
├── data/
│   ├── data_sources/    # API calls
│   ├── models/          # JSON models
│   └── repositories/    # Data logic
├── domain/
│   └── repositories/    # Interfaces
└── presentation/
    ├── bloc/            # State management
    ├── pages/           # Screens
    └── widgets/         # UI components
```

## User Journey Through Features

```
1. Auth → Login via Qiwa
2. Home → See dashboard & featured content
3. Catalog → Browse available courses
4. Course Details → View course info & enroll
5. Training Consumption → Take the course
6. Quiz → Complete assessments
7. My Learnings → Track progress
8. Profile → Manage account
```

## Core Features

### 1. Authentication (`auth/`)

**What it does**: Handles login/logout via Qiwa OAuth integration.

**Implementation Details**:
- **Authentication State Management**: Uses AuthBloc to manage login/logout states with reactive UI updates
- **Qiwa OAuth Integration**: Implements secure OAuth flow through WebView for government authentication
- **Token Management**: Secure storage and automatic refresh of authentication tokens
- **Session Persistence**: Maintains user sessions across app restarts with secure token storage
- **Logout Coordination**: Comprehensive logout process that clears all user data and navigates to login screen

**Key files**:
- `AuthBloc` - Main state management
- `QiwaWebView` - OAuth login interface
- `AuthTokenProvider` - Secure token storage

### 2. Catalog (`catalog/`)

**What it does**: Browse and filter available courses and learning tracks.

**Implementation Details**:
- **Dual Content Types**: Manages both individual trainings and structured learning tracks with separate BLoCs
- **Advanced Filtering**: Multi-criteria filtering by sector, provider, duration, language, and skill level
- **Search Integration**: Real-time search with suggestions and result highlighting
- **Sorting Options**: Multiple sorting criteria including popularity, date, rating, and duration
- **Pagination Support**: Efficient loading of large course catalogs with infinite scroll
- **Cache Management**: Intelligent caching of course data for improved performance and offline browsing

**Key files**:
- `TrainingsBloc` - Course listings
- `LearningTracksBloc` - Learning track listings
- `CatalogPage` - Main catalog interface

### 3. Course Details (`course_details/`)

**What it does**: Shows detailed course information and handles enrollment.

**Implementation Details**:
- **Comprehensive Course Information**: Displays detailed course metadata including description, requirements, outcomes, and syllabus
- **Enrollment Management**: Handles course enrollment with prerequisite validation and capacity checking
- **Progress Integration**: Shows user progress for enrolled courses with completion tracking
- **Prerequisite Validation**: Checks and displays course prerequisites with enrollment eligibility
- **Rating and Reviews**: Displays course ratings and user reviews for informed decision making
- **Instructor Information**: Shows instructor profiles and credentials for course credibility

**Key files**:
- `TrainingDetailsBloc` - Course info & enrollment
- `TrainingDetailsPage` - Course details UI
- `LearningTrackDetailsPage` - Learning track details

### 4. Home (`home/`)

**What it does**: Main dashboard showing featured content and user progress.

**Implementation Details**:
- **Personalized Dashboard**: Displays customized content based on user authentication status and preferences
- **Featured Content**: Showcases popular courses, trending sectors, and recommended learning paths
- **Progress Overview**: Quick access to user's learning progress and recent activities for authenticated users
- **Sector Navigation**: Easy access to different industry sectors with visual sector cards
- **Localization Support**: Dynamic content loading based on user's selected language preference
- **Authentication-Aware UI**: Different layouts and content for authenticated vs guest users

**Key files**:
- `HomePageBloc` - Dashboard state
- `HomePage` - Main dashboard UI
- `SectorsPage` - Sector browsing

### 5. My Learnings (`my_learnings/`)

**Purpose**: User's learning journey management

**Key Responsibilities**:
- Display enrolled courses and learning tracks
- Track learning progress
- Filter by enrollment status
- Access to course materials
- Learning history and achievements

**Main Components**:
- `MyLearningsBloc`: Manages user's learning data
- `MyLearningsFilterCubit`: Client-side filtering
- `ViewAllMyLearnings`: Detailed learning list
- `MyLearningsRepository`: User learning data access

**Key Features**:
- Status-based filtering (enrolled, completed, in-progress)
- Progress tracking
- Quick access to continue learning
- Learning history
- Achievement badges

### 6. Profile (`profile_page/`)

**Purpose**: User profile management and settings

**Key Responsibilities**:
- Display user information
- Profile picture management
- Account settings
- Logout functionality
- Personal information updates

**Main Components**:
- `UserBloc`: Manages user data and profile state
- `ProfilePage`: Main profile interface
- `PersonalInfoPage`: Detailed profile editing
- `ImageCropView`: Profile picture editing

**Key Features**:
- Profile picture upload and cropping
- Personal information editing
- Account preferences
- Logout with confirmation
- Profile data validation

### 7. Qualification Test (`qualification_test/`)

**Purpose**: Pre and post-training assessments

**Key Responsibilities**:
- Conduct mandatory qualification tests
- Question presentation and navigation
- Answer recording and validation
- Score calculation and display
- Retake management

**Main Components**:
- `QualificationTestBloc`: Test state management
- `QualificationTestPage`: Test interface
- `TestRepository`: Test data and submission

**Key Features**:
- Timed test sessions
- Multiple question types
- Progress tracking
- Score reporting
- Retake eligibility
- Integration with training progression

### 8. Quiz (`quiz/`)

**Purpose**: In-lesson quiz functionality

**Key Responsibilities**:
- Present lesson-embedded quizzes
- Immediate feedback and scoring
- Progress tracking within lessons
- Integration with course progression

**Main Components**:
- `QuizBloc`: Quiz state management
- `QuizPage`: Quiz interface
- `QuizRepository`: Quiz data management

**Key Features**:
- Interactive question types
- Immediate feedback
- Score tracking
- Lesson integration
- Progress persistence

### 9. Search (`search/`)

**Purpose**: Global search functionality

**Key Responsibilities**:
- Search across courses and learning tracks
- Search suggestions and autocomplete
- Filter integration with search results
- Search history and saved searches

**Main Components**:
- `SearchBloc`: Search state management
- `SearchPage`: Search interface
- `ViewAllPage`: Detailed search results
- `SearchRepository`: Search data access

**Key Features**:
- Real-time search suggestions
- Advanced search filters
- Search result categorization
- Search history
- Integration with catalog filters

### 10. Training Consumption (`training_consumption/`)

**Purpose**: Course taking and content consumption

**Key Responsibilities**:
- Lesson content presentation
- Progress tracking through courses
- Navigation between lessons
- Content type handling (video, article, file, slide)
- Quiz and test integration

**Main Components**:
- `TrainingConsumptionBloc`: Learning session state
- Content viewers (Article, Video, File, Slide)
- `LessonNavigationMixin`: Navigation logic
- `ProgressTracker`: Learning progress management

**Key Features**:
- Multi-format content support
- Lesson navigation with progress
- Bookmark and note-taking
- Offline content access
- Integration with assessments

## Shared Components (`shared/`)

**Purpose**: Common UI components and utilities

**Key Components**:
- `AppBottomNavBar`: Main navigation
- UI Components (buttons, inputs, cards)
- Custom transitions and animations
- Shared widgets and utilities

## Working with Features

### Common Development Tasks

#### 1. Adding a New Feature Screen

**Process Overview**:
- **Page Widget Creation**: Create a StatelessWidget that provides the feature's BLoC through dependency injection
- **Route Registration**: Add the new route to the GoRouter configuration with appropriate path and parameters
- **Navigation Integration**: Implement navigation to the feature from relevant entry points in the app
- **BLoC Provision**: Ensure the feature's BLoC is properly registered in the dependency injection container
- **State Management**: Follow the established BLoC pattern for consistent state management across features

#### 2. Integrating with Existing Features

**Integration Patterns**:
- **Authentication Awareness**: Listen to AuthBloc state changes to handle login/logout events appropriately
- **Cross-Feature Data Access**: Access shared data through global BLoCs like UserBloc for user information
- **Navigation Coordination**: Use GoRouter for type-safe navigation between features with proper parameter passing
- **State Synchronization**: Implement listeners to update feature state when related features change
- **Event Broadcasting**: Use BLoC events to communicate state changes across feature boundaries

#### 3. Feature Communication Patterns

**Recommended Approaches**:
- **Event-Driven Communication**: Use BLoC events to trigger actions in other features when state changes occur
- **Listener Pattern**: Implement BlocListener widgets to react to state changes from other features
- **Shared State Management**: Use global BLoCs for data that needs to be shared across multiple features
- **Navigation-Based Communication**: Pass data between features through navigation parameters and route state

**Anti-Patterns to Avoid**:
- **Direct BLoC Access**: Avoid accessing other feature BLoCs directly from within a feature's business logic
- **Tight Coupling**: Don't create direct dependencies between feature implementations
- **Global State Abuse**: Avoid putting feature-specific state in global BLoCs

### Feature Data Flow

**Standard Flow Pattern**:
1. **User Interaction**: User performs an action in the UI (tap, swipe, input)
2. **Event Dispatch**: UI component dispatches a BLoC event representing the user action
3. **Business Logic**: BLoC processes the event and determines required data operations
4. **Repository Call**: BLoC calls appropriate repository methods for data access
5. **Data Source Access**: Repository coordinates with data sources (API, local storage)
6. **Data Processing**: Repository transforms raw data into domain entities
7. **State Emission**: BLoC emits new state based on operation results
8. **UI Update**: UI rebuilds reactively based on the new state

### Cross-Feature Dependencies

| Feature | Depends On | Why |
|---------|------------|-----|
| **All Features** | Auth | User authentication state |
| **My Learnings** | Course Details | Enrollment updates |
| **Training Consumption** | Course Details | Course structure data |
| **Quiz** | Training Consumption | Lesson progress context |
| **Profile** | Auth | User data management |

### Feature Testing Strategy

**Testing Levels**:

#### 1. Unit Testing
- **BLoC Testing**: Test business logic and state transitions using bloc_test package
- **Repository Testing**: Test data transformation and error handling with mocked data sources
- **Use Case Testing**: Test individual business operations in isolation

#### 2. Widget Testing
- **UI Component Testing**: Test individual widgets and their behavior
- **Page Testing**: Test complete page functionality with mocked BLoCs
- **Integration Testing**: Test widget interactions and navigation flows

#### 3. Integration Testing
- **Feature Flow Testing**: Test complete user journeys through features
- **Cross-Feature Testing**: Test interactions between different features
- **End-to-End Testing**: Test complete application workflows

## Data Dependencies

### Cross-Feature Data Sharing
- **Authentication** → All features (user context)
- **Profile** → Home, My Learnings (user data)
- **Catalog** → Course Details, My Learnings (course data)
- **Course Details** → Training Consumption (enrollment data)
- **Training Consumption** → Quiz, Qualification Test (progress data)

### State Synchronization
- Login triggers user data refresh across features
- Enrollment updates My Learnings and progress
- Course completion updates achievements and progress
- Profile changes reflect across the app

## Performance Considerations

### Lazy Loading
- Features load data only when accessed
- Pagination for large datasets
- Image lazy loading and caching

### Caching Strategy
- Course data cached for offline access
- User progress cached locally
- Search results cached for quick access

### Memory Management
- Proper BLoC disposal
- Image cache management
- Video player resource cleanup

## Testing Strategy

### Feature-Level Testing
- Unit tests for BLoCs and repositories
- Widget tests for UI components
- Integration tests for complete flows

### Cross-Feature Testing
- Authentication flow testing
- Navigation testing
- Data synchronization testing

## Development Guidelines

### Adding New Features
1. Follow the established feature structure
2. Implement Clean Architecture layers
3. Use BLoC for state management
4. Add comprehensive tests
5. Update documentation

### Feature Communication
- Use dependency injection for shared services
- Implement proper error handling
- Follow established patterns
- Maintain loose coupling

### Code Organization
- Group related functionality
- Use consistent naming conventions
- Implement proper abstractions
- Document complex interactions

## Related Documentation

- [Authentication Feature](auth.md)
- [Catalog Feature](catalog.md)
- [Course Details Feature](course-details.md)
- [Home Feature](home.md)
- [My Learnings Feature](my-learnings.md)
- [Profile Feature](profile.md)
- [Qualification Test Feature](qualification-test.md)
- [Quiz Feature](quiz.md)
- [Search Feature](search.md)
- [Training Consumption Feature](training-consumption.md)
