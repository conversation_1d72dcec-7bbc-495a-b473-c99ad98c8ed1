# Home Feature

This document covers the Home feature of the NSP Flutter application, which serves as the main dashboard and entry point for users. The home screen provides personalized content, featured trainings, and quick access to key app features.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [State Management](#state-management)
- [UI Components](#ui-components)
- [Data Models](#data-models)
- [User Experience](#user-experience)
- [Implementation Details](#implementation-details)
- [Testing](#testing)
- [Best Practices](#best-practices)

## Overview

The Home feature provides a personalized dashboard experience that adapts based on the user's authentication status. It displays featured content, popular trainings, sector highlights, and user-specific information.

### Key Responsibilities

- **Welcome Experience**: Personalized greeting and user information
- **Content Discovery**: Featured trainings and popular courses
- **Quick Navigation**: Easy access to main app sections
- **Sector Exploration**: Highlighted sectors and categories
- **Progress Overview**: User's learning progress (for authenticated users)

### User States

The home screen adapts to two main user states:

1. **Not Authenticated**: Shows promotional content and sign-in options
2. **Authenticated**: Shows personalized content and user progress

## Architecture

### Directory Structure

```
lib/features/home/
├── data/
│   ├── data_sources/
│   │   ├── sectors_datasource.dart
│   │   └── sectors_features_datasource.dart
│   └── models/
│       ├── active_sector_response.dart
│       ├── sector_model.dart
│       └── sectors_features_model.dart
├── domain/
│   └── repositories/
│       ├── sectors_features_repository.dart
│       └── sectors_repository.dart
└── presentation/
    ├── home_page_bloc/
    │   ├── home_page_bloc.dart
    │   ├── home_page_event.dart
    │   └── home_page_state.dart
    ├── pages/
    │   ├── home_page.dart
    │   └── sectors_page.dart
    └── widgets/
        ├── home_page_card.dart
        ├── popular_trainings.dart
        ├── recent_in_progress.dart
        ├── sectors_section.dart
        └── welcome_card.dart
```

### Clean Architecture Layers

- **Presentation**: UI components and BLoC state management
- **Domain**: Business logic and repository interfaces
- **Data**: Data sources and models for API communication

## State Management

### HomePageBloc

The `HomePageBloc` manages the home screen state, handling sector features loading and authentication-based content display.

**HomePageBloc Implementation:**
The HomePageBloc is a singleton service that manages the home screen state, handling sector features loading and authentication-based content display using the BLoC pattern. It handles the HomePageLoad event by first emitting a default HomePageState, then attempting to fetch sectors features through the SectorsFeaturesRepository. The bloc uses try-catch error handling: on success, it updates the state with the loaded sectors data and clears the loading flag; on error, it stores the error message and clears the loading flag. This approach ensures proper state management for the home screen while providing graceful error handling for sector feature loading and maintaining consistent state across the application.

**BLoC Features:**
- **Sector Management**: Loads and manages featured sectors for home display
- **Error Handling**: Graceful error handling with user-friendly messages
- **State Synchronization**: Coordinates with authentication state for personalized content
- **Singleton Pattern**: Single instance ensures consistent state across the app

### State Definition

The home page state manages loading states and sector data with immutable state objects.

**HomePageState Definition:**
The HomePageState is a Freezed-generated immutable data class that manages loading states and sector data with comprehensive state properties. It includes an isSectorsLoading boolean flag defaulting to true for initial loading indication, a sectorsError string defaulting to empty for storing error messages from failed sector loading operations, and a nullable sectors field for containing loaded SectorsFeaturesResponseModel data. This state structure enables reactive UI updates throughout the home screen experience, providing proper loading indicators, error handling, and data display capabilities while maintaining immutability and type safety.

**State Properties:**
- **isSectorsLoading**: Controls loading indicators for sector content
- **sectorsError**: Stores error messages for failed sector loading
- **sectors**: Contains loaded sector features data for display

### Events

**HomePageEvent Definition:**
The HomePageEvent is a Freezed-generated union class that defines the events for home page interactions. It currently includes a single event type: HomePageLoad, which triggers the initial loading of home page content with locale-specific data. The event requires a locale string parameter to ensure proper localization of sector features and other content. This event-driven approach enables clean separation between user interactions and business logic while supporting internationalization requirements for the home screen content loading process.

**Event Types:**
- **HomePageLoad**: Triggers initial loading of home page content with locale-specific data

## UI Components

### Main Home Page

**HomePage Widget Implementation:**
The HomePage is a StatefulWidget that creates the main home screen interface with authentication-aware content display. It accepts an AuthStateEnum parameter and builds a Scaffold with the standard NspAppBar. The body uses a BlocProvider to provide the HomePageBloc and implements a StreamBuilder that listens to the authentication state stream. Based on authentication status, it conditionally renders different content: HomePageCard for unauthenticated users or WelcomeCard and RecentInProgress for authenticated users. Common sections like PopularTrainings and SectorsSection appear for all users. This design provides a personalized experience while maintaining consistent navigation and core functionality across authentication states.

### Key Widgets

#### 1. HomePageCard (Unauthenticated Users)

Displays promotional content and sign-in button for non-authenticated users.

**HomePageCard Widget Implementation:**
The HomePageCard is a StatelessWidget that displays promotional content for unauthenticated users. It accepts a maxHeight parameter and creates a container with horizontal padding and calculated height (maxHeight minus bottom navigation bar height). The widget uses a Stack layout with a background image (AssetsPath.imageWithLinesNew) in a ClipRRect with 8px border radius and cover fit. The content overlay contains a Column with CrossAxisAlignment.start and MainAxisAlignment.center, including a localized skills portal title using h2.semiBold.white text theme, body text using textMedium.white styling, and an AppButton with white background that navigates to the login route. The button text uses textSmall.semiBold.accentGreenPrimary styling for visual contrast.

#### 2. WelcomeCard (Authenticated Users)

Shows personalized welcome message and user information.

**WelcomeCard Widget Implementation:**
The WelcomeCard is a StatelessWidget that displays personalized welcome content for authenticated users. It uses BlocBuilder<UserBloc, UserState> to reactively build UI based on user data state. The widget creates a container with horizontal margins and BoxDecoration featuring 8px border radius and a background image (AssetsPath.imageWithLinesNew) with cover fit. The content includes a Row with a CircleAvatar (30px radius) that displays either the user's profile picture via NetworkImage or a default avatar image, followed by a Flexible Column containing user information. The welcome text uses AppShimmer for loading states and displays a localized welcome message with the user's name using textLarge.semiBold.white styling with ellipsis overflow. The layout also includes a _NumberOfTrainingsInProgress widget for displaying user progress information.

#### 3. PopularTrainings

Displays a horizontal list of popular training courses.

**PopularTrainings Widget Implementation:**
The PopularTrainings is a StatelessWidget that displays a horizontal list of popular training courses. It uses BlocBuilder<TrainingsBloc, TrainingsState> to reactively build UI based on trainings data state. The widget creates a Column with CrossAxisAlignment.start containing a section title with horizontal padding and h3.semiBold text styling. The content area conditionally displays a ShimmerPlaceholder (300px height) during loading states or a LimitedBox (335px max height) containing a horizontal ListView.builder for loaded states. The list displays up to 6 items using min function, with the 6th item (index 5) showing a _SeeAllButton for navigation to the full catalog. Each training item is rendered using TrainingsCard widget with the corresponding training data from the state's trainingsList.

#### 4. SectorsSection

Shows featured sectors with navigation to sector-specific content.

**SectorsSection Widget Implementation:**
The SectorsSection is a StatelessWidget that displays featured sectors in a grid layout. It uses BlocBuilder<HomePageBloc, HomePageState> to reactively build UI based on home page state. The widget creates a Column with CrossAxisAlignment.start containing a localized sectors title with horizontal padding and h3.semiBold text styling. The content area conditionally displays a ShimmerPlaceholder (200px height) during loading states or a GridView.builder when sectors data is available. The grid uses SliverGridDelegateWithFixedCrossAxisCount with 2 columns, 16px spacing, 1.5 aspect ratio, shrinkWrap enabled, and NeverScrollableScrollPhysics to prevent scrolling conflicts. Each sector is rendered using SectorCard widget with the corresponding sector data from the state's sectors list.

## Data Models

### SectorsFeaturesResponseModel

**Sectors Features Data Models:**
The sectors features models provide comprehensive data structures for home screen sector display with JSON serialization support. SectorsFeaturesResponseModel extends Equatable and contains a list of SectorFeatureModel objects, providing automatic JSON serialization through generated factory methods and proper equality comparison. SectorFeatureModel represents individual sector features with required fields for unique identification, display title, display order for sorting, and creation timestamp. Optional imageUrl supports visual representation. Both models include toJson and fromJson methods for seamless API integration while maintaining immutability and type safety throughout the home screen sector management system.

### ActiveSectorResponse

**ActiveSectorResponse Data Model:**
The ActiveSectorResponse is a JsonSerializable data class that extends Equatable for paginated sector data from the API. It contains required fields for content (List<ActiveSectorModel>), page number, size per page, totalRecords count, and totalPages count for pagination support. The class includes a constructor with all required parameters, a fromJson factory method generated by JsonSerializable for automatic JSON deserialization, and an Equatable props getter that includes all fields for proper equality comparison. This model provides comprehensive pagination metadata along with the actual sector content for efficient data handling in the home screen sectors section.

## User Experience

### Authentication-Based Flow

#### Unauthenticated Users
1. **Landing Experience**: Full-screen promotional card with app branding
2. **Call to Action**: Prominent sign-in button
3. **Content Preview**: Popular trainings and sectors (limited view)
4. **Navigation**: Access to catalog and basic features

#### Authenticated Users
1. **Personalized Welcome**: User name and avatar display
2. **Progress Overview**: Current trainings and completion status
3. **Recommendations**: Personalized content based on user data
4. **Quick Access**: Direct navigation to user's learning content

### Content Sections

1. **Hero Section**: Welcome card or promotional content
2. **Recent Progress**: User's current trainings (authenticated only)
3. **Popular Trainings**: Trending courses with horizontal scroll
4. **Sectors**: Featured sectors in grid layout
5. **Quick Actions**: Navigation to main app features

## Implementation Details

### Authentication State Handling

**Authentication State Handling Implementation:**
The home page uses StreamBuilder<bool> to reactively handle authentication state changes by listening to authTokenProvider.authStateStream. When no data is available, it displays a BuildLoader widget. For available data, it extracts the isAuthenticated boolean and builds a ListView with conditional content based on authentication status. Unauthenticated users see the HomePageCard with maxHeight parameter, while authenticated users see WelcomeCard and RecentInProgress widgets. Both user types see PopularTrainings and SectorsSection components, providing a consistent experience with personalized content for authenticated users and promotional content for visitors.

### Data Loading Strategy

**Data Loading Strategy Implementation:**
The home page implements a coordinated data loading strategy using multiple BLoCs. On initialization, it dispatches HomePageLoad event with locale 'en' to the homePageBloc for loading sectors features. Popular trainings are loaded by dispatching GetTrainings event to the TrainingsBloc via context.read. For authenticated users, user data is loaded conditionally by dispatching GetUserData event to the UserBloc. This approach ensures efficient data loading with proper separation of concerns, where each BLoC manages its specific domain data while coordinating to provide a complete home page experience.

### Caching Strategy

The home feature implements caching for frequently accessed data:

**Caching Strategy Implementation:**
The SectorRepository implements in-memory caching for frequently accessed sector data to improve performance and reduce API calls. It maintains _cachedSectors (List<SectorModel>) and _cachedLocale (String) private fields for cache storage and locale tracking. The getSectors method first checks if cached data exists and matches the requested locale, returning cached data immediately if available. For cache misses or locale changes, it fetches fresh data from the data source, updates both cache fields, and returns the new data. This simple but effective caching strategy reduces network requests and improves user experience by providing instant access to previously loaded sector information.

## Testing

### BLoC Testing

**HomePageBloc Testing Implementation:**
The HomePageBloc test uses bloc_test package to verify correct state emissions when HomePageLoad event is dispatched. The test mocks the SectorsFeaturesRepository's getSectorsFeatures method to return an empty SectorsFeaturesResponseModel. When the HomePageLoad event with locale 'en' is added to the bloc, it expects two state emissions: first the initial HomePageState, then an updated HomePageState with isSectorsLoading set to false and sectors containing the empty SectorsFeaturesResponseModel. This test ensures proper state management during the home page loading process and validates that the bloc correctly handles successful data loading scenarios.

### Widget Testing

**Widget Testing Implementation:**
The widget test verifies that the HomePage displays correct content for authenticated users. It pumps a HomePage widget wrapped in NspTestWrapper with authState set to AuthStateEnum.loggedIn to simulate an authenticated user session. The test then asserts that all expected widgets are present exactly once: WelcomeCard for personalized greeting, RecentInProgress for user's current learning status, PopularTrainings for trending courses, and SectorsSection for featured sectors. This comprehensive test ensures that authenticated users see the complete home page experience with all personalized and general content sections properly rendered.

## Best Practices

### Performance Optimization

1. **Lazy Loading**: Load content sections progressively
2. **Image Caching**: Use `CachedNetworkImage` for sector images
3. **List Optimization**: Implement proper list builders for large datasets
4. **State Management**: Minimize unnecessary rebuilds

### User Experience

1. **Loading States**: Show shimmer placeholders during data loading
2. **Error Handling**: Graceful error states with retry options
3. **Responsive Design**: Adapt layout for different screen sizes
4. **Accessibility**: Proper semantic labels and navigation

### Code Organization

1. **Widget Separation**: Break down complex widgets into smaller components
2. **State Isolation**: Keep state management focused and minimal
3. **Reusability**: Create reusable components for common patterns
4. **Testing**: Maintain comprehensive test coverage

## Related Documentation

- [Authentication](auth.md) - User authentication and state management
- [Catalog](catalog.md) - Course catalog and browsing
- [My Learnings](my-learnings.md) - User's learning journey
- [Profile](profile.md) - User profile management
- [State Management](../architecture/state-management.md) - BLoC patterns
- [UI Components](../ui-components/shared-widgets.md) - Reusable widgets

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
