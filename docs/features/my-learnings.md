# My Learnings Feature

## Overview

The My Learnings feature provides users with a comprehensive view of their enrolled courses, learning progress, and achievements. It includes filtering capabilities, progress tracking, and quick access to continue learning activities.

## Architecture

### Feature Structure
```
features/my_learnings/
├── data/
│   ├── datasources/
│   │   └── my_learnings_data_source.dart
│   ├── models/
│   │   ├── my_learning_model.dart
│   │   └── learning_progress_model.dart
│   └── repositories/
│       └── my_learnings_repository.dart
├── domain/
│   ├── entities/
│   │   ├── my_learning_entity.dart
│   │   └── learning_progress_entity.dart
│   └── repositories/
│       └── my_learnings_repository.dart
└── presentation/
    ├── bloc/
    │   ├── my_learnings_bloc.dart
    │   └── my_learnings_filter_cubit.dart
    ├── pages/
    │   ├── my_learnings_page.dart
    │   └── view_all_my_learnings.dart
    └── widgets/
        ├── my_learning_card.dart
        ├── progress_indicator.dart
        └── filter_chips.dart
```

## Core Components

### 1. My Learnings BLoC

**MyLearningsBloc Implementation:**
The MyLearningsBloc is a singleton service that manages the user's learning progress and enrollment data using the BLoC pattern. It depends on a MyLearningsRepository for data operations and handles four main events: initial loading, refresh operations, filtering, and filter clearing. The _onLoadMyLearnings method emits a loading state, calls the repository to fetch learning data, and emits a loaded state with both original and filtered learning lists. The _onRefreshMyLearnings method handles pull-to-refresh functionality by checking the current state, setting a refreshing flag, fetching fresh data with forceRefresh enabled, and applying the current filter to maintain the user's filter selection during refresh operations.

### 2. My Learnings Events and States

**MyLearnings Events and States Implementation:**
The MyLearnings feature defines comprehensive events and states for managing user learning data. Events include LoadMyLearnings for initial data loading, RefreshMyLearnings for pull-to-refresh functionality, and FilterMyLearnings with a filter parameter for client-side filtering. States include MyLearningsInitial as the default state, MyLearningsLoading for data loading indicators, and MyLearningsLoaded containing learnings list, filteredLearnings list, currentFilter selection, and isRefreshing boolean for refresh state management. The MyLearningsLoaded state includes a copyWith method for immutable state updates and proper Equatable implementation with all fields in the props getter for accurate state comparison and efficient rebuilds.

### 3. Filter System

**MyLearningsFilter Enum Implementation:**
The MyLearningsFilter enum defines five filter options for categorizing user learning content: all, enrolled, inProgress, completed, and notStarted. The displayName getter provides localized text for each filter using LocaleKeys with appropriate translations for UI display. The matches method implements filtering logic by checking learning entity properties: all returns true for all items, enrolled checks enrollmentStatus against EnrollmentStatus.enrolled, inProgress validates progress between 0 and 100 exclusive, completed checks progress >= 100, and notStarted verifies progress equals 0. This enum provides both UI representation and business logic for client-side filtering of learning data.

### 4. Filter Cubit for Client-Side Filtering

**MyLearningsFilterCubit Implementation:**
The MyLearningsFilterCubit is an injectable service that manages client-side filtering for the my learnings feature using the Cubit pattern. It maintains the current filter state, starting with the 'all' filter by default. The setFilter method updates the current filter selection and emits the new state, while clearFilter resets to show all learnings. The applyFilter method provides the core filtering logic by checking if the filter is 'all' (returning the complete list) or applying the filter's matches method to return only learnings that meet the filter criteria. This approach enables responsive filtering without requiring API calls for each filter change.

## UI Components

### 1. My Learnings Page

**MyLearningsPage Widget Implementation:**
The MyLearningsPage is a StatefulWidget that provides the main interface for viewing user learning progress. In initState, it dispatches LoadMyLearnings event to trigger initial data loading. The widget builds a Scaffold with an AppBar containing a localized title and filter action button. The body uses RefreshIndicator for pull-to-refresh functionality wrapping a BlocBuilder that renders different views based on MyLearningsState: _LoadingView for loading states, _LoadedView for loaded states with data, _ErrorView for error states with message display, and SizedBox.shrink as fallback. The _onRefresh method dispatches RefreshMyLearnings event to the bloc for data refresh operations. This structure provides comprehensive state management with proper loading, error, and refresh handling.

### 2. My Learning Card

**MyLearningCard Widget Implementation:**
The MyLearningCard is a StatelessWidget that displays individual learning items with comprehensive progress information and action capabilities. It accepts a MyLearningEntity object and optional callback functions for tap and continue actions. The card uses Material Design with proper margins and rounded corners, containing an InkWell for touch feedback. The layout includes a header section, title, progress indicator, metadata, and action button. The _buildProgress method creates a visual progress display with percentage text and a LinearProgressIndicator using the app's color scheme. The _buildActionButton method dynamically determines the appropriate button text and color based on completion status, providing "View Certificate" for completed courses, "Continue" for in-progress courses, and "Start" for new enrollments.

### 3. Filter Chips

**MyLearningsFilterChips Widget Implementation:**
The MyLearningsFilterChips is a StatelessWidget that displays filter options as horizontal scrollable chips. It requires currentFilter and onFilterChanged callback parameters. The widget creates a 50px height container with horizontal padding containing a ListView.separated with horizontal scroll direction. It iterates through all MyLearningsFilter values, creating AppChip widgets with 8px spacing. Each chip displays the filter's displayName and uses conditional styling: selected filters show greenAccentPrimary background with white text, while unselected filters use accentExtraLight background with greyPrimary text. The onTap callback triggers onFilterChanged with the selected filter, enabling responsive filter selection with clear visual feedback for the current selection state.

## Data Models

### 1. My Learning Model

**MyLearningModel Data Structure:**
The MyLearningModel is a comprehensive freezed data class representing user learning information from the API. It includes required fields for id, title, and description, plus extensive optional fields for complete learning tracking. Key fields include imageUrl, enrollmentDate, completionDate, progressPercentage, totalLessons, completedLessons, estimatedDurationHours, timeSpentMinutes, enrollmentStatus, learningType (training or learning_track), provider (TrainingProviderModel), sectors list, certificateUrl, and lastAccessed timestamp. The class uses JsonKey annotations for proper API field mapping with snake_case to camelCase conversion and includes a fromJson factory constructor for JSON deserialization. This model serves as the comprehensive data transfer object for user learning progress and enrollment information.

### 2. Learning Progress Model

**LearningProgressModel Data Structure:**
The LearningProgressModel is a freezed data class that tracks detailed progress information for individual lessons within learning content. It requires learningId and lessonId for identification, plus optional fields for comprehensive progress tracking. Key fields include completionStatus, progressPercentage, timeSpentSeconds, lastPosition (for video/audio content resumption), quizScore, attemptsCount, startedAt, completedAt, and updatedAt timestamps. The class uses JsonKey annotations for proper API field mapping with snake_case to camelCase conversion and includes a fromJson factory constructor for JSON deserialization. This model enables granular progress tracking at the lesson level, supporting features like content resumption, quiz scoring, and detailed time tracking.

## Repository Implementation

### My Learnings Repository

**MyLearningsRepository Implementation:**
The MyLearningsRepository is an injectable service that manages user learning data with comprehensive caching capabilities. It requires MyLearningsDataSource and CacheManager dependencies and uses a 5-minute cache expiry duration. The getMyLearnings method implements cache-first strategy with optional forceRefresh parameter, checking cached data first unless refresh is forced, fetching from API on cache miss, transforming models to entities, and caching results. The updateProgress method handles lesson progress updates and invalidates cache to ensure fresh data. The _getCachedLearnings method safely retrieves cached data by checking file modification time against cache expiry, reading JSON, and deserializing to entities. The _cacheLearnings method stores entity data by converting to models, serializing to JSON, and storing as UTF8-encoded bytes with proper error handling.

## Progress Tracking

### 1. Progress Update Service

**ProgressTrackingService Implementation:**
The ProgressTrackingService is an injectable service that manages lesson progress updates and completion tracking. It depends on MyLearningsRepository for data persistence. The updateLessonProgress method creates a LearningProgressModel with learningId, lessonId, progressPercentage, optional timeSpentSeconds, lastPosition for content resumption, and current timestamp, then calls the repository's updateProgress method. The markLessonCompleted method handles lesson completion by creating a progress model with 'completed' status, 100% progress, optional quizScore, completedAt timestamp, and updatedAt timestamp. Both methods provide clean interfaces for tracking different types of learning progress while ensuring proper data persistence and cache invalidation through the repository layer.

### 2. Progress Calculation

**MyLearningEntityExtension Implementation:**
The MyLearningEntityExtension provides computed properties and helper methods for MyLearningEntity objects. The overallProgress getter calculates completion percentage by dividing completedLessons by totalLessons (returning 0 if no lessons exist). The estimatedTimeRemaining getter computes remaining time by subtracting timeSpentMinutes from total estimated minutes, using math.max to ensure non-negative values. Boolean getters provide convenient status checks: isCompleted (progress >= 100), isInProgress (0 < progress < 100), and isNotStarted (progress == 0). The statusText getter returns localized status strings based on completion state. The statusColor getter provides appropriate colors: statusSuccess for completed, orangeAccentPrimary for in-progress, and greySecondary for not started, enabling consistent visual representation across the UI.

## Refresh Mechanism

### 1. Pull-to-Refresh Implementation

**MyLearningsRefreshIndicator Implementation:**
The MyLearningsRefreshIndicator is a StatelessWidget that provides customized pull-to-refresh functionality for the my learnings feature. It requires a child widget and onRefresh callback function. The widget wraps the child in a RefreshIndicator with app-specific styling: greenAccentPrimary color for the refresh indicator, white background, and 40px displacement for proper visual positioning. This component provides consistent refresh behavior across the my learnings feature while maintaining the app's visual design standards and ensuring smooth user interaction during data refresh operations.

### 2. Auto-Refresh on Return

**MyLearningsPageWrapper Implementation:**
The MyLearningsPageWrapper is a StatefulWidget that implements auto-refresh functionality when users return to the my learnings page. It uses RouteAware mixin to detect navigation events and overrides didPopNext method, which is called when returning from another page. The _refreshData method dispatches RefreshMyLearnings event to the MyLearningsBloc to trigger data refresh, ensuring users always see the most current learning progress when returning from course details, training consumption, or other screens. The build method simply returns the MyLearningsPage widget, providing a transparent wrapper that enhances user experience with automatic data freshness.

## Testing

### BLoC Testing

**MyLearningsBloc Testing Implementation:**
The MyLearningsBloc tests use bloc_test package to verify learning data loading and filtering behavior. The test group sets up MockMyLearningsRepository and MyLearningsBloc instances in setUp. The first blocTest verifies successful learning data loading by mocking the repository's getMyLearnings method to return mockLearnings, then expects the bloc to emit MyLearningsLoading followed by MyLearningsLoaded with both learnings and filteredLearnings containing the mock data. The second blocTest validates filtering functionality by loading data first, then applying a completed filter, expecting the bloc to emit the initial loading and loaded states, followed by an updated loaded state with the original learnings list and filtered results containing only completed learnings with the currentFilter set appropriately.

### Widget Testing

**Widget Testing Implementation:**
The widget test verifies that learning cards are properly displayed when data is loaded. It creates a MockMyLearningsBloc and sets its state to MyLearningsLoaded with mockLearnings data for both learnings and filteredLearnings lists. The test pumps a MaterialApp containing a BlocProvider.value that provides the mock bloc to a MyLearningsPage widget. The assertions verify that MyLearningCard widgets are found in the widget tree and that the first learning's title text is displayed exactly once, confirming that the UI correctly renders learning data when the bloc is in the loaded state.

## Performance Optimizations

### 1. Efficient Filtering

**Optimized Filtering Implementation:**
The OptimizedMyLearningsFilter provides efficient client-side filtering for learning data. The static filterLearnings method takes a learnings list and filter parameter, implementing performance optimizations by returning the original list directly when filter is 'all' (avoiding unnecessary list copying), and using the where method with lazy evaluation for other filters, only creating a new list when needed. The method leverages the filter's matches method to determine inclusion criteria, providing efficient filtering that minimizes memory allocation and processing overhead for large learning datasets.

### 2. Caching Strategy

**Caching Strategy Implementation:**
The MyLearningsCacheManager provides cache validation utilities for learning data with a 5-minute expiry duration. The static isCacheValid method uses SharedPreferences to check cache validity by retrieving the stored timestamp, returning false if no timestamp exists, converting the timestamp to DateTime, and comparing the time difference against the cache expiry duration. This approach enables efficient cache validation without requiring file system access, providing quick cache validity checks that support the repository's caching strategy and help maintain optimal performance while ensuring data freshness.

## Related Documentation

- [Training Consumption Feature](training-consumption.md)
- [Course Details Feature](course-details.md)
- [State Management Guide](../architecture/state-management.md)
- [Caching Strategies](../api/api-architecture.md)
