# Course Details Feature

## Overview

The Course Details feature provides comprehensive information about training courses and learning tracks, enabling users to view detailed course information, check prerequisites, and enroll in courses. It serves as the primary interface for course discovery and enrollment decisions.

## Architecture

### Feature Structure
```
features/course_details/
├── data/
│   ├── datasources/
│   │   ├── training_details_data_source.dart
│   │   └── learning_track_details_data_source.dart
│   ├── models/
│   │   ├── training_details_model.dart
│   │   ├── learning_track_details_model.dart
│   │   └── enrollment_model.dart
│   └── repositories/
│       ├── training_details_repository.dart
│       └── learning_track_details_repository.dart
├── domain/
│   ├── entities/
│   │   ├── training_details_entity.dart
│   │   ├── learning_track_details_entity.dart
│   │   └── enrollment_entity.dart
│   └── repositories/
│       ├── training_details_repository.dart
│       └── learning_track_details_repository.dart
└── presentation/
    ├── bloc/
    │   ├── training_details_bloc.dart
    │   └── learning_track_details_bloc.dart
    ├── pages/
    │   ├── training_details_page.dart
    │   └── learning_track_details_page.dart
    └── widgets/
        ├── course_header.dart
        ├── course_description.dart
        ├── course_syllabus.dart
        ├── enrollment_button.dart
        └── provider_info.dart
```

## Core Components

### 1. Training Details BLoC

The `TrainingDetailsBloc` manages course information loading, enrollment processes, and state updates for the course details interface.

**TrainingDetailsBloc Implementation:**
The TrainingDetailsBloc is an injectable service that manages course information and enrollment processes using the BLoC pattern. It handles three main event types: loading training details, enrollment operations, and refresh functionality. The _onLoadTrainingDetails method emits a loading state then calls the repository to fetch course information and emits the loaded state with the result. The _onEnrollInTraining method manages the enrollment process by checking the current state, updating it to show enrollment progress, calling the enrollment repository method, and updating the state with the new enrollment status. This pattern ensures proper state management for course details while providing user feedback during enrollment operations.

**BLoC Features:**
- **Course Loading**: Fetches comprehensive course details and metadata
- **Enrollment Management**: Handles course enrollment with loading states
- **State Synchronization**: Updates enrollment status in real-time
- **Error Handling**: Provides user-friendly error messages and recovery options

### 2. Training Details Events and States

Events and states provide a clear contract for course details interactions and UI updates.

**Training Details Event Classes:**
The training details feature defines three primary event classes for managing course interactions. LoadTrainingDetails initiates the course information loading process and requires a trainingId parameter to identify the specific course. EnrollInTraining triggers the enrollment process for a specific training course, also requiring the trainingId for identification. RefreshTrainingDetails handles refresh operations to update course information and enrollment status, maintaining the same parameter structure. These events provide a clean interface for triggering different course-related operations while ensuring proper identification of the target training course.

**TrainingDetailsLoaded State Class:**
The TrainingDetailsLoaded state represents successful course data loading and contains comprehensive information for UI rendering. It includes the TrainingDetailsEntity object with complete course information and an isEnrolling boolean flag to track enrollment operation status. The copyWith method enables immutable state updates by creating new instances with modified properties while preserving unchanged values. This pattern supports dynamic UI updates during enrollment processes, allowing the interface to show loading indicators and update button states while maintaining the core course data throughout the operation.

**Event Types:**
- **LoadTrainingDetails**: Fetches course information and enrollment status
- **EnrollInTraining**: Initiates course enrollment process
- **RefreshTrainingDetails**: Refreshes course data and updates UI

**State Types:**
- **TrainingDetailsInitial**: Default state before loading
- **TrainingDetailsLoading**: Shows loading indicators during API calls
- **TrainingDetailsLoaded**: Contains course data and enrollment status
- **TrainingDetailsError**: Displays error messages with retry options

## UI Components

### 1. Training Details Page

**TrainingDetailsPage Implementation:**
The TrainingDetailsPage is a StatefulWidget that manages the course details interface with comprehensive state handling. It requires a trainingId parameter for course identification and automatically triggers data loading in initState by dispatching a LoadTrainingDetails event to the BLoC. The build method implements a BlocConsumer pattern that listens for error states to display toast messages and builds different UI views based on the current state. The builder handles loading states with a dedicated loading view, loaded states with the main course content view, and error states with a retry-enabled error view. The _refreshDetails method provides a way to reload course information by dispatching a RefreshTrainingDetails event.

### 2. Course Header Widget

**CourseHeader Widget Implementation:**
The CourseHeader is a StatelessWidget that displays comprehensive training information in a structured header format. It requires a TrainingDetailsEntity and creates a container with 16px padding containing a Column with CrossAxisAlignment.start. The layout includes an image section, title, provider information, metadata, and tags with appropriate spacing. The _buildImage method creates a ClipRRect with 12px border radius containing a CachedNetworkImage with 200px height, full width, and cover fit, including loading and error state handling. The _buildTitle displays the training title using h2.bold text theme. The _buildProvider conditionally shows provider information with a business icon and provider name. The _buildMetadata creates a Row with duration, skill level, and language information using the _buildMetadataItem helper method that combines icons with text in a consistent format.

### 3. Course Description Widget

**CourseDescription Widget Implementation:**
The CourseDescription is a StatefulWidget that displays training description with expandable functionality. It manages an _isExpanded boolean state and uses a _maxLines constant of 3 for the collapsed view. The widget creates a container with 16px padding containing a Column with the description title and content. The main content uses AnimatedCrossFade to smoothly transition between collapsed (3 lines with ellipsis) and expanded (full text) states with 200ms duration. The _shouldShowExpandButton method uses TextPainter to calculate if the description text exceeds the maximum lines by laying out the text with the available width minus padding. When the expand button is needed, it displays a TextButton with localized "Show More"/"Show Less" text that toggles the _isExpanded state and updates the UI accordingly.

### 4. Enrollment Button Widget

**EnrollmentButton Widget Implementation:**
The EnrollmentButton is a StatelessWidget that provides dynamic enrollment functionality based on the training's current enrollment status. It creates a container with white background and subtle shadow, containing a SafeArea with the appropriate button. The _buildButton method implements a switch statement that renders different button configurations based on enrollment status: enrolled users see a "Start Training" button, completed users get a "View Certificate" option, in-progress users see "Continue Training", and non-enrolled users get an enrollment button with loading state support. The component handles navigation to the training consumption page and provides visual feedback during enrollment operations through loading indicators and disabled states.

### 5. Course Syllabus Widget

**CourseSyllabus Widget Implementation:**
The CourseSyllabus is a StatelessWidget that displays a list of course lessons in a structured format. It requires a List<LessonEntity> and returns an empty widget if the lessons list is empty. The widget creates a container with 16px padding containing a Column with the syllabus title and a ListView.separated that displays lesson items with 8px spacing. Each lesson is rendered using the _LessonItem private widget.

**_LessonItem Widget Structure:**
The _LessonItem displays individual lesson information in a styled container with accentExtraLight background, 8px border radius, and accentLight border. The layout uses a Row containing a circular index indicator (32x32 with greenAccentPrimary background), lesson content in an Expanded Column (title and optional duration), and a lesson type icon. The _getLessonTypeIcon method returns appropriate icons based on LessonType: play_circle_outline for video, article_outlined for article, quiz_outlined for quiz, file_download_outlined for file, slideshow_outlined for slide, and school_outlined as default.

## Data Models

### Training Details Model

**TrainingDetailsModel Data Structure:**
The TrainingDetailsModel is a comprehensive freezed data class representing detailed training information from the API. It includes required fields for id, title, and description, plus extensive optional fields for complete course information. Key fields include imageUrl, durationHours, provider (TrainingProviderModel), sectors list, skillLevel, language, location, enrollmentStatus, prerequisites list, learningOutcomes list, targetAudience, certification details, lessons list (LessonModel objects), totalLessons count, rating, reviewsCount, enrollmentCount, and timestamps. The class uses JsonKey annotations for proper API field mapping with snake_case to camelCase conversion and includes a fromJson factory constructor for JSON deserialization. This model serves as the comprehensive data transfer object for detailed course information.

## Repository Implementation

### Training Details Repository

**TrainingDetailsRepository Implementation:**
The TrainingDetailsRepository is an injectable service that manages detailed training data retrieval with comprehensive caching capabilities. It requires TrainingDetailsDataSource and CacheManager dependencies for API communication and data caching. The getTrainingDetails method implements a cache-first strategy, building a cache key using the training ID, checking for cached data first, and returning it if available. For cache misses, it fetches data from the API, transforms the model to entity, caches the result, and returns the entity. The enrollInTraining method handles enrollment operations and invalidates the cache to force data refresh. The _getCachedTrainingDetails method safely retrieves cached data by reading JSON from the cache manager and deserializing it to TrainingDetailsModel. The _cacheTrainingDetails method stores entity data by converting it to model, serializing to JSON, and storing as UTF8-encoded bytes in the cache manager with proper error handling.

## Prerequisites and Requirements

### Prerequisites Display

**PrerequisitesSection Widget Implementation:**
The PrerequisitesSection is a StatelessWidget that displays course prerequisites in a structured list format. It requires a List<String> of prerequisites and returns an empty widget if the list is empty. The widget creates a container with 16px padding containing a Column with CrossAxisAlignment.start. The layout includes a localized prerequisites title using h4.bold text theme, followed by a mapped list of prerequisite items. Each prerequisite is displayed in a Padding widget with bottom spacing, containing a Row with CrossAxisAlignment.start. The row includes a check_circle_outline icon in statusSuccess color (16px size), 8px spacing, and an Expanded Text widget displaying the prerequisite text using textMedium.greyPrimary styling. This creates a clean, scannable list of course requirements.

## Testing

### BLoC Testing

**TrainingDetailsBloc Testing Implementation:**
The TrainingDetailsBloc tests use the bloc_test package to verify training details loading and enrollment behavior. The test group sets up MockTrainingDetailsRepository and TrainingDetailsBloc instances in setUp. The first blocTest verifies successful training details loading by mocking the repository's getTrainingDetails method to return mockTrainingDetails, then expects the bloc to emit TrainingDetailsLoading followed by TrainingDetailsLoaded with the mock data. The second blocTest validates enrollment functionality by seeding the bloc with TrainingDetailsLoaded state, mocking the enrollInTraining method, and expecting the bloc to emit loading state with isEnrolling true, followed by updated state with enrolled status and isEnrolling false. These tests ensure proper state transitions during training details loading and enrollment operations.

### Widget Testing

**Widget Testing Implementation:**
The widget test verifies that training details are properly displayed when the bloc is in the loaded state. It creates a MockTrainingDetailsBloc and sets its state to TrainingDetailsLoaded with mockTrainingDetails data. The test pumps a MaterialApp containing a BlocProvider.value that provides the mock bloc to a TrainingDetailsPage widget with trainingId '1'. The assertions verify that the training title and description text are found exactly once in the widget tree, and that the EnrollmentButton widget is present, confirming that the UI correctly renders training details when the bloc is in the loaded state.

## Performance Optimizations

### Image Loading Optimization

**Optimized Course Image Implementation:**
The OptimizedCourseImage implements performance-focused image loading for course detail pages using CachedNetworkImage with memory optimization. It accepts an optional imageUrl parameter and sets memCacheHeight to 400 and memCacheWidth to 600 to limit memory cache size and prevent excessive memory usage on detail pages. The widget uses DefaultCacheManager for consistent caching behavior, includes a ShimmerPlaceholder with 200px height for loading states to provide better user feedback, and displays a DefaultImagePlaceholder with 200px height for error states. This optimization balances image quality with memory efficiency for detailed course images.

### Lazy Loading Content

**Lazy Loaded Syllabus Implementation:**
The LazyLoadedSyllabus is a StatefulWidget that implements performance optimization by loading lesson data only when the user expands the syllabus section. It manages _isExpanded boolean and _lessons list state variables. The widget builds an ExpansionTile with localized syllabus title and an onExpansionChanged callback that updates the expanded state and triggers lesson loading when expanded for the first time. The children list conditionally shows either the CourseSyllabus widget with loaded lessons or a CircularProgressIndicator during loading. The _loadLessons method asynchronously fetches lessons from the TrainingDetailsRepository using the trainingId, then safely updates the state if the widget is still mounted. This lazy loading approach improves initial page load performance by deferring non-critical content until user interaction.

## Related Documentation

- [Catalog Feature](catalog.md)
- [Training Consumption Feature](training-consumption.md)
- [My Learnings Feature](my-learnings.md)
- [State Management Guide](../architecture/state-management.md)
