# Authentication Feature

## Overview

The Authentication feature manages user login, logout, and session handling through Qiwa OAuth integration. It provides secure token management and maintains user authentication state throughout the application.

## Architecture

### Feature Structure
```
features/auth/
├── data/
│   ├── datasources/
│   │   └── auth_data_source.dart
│   ├── models/
│   │   ├── auth_model.dart
│   │   └── refresh_token_model.dart
│   └── repositories/
│       └── auth_repository.dart
├── domain/
│   └── repositories/
│       └── auth_repository.dart
└── presentation/
    ├── bloc/
    │   ├── auth_bloc.dart
    │   ├── auth_event.dart
    │   └── auth_state.dart
    ├── pages/
    │   ├── get_started_page.dart
    │   └── login_page.dart
    └── widgets/
        └── qiwa_web_view.dart
```

## Core Components

### 1. AuthBloc - State Management

The `AuthBloc` is the central state management component for authentication. It handles user login, logout, and token refresh operations using the BLoC pattern.

**AuthBloc Architecture:**

The AuthBloc manages authentication state and handles all authentication-related events throughout the application. This BLoC serves as the central authentication controller, coordinating between the UI and authentication services.

**BLoC Structure:**
- **Singleton Registration**: Ensures single instance across the app for consistent authentication state
- **Repository Integration**: Uses AuthRepository for authentication operations and data management
- **Event Handling**: Processes core authentication events including authenticate, refresh token, and logout
- **State Management**: Maintains authentication state and emits appropriate states based on operation results

**Event Processing:**
- **Authenticate**: Handles user login flow through Qiwa SSO integration
- **Refresh Token**: Manages automatic token renewal for session maintenance
- **Logout**: Handles user logout and token cleanup operations

**Key Features:**
- **Singleton Pattern**: Ensures single instance across the app
- **Event Handlers**: Processes authentication events (login, logout, refresh)
- **Repository Integration**: Uses `AuthRepository` for data operations
- **State Emission**: Emits appropriate states based on operation results

### 2. Authentication Events

Authentication events represent user actions and system triggers that initiate authentication processes.

**Authentication Event Classes:**
The authentication feature defines two primary event classes for managing user authentication flows. The Authenticate event initiates the login process and requires an AuthModel parameter containing the necessary authentication credentials and verification codes from the OAuth flow. The Logout event triggers the sign-out process and requires no parameters, handling session cleanup and token removal. These events provide a clean interface for triggering authentication operations from the UI layer while maintaining separation of concerns between presentation and business logic.

**Event Types:**
- **Authenticate**: Triggered when user completes OAuth flow with auth code
- **Logout**: Initiated when user wants to sign out
- **RefreshToken**: Automatically triggered when access token expires

### 3. Authentication States

Authentication states represent the current status of authentication operations and drive UI updates.

**Authentication State Classes:**
The authentication feature implements three key state classes for managing UI updates during authentication flows. AuthLoading represents the processing state during login operations, triggering loading indicators and disabling user interactions. AuthSuccess indicates successful authentication completion, typically triggering navigation to the main app areas and updating the authentication status. AuthError contains error information with a required error message string, enabling the display of specific error feedback to users when authentication fails. These states drive reactive UI updates throughout the authentication process.

**State Types:**
- **AuthInitial**: Default state when no authentication is in progress
- **AuthLoading**: Shows loading indicators during authentication
- **AuthSuccess**: Triggers navigation to authenticated areas
- **AuthError**: Displays error messages to user

## Authentication Flow

### 1. Qiwa OAuth Integration

```mermaid
sequenceDiagram
    participant User
    participant App
    participant QiwaWebView
    participant QiwaOAuth
    participant Backend
    participant TokenProvider

    User->>App: Tap Login
    App->>QiwaWebView: Open Qiwa OAuth
    QiwaWebView->>QiwaOAuth: Redirect to Qiwa
    User->>QiwaOAuth: Enter credentials
    QiwaOAuth->>QiwaWebView: Return auth code
    QiwaWebView->>App: Extract auth code
    App->>Backend: Exchange code for tokens
    Backend->>App: Return access & refresh tokens
    App->>TokenProvider: Store tokens securely
    App->>User: Navigate to Home
```

### 2. Login Implementation

The login flow consists of two main pages that guide users through authentication.

**Get Started Page** - Initial entry point with language selection:

**Get Started Button Implementation:**
The Get Started page features a primary action button that initiates the authentication flow. The button uses the app's standard AppButton component with a tap handler that navigates to the login route using the router's pushNamed method. The button text is localized using the easy_localization package, displaying the appropriate "Get Started" text based on the user's selected language. This provides a clean entry point into the authentication process with proper internationalization support.

**Login Page** - Handles authentication state and user feedback:

**Login Page BLoC Integration:**
The login page implements a BlocConsumer widget that handles both state listening and UI building for authentication operations. The listener responds to state changes by navigating back on successful authentication or displaying error toast messages when authentication fails. The builder creates an AppLoadingOverlay that shows loading indicators during authentication processing while rendering the main LoginView component. This pattern provides comprehensive state management with proper user feedback and loading states throughout the authentication flow.

**Key Features:**
- **State-driven UI**: Loading overlay appears during authentication
- **Error Handling**: Toast messages for authentication failures
- **Navigation**: Automatic redirect on successful login

### 3. Qiwa WebView Component

The `QiwaWebView` handles the OAuth flow by embedding Qiwa's authentication page and capturing the authorization code.

**Qiwa WebView Implementation:**
The QiwaWebView component initializes a WebViewController with JavaScript enabled to support Qiwa's OAuth interface. It sets up a NavigationDelegate to monitor URL changes during the authentication flow, specifically watching for redirect URLs containing authorization codes. When a URL change occurs, the handler checks for the presence of 'code=' parameter and extracts the authorization code for token exchange. The WebView loads the constructed Qiwa OAuth URL with PKCE parameters, providing a seamless embedded authentication experience within the app.

**Implementation Details:**
- **JavaScript Enabled**: Required for Qiwa's OAuth interface
- **URL Monitoring**: Watches for redirect URLs containing auth codes
- **Code Extraction**: Automatically captures authorization code from callback URL
- **PKCE Integration**: Uses code challenge/verifier for enhanced security

## Token Management

### 1. AuthTokenProvider

The `AuthTokenProvider` manages secure token storage and provides authentication state streams throughout the app.

**AuthTokenProvider Implementation:**
The AuthTokenProvider is a lazy singleton service that manages secure token storage and authentication state throughout the app. It uses a BehaviorSubject to provide a reactive authentication state stream that other components can subscribe to for real-time authentication status updates. The updateTokens method securely stores both access and refresh tokens using FlutterSecureStorage, then broadcasts the authenticated state to all listeners. This centralized approach ensures consistent authentication state management across the entire application while maintaining security through encrypted storage.

**Key Features:**
- **Secure Storage**: Uses `FlutterSecureStorage` for token persistence
- **Authentication Stream**: Reactive authentication state for UI updates
- **Token Caching**: In-memory caching for performance optimization
- **Lazy Singleton**: Single instance with lazy initialization

### 2. Automatic Token Refresh

The `RefreshTokenInterceptor` automatically handles token expiration by intercepting 401/403 responses and refreshing tokens transparently.

**Refresh Token Interceptor Logic:**
The RefreshTokenInterceptor implements automatic token refresh by intercepting HTTP errors and handling token expiration transparently. The onError method checks if the error warrants a token refresh (401/403 status codes), then attempts to refresh the tokens and retry the original request. If refresh succeeds, it resolves the handler with the retry response, making the process invisible to the calling code. If refresh fails, it handles the failure (typically by logging out the user) and passes the original error forward. The _shouldRefreshToken helper method determines refresh eligibility based on HTTP status codes, ensuring only authentication-related errors trigger refresh attempts.

**Refresh Process:**
1. **Error Detection**: Intercepts 401/403 HTTP responses
2. **Token Refresh**: Calls refresh token endpoint with stored refresh token
3. **Request Retry**: Automatically retries original request with new token
4. **Failure Handling**: Redirects to login if refresh fails

## Data Models

### 1. AuthModel

**AuthModel Data Structure:**
The AuthModel class extends Equatable and represents the authentication data structure used throughout the OAuth flow. It contains required PKCE parameters (codeVerifier and codeChallenge) for secure authentication, an optional token field for the authorization code, and a role field defaulting to trainee. The toJson method serializes the model for API requests, including the mobile flag to indicate the request source. The Equatable implementation provides value-based equality comparison using the props getter, ensuring proper state management and comparison operations within the BLoC pattern.

### 2. RefreshTokenModel

**RefreshTokenModel Data Structure:**
The RefreshTokenModel class extends Equatable and manages refresh token data for authentication renewal. It requires a refreshToken string parameter and includes an optional role field that defaults to Constants.TRAINEE. The toJson method serializes the model for API requests, including the refreshToken, role, and a mobile flag set to true to indicate the request source. The Equatable implementation provides value-based equality comparison through the props getter, ensuring proper state management and comparison operations within the authentication system.

## Repository Implementation

### AuthRepository

**AuthRepository Implementation:**
The AuthRepository is an injectable service that acts as the domain layer interface for authentication operations. It depends on an AuthDataSource for API communication and an AuthTokenProvider for secure token management. The signIn method handles the OAuth token exchange by calling the data source with the AuthModel, then updating the stored tokens through the token provider. The refreshToken method performs similar operations for token renewal, maintaining the same pattern of data source interaction followed by token storage. This repository pattern provides clean separation between domain logic and data access while ensuring consistent token management across all authentication operations.

## Security Features

### 1. PKCE (Proof Key for Code Exchange)

**PKCE Generator Implementation:**
The PKCEGenerator class provides static methods for generating PKCE (Proof Key for Code Exchange) parameters required for secure OAuth flows. The generateCodeVerifier method creates a cryptographically secure 128-character random string using a charset containing alphanumeric characters and URL-safe symbols (-._~), utilizing Random.secure() for cryptographic randomness. The generateCodeChallenge method takes the code verifier and creates a SHA256 hash, then encodes it using base64Url encoding with padding removed, following RFC 7636 specifications for PKCE implementation.

### 2. Secure Token Storage

**Secure Token Storage Configuration:**
Tokens are stored using FlutterSecureStorage with platform-specific security configurations. On iOS, the storage uses KeychainAccessibility.first_unlock to ensure tokens are accessible after the device is unlocked for the first time after restart. On Android, the configuration enables encryptedSharedPreferences for additional encryption layer beyond the default secure storage. This setup ensures tokens remain secure while being accessible for automatic refresh operations and app functionality.

## Error Handling

### Authentication Errors

**Authentication Error Handling Implementation:**
The _authenticate method in AuthBloc implements comprehensive error handling for the authentication process. It begins by emitting AuthLoading state, then validates that the AuthModel contains a valid token before proceeding. If the token is null, it immediately emits an AuthError with a generic authentication error message. For valid tokens, it calls the repository's signIn method using the errorHandler pattern, which emits AuthSuccess on successful authentication or AuthError with the specific error message on failure. A try-catch block provides additional protection against unexpected exceptions, ensuring the UI always receives appropriate state updates.

### Token Refresh Errors

**Token Refresh Error Handling Implementation:**
The _refreshToken method handles token renewal with comprehensive error recovery. It calls the repository's refreshToken method using the errorHandler pattern, emitting RefreshSuccess on successful token renewal. When refresh fails, the error handler invalidates stored tokens through the AuthTokenProvider and implements intelligent navigation logic. It checks the current route configuration to avoid unnecessary navigation, only redirecting to the home page if the user isn't already on the root/home path. The navigation uses popUntil to clear the navigation stack to the root page, then pushes the home page route, ensuring users are properly redirected to the login flow when token refresh fails.

## Testing

### BLoC Testing

**AuthBloc Testing Implementation:**
The authentication BLoC tests use the bloc_test package to verify authentication behavior under different scenarios. The first test validates error handling when an invalid AuthModel with null token is provided, expecting the bloc to emit AuthLoading followed by AuthError states while ensuring no user data reset occurs. The second test verifies successful authentication flow with a valid AuthModel, expecting AuthLoading followed by AuthSuccess states and confirming that user data reset is triggered. Both tests use mock dependencies (repository, user bloc, token provider) to isolate the authentication logic and verify proper interaction between components during the authentication process.

### Integration Testing

**Integration Test Implementation:**
The login flow integration test validates the complete authentication process from start to finish. It begins by creating a test app environment, then simulates user interactions by tapping the 'Get Started' button and subsequently the 'Login' button, using pumpAndSettle to wait for animations and state changes. The test includes WebView interaction simulation to mock the OAuth flow completion, and finally asserts that the HomePage widget is displayed, confirming successful navigation after authentication. This comprehensive test ensures the entire login flow works correctly from UI interaction to final navigation.

## Configuration

### Environment Setup

**Environment Configuration Implementation:**
The EnvironmentConfigs class provides static getters for accessing environment-specific configuration values required for OAuth integration. The qiwaClientId getter retrieves the Qiwa OAuth client identifier, qiwaRedirectUri gets the callback URL for OAuth redirects, and baseUrl provides the API base URL. All values are obtained using String.fromEnvironment, allowing configuration through build-time environment variables for different deployment environments (development, staging, production).

### OAuth URLs

**OAuth URL Construction Implementation:**
The _buildQiwaUrl method constructs the complete OAuth authorization URL with all required parameters. It creates a parameters map containing the client_id from environment configuration, redirect_uri for callback handling, response_type set to 'code' for authorization code flow, scope requesting 'openid profile' permissions, code_challenge for PKCE security, and code_challenge_method set to 'S256' for SHA256 hashing. The method then transforms the parameters map into a properly encoded query string using Uri.encodeComponent for URL safety, and combines it with the Qiwa base URL and OAuth authorize endpoint to create the final authorization URL.

## Best Practices

### 1. Security
- Always use PKCE for OAuth flows
- Store tokens securely using FlutterSecureStorage
- Implement automatic token refresh
- Clear tokens on logout

### 2. User Experience
- Show loading states during authentication
- Provide clear error messages
- Handle network connectivity issues
- Support language selection

### 3. State Management
- Use BLoC for authentication state
- Coordinate with other BLoCs (UserBloc)
- Handle authentication state across app restarts
- Implement proper error handling

### 4. Testing
- Test all authentication scenarios
- Mock external dependencies
- Test token refresh flows
- Verify security measures

## Related Documentation

- [State Management Guide](../architecture/state-management.md)
- [API Architecture](../api/api-architecture.md)
- [Security Best Practices](../development/coding-standards.md)
- [Testing Guide](../development/testing-guide.md)
