# Flutter SDK Setup for SonarQube Analysis

This guide explains where and how to configure the Flutter SDK path for SonarQube analysis to resolve dependency issues.

## Quick Setup (Automatic)

Run the automatic configuration script:

```bash
./scripts/configure-flutter-sdk.sh
```

This script will:
- ✅ Auto-detect your Flutter SDK installation
- ✅ Update `sonar-project.properties` with correct paths
- ✅ Configure pub cache location
- ✅ Verify the setup

## Manual Setup

### 1. Find Your Flutter SDK Path

**Method 1: Using Flutter Command**
```bash
which flutter
# Example output: /Users/<USER>/fvm/versions/3.29.3/bin/flutter
# SDK path would be: /Users/<USER>/fvm/versions/3.29.3
```

**Method 2: Check Common Locations**
- **macOS/Linux:**
  - `~/flutter`
  - `~/fvm/versions/[version]`
  - `~/development/flutter`
  - `/usr/local/flutter`
  - `/opt/flutter`

- **Windows:**
  - `C:\flutter`
  - `C:\src\flutter`
  - `%USERPROFILE%\flutter`

**Method 3: Using Flutter Doctor**
```bash
flutter doctor -v
# Look for "Flutter version" line which shows the installation path
```

### 2. Configure SonarQube Properties

Edit `sonar-project.properties` and add/update these lines:

```properties
# Flutter SDK path (REQUIRED)
sonar.dart.sdk=/path/to/your/flutter/sdk

# Pub cache path (RECOMMENDED)
sonar.dart.pub.cache=/path/to/your/.pub-cache
```

**Example configurations:**

**macOS with FVM:**
```properties
sonar.dart.sdk=/Users/<USER>/fvm/versions/3.29.3
sonar.dart.pub.cache=/Users/<USER>/.pub-cache
```

**macOS with standard Flutter:**
```properties
sonar.dart.sdk=/Users/<USER>/flutter
sonar.dart.pub.cache=/Users/<USER>/.pub-cache
```

**Linux:**
```properties
sonar.dart.sdk=/home/<USER>/flutter
sonar.dart.pub.cache=/home/<USER>/.pub-cache
```

**Windows:**
```properties
sonar.dart.sdk=C:/flutter
sonar.dart.pub.cache=C:/Users/<USER>/AppData/Local/Pub/Cache
```

## Environment-Specific Setup

### Local Development

1. **Run configuration script:**
   ```bash
   ./scripts/configure-flutter-sdk.sh
   ```

2. **Or manually set environment variables:**
   ```bash
   export FLUTTER_ROOT=/path/to/flutter
   export PUB_CACHE=/path/to/.pub-cache
   ```

### CI/CD Environments

#### GitHub Actions
The workflow automatically detects Flutter SDK:
```yaml
- name: Setup Flutter
  uses: subosito/flutter-action@v2
  with:
    flutter-version: '3.29.3'
    
- name: Get Flutter SDK path
  id: flutter-sdk
  run: |
    FLUTTER_SDK_PATH=$(dirname "$(dirname "$(which flutter)")")
    echo "path=$FLUTTER_SDK_PATH" >> $GITHUB_OUTPUT
```

#### GitLab CI
```yaml
before_script:
  - export FLUTTER_SDK_PATH=$(dirname "$(dirname "$(which flutter)")")
  - ./scripts/configure-flutter-sdk.sh
```

#### Jenkins
```groovy
environment {
    FLUTTER_SDK_PATH = sh(script: 'dirname "$(dirname "$(which flutter)")"', returnStdout: true).trim()
}
```

## Verification

### 1. Check Configuration
```bash
# View current configuration
cat sonar-project.properties | grep dart.sdk

# Should show something like:
# sonar.dart.sdk=/Users/<USER>/fvm/versions/3.29.3
```

### 2. Test Flutter Access
```bash
# Test if Flutter SDK path is correct
ls -la /path/to/your/flutter/sdk/bin/flutter

# Test if pub cache exists
ls -la /path/to/your/.pub-cache
```

### 3. Run Preparation Script
```bash
./scripts/prepare-sonar-analysis.sh
```

This should show:
- ✅ Flutter found
- ✅ Dependencies installed
- ✅ Generated files created
- ✅ Analysis passes

## Troubleshooting

### Common Issues

**1. "Flutter SDK not found"**
```bash
# Check if Flutter is in PATH
which flutter

# If not found, add to PATH:
export PATH="$PATH:/path/to/flutter/bin"
```

**2. "Permission denied"**
```bash
# Make scripts executable
chmod +x scripts/*.sh
```

**3. "Pub cache not accessible"**
```bash
# Check pub cache location
flutter pub cache dir

# Create if doesn't exist
mkdir -p ~/.pub-cache
```

**4. "SonarQube can't resolve dependencies"**
- Ensure `flutter pub get` was run before analysis
- Verify SDK path is absolute, not relative
- Check that pub cache path is correct

### Debug Commands

```bash
# Show current Flutter configuration
flutter doctor -v

# Show pub cache location
flutter pub cache dir

# Test dependency resolution
flutter analyze

# Show environment variables
echo $FLUTTER_ROOT
echo $PUB_CACHE
```

## Best Practices

1. **Use absolute paths** in sonar-project.properties
2. **Run configuration script** before each analysis
3. **Keep Flutter version consistent** across environments
4. **Cache dependencies** in CI/CD for faster builds
5. **Verify setup** with preparation script before SonarQube analysis

## Support

If you continue to have issues:

1. Run the diagnostic script: `./scripts/configure-flutter-sdk.sh`
2. Check SonarQube logs for specific error messages
3. Verify Flutter and SonarScanner versions are compatible
4. Ensure all environment variables are set correctly
