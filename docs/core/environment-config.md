# Environment Configuration

This document explains how environment-specific configurations are managed in the NSP Flutter application. The app supports multiple environments (development, staging, UAT, production) with different API endpoints and settings.

## Table of Contents

- [Overview](#overview)
- [Environment Configs Class](#environment-configs-class)
- [Supported Environments](#supported-environments)
- [Configuration Variables](#configuration-variables)
- [Build Configuration](#build-configuration)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)

## Overview

The NSP app uses <PERSON><PERSON>'s `--dart-define` feature to pass environment-specific configurations at build time. This approach allows the same codebase to work across different environments without hardcoding sensitive information.

### Key Features

- **Build-time configuration**: Values set during compilation
- **Environment isolation**: Different settings per environment
- **Security**: No sensitive data in source code
- **Flexibility**: Easy to add new configuration variables

## Environment Configs Class

The `EnvironmentConfigs` class centralizes all environment-specific settings.

**EnvironmentConfigs Class Structure:**

The `EnvironmentConfigs` class is a static configuration container that manages environment-specific settings using <PERSON>lut<PERSON>'s compile-time environment variables. This class provides a centralized way to access different configuration values based on the build environment.

**Key Configuration Properties:**
- **Environment Identifier**: Determines the current environment (development, stage, uat, prod) with 'stage' as the default
- **API Base URL**: The primary API endpoint URL that changes per environment, defaulting to the staging API server
- **Website URL**: The web application URL corresponding to the current environment
- **OAuth Client ID**: Environment-specific client identifier for authentication services
- **Qiwa Login URL**: The Qiwa SSO endpoint URL for authentication integration

**Implementation Details:**
- Uses `String.fromEnvironment()` to read compile-time defined values
- Provides sensible default values for development and testing scenarios
- All properties are static constants for optimal performance and memory usage
- Default values point to staging environment to ensure safe development practices

### Configuration Variables

| Variable | Purpose | Default Value |
|----------|---------|---------------|
| `environment` | Current environment name | `stage` |
| `baseContentUrl` | API base URL | Stage API URL |
| `websiteUrl` | Web application URL | Stage web URL |
| `clientId` | OAuth client identifier | Stage client ID |
| `baseQiwaLoginUrl` | Qiwa SSO endpoint | Qiwa production URL |

## Supported Environments

### Development
- **Purpose**: Local development and testing
- **API**: Development server
- **Features**: Debug logging, Alice HTTP inspector
- **Build**: `flutter run --dart-define=environment=development`

### Stage
- **Purpose**: Internal testing and QA
- **API**: Staging server
- **Features**: Limited logging, testing features enabled
- **Build**: `flutter run --dart-define=environment=stage`

### UAT (User Acceptance Testing)
- **Purpose**: Client testing and validation
- **API**: UAT server
- **Features**: Production-like environment
- **Build**: `flutter run --dart-define=environment=uat`

### Production
- **Purpose**: Live application for end users
- **API**: Production server
- **Features**: Minimal logging, all optimizations enabled
- **Build**: `flutter run --dart-define=environment=prod`

## Build Configuration

### Command Line Usage

#### Development Build
```bash
flutter run \
  --flavor demo \
  --dart-define=environment=development \
  --dart-define=baseContentUrl=https://dev-api.nsp.com/api/v1 \
  --dart-define=clientId=dev_client_id \
  --dart-define=websiteUrl=https://dev.nsp.com
```

#### Staging Build
```bash
flutter run \
  --flavor stage \
  --dart-define=environment=stage \
  --dart-define=baseContentUrl=https://api.nsp.stage.devops.takamol.support/api/v1 \
  --dart-define=clientId=stage_client_id \
  --dart-define=websiteUrl=https://spa.nsp.stage.devops.takamol.support
```

#### Production Build
```bash
flutter build apk \
  --flavor prod \
  --dart-define=environment=prod \
  --dart-define=baseContentUrl=https://api.nsp.prod.com/api/v1 \
  --dart-define=clientId=prod_client_id \
  --dart-define=websiteUrl=https://nsp.gov.sa
```

### Android Flavors

The app uses Android flavors to support different environments:

```gradle
// android/app/build.gradle
android {
    flavorDimensions "default"
    productFlavors {
        demo {
            dimension "default"
            applicationIdSuffix ".demo"
            versionNameSuffix "-demo"
            resValue "string", "app_name", "NSP Demo"
        }
        stage {
            dimension "default"
            applicationIdSuffix ".stage"
            versionNameSuffix "-stage"
            resValue "string", "app_name", "NSP Stage"
        }
        uat {
            dimension "default"
            applicationIdSuffix ".uat"
            versionNameSuffix "-uat"
            resValue "string", "app_name", "NSP UAT"
        }
        prod {
            dimension "default"
            resValue "string", "app_name", "NSP"
        }
    }
}
```

## Usage Examples

### Accessing Configuration in Code

**Configuration Import Pattern**: Import the environment configuration class from the core environment package to access environment-specific settings.

**Service Configuration**: Create service classes that access configuration values through static properties from EnvironmentConfigs. This pattern ensures that services like ApiService can access the correct base URL and client ID for the current environment.

**Runtime Configuration Usage**: Services can access configuration values at runtime and use them for operations like API requests, ensuring that the application connects to the correct endpoints based on the current environment setup. The configuration values are used to build complete endpoint URLs by combining base URLs with specific API paths.

### Environment-Specific Behavior

**Environment Detection**: Create utility classes that check the current environment to enable or disable specific features based on the deployment context.

**Debug Mode Logic**: Implement debug-specific functionality that only activates in development environments. This includes conditional logging, debug information display, and development-only features.

**Production Detection**: Provide methods to identify production environments for enabling production-specific behaviors like crash reporting and performance monitoring.

**Conditional Feature Activation**: Use environment checks to control feature availability, ensuring that debug tools, verbose logging, and development utilities are only active in appropriate environments while remaining disabled in production builds.

### Conditional Feature Flags

**Feature Flag Management**: Implement a centralized feature flag system that controls application features based on the current environment configuration.

**Development Tools**: Enable debugging and inspection tools like Alice HTTP inspector only in non-production environments to aid development and testing.

**Environment-Specific Logging**: Configure detailed logging for development and staging environments while maintaining minimal logging in production for performance.

**Production Features**: Activate production-specific features like crash reporting and analytics only in production environments to avoid noise in development data.

### Dio Client Configuration

**HTTP Client Setup**: Configure the Dio HTTP client using environment-specific base URLs and timeout settings through dependency injection.

**Environment-Aware Configuration**: Set up HTTP client with base URLs from environment configuration, ensuring requests are directed to the correct API endpoints for each environment.

**Conditional Interceptors**: Add development and debugging interceptors like Alice HTTP inspector only in non-production environments to monitor network traffic during development.

**Timeout Configuration**: Configure appropriate connection and receive timeouts for reliable network communication across different environments and network conditions.

## Best Practices

### Security Considerations

1. **Never commit sensitive values**: Use default values that are safe for public repositories
2. **Use different client IDs**: Each environment should have its own OAuth client
3. **Validate configurations**: Check that required values are provided at runtime
4. **Rotate secrets regularly**: Update client IDs and other secrets periodically

### Configuration Management

1. **Document all variables**: Maintain clear documentation of all configuration options
2. **Use meaningful defaults**: Provide sensible default values for development
3. **Validate at startup**: Check configuration validity when the app starts
4. **Environment detection**: Implement helper methods to detect current environment

### Build Scripts

Create shell scripts for common build configurations:

```bash
#!/bin/bash
# scripts/build-stage.sh
flutter build apk \
  --flavor stage \
  --dart-define=environment=stage \
  --dart-define=baseContentUrl=$STAGE_API_URL \
  --dart-define=clientId=$STAGE_CLIENT_ID \
  --dart-define=websiteUrl=$STAGE_WEB_URL
```

### CI/CD Integration

```yaml
# .github/workflows/build.yml
- name: Build APK
  run: |
    flutter build apk \
      --flavor ${{ matrix.flavor }} \
      --dart-define=environment=${{ matrix.environment }} \
      --dart-define=baseContentUrl=${{ secrets.API_URL }} \
      --dart-define=clientId=${{ secrets.CLIENT_ID }}
```

### Runtime Validation

**Configuration Validation**: Implement runtime validation to ensure all required environment configuration values are properly set before application startup.

**Required Field Checks**: Validate that critical configuration values like base URL and client ID are not empty, preventing runtime failures due to missing configuration.

**Environment-Specific Validation**: Perform environment-specific checks, such as ensuring production builds don't accidentally use staging URLs or development configurations.

**Startup Integration**: Integrate configuration validation into the application startup process in main.dart to catch configuration issues early and provide clear error messages for missing or invalid settings.

### Environment Switching

**Visual Environment Indicators**: Create UI components that display the current environment to developers and testers, helping prevent confusion between different deployment environments.

**Environment Banner Widget**: Implement a banner widget that shows the current environment name in non-production builds, providing clear visual feedback about which environment the app is running against.

**Production Hiding**: Ensure that environment indicators are hidden in production builds to maintain a clean user experience while providing valuable context during development and testing phases.

**Development Aid**: Use environment banners as a development tool to quickly identify which environment is active, especially useful when testing multiple environment configurations or when switching between different builds.

## Troubleshooting

### Common Issues

1. **Missing dart-define values**: Ensure all required variables are provided
2. **Wrong environment**: Verify the environment variable matches the intended target
3. **Cached builds**: Clean build cache when switching environments
4. **Flavor mismatches**: Ensure Android flavor matches the environment

### Debugging Configuration

**Configuration Debugging**: Implement debugging utilities that display current environment configuration values for troubleshooting and verification purposes.

**Configuration Inspection**: Create methods that print all current configuration values in a formatted way, making it easy to verify that the correct environment settings are loaded.

**Development Tool**: Use configuration debugging as a development aid to quickly check environment settings, especially useful when troubleshooting environment-specific issues or verifying configuration changes.

**Formatted Output**: Provide well-formatted configuration output that includes all critical environment values like URLs, client IDs, and environment names for comprehensive configuration verification.

## Related Documentation

- [Build & Deployment](../development/build-deployment.md) - Build process and deployment
- [Setup Guide](../development/setup-guide.md) - Development environment setup
- [API Architecture](../api/api-architecture.md) - API communication setup
- [Constants](constants.md) - Application constants

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
