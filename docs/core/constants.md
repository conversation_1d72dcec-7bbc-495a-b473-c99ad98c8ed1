# Application Constants

This document provides a comprehensive overview of all constants used throughout the NSP Flutter application. Constants are centralized to prevent magic strings/numbers and ensure consistency across the codebase.

## Table of Contents

- [Overview](#overview)
- [General Constants](#general-constants)
- [API Constants](#api-constants)
- [Asset Paths](#asset-paths)
- [Router Query Constants](#router-query-constants)
- [Storage Keys](#storage-keys)
- [Usage Guidelines](#usage-guidelines)
- [Best Practices](#best-practices)

## Overview

All constants are defined in `lib/core/shared/constants.dart` and organized into logical groups. This centralized approach prevents magic strings and makes the codebase more maintainable.

### Constant Classes

- **Constants**: General application constants
- **ApiConstants**: API endpoints and paths
- **AssetsPath**: Asset file paths
- **RouterQueryConstants**: Router query parameter names
- **HiveKeys**: Local storage keys
- **SecureStorageKeys**: Secure storage keys

## General Constants

### Locale Constants

**Locale Constants Structure:**

The Constants class defines fundamental locale identifiers used throughout the application for internationalization and localization support. These constants ensure consistent language handling across all app features.

**Locale Definitions:**
- **Arabic Locale**: 'ar' identifier for Arabic language support with RTL text direction
- **English Locale**: 'en' identifier for English language support with LTR text direction
- **Standardized Format**: Uses ISO 639-1 language codes for international compatibility
- **Application-Wide Usage**: Referenced by localization systems, theme selection, and content formatting

### HTTP Headers

The application defines standard HTTP header constants for consistent API communication:

- **Content-Type headers**: Constants for multipart form data and JSON application types
- **Authorization headers**: Bearer token authentication header names
- **Custom headers**: Application-specific headers including current user role, request ID, and response ID tracking
- **Localization headers**: Accept-Language header for internationalization support
- **Request identification**: Headers for tracing and debugging API requests across the system

### User Roles and Authentication

The authentication system defines constants for user roles and token management:

- **User roles**: Primary role constant for trainee users in the platform
- **Token management**: Constants for access tokens, refresh tokens, and general token handling
- **OAuth flow**: Authorization code constants for secure authentication flow
- **PKCE implementation**: Code verifier and challenge constants for enhanced security
- **Role management**: Constants for role identification and admin status checking
- **Profile management**: Constants for user profile type identification and management

### Training and Course Status

The system defines comprehensive status constants for training and course management:

- **Enrollment Status**: Constants for tracking user enrollment states including enrolled, nominated, and completed statuses
- **General Status**: System-wide status constants for disabled, active, draft, and published states
- **Training Types**: Categorization constants for self-paced, offline, and instructor-led learning approaches
- **Lesson Content Types**: Constants for different lesson formats including videos, articles, quizzes, slides, and files
- **Question Types**: Assessment constants for true/false and radio button question formats
- **Delivery Methods**: Constants distinguishing between online and in-person training delivery modes

### File and Media Constants

The application defines constants for file handling and media management:

- **File type identifier**: Generic file constant for file operations and type checking
- **Image formats**: Supported image file extensions including JPG, JPEG, and PNG formats
- **File size units**: Megabyte unit constant for file size display and calculations
- **URL handling**: Pre-signed URL constant for secure file access and HTTP/HTTPS protocol prefixes

### Days of Week

The system provides standardized day constants for scheduling and calendar functionality:

- **Individual day constants**: String constants for each day of the week from Sunday through Saturday
- **Ordered week collection**: A comprehensive list containing all seven days in chronological order starting with Sunday
- **Weekdays collection**: A filtered list containing only Monday through Friday for business day operations
- **Weekend collection**: A specialized list containing Friday and Saturday for weekend scheduling (following regional conventions)
- **Consistent naming**: Standardized day names for use across scheduling, filtering, and display components
- **Localization support**: English day names that can be used as keys for internationalization

### HTTP Status Codes

The application defines commonly used HTTP status code constants for consistent API response handling:

- **Success codes**: Constants for successful operations including OK (200) and Created (201) responses
- **Client error codes**: Constants for authentication failures (401), forbidden access (403), and not found (404) errors
- **Standardized handling**: Consistent status code references across all API communication layers
- **Error categorization**: Clear distinction between different types of HTTP response statuses

### Query Parameters

The system defines standard query parameter constants for URL construction and API requests:

- **Device parameters**: Mobile identifier for device-specific API responses and behavior
- **Content parameters**: Body and demo parameters for content delivery and testing modes
- **Search parameters**: Term parameter for search functionality and query operations
- **Pagination parameters**: Page, abbreviated page (p), and size parameters for data pagination and result limiting
- **Consistent naming**: Standardized parameter names across all API endpoints and routing operations

## API Constants

### Base URLs and Authentication

**API Constants Structure:**

The ApiConstants class centralizes all API endpoint paths and URLs used for server communication. This organization ensures consistent API path management and simplifies endpoint maintenance across the application.

**Authentication Endpoints:**
- **Fallback URL**: Default localhost URL for development and testing scenarios
- **Sign-In Path**: SSO token request endpoint for user authentication
- **Refresh Token Path**: Token renewal endpoint for maintaining user sessions
- **Logout Path**: Session termination endpoint for secure user logout

**Endpoint Management:**
- **Centralized Paths**: All API paths defined in one location for easy maintenance
- **Consistent Naming**: Clear, descriptive path names following REST conventions
- **Environment Flexibility**: Works with different base URLs across environments

### Catalog and Course Endpoints

The API defines specific endpoint paths for course catalog and training management:

- **Training catalog**: Endpoint path for retrieving training course listings with query parameter support
- **Learning tracks catalog**: Dedicated path for learning track collections and browsing functionality
- **Training details**: Base path for accessing individual training information and metadata
- **Learning tracks**: Endpoint for learning track details and progression information
- **Search functionality**: Course catalog search suggestions endpoint for autocomplete and search features

### User and Learning Endpoints

The API provides comprehensive endpoints for user management and learning progress tracking:

- **User profile**: Base endpoint for user information retrieval and profile management
- **Avatar management**: Dedicated path for user profile picture upload, update, and deletion operations
- **Learning history**: Endpoint for accessing user's complete learning history and progress records
- **Enrolled courses**: Specific path for retrieving currently enrolled courses with active status
- **All courses**: Comprehensive endpoint for all user courses regardless of enrollment status
- **Training progress**: Applicant-specific training endpoints for progress tracking and management
- **Learning track progress**: Dedicated paths for learning track enrollment and completion tracking

### Content and Media Endpoints

The API defines specialized endpoints for content delivery and media file access:

- **Video lessons**: Secure endpoint for video content access with playlist signing and key-based authentication
- **Training files**: General file access endpoint for training-related documents, resources, and materials
- **User files**: Personal file storage endpoint for user-uploaded content and profile-related media
- **Key-based access**: All endpoints use secure key parameters for authorized file access and content protection

### Assessment Endpoints

The API provides dedicated endpoints for assessment and testing functionality:

- **Quiz submissions**: Endpoint for submitting quiz answers and receiving immediate feedback
- **Qualification tests**: Dedicated path for formal qualification test answer submission and scoring
- **Test retakes**: Specialized endpoint for retaking qualification tests with test type specification
- **Assessment tracking**: All endpoints support progress tracking and result management for learning evaluation

### Dictionary and Reference Data

The API provides endpoints for accessing reference data and system dictionaries:

- **Sectors hierarchy**: Tree-structured endpoint for sector categorization and hierarchical organization
- **Organizations**: Public endpoint for accessing organization information and provider details
- **Geographic data**: Cities endpoint for location-based training catalog filtering and search
- **Sector features**: Specialized endpoint for sector-specific features and characteristics
- **Streams**: Content streaming endpoints for real-time data and live content delivery

## Asset Paths

### Logo and Branding

**Asset Paths Structure:**

The AssetsPath class provides centralized management of all static asset file paths used throughout the application. This organization ensures consistent asset referencing and simplifies asset management during development and maintenance.

**Logo Assets:**
- **SVG Logo**: Vector-based logo for scalable, high-quality rendering across different screen densities
- **Black Text Logo**: PNG logo variant with black text for light backgrounds and themes
- **White Text Logo**: PNG logo variant with white text for dark backgrounds and overlays
- **Qiwa Logo**: Partner organization logo for authentication and branding integration

**Asset Organization Benefits:**
- **Centralized Management**: All asset paths defined in one location for easy maintenance
- **Type Safety**: Compile-time validation of asset path references
- **Consistent Naming**: Clear, descriptive asset names following naming conventions
- **Easy Refactoring**: Simple asset path updates when file locations change

### Background Images

The application defines background image assets for visual design and branding:

- **Lined backgrounds**: Vector and raster versions of decorative line patterns for visual enhancement
- **Mini variants**: Smaller versions of line patterns for compact UI elements and reduced visual weight
- **Format flexibility**: Both SVG vector and PNG raster formats available for different rendering requirements
- **NSP branding**: Official NSP background image for brand consistency and visual identity
- **Scalable design**: Vector formats ensure crisp rendering across different screen densities

### User Interface Icons

The application provides a comprehensive set of UI icons for consistent interface design:

- **User actions**: Profile icon for user account access and personal information management
- **Search functionality**: Search icon for discovery features and content exploration
- **Content representation**: Book icon for learning materials and educational content
- **Data manipulation**: Filter and sort icons for content organization and data filtering
- **State variations**: Multiple filter icon states including standard and active (green) versions
- **Social features**: Share icon for content sharing and social interaction capabilities
- **Navigation**: Back icon for consistent navigation patterns and user flow control

### Lesson Type Icons

The system provides specific icons for different types of learning content:

- **Interactive assessments**: Quiz lesson icon for interactive testing and knowledge evaluation
- **Text content**: Article lesson icon for reading materials and written educational content
- **Document resources**: File lesson icon for downloadable resources and reference materials
- **Presentation content**: Slides lesson icon for structured presentations and visual learning materials
- **Video content**: Video lesson icon for multimedia learning experiences and visual instruction
- **Content categorization**: Clear visual distinction between different lesson formats for improved user experience

### Training Type Badges

The application provides visual badges to distinguish different training delivery methods:

- **In-person training**: Badge for classroom-based and face-to-face learning experiences
- **Learning tracks**: Specialized badge for structured learning path programs and sequential courses
- **Self-study**: Badge for independent learning and self-paced educational content
- **Online training**: Badge for digital and remote learning experiences
- **Visual categorization**: Clear visual indicators help users quickly identify training format and delivery method

### Bottom Navigation Icons

The application provides state-aware icons for the main navigation interface:

- **Home navigation**: Active and inactive states for the main dashboard and home screen access
- **Catalog navigation**: State-specific icons for course catalog browsing and discovery features
- **My Learning navigation**: Active and inactive icons for personal learning progress and enrolled courses
- **Profile navigation**: State-aware icons for user profile access and account management
- **Visual feedback**: Clear distinction between active and inactive states for improved user experience
- **Consistent design**: Unified icon style across all navigation elements for cohesive interface design

## Router Query Constants

**Router Query Constants Structure:**

The RouterQueryConstants class defines standardized query parameter names used in navigation and routing throughout the application. This ensures consistent parameter naming and simplifies route parameter management.

**Query Parameters:**
- **Learning Track Tab**: Parameter for specifying learning track tab selection in navigation
- **Tab Parameter**: Generic tab parameter for various tabbed interface navigation
- **Consistent Naming**: Standardized parameter names across all routing operations
- **Type Safety**: Compile-time validation of query parameter names to prevent typos

Used for passing query parameters in navigation:

**Navigation with Query Parameters**: The router query constants enable type-safe navigation with parameters. When navigating to routes like the catalog, developers can pass tab selection parameters using the predefined constant names. This ensures consistent parameter naming across the application and prevents typos in route parameter keys. The system supports various query parameters for different navigation scenarios including tab selection, content filtering, and state preservation.

## Storage Keys

### Hive Local Storage

**Hive Storage Keys Structure:**

The HiveKeys class defines standardized keys for local storage operations using the Hive database. This ensures consistent data storage and retrieval across the application with type-safe key management.

**Storage Keys:**
- **NSP Storage**: Main storage box identifier for application-specific data
- **Current Locale**: Key for storing user's selected language preference
- **Visited Before**: Key for tracking first-time user experience and onboarding state
- **Consistent Naming**: Standardized key names to prevent storage conflicts and ensure data integrity

### Secure Storage

The application defines secure storage keys for sensitive authentication data:

**SecureStorageKeys Class**: This class provides constants for accessing secure storage where sensitive authentication tokens are stored. The class includes keys for both access tokens and refresh tokens, ensuring consistent and secure storage access patterns. These keys are used with Flutter's secure storage system to protect user authentication data from unauthorized access while maintaining easy programmatic access for legitimate application operations.

## Usage Guidelines

### Importing Constants

**Standard Import Pattern**: Import the constants file from the core shared package to access all application constants.

**Usage Examples**:
- **Status Checking**: Compare status values using the predefined constants like ENROLLED for consistent status evaluation
- **API Calls**: Use ApiConstants for endpoint paths to ensure consistent API communication across the application
- **Asset References**: Access asset paths through AssetsPath constants for reliable asset loading and management
- **Type Safety**: All constant usage provides compile-time validation and prevents runtime errors from typos

### String Interpolation

**Dynamic Path Construction**: Build API URLs by combining base paths from ApiConstants with dynamic identifiers like training IDs for specific resource access.

**File Type Validation**: Use file extension constants to check file types programmatically. This pattern allows for reliable file type detection by comparing file names against predefined extension constants, supporting multiple formats through logical OR operations for comprehensive file type checking.

### Status Comparisons

**Enrollment Status Evaluation**: Compare training enrollment status using predefined constants to determine if a user is enrolled or has completed a training program.

**Training Type Classification**: Evaluate training delivery methods by comparing against training type constants. This enables the application to handle different learning approaches like self-paced study or instructor-led sessions with appropriate logic and UI adaptations.

**Boolean Logic Patterns**: Use constant comparisons to create boolean flags that drive conditional logic throughout the application, ensuring consistent status evaluation and type checking across all features.

## Best Practices

### Naming Conventions

1. **Use UPPER_CASE**: For enum-like string constants
2. **Use camelCase**: For general constants and paths
3. **Be descriptive**: Use clear, meaningful names
4. **Group logically**: Organize related constants together

### Adding New Constants

1. **Check for existing**: Ensure the constant doesn't already exist
2. **Choose appropriate class**: Add to the most logical constant class
3. **Document usage**: Add comments for complex constants
4. **Update documentation**: Update this file when adding new constants

### Avoiding Magic Strings

**Anti-Pattern Examples**: Avoid using literal strings and numbers directly in code comparisons as they create maintenance issues and potential typos.

**Best Practice Implementation**: Use predefined constants for all string and number comparisons to ensure consistency, prevent typos, and enable easy refactoring.

**Benefits of Constants**:
- **Maintainability**: Single point of change when values need updating
- **Type Safety**: Compile-time validation prevents runtime errors
- **Code Clarity**: Descriptive constant names improve code readability
- **Refactoring Support**: IDE can safely rename and track constant usage across the codebase

### Maintenance

1. **Regular review**: Periodically review for unused constants
2. **Consistent updates**: Update constants when API changes
3. **Version control**: Track changes to constants carefully
4. **Team communication**: Notify team of breaking constant changes

## Related Documentation

- [Shared Utilities](shared-utilities.md) - Core utilities and helpers
- [Environment Config](environment-config.md) - Environment-specific configurations
- [API Architecture](../api/api-architecture.md) - API communication setup
- [Error Handling](error-handling.md) - Error management strategies

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
