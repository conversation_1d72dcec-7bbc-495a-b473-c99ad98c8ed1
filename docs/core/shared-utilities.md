# Shared Utilities

Essential utilities and helpers used throughout the NSP app. These provide consistent, reusable functionality across all features.

## Quick Reference

| Utility | Purpose | Usage |
|---------|---------|-------|
| `errorHandler()` | API error handling | `apiCall().errorHandler(onSuccess: ..., onError: ...)` |
| `removeHtmlElements()` | Clean HTML text | `htmlString.removeHtmlElements()` |
| `AuthTokenProvider` | Secure token storage | `await tokenProvider.getToken()` |
| `appPrint()` | Debug-safe logging | `appPrint('Debug message')` |
| `languageCodeToName()` | Language display | `'en'.languageCodeToName()` |

## Table of Contents

- [Error Handling](#error-handling) - Standardized async error handling
- [String Processing](#string-processing) - Text manipulation utilities
- [Token Management](#token-management) - Secure authentication storage
- [Data Converters](#data-converters) - Type conversion helpers
- [Logging](#logging) - Debug-aware logging
- [Common Patterns](#common-patterns) - Real-world usage examples

## Error Handling

### The `errorHandler` Extension

**What it does**: Provides consistent error handling for all API calls with automatic error translation and user-friendly messages.

**When to use**: Every API call in repositories and data sources.

**Standard API Call Pattern**: The error handler extension provides a consistent pattern for all API calls in BLoCs, ensuring uniform error handling across the application.

**Success and Error Callbacks**: The pattern uses separate callbacks for success and error scenarios, allowing clean separation of happy path logic from error handling logic.

**State Management Integration**: The callbacks directly emit appropriate BLoC states, making it easy to integrate error handling with state management patterns.

**Automatic Error Processing**: The extension automatically processes different types of exceptions and provides localized error messages to the error callback.

**Why this pattern**:
- Automatically handles Dio exceptions
- Provides user-friendly error messages
- Includes request IDs for support
- Consistent across the entire app

### Error Types Handled

**Error Mapping Strategy**: The error handler automatically categorizes different error types and provides appropriate user-friendly messages. Network connectivity issues display connection prompts, authentication errors trigger token refresh flows, server errors show retry suggestions, and unknown errors include request IDs for support tracking.

### Common Error Scenarios

**Comparison Pattern**: Traditional try-catch blocks expose raw error messages to users and require manual error handling in each location. The errorHandler extension provides consistent error processing with automatic user-friendly message generation, centralized error logging, and standardized error state management across all API calls.

## String Processing

### HTML Content Cleaning

**Problem**: API returns HTML content that needs to be displayed as plain text.

**Solution**: Use `removeHtmlElements()` extension.

**HTML Cleaning Pattern**: Apply the removeHtmlElements extension method to HTML strings before displaying them in Text widgets. This method strips all HTML tags, converts HTML entities to plain text, and removes extra whitespace to ensure clean, readable content for users.

**What it removes**:
- HTML tags: `<p>`, `<br>`, `<strong>`, etc.
- HTML entities: `&nbsp;`, `&amp;`, etc.
- Extra whitespace

### Language Display

**Problem**: Need to show user-friendly language names instead of codes.

**Language Display Pattern**: Convert language codes to localized display names using the languageCodeToName extension method. This transforms technical codes like 'en' into user-friendly names such as "English" or "الإنجليزية" based on the current app locale, improving user experience with proper language representation.

**Supported languages**: English (`en`) and Arabic (`ar`) with proper localization.

## Data Converters

### String Converters

Located in `lib/core/shared/converters/string_converters.dart`, these functions handle common data type conversions.

#### Language Code Conversion

**Language Code Translation**: Converts language codes to user-friendly display text using localization keys for consistent language representation across the application.

**Locale Support**: Handles English and Arabic language codes, converting them to localized display names that users can understand.

**Localization Integration**: Uses the app's localization system to ensure language names are displayed in the user's current language preference.

#### Enum Conversions

**String to Enum Conversion**: Converts string values from API responses to strongly-typed enum values, providing type safety and preventing invalid state representation.

**Training Type Conversion**: Handles conversion of training type strings to TrainingType enums, supporting instructor-led and self-paced formats with fallback to none for unknown types.

**Lesson Type Conversion**: Converts lesson type strings to LessonType enums, supporting various content types including videos, articles, quizzes, slides, and files with null return for unknown types.

**Default Handling**: Includes appropriate fallback handling for unknown or invalid values, preventing crashes and providing graceful degradation when encountering unexpected data.

**Type Safety**: Provides strongly-typed enum values that prevent invalid state representation and enable compile-time validation of training and lesson types throughout the application.

### Date Time Converter

**Duration Conversion**: Converts duration objects from JSON responses to string format for display and processing purposes.

**Simple String Conversion**: Provides a straightforward conversion from Object type to string representation, handling various duration formats from API responses.

**Display Formatting**: Enables consistent duration display across the application by converting API duration data to readable string format.

### Bytes to MB Converter

**File Size Conversion**: Converts byte values to megabytes for user-friendly file size display with appropriate decimal precision.

**Calculation Logic**: Performs standard byte-to-megabyte conversion using 1024-based calculation and formats the result to two decimal places for consistent display.

**User Interface Support**: Provides formatted file size strings that can be directly displayed in UI components for file size information.

## Token Management

### AuthTokenProvider - Secure Authentication Storage

**Purpose**: Manages JWT tokens securely with automatic caching and state management.

**Key Features**:
- 🔒 Secure storage (encrypted on device)
- ⚡ In-memory caching for performance
- 📡 Reactive authentication state
- 🔄 Automatic token refresh integration

### Essential Methods

**Authentication Status Check**: Provides methods to check current authentication status by verifying the presence of valid tokens in secure storage.

**Reactive Authentication State**: Offers a stream-based approach to monitor authentication state changes, enabling reactive UI updates when users log in or out.

**Token Management**: Includes methods for saving tokens after successful login and clearing authentication tokens during logout processes, ensuring complete session management.

**Logout Handling**: Demonstrates integration with authentication state monitoring to handle logout scenarios and redirect users appropriately.

**Secure Storage Integration**: All token operations use secure storage to protect sensitive authentication data while providing easy programmatic access.

### Real-World Usage

**Widget Authentication Integration**: Demonstrates how to integrate authentication state monitoring into Flutter widgets using StreamBuilder for reactive UI updates.

**Conditional UI Rendering**: Shows how to conditionally render different screens based on authentication status, providing seamless user experience transitions between authenticated and unauthenticated states.

**Interceptor Integration**: Illustrates how to use the token provider in HTTP interceptors to automatically add authentication headers to API requests.

**Stream-Based Architecture**: Uses the authentication state stream to automatically update UI when authentication status changes, eliminating manual state management.

**Token Header Management**: Shows proper token retrieval and header injection for secure API communication with null safety checks.

## Logging

### Debug-Safe Logging with `appPrint`

**Problem**: `print()` statements appear in production builds and can expose sensitive data.

**Solution**: Use `appPrint()` for all debug logging.

**Production Safety**: AppPrint provides debug-only logging that automatically excludes sensitive information from production builds, preventing security vulnerabilities.

**Debug Mode Detection**: The utility automatically detects debug mode and only outputs logs during development, ensuring clean production builds without debug information.

**Secure Logging Pattern**: Demonstrates the difference between unsafe production logging and secure debug-only logging for sensitive user information and system data.

**Structured Logging**: Supports multiple log statements for different types of debug information including user actions and API errors.

**When to use**:
- Debugging API calls
- Tracking user actions
- Error investigation
- Performance monitoring

**Best practices**:
- Include context: `appPrint('LOGIN_ERROR: $message')`
- Use structured format: `appPrint('API_CALL: GET /trainings - 200 OK')`
- Never log sensitive data (tokens, passwords, personal info)

## Language Utilities

### Text Language Utils

Utilities for handling text direction and language-specific formatting.

**RTL Language Detection**: Provides utility functions to detect right-to-left languages (specifically Arabic) for proper text direction and layout handling.

**Font Family Selection**: Automatically selects appropriate font families based on the current locale, using Tajawal for Arabic and Noto Sans for English.

**Localization Integration**: Integrates with EasyLocalization to access current locale information and make language-specific UI decisions.

**Typography Support**: Enables proper typography handling across different languages with appropriate font selection for optimal readability.

## Usage Examples

### Error Handling in Repository

**Repository Error Pattern**: Demonstrates the standard pattern for implementing error handling in repository classes using the errorHandler extension.

**Data Processing**: Shows how to process successful API responses by converting JSON data to domain objects within the success callback.

**Error Propagation**: Illustrates proper error handling by re-throwing exceptions with processed error messages for upper layers to handle.

**Callback Structure**: Uses the onSuccess and onError callback pattern to separate happy path logic from error handling logic in repository methods.

### String Processing in UI

**HTML Content Processing**: Demonstrates how to clean HTML content for display in Flutter Text widgets using the removeHtmlElements extension.

**Widget Integration**: Shows integration of string processing utilities within Flutter widgets for clean content presentation.

**Theme Integration**: Uses the app's text theme system for consistent typography while processing and displaying cleaned content.

**Content Sanitization**: Ensures that HTML tags are removed from content before display, preventing rendering issues in Text widgets.

### Token Usage in Interceptor

**HTTP Interceptor Integration**: Shows how to integrate the AuthTokenProvider with Dio interceptors for automatic authentication header injection.

**Token Retrieval**: Demonstrates asynchronous token retrieval from secure storage within HTTP request interceptors.

**Header Management**: Illustrates proper authorization header construction using application constants for consistent header formatting.

**Request Processing**: Shows the complete interceptor pattern including token checking, header injection, and request forwarding for seamless authentication.

## Common Patterns

### 1. Repository with Error Handling

**Complete Repository Pattern**: Demonstrates a full repository implementation with error handling, logging, and proper return value management.

**Success Logging**: Shows how to use appPrint for debug logging of successful operations with meaningful context information.

**Error Logging**: Illustrates error logging patterns with clear error indicators and descriptive messages for debugging purposes.

**Data Flow Management**: Demonstrates proper data flow from data source through error handling to return value, ensuring clean repository interfaces.

### 2. BLoC with Utilities

**BLoC Integration Pattern**: Shows how to integrate multiple utilities (error handling, string processing) within BLoC event handlers for comprehensive data processing.

**Data Transformation**: Demonstrates data cleaning and transformation using utility functions (removeHtmlElements) before emitting states.

**State Management Flow**: Illustrates the complete flow from loading state through data processing to final state emission with error handling.

**Utility Composition**: Shows how different utilities work together in real-world scenarios, combining error handling with content processing for clean, user-ready data.

### 3. Authentication Flow

**Authentication Service Pattern**: Demonstrates a complete authentication service implementation using error handling and token management utilities.

**Token Management Integration**: Shows how to integrate token provider utilities for secure token storage and management during login and logout flows.

**Comprehensive Logging**: Illustrates proper logging for authentication events with success and failure indicators for debugging and monitoring.

**Error Handling**: Shows proper error handling with custom exception throwing for authentication-specific errors while maintaining clean service interfaces.

### 4. UI with Authentication State

**Reactive UI Pattern**: Demonstrates reactive UI implementation using authentication state streams for automatic UI updates based on authentication status.

**Dependency Injection Integration**: Shows how to access utilities through dependency injection (GetIt) for clean separation of concerns in UI components.

**Loading State Handling**: Includes proper handling of loading states while authentication status is being determined, providing smooth user experience.

**Conditional Rendering**: Illustrates conditional UI rendering based on authentication state, automatically switching between authenticated and unauthenticated interfaces.

## Quick Troubleshooting

| Problem | Solution |
|---------|----------|
| API errors not user-friendly | Use `errorHandler()` extension |
| HTML showing in UI | Use `string.removeHtmlElements()` |
| Auth state not updating | Check `authStateStream` subscription |
| Debug logs in production | Replace `print()` with `appPrint()` |
| Language codes showing | Use `languageCodeToName()` |

## Related Documentation

- [Error Handling](error-handling.md) - Detailed error handling strategies
- [Constants](constants.md) - Application constants and configurations
- [Theme System](theme-system.md) - Theming and typography
- [API Architecture](../api/api-architecture.md) - API communication setup
- [State Management](../architecture/state-management.md) - BLoC patterns

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
