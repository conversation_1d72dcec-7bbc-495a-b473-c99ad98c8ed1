# Error Handling

## Overview

The NSP mobile application implements a comprehensive error handling strategy that provides consistent error management across all layers of the application. This document covers error handling patterns, custom exceptions, user feedback mechanisms, and debugging strategies.

## Error Handling Architecture

### Error Flow Through Layers

```mermaid
graph TD
    A[API Error] -->|Caught by| B[Data Source]
    B -->|Transforms to| C[Domain Exception]
    C -->|Handled by| D[Repository]
    D -->|Processed by| E[BLoC]
    E -->|Emits| F[Error State]
    F -->|Displays| G[User-Friendly Message]
    
    H[Validation Error] -->|Caught by| I[Input Validator]
    I -->|Returns| J[Validation Message]
    J -->|Displayed in| K[Form Field]
```

## Custom Exception Types

### 1. Base Exception Classes

**Exception Hierarchy**: The application implements a structured exception hierarchy with a base AppException class that provides common functionality for all application-specific errors.

**Base Exception Structure**: AppException serves as the foundation with message and optional code properties, providing consistent error representation across the application with standardized toString formatting.

**Network Exception Handling**: NetworkException extends the base class to handle connectivity issues, network timeouts, and communication failures with appropriate error messaging.

**Authentication Exception Management**: AuthenticationException handles login failures, token expiration, and authorization issues with specific error categorization.

**Validation Exception Processing**: ValidationException manages input validation errors with optional field tracking to identify specific validation failures in forms and user input.

**Business Logic Exceptions**: BusinessLogicException handles application-specific business rule violations and domain-specific errors that don't fit into other categories.

### 2. Specific Exception Types

**Enrollment Exception Management**: EnrollmentException handles course enrollment-specific errors with factory methods for common scenarios like already enrolled users, full capacity courses, and unmet prerequisites. Each exception includes descriptive error codes for programmatic handling.

**Content Access Exception Handling**: ContentAccessException manages content access restrictions including enrollment requirements and content progression locks. Factory methods provide specific error scenarios with appropriate user-facing messages.

**Factory Method Pattern**: All specific exception types use factory methods to create common error scenarios, providing consistent error messages and reducing code duplication across the application.

**Error Code Integration**: Each exception includes specific error codes that enable programmatic error handling and allow for different UI responses based on the specific error type encountered.

## Error Handler Extension

### Universal Error Handler

**Universal Error Handler Extension:**

The ErrorHandler extension provides a comprehensive, standardized approach to handling asynchronous operations with intelligent error management and localized user feedback. This extension simplifies error handling across the entire application.

**Core Functionality:**
- **Generic Type Support**: Works with any Future<T> for maximum flexibility across different data types
- **Callback Architecture**: Clean separation of success and error handling logic
- **Exception Classification**: Intelligent handling of DioException, AppException, and generic exceptions
- **Localized Messages**: All error messages use localization keys for multi-language support

**Error Handling Strategy:**
- **DioException Processing**: Specialized handling for HTTP and network-related errors with detailed categorization
- **Timeout Management**: Comprehensive timeout handling for connection, send, and receive operations
- **HTTP Status Mapping**: Intelligent mapping of HTTP status codes to user-friendly localized messages
- **Server Message Extraction**: Attempts to extract meaningful error messages from server responses

**HTTP Error Classification:**
- **Client Errors (4xx)**: Bad requests, unauthorized, forbidden, not found, validation errors
- **Server Errors (5xx)**: Internal server errors, service unavailable, gateway timeouts
- **Network Errors**: Connection timeouts, no internet connection, request cancellation
- **Fallback Handling**: Generic error messages for unexpected scenarios with status code inclusion

### Usage in BLoCs

**BLoC Error Integration**: The error handler extension integrates seamlessly with BLoC pattern, providing clean separation between success and error state emissions.

**Repository Error Handling**: BLoCs use the error handler extension on repository methods to automatically handle errors and emit appropriate states based on the operation outcome.

**State Management**: The pattern ensures that loading states are emitted before operations, success states contain the data, and error states include localized error messages for user display.

**Callback Pattern**: The onSuccess and onError callbacks provide clear separation of concerns, allowing BLoCs to focus on state management while delegating error processing to the extension.

## Validation Error Handling

### 1. Input Validation

**Input Validation System**: The InputValidator class provides static methods for validating common input types with localized error messages and consistent validation patterns.

**Email Validation**: Validates email format using regular expressions, checking for null/empty values and proper email structure with appropriate error messaging for different failure scenarios.

**Password Validation**: Implements comprehensive password validation including length requirements, complexity checks for uppercase, lowercase, and numeric characters with specific error messages for each validation rule.

**ValidationResult Pattern**: Uses a result pattern that encapsulates validation success/failure state with optional error messages, providing a clean API for validation operations.

**Localization Integration**: All validation error messages use localization keys to support multiple languages and provide consistent user-facing error messages across the application.

### 2. Form Validation

**Form Validation Architecture**: ValidatedForm demonstrates a comprehensive form validation pattern using Flutter's Form widget with custom validation logic and real-time error feedback.

**Real-Time Validation**: The form implements both real-time validation (onChanged callbacks) and submission validation (Form validators) to provide immediate feedback and prevent invalid submissions.

**Error State Management**: Each input field maintains its own error state that updates in real-time as users type, providing immediate feedback about validation issues.

**Validation Integration**: The form integrates with the InputValidator class to leverage centralized validation logic while maintaining form-specific error display and state management.

**User Experience**: The pattern provides a smooth user experience with immediate validation feedback, clear error messages, and prevention of invalid form submissions through comprehensive validation checks.

## Error State Management

### 1. Error States in BLoCs

**Generic State Management**: The DataState hierarchy provides a generic, reusable pattern for managing data loading states across different BLoCs with type safety and consistent state representation.

**State Lifecycle**: The pattern covers the complete data loading lifecycle from initial state through loading, success (loaded), and error states with appropriate properties for each phase.

**Error State Details**: DataError includes comprehensive error information including user-friendly messages, original exceptions, stack traces for debugging, and retry capability flags for UI decision making.

**Equatable Integration**: All states extend Equatable to enable proper state comparison in BLoCs, ensuring UI rebuilds only occur when state actually changes and improving performance.

**Type Safety**: The generic type parameter ensures type safety across different data types while maintaining a consistent state management pattern throughout the application.

### 2. Error Recovery

**Error Recovery System**: ErrorRecoveryBloc implements a sophisticated error recovery mechanism with automatic retry logic and exponential backoff to handle transient failures gracefully.

**Retry Logic**: The bloc supports configurable retry attempts with automatic retry triggering for failed operations, reducing user friction when dealing with temporary network or service issues.

**Exponential Backoff**: Implements exponential backoff strategy to avoid overwhelming failing services while providing reasonable retry intervals that increase with each attempt.

**State Management**: Manages recovery states including loading, success, and error states with retry capability tracking to inform UI about available recovery options.

**Operation Abstraction**: Accepts generic operation functions that can be retried, making the recovery system reusable across different types of operations and failure scenarios.

## User Feedback Mechanisms

### 1. Toast Messages

**Toast Message System:**

The toast message system provides a consistent, user-friendly way to display temporary feedback messages throughout the application. This system integrates with the app's theme and provides semantic color coding for different message types.

**Toast Configuration:**
- **Message Display**: Configurable message text with support for localized content
- **Type-Based Styling**: Four distinct toast types (success, error, warning, info) with semantic color coding
- **Animation System**: Smooth slide animations from top with configurable duration (default 3 seconds)
- **Theme Integration**: Uses app's text theme and color system for consistent visual appearance

**Toast Types and Colors:**
- **Success Toast**: Uses AppColors.statusSuccess (green) for positive feedback and confirmations
- **Error Toast**: Uses AppColors.statusWarning (red) for error messages and failures
- **Warning Toast**: Uses AppColors.orangeAccentPrimary (orange) for cautionary messages
- **Info Toast**: Uses AppColors.greenAccentPrimary (blue) for informational messages

**Visual Features:**
- Top-positioned display for non-intrusive user experience
- White text on colored background for optimal readability
- Automatic dismissal with configurable duration
- Consistent typography using app's text theme system

### 2. Error Dialog

**Error Dialog Component**: ErrorDialog provides a standardized modal dialog for displaying error messages with consistent styling, iconography, and action buttons across the application.

**Visual Design**: The dialog features an error icon with warning color, clear title and message layout, and action buttons for user interaction including optional retry functionality.

**Action Handling**: Supports optional retry and dismiss callbacks, allowing flexible error handling patterns where some errors can be retried while others only need acknowledgment.

**Static Show Method**: Includes a convenient static show method that simplifies dialog presentation with a clean API for displaying error dialogs from anywhere in the application.

**Localization Support**: All button text uses localization keys to support multiple languages and provide consistent user interface text across different locales.

### 3. Error State Widgets

**Error State Display**: ErrorStateWidget provides a full-screen error state component for displaying errors when entire screens or major sections fail to load data.

**Visual Hierarchy**: The widget uses a centered layout with large error icon, primary error message, detailed description, and optional retry button to create clear visual hierarchy.

**Customizable Icon**: Supports custom error icons while providing a sensible default, allowing different error types to have appropriate visual representation.

**Typography Integration**: Uses the app's text theme system with appropriate styling for headings and body text, maintaining consistency with the overall design system.

**Conditional Actions**: Displays retry button only when retry functionality is available, providing clean UI that adapts to different error scenarios and recovery options.

## Logging and Monitoring

### 1. Error Logging

**Comprehensive Error Logging**: ErrorLogger provides a centralized logging system that captures errors across multiple channels including console output, crash reporting, and analytics.

**Debug Mode Logging**: In debug mode, errors are logged to console with full stack traces and context information for immediate developer feedback during development.

**Crash Reporting Integration**: Automatically logs errors to Firebase Crashlytics with stack traces and contextual information for production error monitoring and debugging.

**Analytics Integration**: Records error events in Firebase Analytics with error type and message information to track error patterns and frequency across the user base.

**Resilient Logging**: Each logging channel is wrapped in try-catch blocks to ensure that logging failures don't crash the application or interfere with error recovery processes.

**Contextual Information**: Supports additional context data that can be attached to error logs for better debugging and understanding of error circumstances.

### 2. Global Error Handler

**Global Error Handling**: GlobalErrorHandler provides application-wide error capture for both Flutter framework errors and platform-level errors that occur outside the Flutter framework.

**Flutter Error Integration**: Hooks into Flutter's error handling system to capture widget errors, rendering issues, and other framework-related problems with detailed context information including library and context details.

**Platform Error Capture**: Handles errors that occur outside the Flutter framework, including native platform errors and uncaught exceptions in isolates or background processes.

**Centralized Logging**: Routes all captured errors through the centralized ErrorLogger system to ensure consistent error reporting and monitoring across all error sources.

**Initialization Pattern**: Provides a simple initialization method that should be called early in the application lifecycle (typically in main.dart) to ensure comprehensive error coverage from app startup.

## Testing Error Handling

### 1. Testing Exception Handling

**Exception Testing Strategy**: Comprehensive testing approach for validating that different types of exceptions are properly handled and transformed into appropriate application-specific exceptions.

**Network Error Testing**: Tests verify that network-related DioExceptions (like connection timeouts) are properly caught and converted to NetworkException instances with appropriate error handling.

**Authentication Error Testing**: Validates that HTTP 401 responses are correctly identified and transformed into AuthenticationException instances for proper authentication error handling.

**Mock-Based Testing**: Uses mock objects to simulate various error conditions, allowing comprehensive testing of error handling logic without relying on actual network failures or server errors.

**Exception Type Validation**: Tests verify not just that exceptions are thrown, but that they are of the correct type, ensuring proper error categorization and handling throughout the application.

### 2. Testing Error States

**BLoC Error State Testing**: Uses bloc_test package to verify that BLoCs properly handle exceptions from repositories and emit appropriate error states with correct error messages.

**State Sequence Validation**: Tests verify the complete state sequence from loading state through error state, ensuring proper state management during error scenarios.

**Repository Exception Simulation**: Mocks repository methods to throw specific exceptions, allowing testing of error handling logic without external dependencies or actual failures.

**Error Message Propagation**: Validates that error messages from exceptions are properly propagated through BLoC states to ensure users receive appropriate feedback about failures.

## Best Practices

### 1. Error Handling Guidelines

- **Fail Fast**: Detect and handle errors as early as possible
- **Graceful Degradation**: Provide fallback functionality when possible
- **User-Friendly Messages**: Show meaningful messages to users
- **Logging**: Log errors for debugging and monitoring
- **Recovery**: Provide retry mechanisms where appropriate

### 2. Exception Design

- **Specific Exceptions**: Create specific exception types for different error scenarios
- **Meaningful Messages**: Include helpful error messages
- **Error Codes**: Use error codes for programmatic handling
- **Context Information**: Include relevant context in exceptions

### 3. User Experience

- **Non-Blocking**: Don't block the UI with error dialogs unless critical
- **Progressive Disclosure**: Show basic error message with option for details
- **Actionable**: Provide clear actions users can take
- **Consistent**: Use consistent error presentation patterns

## Related Documentation

- [API Architecture](../api/api-architecture.md)
- [State Management](../architecture/state-management.md)
- [Testing Guide](../development/testing-guide.md)
- [Debugging Guide](../troubleshooting/debugging.md)
