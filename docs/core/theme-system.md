# Theme System

## Table of Contents
- [Overview](#overview)
- [Theme Architecture](#theme-architecture)
- [Color System](#color-system)
- [Typography System](#typography-system)
- [Multi-Language Support](#multi-language-support)
- [Component Integration](#component-integration)
- [Responsive Design](#responsive-design)
- [Best Practices](#best-practices)

## Overview

The NSP mobile application implements a comprehensive theme system that provides consistent styling, supports multiple languages with appropriate fonts, and maintains design system standards throughout the app. The theme system serves as the foundation for visual consistency and user experience across all application features.

### Key Features
- **Unified Color Palette**: Centralized color management with semantic naming
- **Multi-Language Typography**: Automatic font selection for English and Arabic content
- **Component Integration**: Seamless theme integration across all UI components
- **Responsive Design**: Adaptive styling for different screen sizes
- **Accessibility Support**: High contrast ratios and readable typography
- **Design System Compliance**: Adherence to Figma UI Kit specifications

## Theme Architecture

### System Structure

```mermaid
graph TB
    subgraph "🎨 Theme System"
        Core[Core Theme<br/>• AppTheme<br/>• Material 3 Integration<br/>• Locale Awareness]

        subgraph "🎯 Color System"
            Colors[AppColors<br/>• Brand Colors<br/>• Status Colors<br/>• Neutral Palette]
            Semantic[Semantic Colors<br/>• Primary/Secondary<br/>• Success/Warning<br/>• Background/Surface]
        end

        subgraph "📝 Typography"
            TextTheme[AppTextTheme<br/>• Heading Hierarchy<br/>• Body Text Styles<br/>• Locale-Aware Sizing]
            Fonts[Font Management<br/>• Noto Sans (EN)<br/>• Tajawal (AR)<br/>• Auto Selection]
        end

        subgraph "🧩 Component Themes"
            Buttons[Button Themes<br/>• AppButton<br/>• TextButton<br/>• IconButton]
            Inputs[Input Themes<br/>• TextFields<br/>• Decorations<br/>• Focus States]
            Cards[Card Themes<br/>• Containers<br/>• Shadows<br/>• Borders]
        end
    end

    subgraph "📱 Application"
        Features[Feature Modules<br/>• Auth<br/>• Catalog<br/>• Profile]
        Widgets[UI Components<br/>• Shared Widgets<br/>• Custom Components<br/>• Layout Elements]
    end

    Core --> Colors
    Core --> TextTheme
    Colors --> Semantic
    TextTheme --> Fonts

    Core --> Buttons
    Core --> Inputs
    Core --> Cards

    Buttons --> Features
    Inputs --> Features
    Cards --> Features

    Features --> Widgets

    style Core fill:#e3f2fd
    style Colors fill:#f3e5f5
    style TextTheme fill:#e8f5e8
    style Buttons fill:#fff3e0
    style Features fill:#fce4ec
```

### Theme Component Structure

**Core Theme Files**:
- **app_colors.dart**: Complete color palette with semantic naming
- **app_text_theme.dart**: Typography system with locale awareness
- **app_theme.dart**: Main theme configuration and Material 3 integration
- **theme.dart**: Centralized export file for easy imports

## Color System

### AppColors Definition

The `AppColors` class defines the complete color palette based on the Figma UI Kit, ensuring consistent visual identity across the app.

**AppColors Class Structure:**

The `AppColors` class serves as the central color palette repository, defining all color constants used throughout the application. This class ensures consistent visual identity by providing a single source of truth for all color values.

**Color Organization:**
- **Primary Brand Colors**: Core brand identity colors including primary green accent, secondary accent, and dark accent variations
- **Status Colors**: Semantic colors for user feedback including success green, warning red, and their lighter variants
- **Neutral Colors**: Foundation colors including pure white, deep black, and primary grey for text and backgrounds
- **Extended Palette**: Additional color variations for different UI states and components

**Implementation Approach:**
- Uses static constant Color objects for optimal performance
- Follows Material Design color naming conventions
- Provides hex color values for precise color matching with design specifications
- Organized by functional categories for easy maintenance and usage

**Color Categories:**
- **Brand Colors**: Primary green tones for main UI elements and branding
- **Status Colors**: Success (green) and warning (red) for user feedback
- **Neutral Colors**: Black, white, and grey tones for text and backgrounds
- **Background Colors**: Light tones for app backgrounds and surfaces

### Color Palette Structure

```mermaid
graph LR
    subgraph "🎨 Brand Colors"
        Primary[Green Accent Primary<br/>#0060FF<br/>Primary Actions]
        Secondary[Green Accent Secondary<br/>#80A9AC<br/>Secondary Actions]
        Dark[Green Accent Dark<br/>#173255<br/>Text & Emphasis]
    end

    subgraph "📊 Status Colors"
        Success[Status Success<br/>Green Tones<br/>Confirmations]
        Warning[Status Warning<br/>Red Tones<br/>Errors]
        Info[Status Info<br/>Blue Tones<br/>Information]
    end

    subgraph "⚪ Neutral Colors"
        White[Neutral White<br/>#FFFFFF<br/>Backgrounds]
        Black[Neutral Black<br/>#000000<br/>Text]
        Grey[Grey Variants<br/>Multiple Shades<br/>Secondary Content]
    end

    subgraph "🏠 Background Colors"
        UIBg[UI Background<br/>Primary Surfaces]
        CardBg[Card Background<br/>Container Surfaces]
        AccentBg[Accent Background<br/>Highlighted Areas]
    end

    style Primary fill:#0060FF,color:#fff
    style Secondary fill:#80A9AC,color:#fff
    style Dark fill:#173255,color:#fff
    style Success fill:#4CAF50,color:#fff
    style Warning fill:#F44336,color:#fff
    style Info fill:#2196F3,color:#fff
```

### Color Usage Guidelines

#### Brand Color Applications
- **Primary Green**: Main call-to-action buttons, active navigation states, primary links
- **Secondary Green**: Hover states, secondary buttons, accent elements
- **Dark Green**: High-contrast text, emphasis elements, dark mode preparations

#### Status Color Applications
- **Success Colors**: Completion indicators, positive feedback, success messages
- **Warning Colors**: Error states, destructive actions, critical alerts
- **Neutral Colors**: Inactive states, disabled elements, secondary information

#### Background Color Applications
- **Primary Backgrounds**: Main app surfaces, screen backgrounds
- **Secondary Backgrounds**: Card containers, modal backgrounds
- **Accent Backgrounds**: Highlighted sections, featured content areas

### Color Accessibility

**Contrast Ratios**:
- **Text on Light**: Minimum 4.5:1 contrast ratio for normal text
- **Text on Dark**: Minimum 3:1 contrast ratio for large text
- **Interactive Elements**: Minimum 3:1 contrast ratio for focus indicators
- **Brand Colors**: Tested for accessibility across all usage contexts

## Typography System

### Font Configuration

The theme system automatically selects appropriate fonts based on the current locale, ensuring optimal readability for both English and Arabic content.

**AppTheme Class Structure:**

The `AppTheme` class manages the application's visual theme configuration, providing locale-aware font selection and Material Design 3 integration. This class serves as the central theme configuration point for the entire application.

**Font Management:**
- **English Font**: Uses 'Noto Sans' for clean, modern typography in Latin scripts
- **Arabic Font**: Uses 'Tajawal' for optimal Arabic text rendering and RTL support
- **Automatic Selection**: Dynamically chooses appropriate font family based on current app locale
- **Material 3 Integration**: Leverages Material Design 3 theming system for consistent UI patterns

**Theme Configuration:**
- Implements light theme with Material 3 design principles
- Applies locale-specific font families to the entire text theme
- Provides foundation for future dark theme implementation
- Ensures consistent typography across all app components

**Font Selection Logic:**
- **English Content**: Uses 'Noto Sans' for clean, modern appearance
- **Arabic Content**: Uses 'Tajawal' for proper RTL support and readability
- **Automatic Switching**: Font family changes based on current app locale
- **Material 3**: Leverages Material Design 3 typography system

### Text Styles System

The text theme system provides a comprehensive set of typography styles with automatic locale-aware font sizing and weight adjustments.

**AppTextTheme System Architecture:**

The text theme system provides a comprehensive typography solution with automatic locale-aware adjustments. It extends BuildContext to provide easy access to styled text throughout the application.

**System Components:**
- **Context Extension**: Adds `textTheme` property to BuildContext for convenient access
- **AppTextStyles Class**: Private class managing all text style definitions and locale logic
- **Locale Detection**: Automatically detects Arabic locale for appropriate font sizing adjustments
- **Style Hierarchy**: Comprehensive set of heading and body text styles

**Key Features:**
- **Locale-Aware Sizing**: Different font sizes for English (32px) vs Arabic (33px) for optimal readability
- **Weight Management**: Consistent font weight application across all text styles
- **Easy Access Pattern**: Simple usage via `context.textTheme.h1` throughout the app
- **Design System Compliance**: Matches Figma UI Kit specifications for both languages

**Text System Features:**
- **Context Extension**: Easy access via `context.textTheme.h1`
- **Locale-Aware Sizing**: Different font sizes for English vs Arabic
- **Consistent Styling**: Base styles with modifier support
- **UI Kit Compliance**: Matches Figma design specifications

### Typography Hierarchy

```mermaid
graph TB
    subgraph "📝 Typography System"
        subgraph "🏷️ Heading Styles"
            H1[H1 - Page Titles<br/>32px/33px (EN/AR)<br/>Bold Weight]
            H2[H2 - Section Headers<br/>28px/29px (EN/AR)<br/>Bold Weight]
            H3[H3 - Subsection Headers<br/>24px/25px (EN/AR)<br/>SemiBold Weight]
            H4[H4 - Component Titles<br/>20px/21px (EN/AR)<br/>SemiBold Weight]
            H5[H5 - Small Headers<br/>18px/19px (EN/AR)<br/>Medium Weight]
            H6[H6 - Smallest Headers<br/>16px/17px (EN/AR)<br/>Medium Weight]
        end

        subgraph "📄 Body Text Styles"
            Large[Text Large<br/>18px/19px (EN/AR)<br/>Regular Weight]
            Medium[Text Medium<br/>16px/17px (EN/AR)<br/>Regular Weight]
            Small[Text Small<br/>14px/15px (EN/AR)<br/>Regular Weight]
            XSmall[Text XSmall<br/>12px/13px (EN/AR)<br/>Regular Weight]
        end

        subgraph "🎨 Style Modifiers"
            Weight[Weight Modifiers<br/>• Bold (700)<br/>• SemiBold (600)<br/>• Medium (500)<br/>• Regular (400)]
            Color[Color Modifiers<br/>• Primary Grey<br/>• Secondary Grey<br/>• Accent Green<br/>• Status Colors]
        end
    end

    H1 --> Weight
    H2 --> Weight
    H3 --> Weight
    Large --> Color
    Medium --> Color
    Small --> Color

    style H1 fill:#e3f2fd
    style H2 fill:#f3e5f5
    style H3 fill:#e8f5e8
    style Large fill:#fff3e0
    style Medium fill:#fce4ec
    style Small fill:#e0f2f1
```

#### Heading Applications
- **H1 (32px/33px)**: Main page titles, primary headings, feature names
- **H2 (28px/29px)**: Section headers, major content divisions
- **H3 (24px/25px)**: Subsection headers, card titles, modal headers
- **H4 (20px/21px)**: Component titles, form section headers
- **H5 (18px/19px)**: Small headers, list item titles
- **H6 (16px/17px)**: Smallest headers, metadata labels

#### Body Text Applications
- **Text Large (18px/19px)**: Important body content, feature descriptions
- **Text Medium (16px/17px)**: Standard body text, form labels
- **Text Small (14px/15px)**: Secondary content, helper text
- **Text XSmall (12px/13px)**: Caption text, timestamps, metadata

#### Style Modifier System
**Weight Modifiers**:
- **Bold (700)**: Emphasis, important information, headings
- **SemiBold (600)**: Subheadings, section titles, highlighted content
- **Medium (500)**: Labels, navigation items, secondary emphasis
- **Regular (400)**: Standard body text, default weight

**Color Modifiers**:
- **Primary Grey**: Main text content, high readability
- **Secondary Grey**: Supporting text, less prominent information
- **Accent Green**: Links, interactive elements, brand emphasis
- **Status Colors**: Success, warning, error, and info text

## Multi-Language Support

### Font Selection System

```mermaid
graph LR
    subgraph "🌐 Locale Detection"
        Context[App Context] --> Locale{Current Locale}
        Locale -->|English| EN[English Font]
        Locale -->|Arabic| AR[Arabic Font]
    end

    subgraph "🔤 Font Families"
        EN --> NotoSans[Noto Sans<br/>• Clean Design<br/>• Latin Scripts<br/>• Modern Sans-serif]
        AR --> Tajawal[Tajawal<br/>• Arabic Optimized<br/>• RTL Support<br/>• Proper Rendering]
    end

    subgraph "📏 Size Adjustments"
        NotoSans --> ENSize[English Sizing<br/>• Base Sizes<br/>• Standard Metrics]
        Tajawal --> ARSize[Arabic Sizing<br/>• +1px Adjustment<br/>• Optimal Readability]
    end

    style EN fill:#e3f2fd
    style AR fill:#f3e5f5
    style NotoSans fill:#e8f5e8
    style Tajawal fill:#fff3e0
```

### Font Family Characteristics

#### Noto Sans (English)
- **Design Philosophy**: Clean, modern sans-serif optimized for digital interfaces
- **Character Support**: Comprehensive Latin character set with excellent readability
- **Usage Context**: All English content, UI labels, and Latin-based text
- **Performance**: Optimized for mobile rendering with clear letterforms

#### Tajawal (Arabic)
- **Design Philosophy**: Arabic-specific font designed for optimal RTL text rendering
- **Character Support**: Complete Arabic character set with proper contextual forms
- **Usage Context**: All Arabic content with automatic RTL layout support
- **Performance**: Optimized for Arabic text flow and reading patterns

### Locale-Aware Typography

**Automatic Font Selection**:
- **Context Awareness**: System detects current app locale automatically
- **Dynamic Switching**: Font family changes instantly when locale changes
- **Consistent Application**: All text elements use appropriate font for current locale
- **Performance Optimization**: Fonts loaded efficiently based on active locale

**Size Optimization**:
- **English Text**: Uses base font sizes as defined in design system
- **Arabic Text**: Applies +1px adjustment for improved readability
- **Consistent Hierarchy**: Maintains typography hierarchy across both languages
- **Accessibility**: Ensures readable text sizes for both language systems

## Component Integration

### Theme Configuration Flow

```mermaid
graph TB
    subgraph "🎨 Theme Setup"
        Core[Core Theme Configuration<br/>• Material 3 Integration<br/>• Color Scheme Generation<br/>• Font Family Assignment]

        subgraph "🧩 Component Themes"
            Buttons[Button Themes<br/>• TextButton<br/>• IconButton<br/>• ElevatedButton]
            Inputs[Input Themes<br/>• TextField<br/>• Decoration<br/>• Focus States]
            Cards[Container Themes<br/>• Card<br/>• Surface<br/>• Elevation]
        end

        subgraph "🎯 Custom Components"
            AppButton[AppButton<br/>• Brand Colors<br/>• Consistent Sizing<br/>• Touch Targets]
            AppChip[AppChip<br/>• Themed Colors<br/>• Standard Padding<br/>• Typography]
            AppCard[AppCard<br/>• Shadow System<br/>• Border Radius<br/>• Surface Colors]
        end
    end

    Core --> Buttons
    Core --> Inputs
    Core --> Cards

    Buttons --> AppButton
    Inputs --> AppButton
    Cards --> AppCard

    AppButton --> AppChip

    style Core fill:#e3f2fd
    style Buttons fill:#f3e5f5
    style Inputs fill:#e8f5e8
    style AppButton fill:#fff3e0
    style AppChip fill:#fce4ec
```

### Material 3 Integration

**Theme Foundation**:
- **Color Scheme Generation**: Creates Material 3 color scheme using brand primary color as seed
- **Surface Configuration**: Establishes white as primary surface color for clean appearance
- **Typography Integration**: Applies locale-aware font families to Material text theme
- **Component Styling**: Configures Material components with brand-specific styling

**Interaction Design**:
- **Touch Feedback**: Custom control over splash, hover, and highlight effects
- **State Management**: Consistent visual states across all interactive elements
- **Accessibility**: Proper touch targets and contrast ratios for all components
- **Performance**: Optimized rendering with minimal visual effects overhead

### Component Theme Specifications

#### Button Theme Configuration
**TextButton Styling**:
- **Tap Target**: Shrink wrap sizing for precise control over button dimensions
- **Padding**: Zero default padding allowing custom spacing control
- **Interaction Effects**: Disabled splash factory for custom touch feedback
- **Color States**: Black foreground color with transparent overlay states

**IconButton Styling**:
- **Brand Colors**: Green accent primary for consistent brand application
- **State Handling**: Proper color resolution for different interaction states
- **Accessibility**: Maintained touch target sizes for usability
- **Integration**: Seamless integration with app's color system

#### Input Theme Configuration
**TextField Styling**:
- **Fill Color**: Light neutral background for input field visibility
- **Border Design**: 8px border radius matching design system consistency
- **State Colors**: Grey borders for enabled, focused, and default states
- **Content Padding**: Optimized padding for text input and icon placement

**Focus Management**:
- **Visual Feedback**: Clear focus indicators for accessibility
- **Color Consistency**: Consistent border colors across all input states
- **Layout Stability**: Stable dimensions preventing layout shifts
- **Icon Integration**: Proper icon color and positioning within inputs

## Custom Components

### AppButton Theme Integration

**AppButton Component Structure:**

The `AppButton` class is a custom button component that integrates seamlessly with the app's theme system. It provides a consistent button interface with extensive customization options while maintaining design system compliance.

**Component Features:**
- **Theme Integration**: Uses AppColors.greenAccentPrimary as default background color
- **Flexible Content**: Supports text, leading icons, and trailing icons
- **Customizable Appearance**: Configurable height (default 48px), width, margins, and padding
- **Border Styling**: Customizable border color and radius (default 8px for design consistency)
- **Layout Control**: Configurable main axis alignment for content positioning

**Design System Compliance:**
- Default 8px border radius matching design specifications
- Standard 48px height for optimal touch targets
- Primary brand color as default background
- Flexible text styling with theme-aware defaults

### AppChip Theme Integration

**AppChip Component Structure:**

The `AppChip` component creates themed chip elements with consistent styling and theme integration. It provides a standardized way to display tags, filters, and other chip-like UI elements.

**Component Design:**
- **Consistent Padding**: Standard 16px horizontal and 7px vertical padding for optimal content spacing
- **Theme Colors**: Uses AppColors.accentExtraLight as default background with AppColors.accentLight border
- **Border Styling**: 8px border radius maintaining design system consistency
- **Typography Integration**: Uses context.textTheme.textXSmall.medium for consistent text styling
- **Customization**: Supports custom background colors, text colors, and margins

**Theme Integration Features:**
- Automatic theme color application with fallback defaults
- Text theme system integration for consistent typography
- Flexible color customization while maintaining design consistency
- Standard border radius matching other UI components

## Dark Mode Support

### Architecture Preparation

```mermaid
graph LR
    subgraph "🌙 Dark Mode Architecture"
        Current[Current Light Theme<br/>• Material 3 Light<br/>• Brand Colors<br/>• Light Surfaces]

        Future[Future Dark Theme<br/>• Material 3 Dark<br/>• Inverted Colors<br/>• Dark Surfaces]

        System[Theme System<br/>• Dynamic Switching<br/>• State Management<br/>• User Preference]
    end

    Current --> System
    Future --> System
    System --> App[Application UI<br/>• Automatic Updates<br/>• Consistent Styling<br/>• Smooth Transitions]

    style Current fill:#e3f2fd
    style Future fill:#263238,color:#fff
    style System fill:#f3e5f5
    style App fill:#e8f5e8
```

**Future Implementation Strategy**:
- **Architecture Ready**: Current theme system designed to support dark mode
- **Color Adaptation**: Brand colors will maintain identity while adapting to dark backgrounds
- **Surface Inversion**: Light surfaces will become dark with appropriate contrast adjustments
- **Accessibility Maintained**: Contrast ratios and readability preserved in dark mode

**Dark Theme Considerations**:
- **Brand Consistency**: Green accent colors adapted for dark backgrounds
- **Surface Hierarchy**: Clear visual hierarchy maintained with dark surface variations
- **Text Contrast**: High contrast text colors for optimal readability
- **Component Adaptation**: All custom components designed to work in both themes

## Theme Usage Patterns

### Widget Integration Patterns

**Theme-Aware Widget Design**:
- **Color Access**: Widgets access colors through AppColors constants for consistency
- **Typography Usage**: Text styling applied through context.textTheme extension methods
- **Modifier Chaining**: Style modifiers combined for flexible text appearance
- **Responsive Design**: Theme elements adapt to different screen sizes and orientations

**Component Styling Approach**:
- **Container Styling**: Background colors applied using theme color constants
- **Text Hierarchy**: Heading and body text styles used appropriately for content structure
- **Color Modifiers**: Text colors applied through semantic modifier methods
- **Layout Consistency**: Consistent spacing and sizing through theme-defined values

### Theme-Aware Component Design

**Themed Container Pattern**:
- **Background Colors**: Uses neutral white from theme color system
- **Border Radius**: Consistent 12px radius matching design system standards
- **Shadow System**: Subtle shadows using theme colors with appropriate opacity
- **Child Integration**: Flexible child widget support with theme inheritance

**Design Principles**:
- **Color Consistency**: All colors sourced from centralized theme system
- **Visual Hierarchy**: Clear elevation and depth through shadow and color
- **Accessibility**: Proper contrast ratios and touch target sizes
- **Maintainability**: Easy updates when theme colors change

## Responsive Design

### Responsive Architecture

```mermaid
graph TB
    subgraph "📱 Device Categories"
        Mobile[Mobile Devices<br/>< 480px<br/>• Phone Portrait<br/>• Compact Layout]
        Tablet[Tablet Devices<br/>480px - 768px<br/>• Phone Landscape<br/>• Small Tablets]
        Desktop[Desktop Devices<br/>> 768px<br/>• Large Tablets<br/>• Desktop Browsers]
    end

    subgraph "🎨 Responsive Elements"
        Typography[Typography Scaling<br/>• Mobile: 1.0x<br/>• Tablet: 1.1x<br/>• Desktop: 1.2x]
        Layout[Layout Adaptation<br/>• Spacing Adjustments<br/>• Component Sizing<br/>• Grid Systems]
        Navigation[Navigation Patterns<br/>• Bottom Nav (Mobile)<br/>• Side Nav (Tablet+)<br/>• Top Nav (Desktop)]
    end

    Mobile --> Typography
    Tablet --> Typography
    Desktop --> Typography

    Mobile --> Layout
    Tablet --> Layout
    Desktop --> Layout

    style Mobile fill:#e3f2fd
    style Tablet fill:#f3e5f5
    style Desktop fill:#e8f5e8
    style Typography fill:#fff3e0
    style Layout fill:#fce4ec
```

### Breakpoint System

**Device Categories**:
- **Mobile (< 480px)**: Primary phone usage in portrait orientation
- **Tablet (480px - 768px)**: Phone landscape and small tablet devices
- **Desktop (> 768px)**: Large tablets and desktop browser environments

**Responsive Features**:
- **Context Extensions**: Easy device type detection through BuildContext extensions
- **Automatic Scaling**: Typography automatically scales based on device category
- **Layout Adaptation**: Components adapt spacing and sizing for optimal viewing
- **Touch Optimization**: Touch targets sized appropriately for each device type

### Typography Scaling Strategy

**Scaling Factors**:
- **Mobile Baseline**: 1.0x scaling for optimal mobile readability
- **Tablet Enhancement**: 1.1x scaling for improved tablet viewing
- **Desktop Optimization**: 1.2x scaling for comfortable desktop reading

**Implementation Benefits**:
- **Automatic Application**: Text scaling applied automatically based on device detection
- **Consistent Hierarchy**: Typography hierarchy maintained across all device sizes
- **Readability Optimization**: Text sizes optimized for viewing distance and device type
- **Performance Efficient**: Minimal overhead for responsive text calculations

## Best Practices

### 1. Color Usage
- Use semantic color names (primary, secondary, success, warning)
- Maintain consistent color relationships
- Test color accessibility and contrast ratios
- Use color constants instead of hardcoded values

### 2. Typography
- Use the text theme extension for all text styling
- Combine base styles with modifiers for flexibility
- Ensure proper font loading for both languages
- Test text rendering in both LTR and RTL layouts

### 3. Component Theming
- Create reusable themed components
- Use theme colors and text styles consistently
- Implement proper theme inheritance
- Test components in different theme modes

### 4. Maintenance
- Keep theme definitions centralized
- Document color and typography decisions
- Update theme when design system changes
- Maintain consistency across the app

## Testing Strategy

### Theme Testing Approach

```mermaid
graph TB
    subgraph "🧪 Testing Levels"
        Unit[Unit Tests<br/>• Color Values<br/>• Typography Specs<br/>• Component Themes]
        Widget[Widget Tests<br/>• Theme Application<br/>• Component Styling<br/>• Responsive Behavior]
        Integration[Integration Tests<br/>• Theme Switching<br/>• Locale Changes<br/>• Cross-Component Consistency]
    end

    subgraph "🎯 Test Categories"
        Colors[Color Testing<br/>• Correct Values<br/>• Accessibility<br/>• Contrast Ratios]
        Typography[Typography Testing<br/>• Font Families<br/>• Size Scaling<br/>• Locale Awareness]
        Components[Component Testing<br/>• Theme Integration<br/>• Style Application<br/>• State Handling]
    end

    Unit --> Colors
    Widget --> Typography
    Integration --> Components

    style Unit fill:#e3f2fd
    style Widget fill:#f3e5f5
    style Integration fill:#e8f5e8
    style Colors fill:#fff3e0
    style Typography fill:#fce4ec
```

### Testing Methodologies

#### Theme Application Testing
**Color Verification**:
- **Value Accuracy**: Verify theme colors match design specifications
- **Component Application**: Ensure components use correct theme colors
- **State Consistency**: Test color application across different component states
- **Accessibility Compliance**: Validate contrast ratios meet accessibility standards

#### Typography Testing
**Font Family Validation**:
- **Locale Detection**: Verify correct font selection based on app locale
- **Family Application**: Ensure proper font family application to text elements
- **Fallback Handling**: Test font fallback behavior for unsupported characters
- **Performance Impact**: Measure font loading and rendering performance

#### Responsive Testing
**Breakpoint Validation**:
- **Device Detection**: Test accurate device category detection
- **Scaling Application**: Verify typography scaling across device types
- **Layout Adaptation**: Ensure components adapt properly to different screen sizes
- **Touch Target Optimization**: Validate touch targets meet platform guidelines

### Test Coverage Areas

**Theme Integration**:
- **Material Theme**: Verify Material 3 theme integration works correctly
- **Custom Components**: Test custom component theme inheritance
- **State Management**: Ensure theme changes propagate throughout app
- **Performance**: Monitor theme application performance impact

**Localization Integration**:
- **Font Switching**: Test automatic font family changes with locale
- **Size Adjustments**: Verify locale-specific size adjustments work
- **RTL Support**: Test right-to-left layout support with Arabic fonts
- **Text Rendering**: Ensure proper text rendering in both languages

## Related Documentation

- [UI Components](../ui-components/shared-widgets.md)
- [Design System](../ui-components/design-system.md)
- [Localization](../ui-components/localization.md)
- [Development Standards](../development/coding-standards.md)
