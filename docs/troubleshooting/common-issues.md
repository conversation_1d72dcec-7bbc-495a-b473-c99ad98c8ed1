# Common Issues and Solutions

## Overview

This document provides solutions to common issues encountered during development, testing, and deployment of the NSP mobile application.

## Development Environment Issues

### Flutter Installation Issues

#### Issue: Flutter command not found

**Symptoms:** Terminal shows `flutter: command not found` when running Flutter commands.

**Solution:**
1. **Verify Installation**: Check if Flutter is properly installed in your system
2. **Update PATH**: Add Flutter to your system PATH environment variable:
   **PATH Environment Configuration:**

   Add Flutter binary directory to your system PATH by editing your shell configuration file (~/.bashrc, ~/.zshrc, or ~/.profile) and including the export statement that appends Flutter's bin directory to the existing PATH variable. Replace "/path/to/flutter/bin" with your actual Flutter installation directory path.
3. **Restart Terminal**: Close and reopen terminal to apply PATH changes
4. **Verify Setup**: Run `flutter doctor` to confirm installation is working

#### Issue: Flutter doctor shows issues
**Error Indicators:**
- Android toolchain warnings about missing or misconfigured Android development tools
- Xcode warnings about missing or improperly configured iOS development environment

**Solution:**
1. **Android Issues:**
   Run `flutter doctor --android-licenses` to accept all required Android SDK licenses automatically

2. **iOS Issues (macOS only):**
   Execute `sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer` to set the correct Xcode developer directory, followed by `sudo xcode-select --install` to install command line tools

### Dependency Issues

#### Issue: Package version conflicts

**Symptoms:** Dependency resolution fails with version conflict messages.

**Solution:**
1. **Clean Dependencies**: Remove cached packages and reinstall:
   **Dependency Cleanup Commands:**

   Execute flutter clean to remove build artifacts and cached files, followed by flutter pub get to reinstall all dependencies from pubspec.yaml. This resolves most dependency conflicts by starting with a clean state.

2. **Update Packages**: Upgrade to latest compatible versions:
   **Package Update Command:**

   Run flutter pub upgrade to update all dependencies to their latest compatible versions within the constraints specified in pubspec.yaml. This often resolves version conflicts by finding compatible package combinations.

3. **Manual Resolution**: Check `pubspec.yaml` for conflicting version constraints and adjust as needed

#### Issue: Build runner conflicts
**Error Message:** SEVERE level error indicating failure to generate build script during code generation process

**Solution:**
1. Clean build runner cache:
   Execute `flutter packages pub run build_runner clean` to remove cached build artifacts

2. Regenerate files:
   Run `flutter packages pub run build_runner build --delete-conflicting-outputs` to force regeneration of all generated files

## Build Issues

### Android Build Issues

#### Issue: Gradle build fails
**Error Message:** FAILURE status with exception details indicating Android build process failure

**Common Solutions:**

1. **Clean Gradle cache:**
   Navigate to android directory, run `./gradlew clean`, return to project root, then execute `flutter clean` followed by `flutter pub get` to refresh dependencies

2. **Update Gradle wrapper:**
   Change to android directory and run `./gradlew wrapper --gradle-version=7.6` to update Gradle wrapper to compatible version

3. **Check Java version:**
   Verify Java installation with `java -version` command and ensure Java 11 or higher is installed

#### Issue: Android SDK not found
**Error Message:** System cannot locate Android SDK installation directory

**Solution:**
1. Set ANDROID_HOME environment variable:
   Configure environment variables by setting ANDROID_HOME to your Android SDK path and updating PATH to include tools and platform-tools directories

2. Or configure in Android Studio:
   - File > Project Structure > SDK Location

#### Issue: Multidex error
**Error Message:** Cannot fit requested classes in a single dex file - indicates app exceeds 65K method limit

**Solution:**
Add multidex configuration to `android/app/build.gradle`:
- Enable multiDexEnabled true in android.defaultConfig section
- Add androidx.multidex:multidex:2.0.1 dependency to dependencies block

### iOS Build Issues

#### Issue: CocoaPods installation fails
**Error Message:** CocoaPods not installed warning during iOS build process

**Solution:**
1. Install CocoaPods:
   Run `sudo gem install cocoapods` to install CocoaPods dependency manager

2. If using Apple Silicon Mac:
   Execute `sudo arch -x86_64 gem install ffi` for compatibility, then run `arch -x86_64 pod install` for installation

#### Issue: Pod install fails
**Error Message:** Error installing pods during iOS dependency installation

**Solution:**
1. Clean and reinstall pods:
   Navigate to ios directory, remove Pods folder and Podfile.lock, run `pod install`, then return to project root

2. Update CocoaPods:
   Execute `sudo gem update cocoapods` to update CocoaPods to latest version

#### Issue: Xcode signing issues
**Error Message:** Code signing error during iOS build process

**Solution:**
1. Open `ios/Runner.xcworkspace` in Xcode
2. Select Runner target
3. Go to Signing & Capabilities
4. Select your development team
5. Ensure bundle identifier is unique

## Runtime Issues

### Authentication Issues

#### Issue: Qiwa WebView not loading
**Error Message:** WebView failed to load indicating authentication page cannot be displayed

**Solution:**
1. Check network connectivity
2. Verify Qiwa URLs are correct in environment config
3. Check if device/emulator has internet access
4. Verify SSL certificates are valid

#### Issue: Token refresh fails
**Error Message:** 401 Unauthorized status after attempting token refresh operation

**Solution:**
1. Check refresh token validity
2. Verify API endpoints are correct
3. Check server-side token refresh implementation
4. Clear app data and re-authenticate

### Navigation Issues

#### Issue: GoRouter navigation fails
**Error Message:** Could not navigate to route indicating routing configuration problem

**Solution:**
1. Verify route is defined in router configuration
2. Check route parameters are passed correctly
3. Ensure route names match enum values
4. Check for navigation context issues

#### Issue: Bottom navigation state lost
**Error Message:** Navigation state not preserved when switching between tabs

**Solution:**
1. Verify `StatefulShellRoute` is configured correctly
2. Check navigator keys are properly set
3. Ensure proper use of `goBranch` method

### State Management Issues

#### Issue: BLoC state not updating UI

**Symptoms:** UI doesn't rebuild when BLoC state changes, causing stale data display.

**Solution:**
1. **Verify BlocBuilder**: Ensure `BlocBuilder` is properly configured:
   **BlocBuilder Configuration:**

   Implement BlocBuilder with proper generic types (ExampleBloc, ExampleState) and builder function that accepts context and state parameters. Use switch expression to handle different state types (ExampleLoading, ExampleLoaded, ExampleError) with pattern matching syntax (:final data, :final message) and return appropriate widgets for each state. Ensure all possible states are handled to prevent runtime errors.

2. **Check Equatable**: Ensure states implement `Equatable` with proper `props`:
   **Equatable State Implementation:**

   Create state classes that extend the base state and implement Equatable for proper equality comparison. Include required data fields (like List<ExampleModel> data) and override the props getter to return a list of all properties that should be compared for equality. This ensures BlocBuilder rebuilds only when state data actually changes, preventing unnecessary UI updates.

3. **Verify State Emission**: Ensure `emit` is called in BLoC event handlers
4. **Check Provider Setup**: Verify BLoC is properly provided in widget tree

#### Issue: Memory leaks in BLoCs
**Error Message:** BLoC not disposed properly indicating memory management issues

**Solution:**
1. Ensure BLoCs are properly closed
2. Use `BlocProvider` for automatic disposal
3. Cancel subscriptions in BLoC `close()` method
4. Check for circular references

## API Issues

### Network Issues

#### Issue: API calls failing
**Error Message:** DioException with Connection timeout indicating network request failure

**Solution:**
1. Check network connectivity
2. Verify API base URL is correct
3. Check firewall/proxy settings
4. Increase timeout values:

   **Configuration Pattern**: Create Dio instance with BaseOptions that specify longer timeout durations for both connection establishment and data reception. This helps resolve issues with slow network connections or server response delays by allowing more time for requests to complete before timing out.

#### Issue: SSL certificate errors
**Error Message:** Certificate verification failed during HTTPS requests

**Solution:**
1. **For development only:**

   **Certificate Bypass Pattern**: Configure the Dio HTTP client adapter to override certificate validation by setting a callback that always returns true for certificate verification. This approach bypasses SSL certificate validation for development environments but should never be used in production due to security risks.

2. **For production:** Ensure valid SSL certificates

### Authentication API Issues

#### Issue: CORS errors in development
**Error Message:** Access to XMLHttpRequest blocked by CORS policy during web development

**Solution:**
1. Configure backend to allow CORS
2. Use proper API endpoints
3. Check request headers

#### Issue: Token format errors
**Error Message:** Invalid token format when processing authentication tokens

**Solution:**
1. Verify token is properly formatted
2. Check Bearer token prefix
3. Ensure token is not expired
4. Validate token encoding

## Testing Issues

### Unit Test Issues

#### Issue: Mock generation fails
**Error Message:** Missing @GenerateNiceMocks annotation during test compilation

**Solution:**
1. Add proper annotations:

   **Mock Generation Pattern**: Use the GenerateNiceMocks annotation with MockSpec to specify which classes should have mock implementations generated. Import the generated mocks file to access the mock classes. This enables automatic generation of test doubles for dependency injection and unit testing.

2. Run code generation:
   Execute `flutter packages pub run build_runner build` to generate mock classes

#### Issue: BLoC tests failing
**Error Message:** BLoC test expectations not met during test execution

**Solution:**
1. Verify mock setup is correct
2. Check event/state sequence
3. Ensure proper async handling
4. Use `blocTest` package correctly

### Widget Test Issues

#### Issue: Widget not found in tests
**Error Message:** Widget not found in widget tree during widget testing

**Solution:**
1. Use `pumpAndSettle()` for async operations
2. Check widget keys and text
3. Verify widget is actually rendered
4. Use `find.byType()` instead of `find.text()`

#### Issue: Golden test failures
**Error Message:** Golden file mismatch indicating visual regression in UI tests

**Solution:**
1. Update golden files:
   Run `flutter test --update-goldens` to regenerate reference images

2. Check for font loading issues
3. Verify device configurations
4. Ensure consistent test environment

## Performance Issues

### App Performance

#### Issue: Slow app startup
**Error Message:** App takes too long to start indicating performance bottleneck

**Solution:**
1. Optimize dependency injection setup
2. Use lazy loading for heavy resources
3. Minimize main thread work
4. Profile app startup with DevTools

#### Issue: Memory usage high
**Error Message:** App consuming too much memory during runtime

**Solution:**
1. Check for memory leaks
2. Optimize image loading and caching
3. Dispose resources properly
4. Use memory profiling tools

### UI Performance

#### Issue: Janky animations
**Error Message:** Frame drops during animations indicating rendering performance issues

**Solution:**
1. Use `RepaintBoundary` for complex widgets
2. Optimize widget rebuilds
3. Use `const` constructors
4. Profile with Flutter Inspector

#### Issue: List scrolling performance
**Error Message:** ListView scrolling is slow during user interaction

**Solution:**
1. Use `ListView.builder` for large lists
2. Implement proper item caching
3. Optimize item widget complexity
4. Use `AutomaticKeepAliveClientMixin` if needed

## Deployment Issues

### App Store Issues

#### Issue: iOS App Store rejection
**Error Message:** App rejected due to metadata issues during App Store review

**Solution:**
1. Check app metadata and descriptions
2. Verify privacy policy links
3. Ensure proper app icons
4. Test on physical devices

#### Issue: Android Play Store rejection
**Error Message:** App rejected due to permissions during Play Store review

**Solution:**
1. Review requested permissions
2. Provide permission usage descriptions
3. Test app without unnecessary permissions
4. Follow Play Store guidelines

### Environment Issues

#### Issue: Environment variables not working
**Error Message:** Environment config not loaded during application startup

**Solution:**
1. Verify `--dart-define` arguments
2. Check environment config class
3. Ensure proper build configuration
4. Test with different environments

## Debugging Tips

### General Debugging

1. **Use Flutter Inspector:**
   - Inspect widget tree
   - Check widget properties
   - Identify performance issues

2. **Enable verbose logging:**

   **Debug Logging Pattern**: Use conditional logging that only executes in debug mode to avoid performance impact in release builds. Wrap debug print statements with kDebugMode checks to ensure logging code is excluded from production builds while providing detailed information during development.

3. **Use breakpoints:**
   - Set breakpoints in IDE
   - Step through code execution
   - Inspect variable values

4. **Check device logs:**
   For Android: Use `adb logcat` to view system and application logs
   For iOS: Execute `xcrun simctl spawn booted log stream --predicate 'process == "Runner"'` to monitor iOS simulator logs

### Network Debugging

1. **Use Alice for HTTP inspection:**
   - Shake device to open Alice
   - Inspect request/response details
   - Check headers and timing

2. **Enable network logging:**

   **Network Debugging Pattern**: Add LogInterceptor to Dio client conditionally in debug mode to capture detailed HTTP request and response information. Configure the interceptor to log request and response bodies for comprehensive debugging while ensuring this overhead is excluded from production builds.

### State Debugging

1. **Use BLoC Observer:**
   **BLoC Observer Implementation**: Create AppBlocObserver class extending BlocObserver with overridden onChange method that calls super.onChange(bloc, change) and prints debug information using '${bloc.runtimeType} $change' format to track state changes across all BLoCs in the application.

2. **Add debug prints in BLoCs:**
   **BLoC Event Debugging**: Implement _handleEvent method with Future<void> return type accepting Event and Emitter<State> parameters. Add debug print statements before processing ('Processing event: $event') and before state emission ('Emitting state: $newState') to track event flow and state transitions in BLoC implementations.

## Getting Help

### Resources

1. **Flutter Documentation:** [flutter.dev](https://flutter.dev)
2. **Stack Overflow:** Search for Flutter-specific issues
3. **GitHub Issues:** Check package repositories for known issues
4. **Flutter Community:** Discord, Reddit, and forums

### Reporting Issues

When reporting issues:

1. **Provide context:**
   - Flutter version (`flutter --version`)
   - Platform (iOS/Android)
   - Device/emulator details

2. **Include error logs:**
   - Full error messages
   - Stack traces
   - Relevant code snippets

3. **Steps to reproduce:**
   - Clear reproduction steps
   - Expected vs actual behavior
   - Screenshots if applicable

## Related Documentation

- [Setup Guide](../development/setup-guide.md)
- [Testing Guide](../development/testing-guide.md)
- [Debugging Guide](debugging.md)
- [Performance Guide](performance.md)
