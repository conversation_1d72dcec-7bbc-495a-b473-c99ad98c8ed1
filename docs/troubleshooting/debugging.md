# Debugging Guide

This document provides comprehensive debugging strategies and tools for the NSP Flutter application. It covers common debugging scenarios, tools, and best practices for identifying and resolving issues.

## Table of Contents

- [Overview](#overview)
- [Debugging Tools](#debugging-tools)
- [Common Debugging Scenarios](#common-debugging-scenarios)
- [State Management Debugging](#state-management-debugging)
- [Network Debugging](#network-debugging)
- [Performance Debugging](#performance-debugging)
- [Platform-Specific Debugging](#platform-specific-debugging)
- [Production Debugging](#production-debugging)
- [Best Practices](#best-practices)

## Overview

Effective debugging is crucial for maintaining code quality and user experience. The NSP app includes various debugging tools and strategies to help developers identify and resolve issues quickly.

### Debugging Philosophy

- **Reproduce First**: Always try to reproduce the issue consistently
- **Isolate the Problem**: Narrow down the scope to specific components
- **Use Appropriate Tools**: Choose the right debugging tool for the problem
- **Document Findings**: Keep track of debugging steps and solutions
- **Test Fixes**: Verify that fixes don't introduce new issues

## Debugging Tools

### 1. Flutter Inspector

The Flutter Inspector provides visual debugging for widget trees and layouts.

#### Usage
**Flutter Inspector Launch Commands:**
- **Debug Mode**: Run `flutter run --debug` to launch app with inspector enabled
- **IDE Access**: Navigate to View > Tool Windows > Flutter Inspector in your IDE

#### Key Features
- **Widget Tree**: Visualize widget hierarchy
- **Properties Panel**: Inspect widget properties
- **Layout Explorer**: Debug layout issues
- **Performance Overlay**: Monitor rendering performance

#### Common Use Cases
**Debug Layout Issues**: Create a DebugContainer class extending StatelessWidget with a build method that returns a Container widget. Add debug properties like BoxDecoration with a red border using Border.all(color: Colors.red) to visualize widget boundaries and layout issues.

### 2. Dart DevTools

Comprehensive debugging suite for Flutter applications.

#### Launch DevTools
**DevTools Launch Options:**
- **Command Line**: Run `flutter pub global activate devtools` followed by `flutter pub global run devtools`
- **IDE Integration**: Access through Run > Flutter DevTools menu in your IDE

#### Key Features
- **Debugger**: Set breakpoints and step through code
- **Memory**: Monitor memory usage and detect leaks
- **Performance**: Analyze app performance
- **Network**: Monitor HTTP requests
- **Logging**: View application logs

### 3. Alice HTTP Inspector

Debug HTTP requests and responses in development.

#### Setup
**Alice HTTP Inspector Setup:**

- **Initialization**: Create Alice instance with `final alice = Alice()`
- **Dio Integration**: Add Alice interceptor to Dio with `dio.interceptors.add(alice.getDioInterceptor())`
- **Inspector Display**: Show HTTP inspector interface with `alice.showInspector()`

#### Features
- **Request/Response Details**: Complete HTTP traffic
- **Headers and Body**: Inspect all request data
- **Response Time**: Monitor API performance
- **Error Details**: Debug failed requests

### 4. BLoC Observer

Monitor BLoC events and state changes.

#### Setup
**BLoC Observer Setup:**

- **Observer Class**: Create AppBlocObserver extending BlocObserver with overridden methods for onCreate, onEvent, onTransition, and onError
- **Lifecycle Logging**: Each method calls super and uses appPrint to log BLoC creation, events, state transitions, and errors with bloc.runtimeType
- **Registration**: Set Bloc.observer = AppBlocObserver() in main() function before runApp(MyApp()) to enable global BLoC monitoring

## Common Debugging Scenarios

### 1. Widget Not Updating

#### Problem
Widget doesn't reflect state changes.

#### Debugging Steps
**Widget Not Updating Debugging Steps:**

- **StatefulWidget Check**: Ensure setState() is called in _updateText() method within State class to trigger widget rebuilds
- **BLoC State Emissions**: Add debug logging in mapEventToState with appPrint('Processing event: $event') and verify yield NewState() is called
- **BlocBuilder Verification**: Add debug logging in BlocBuilder's builder function with appPrint('Building with state: $state') to track rebuilds

### 2. Navigation Issues

#### Problem
Navigation not working as expected.

#### Debugging Steps
**Navigation Issues Debugging Steps:**

- **Route Definition Check**: Define GoRouter with routes containing GoRoute with path '/training/:id', name 'training-details', and builder extracting id from state.pathParameters with debug logging
- **Navigation Call Debugging**: Create navigateToTraining function with try-catch around context.pushNamed, logging attempt and success/failure with appPrint
- **Navigation State Check**: Create NavigationDebugger class with static logCurrentRoute method using GoRouterState.of(context).uri.toString() to log current route location

### 3. API Call Failures

#### Problem
API requests failing or returning unexpected data.

#### Debugging Steps
**API Call Failures Debugging Steps:**

- **Detailed Data Source Logging**: Add try-catch in TrainingsDataSource.getTrainings() with appPrint for request URL, response status, response data, and error details including DioException handling
- **Alice HTTP Debugging**: Use Alice HTTP Inspector (see setup section above) for comprehensive HTTP traffic monitoring
- **Mock API Testing**: Create MockTrainingsDataSource implementing TrainingsDataSource interface with simulated delay and mock data for testing API integration

## State Management Debugging

### BLoC Debugging

#### Debug BLoC Events and States
**BLoC Events and States Debugging:**

- **Event Processing**: Add appPrint('🔄 Processing event: ${event.runtimeType}') at start of mapEventToState method
- **Loading State**: Log '📥 Loading trainings...' before yielding TrainingsLoading state
- **Success Handling**: Log '✅ Loaded ${trainings.length} trainings' and yield TrainingsLoaded with data
- **Error Handling**: Log '❌ Failed to load trainings: $e' in catch block and yield TrainingsError with error message

#### Debug Widget Rebuilds
**Debug Widget Rebuilds Implementation:**

- **DebugBlocBuilder Class**: Create generic StatelessWidget with BlocWidgetBuilder<S> builder and String debugName parameters
- **Build Method**: Override build to return BlocBuilder<B, S> with builder that logs '🔄 $debugName rebuilding with state: ${state.runtimeType}' before calling original builder
- **Usage Pattern**: Use DebugBlocBuilder<TrainingsBloc, TrainingsState> with debugName 'TrainingsList' to track specific widget rebuilds

### State Inspection

#### Custom State Inspector
**Custom State Inspector Implementation:**

- **StateInspector Class**: Create static inspectBlocState<T extends BlocBase> method that logs bloc.runtimeType, bloc.state, and bloc.state.runtimeType
- **Equatable Support**: Check if bloc.state is Equatable and cast to log equatable.props for detailed state property inspection
- **Debug Usage**: Use StateInspector.inspectBlocState(context.read<TrainingsBloc>()) within kDebugMode conditional for development-only state inspection

## Network Debugging

### HTTP Request Debugging

#### Custom Dio Interceptor
**Custom Dio Interceptor Implementation:**

- **DebugInterceptor Class**: Extend Interceptor with overridden onRequest, onResponse, and onError methods for comprehensive HTTP debugging
- **Request Logging**: Log HTTP method, URI, headers, and request body (if present) in onRequest method before calling handler.next(options)
- **Response Logging**: Log response status code, request URI, and response data in onResponse method before calling handler.next(response)
- **Error Logging**: Log error message, request URI, and response details (if available) in onError method before calling handler.next(err)

### Network Connectivity Debugging

**Network Connectivity Debugging Implementation:**

- **ConnectivityDebugger Class**: Create static monitorConnectivity method that listens to Connectivity().onConnectivityChanged stream
- **Connectivity Logging**: Log connectivity changes with appPrint('🌐 Connectivity changed: $result') for all connectivity state changes
- **State-Specific Messages**: Use switch statement on ConnectivityResult to log specific messages for wifi, mobile, none, and unknown connectivity states

## Performance Debugging

### Widget Performance

#### Performance Overlay
**Performance Overlay Setup:**

- **MaterialApp Configuration**: Set showPerformanceOverlay: kDebugMode in MaterialApp constructor to enable performance monitoring only in debug builds
- **Debug-Only Display**: Performance overlay shows frame rendering times and helps identify performance bottlenecks during development

#### Custom Performance Monitor
**Custom Performance Monitor Implementation:**

- **PerformanceMonitor Class**: Create static measureWidgetBuild method that takes widgetName and VoidCallback build parameters
- **Timing Logic**: Use Stopwatch to measure build() execution time and log warning if duration exceeds 16ms (one frame at 60fps)
- **Widget Usage**: Wrap widget build logic in PerformanceMonitor.measureWidgetBuild call with widget name for performance tracking

### Memory Debugging

#### Memory Usage Monitor
**Memory Usage Monitor Implementation:**

- **MemoryMonitor Class**: Create static logMemoryUsage method that checks kDebugMode and logs ProcessInfo.currentRss converted to MB
- **Periodic Monitoring**: Create static monitorMemoryLeaks method using Timer.periodic with 30-second intervals to call logMemoryUsage for continuous memory tracking

## Platform-Specific Debugging

### Android Debugging

#### ADB Commands
**Android Debug Bridge Commands:**
- **View App Logs**: Use `adb logcat | grep flutter` to filter Flutter-specific logs
- **Clear Logs**: Execute `adb logcat -c` to clear existing log buffer
- **Install Debug APK**: Run `adb install app-debug.apk` to install debug builds
- **Device Information**: Use `adb shell getprop ro.build.version.release` to check Android version

#### Android Studio Debugging
**Android Studio Debugging Setup:**

- **Developer Import**: Import 'dart:developer' as developer for Android Studio console integration
- **Debug Function**: Create debugLog function that calls developer.log with message and name: 'NSP_APP' for filtered logging in Android Studio

### iOS Debugging

#### Xcode Console
**iOS Simulator Logging:**
Execute `xcrun simctl spawn booted log stream --predicate 'process == "Runner"'` to view iOS simulator logs filtered for the Flutter app

#### iOS-Specific Debugging
**iOS-Specific Debugging Setup:**

- **Platform Import**: Import 'dart:io' for Platform detection capabilities
- **iOS Debug Function**: Create iosDebugLog function that checks Platform.isIOS and prints messages with '🍎 iOS:' prefix for iOS-specific debugging

## Production Debugging

### Crash Reporting

#### Firebase Crashlytics Integration
**Firebase Crashlytics Integration Setup:**

- **Initialization**: Call WidgetsFlutterBinding.ensureInitialized() and await Firebase.initializeApp() in main function
- **Flutter Error Handling**: Set FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError for automatic Flutter error reporting
- **Platform Error Handling**: Set PlatformDispatcher.instance.onError callback to record platform errors with FirebaseCrashlytics.instance.recordError

#### Custom Error Reporting
**Custom Error Reporting Implementation:**

- **ErrorReporter Class**: Create static reportError method with error, stackTrace, optional context, and additionalData parameters
- **Local Logging**: Use appPrint to log error context and stack trace locally for immediate debugging
- **Crashlytics Integration**: Call FirebaseCrashlytics.instance.recordError with error, stackTrace, context, and information mapped from additionalData entries to DiagnosticsProperty objects

### Remote Logging

#### Structured Logging
**Structured Logging Implementation:**

- **Logger Class**: Create static log method with level, message, optional category, and data parameters for structured logging
- **Log Entry Structure**: Build logEntry map with timestamp, level, message, category, and data fields for consistent log formatting
- **Local and Remote Logging**: Use appPrint('📝 $level: $message') for local logging and _sendToRemoteLogger for production logging in kReleaseMode

## Best Practices

### 1. Conditional Debugging

**Conditional Debugging Best Practices:**

- **✅ Good Practice**: Use `if (kDebugMode)` conditional to wrap debug-only code like appPrint statements
- **✅ Good Practice**: Use assert() blocks with anonymous functions for debug-only checks that are automatically removed in release builds
- **❌ Bad Practice**: Avoid using print() statements without conditionals as they will appear in production builds

### 2. Structured Logging

**Structured Logging Best Practices:**

- **✅ Good Practice**: Use structured format like 'API_CALL: GET /trainings - Status: 200 - Duration: 150ms' for searchable logs
- **✅ Good Practice**: Include context with 'USER_ACTION: Training enrolled - TrainingID: 123 - UserID: 456' format for actionable information
- **❌ Bad Practice**: Avoid vague messages like 'something happened' that provide no useful debugging information

### 3. Error Context

**Error Context Best Practices:**

- **✅ Good Practice**: Use ErrorReporter.reportError with StackTrace.current, descriptive context like 'Loading user trainings', and additionalData map containing userId and timestamp
- **❌ Bad Practice**: Avoid generic error handling with simple print('Error: $e') that provides no context or actionable information

### 4. Performance Monitoring

**Performance Monitoring Best Practices:**

- **CriticalPathMonitor Class**: Create static generic monitor<T> method that takes operation name and Future<T> Function() task parameters
- **Timing Logic**: Use Stopwatch to measure task execution time and log warnings for operations exceeding 1000ms threshold
- **Error Handling**: Log failed operations with execution time before rethrowing exceptions for proper error propagation

## Related Documentation

- [Common Issues](common-issues.md) - Common problems and solutions
- [Performance](performance.md) - Performance optimization guidelines
- [Testing Guide](../development/testing-guide.md) - Testing strategies
- [Build & Deployment](../development/build-deployment.md) - Build troubleshooting

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
