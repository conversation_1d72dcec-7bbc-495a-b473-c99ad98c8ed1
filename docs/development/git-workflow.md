# Git Workflow

This document outlines the Git workflow and best practices for the NSP Flutter application development team. It covers branching strategies, commit conventions, and collaboration guidelines.

## Table of Contents

- [Overview](#overview)
- [Branching Strategy](#branching-strategy)
- [Commit Conventions](#commit-conventions)
- [Pull Request Process](#pull-request-process)
- [Code Review Guidelines](#code-review-guidelines)
- [Release Process](#release-process)
- [Hotfix Process](#hotfix-process)
- [Best Practices](#best-practices)

## Overview

The NSP project follows a Git Flow-inspired workflow with adaptations for mobile app development. This ensures code quality, facilitates collaboration, and maintains a stable main branch for production releases.

### Key Principles

- **Main branch is always deployable**: The main branch should always be in a releasable state
- **Feature isolation**: Each feature is developed in its own branch
- **Code review required**: All changes must be reviewed before merging
- **Conventional commits**: Use standardized commit message format
- **Automated testing**: CI/CD pipeline runs tests on all pull requests

## Branching Strategy

### Branch Types

#### 1. Main Branch (`main`)
- **Purpose**: Production-ready code
- **Protection**: Protected branch, requires PR and reviews
- **Deployment**: Automatically deployed to production
- **Naming**: `main`

#### 2. Development Branch (`develop`)
- **Purpose**: Integration branch for features
- **Protection**: Protected branch, requires PR and reviews
- **Testing**: Deployed to staging environment
- **Naming**: `develop`

#### 3. Feature Branches
- **Purpose**: Individual feature development
- **Source**: Created from `develop`
- **Target**: Merged back to `develop`
- **Naming**: `feature/[ticket-id]-[short-description]`

Examples:
```bash
feature/NSP-123-user-authentication
feature/NSP-456-course-catalog-filters
feature/NSP-789-video-player-improvements
```

#### 4. Release Branches
- **Purpose**: Prepare releases, bug fixes, and final testing
- **Source**: Created from `develop`
- **Target**: Merged to both `main` and `develop`
- **Naming**: `release/[version]`

**Release Branch Examples:**
- release/1.2.0 (Standard release)
- release/2.0.0-beta.1 (Beta release)

#### 5. Hotfix Branches
- **Purpose**: Critical production bug fixes
- **Source**: Created from `main`
- **Target**: Merged to both `main` and `develop`
- **Naming**: `hotfix/[ticket-id]-[short-description]`

**Hotfix Branch Examples:**
- hotfix/NSP-999-login-crash-fix (Critical login issue)
- hotfix/NSP-888-payment-gateway-error (Payment system fix)

### Branch Workflow

```mermaid
graph TD
    A[main] --> B[hotfix/bug-fix]
    A --> C[release/1.2.0]
    D[develop] --> C
    D --> E[feature/new-feature]
    E --> D
    C --> A
    C --> D
    B --> A
    B --> D
```

## Commit Conventions

### Commit Message Format

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

| Type | Description | Example |
|------|-------------|---------|
| `feat` | New feature | `feat(auth): add biometric login support` |
| `fix` | Bug fix | `fix(video): resolve playback crash on Android` |
| `docs` | Documentation | `docs(api): update authentication flow documentation` |
| `style` | Code style changes | `style(catalog): fix linting issues in training cards` |
| `refactor` | Code refactoring | `refactor(bloc): simplify user state management` |
| `test` | Adding/updating tests | `test(auth): add unit tests for login validation` |
| `chore` | Maintenance tasks | `chore(deps): update dependencies to latest versions` |
| `perf` | Performance improvements | `perf(images): optimize image loading and caching` |
| `ci` | CI/CD changes | `ci(github): add automated testing workflow` |
| `build` | Build system changes | `build(android): update gradle configuration` |

### Commit Examples

**Commit Message Examples:**

**Feature Addition:**
`feat(catalog): add advanced filtering options for courses`

**Bug Fix:**
`fix(video): resolve memory leak in video player component`

**Documentation Update:**
`docs(setup): add environment configuration guide`

**Refactoring:**
`refactor(navigation): simplify route management with GoRouter`

**Performance Improvement:**
`perf(images): implement lazy loading for course thumbnails`

**Breaking Change:**
`feat(auth)!: migrate to OAuth 2.0 authentication`

**Breaking Change Body:**
BREAKING CHANGE: The authentication system now requires OAuth 2.0. Users will need to re-authenticate after this update.

### Scope Guidelines

Use scopes to indicate the area of change:

- `auth` - Authentication and authorization
- `catalog` - Course catalog and browsing
- `video` - Video player and streaming
- `profile` - User profile management
- `navigation` - App navigation and routing
- `api` - API communication and data layer
- `ui` - User interface components
- `test` - Testing infrastructure
- `docs` - Documentation
- `build` - Build and deployment

## Pull Request Process

### 1. Creating a Pull Request

**Pull Request Creation Process:**

**Branch Setup:**
- Switch to develop branch with `git checkout develop`
- Update local develop with `git pull origin develop`
- Create feature branch using `git checkout -b feature/NSP-123-new-feature`

**Development Workflow:**
- Stage changes with `git add .`
- Commit with descriptive message: `git commit -m "feat(catalog): add course search functionality"`

# Push branch
git push origin feature/NSP-123-new-feature

# Create PR through GitHub/GitLab interface
```

### 2. PR Title and Description

#### Title Format
```
[Type] Brief description of changes
```

Examples:
```
[Feature] Add advanced course filtering
[Fix] Resolve video playback issues on iOS
[Refactor] Simplify authentication state management
```

#### Description Template
```markdown
## Description
Brief description of what this PR does.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Tested on both Android and iOS

## Screenshots/Videos
[If applicable, add screenshots or videos demonstrating the changes]

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is properly commented
- [ ] Documentation updated if needed
- [ ] No new warnings or errors introduced

## Related Issues
Closes #123
Related to #456
```

### 3. PR Requirements

Before merging, ensure:
- [ ] All CI checks pass
- [ ] At least one code review approval
- [ ] No merge conflicts
- [ ] Branch is up to date with target branch
- [ ] All conversations resolved

## Code Review Guidelines

### For Authors

1. **Self-review first**: Review your own code before requesting review
2. **Small, focused PRs**: Keep changes focused and reasonably sized
3. **Clear descriptions**: Provide context and reasoning for changes
4. **Test thoroughly**: Ensure all tests pass and manual testing is complete
5. **Respond promptly**: Address review comments in a timely manner

### For Reviewers

1. **Be constructive**: Provide helpful, actionable feedback
2. **Focus on important issues**: Prioritize logic, security, and performance
3. **Suggest improvements**: Offer specific suggestions when possible
4. **Approve when ready**: Don't hold up good code for minor style issues
5. **Test if needed**: Pull and test complex changes locally

### Review Checklist

#### Code Quality
- [ ] Code is readable and well-structured
- [ ] Follows project coding standards
- [ ] No obvious bugs or logic errors
- [ ] Error handling is appropriate
- [ ] Performance considerations addressed

#### Testing
- [ ] Adequate test coverage
- [ ] Tests are meaningful and well-written
- [ ] Manual testing scenarios covered
- [ ] Edge cases considered

#### Documentation
- [ ] Code is properly commented
- [ ] Documentation updated if needed
- [ ] API changes documented
- [ ] Breaking changes clearly marked

## Release Process

### 1. Prepare Release

**Release Preparation Process:**
- Switch to develop branch: `git checkout develop`
- Update local develop: `git pull origin develop`
- Create release branch: `git checkout -b release/1.2.0`

# Update version numbers
# - pubspec.yaml
# - Android: android/app/build.gradle
# - iOS: ios/Runner/Info.plist

# Update CHANGELOG.md
# Commit version updates
git add .
git commit -m "chore(release): bump version to 1.2.0"
git push origin release/1.2.0
```

### 2. Release Testing

- Deploy to staging environment
- Perform comprehensive testing
- Fix any critical issues in the release branch
- Update documentation if needed

### 3. Finalize Release

```bash
# Merge to main
git checkout main
git pull origin main
git merge --no-ff release/1.2.0
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin main --tags

# Merge back to develop
git checkout develop
git pull origin develop
git merge --no-ff release/1.2.0
git push origin develop

# Delete release branch
git branch -d release/1.2.0
git push origin --delete release/1.2.0
```

## Hotfix Process

### 1. Create Hotfix

```bash
# Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/NSP-999-critical-bug

# Fix the issue
git add .
git commit -m "fix(auth): resolve login crash on Android 14"

# Update version (patch increment)
git add .
git commit -m "chore(hotfix): bump version to 1.2.1"
git push origin hotfix/NSP-999-critical-bug
```

### 2. Deploy Hotfix

```bash
# Merge to main
git checkout main
git merge --no-ff hotfix/NSP-999-critical-bug
git tag -a v1.2.1 -m "Hotfix version 1.2.1"
git push origin main --tags

# Merge to develop
git checkout develop
git merge --no-ff hotfix/NSP-999-critical-bug
git push origin develop

# Delete hotfix branch
git branch -d hotfix/NSP-999-critical-bug
git push origin --delete hotfix/NSP-999-critical-bug
```

## Best Practices

### 1. Commit Practices

```bash
# ✅ Good: Atomic commits with clear messages
git commit -m "feat(auth): add biometric authentication support"
git commit -m "test(auth): add unit tests for biometric auth"
git commit -m "docs(auth): update authentication documentation"

# ❌ Bad: Large commits with vague messages
git commit -m "various fixes and improvements"
git commit -m "WIP"
git commit -m "fix stuff"
```

### 2. Branch Management

```bash
# ✅ Good: Keep branches up to date
git checkout feature/my-feature
git rebase develop  # or git merge develop

# ✅ Good: Clean up merged branches
git branch -d feature/completed-feature
git push origin --delete feature/completed-feature

# ❌ Bad: Long-lived feature branches without updates
# Feature branch created 2 months ago, never updated
```

### 3. Merge Strategies

```bash
# ✅ Good: Use merge commits for features (preserves history)
git merge --no-ff feature/new-feature

# ✅ Good: Use rebase for small fixes (clean history)
git rebase develop

# ✅ Good: Squash commits when merging via PR
# GitHub/GitLab: "Squash and merge" option
```

### 4. Collaboration

```bash
# ✅ Good: Communicate about shared branches
# Post in team chat: "Working on feature/shared-component"

# ✅ Good: Coordinate on conflicts
# Discuss with team member before resolving merge conflicts

# ✅ Good: Regular updates
git fetch origin  # Check for updates regularly
```

### 5. Security

```bash
# ✅ Good: Never commit sensitive data
echo "*.env" >> .gitignore
echo "android/key.properties" >> .gitignore

# ✅ Good: Use environment variables
# API_KEY=your_key_here (in CI/CD settings)

# ❌ Bad: Committing secrets
git add config/secrets.json  # Contains API keys
```

## Related Documentation

- [Setup Guide](setup-guide.md) - Development environment setup
- [Coding Standards](coding-standards.md) - Code style guidelines
- [Testing Guide](testing-guide.md) - Testing strategies and practices
- [Build & Deployment](build-deployment.md) - Build and deployment processes

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
