# Coding Standards

## Overview

This document outlines the coding standards and best practices for the NSP mobile application. Following these standards ensures code consistency, maintainability, and team collaboration efficiency.

## General Principles

### 1. Code Quality
- Write clean, readable, and self-documenting code
- Follow SOLID principles
- Prefer composition over inheritance
- Keep functions and classes small and focused
- Use meaningful names for variables, functions, and classes

### 2. Consistency
- Follow established patterns throughout the codebase
- Use consistent naming conventions
- Maintain consistent file and folder structure
- Apply consistent formatting and styling

### 3. Performance
- Write efficient code that performs well
- Avoid premature optimization
- Profile and measure performance when needed
- Consider memory usage and disposal

## Dart/Flutter Specific Standards

### 1. File Naming

**File Naming Convention:**
- Use snake_case for all Dart file names (e.g., auth_bloc.dart, training_details_page.dart, my_learnings_repository.dart)
- Use PascalCase for class names (e.g., AuthBloc extends Bloc<AuthEvent, AuthState>, TrainingDetailsPage extends StatefulWidget, MyLearningsRepository)
- File names should clearly reflect the class or functionality they contain

### 2. Variable and Function Naming

**Variable and Function Naming Convention:**
- Use camelCase for variables and functions (e.g., userName, totalCourses, isAuthenticated)
- Function names should be descriptive (e.g., authenticateUser(), getTrainings() returning Future<List<Training>>)
- Use PascalCase for classes and enums (e.g., UserProfile class, AuthenticationStatus enum with values authenticated/unauthenticated)
- Use SCREAMING_SNAKE_CASE for constants (e.g., API_BASE_URL, MAX_RETRY_ATTEMPTS)

### 3. Import Organization

**Import Organization Structure:**
1. **Dart core libraries** - Import standard Dart libraries first (dart:async, dart:convert)
2. **Flutter libraries** - Import Flutter framework packages (flutter/material.dart, flutter/services.dart)
3. **Third-party packages** - Import external dependencies in alphabetical order (bloc, dio, equatable, get_it)
4. **Internal imports** - Import project-specific files in alphabetical order (core/shared/constants.dart, features/auth/domain/entities/user_entity.dart, features/auth/presentation/bloc/auth_bloc.dart)

Each section should be separated by blank lines for clarity.

### 4. Class Structure

**Class Structure Organization:**

Classes should follow this specific order for consistency:

1. **Static constants** - Define class-level constants (e.g., defaultValue)
2. **Static variables** - Class-level variables (e.g., instanceCount)
3. **Instance variables** - Private fields first, then public fields (_privateField, publicField)
4. **Constructor** - Main constructor with required and optional parameters
5. **Named constructors** - Alternative constructors (e.g., ExampleClass.empty())
6. **Getters and setters** - Property accessors
7. **Public methods** - External interface methods
8. **Private methods** - Internal helper methods (prefixed with underscore)
9. **Overridden methods** - Methods from parent classes or interfaces (with @override annotation)

Example class: ExampleClass extends BaseClass with MixinClass implements InterfaceClass

## Architecture Standards

### 1. Clean Architecture Layers

**Clean Architecture Layer Implementation:**

**Domain Layer** - Contains pure business logic with abstract interfaces:
- AuthRepository abstract class defining signIn(AuthModel) and signOut() methods
- No external dependencies, only business rules

**Data Layer** - Handles external concerns and data sources:
- AuthRepositoryImpl class implementing AuthRepository interface
- Uses @injectable annotation for dependency injection
- Depends on AuthDataSource and AuthTokenProvider
- Implements signIn method by calling dataSource.signInWithToken() and updating tokens

**Presentation Layer** - Manages UI state and user interactions:
- AuthBloc extends Bloc<AuthEvent, AuthState> for state management
- Uses @singleton annotation for single instance across app
- Takes AuthRepository as dependency through constructor
- Initializes with AuthInitial state

### 2. BLoC Pattern Standards

**BLoC Pattern Implementation Standards:**

**Events Structure:**
- Abstract AuthEvent class extending Equatable for proper comparison
- Concrete events like Authenticate class with descriptive names
- Include necessary data (e.g., authModel field)
- Implement props getter for Equatable functionality

**States Structure:**
- Abstract AuthState class extending Equatable
- Specific states: AuthLoading (no data), AuthSuccess (with UserEntity), AuthError (with message)
- Each state implements props getter for proper state comparison

**BLoC Implementation:**
- AuthBloc extends Bloc<AuthEvent, AuthState>
- Constructor takes AuthRepository dependency and sets initial state (AuthInitial)
- Register event handlers using on<EventType>(handlerMethod) pattern
- Event handlers follow async pattern: emit loading state, try operation, emit success/error
- Use private methods for event handling (e.g., _onAuthenticate)
- Handle exceptions and emit appropriate error states

### 3. Dependency Injection Standards

**Dependency Injection Standards:**

**Injectable Annotations:**
- Use @injectable for regular dependencies (e.g., AuthDataSource with Dio dependency)
- Use @singleton for state management classes that need single instance (e.g., AuthBloc)
- Use @lazySingleton for expensive resources that should be created only when needed (e.g., AuthTokenProvider)

**RegisterModule Pattern:**
- Create abstract RegisterModule class with @module annotation
- Define factory methods for external dependencies (e.g., Dio instance)
- Use @singleton annotation for shared instances
- Configure external libraries with proper settings (e.g., Dio with BaseOptions and EnvironmentConfigs.baseUrl)

## Widget Standards

### 1. Widget Structure

**StatelessWidget Structure:**

**Constructor Pattern:**
- Use const constructor with super.key parameter
- Define required parameters first, then optional parameters
- Use descriptive parameter names (title, subtitle, onTap)

**Field Declarations:**
- Declare final fields for immutability
- Use nullable types for optional parameters (String?, VoidCallback?)
- Group related fields together

**Build Method:**
- Implement build(BuildContext context) method
- Return appropriate widget structure (e.g., Card with ListTile)
- Handle nullable parameters with conditional rendering (subtitle != null ? Text(subtitle!) : null)
- Use meaningful widget composition

### 2. Stateful Widget Standards

**StatefulWidget Structure:**

**Widget Class:**
- StatefulCustomWidget extends StatefulWidget with const constructor
- Define required and optional parameters (initialValue, onChanged)
- Implement createState() method returning private state class

**State Class:**
- Private state class (_StatefulCustomWidgetState) extends State<WidgetType>
- Declare controllers and other stateful resources (late TextEditingController _controller)

**Lifecycle Methods:**
- **initState()** - Initialize controllers and resources, call super.initState() first
- **dispose()** - Clean up resources like controllers, call super.dispose() last
- **build()** - Return widget tree using controllers and widget properties

**Resource Management:**
- Initialize controllers with widget data (TextEditingController(text: widget.initialValue))
- Access widget properties using widget.propertyName
- Properly dispose of controllers to prevent memory leaks

### 3. Theme Usage

**Theme Usage Standards:**

**Consistent Styling Approach:**
- Use theme extensions for consistent styling across the app
- Access colors through AppColors class (e.g., AppColors.greenAccentPrimary)
- Use context.textTheme for text styling with extensions (e.g., context.textTheme.textMedium.bold)

**Button Styling:**
- Configure ElevatedButton.styleFrom() with backgroundColor and foregroundColor
- Apply text styles using theme extensions rather than hardcoded values
- Maintain consistency with app's design system

## Error Handling Standards

### 1. Exception Handling

**Exception Handling Standards:**

**Custom Exception Types:**
- Create specific exception classes implementing Exception interface
- Include meaningful message field and descriptive toString() method
- Use const constructor for immutable exceptions (e.g., AuthenticationException)

**Exception Handling Pattern:**
- Handle exceptions at appropriate architectural layers
- Use specific catch blocks for known exception types (on DioException catch)
- Provide fallback catch block for unexpected errors
- Transform low-level exceptions into domain-specific exceptions
- Include helper methods for error message transformation (_handleDioError)

### 2. Error Handler Extension

**Error Handler Extension Pattern:**

**Extension Definition:**
- Create ErrorHandler extension on Future<T> for consistent error handling
- Define errorHandler method with onSuccess and onError callbacks
- Handle try-catch logic internally and call appropriate callback

**Usage Pattern:**
- Use in BLoC classes for consistent error handling
- Call repository methods followed by .errorHandler()
- Provide onSuccess callback to emit success states (e.g., TrainingsLoaded)
- Provide onError callback to emit error states (e.g., TrainingsError)
- Transform exceptions into user-friendly error messages using helper methods

## Testing Standards

### 1. Test Organization

**Test Organization Structure:**

**Main Test Structure:**
- Use group() to organize related tests (e.g., 'AuthBloc' group)
- Declare late variables for mocks and test subjects (MockAuthRepository, AuthBloc)

**Setup and Teardown:**
- Use setUp() to initialize mocks and test subjects before each test
- Use tearDown() to clean up resources (e.g., authBloc.close())

**Nested Groups:**
- Create nested groups for specific functionality (e.g., 'Authenticate Event')
- Use descriptive test names that explain expected behavior

**Test Pattern (AAA):**
- **Arrange** - Set up mocks and expectations using when().thenAnswer()
- **Act** - Trigger the action being tested (authBloc.add())
- **Assert** - Verify expected outcomes using expectLater() and emitsInOrder()

**BLoC Testing:**
- Test state transitions by verifying emitted states in order
- Use stream testing with expectLater() for async state changes

### 2. Mock Standards

**Mock Generation and Test Data Standards:**

**Automatic Mock Generation:**
- Use @GenerateNiceMocks annotation for automatic mock creation
- Specify MockSpec for each class to be mocked (AuthRepository, AuthDataSource)
- Import generated mocks from .mocks.dart file

**Test Data Factories:**
- Create TestDataFactory class with static factory methods
- Provide methods for creating valid test objects (createValidAuthModel, createTestUser)
- Use const constructors for immutable test data
- Include realistic test values that represent valid application data

**Factory Method Benefits:**
- Centralized test data creation
- Consistent test data across test files
- Easy to modify test data structure when models change
- Reusable test objects for different test scenarios

## Documentation Standards

### 1. Code Comments

**Code Documentation Standards:**

**Method Documentation:**
- Use triple-slash comments (///) for public API documentation
- Describe method purpose and behavior clearly
- Document parameters, return values, and exceptions
- Use [ClassName] syntax for type references in documentation

**Documentation Structure:**
- Brief description of what the method does
- Parameter descriptions if not obvious from names
- Exception documentation with [ExceptionType] references
- Return value description

**Inline Comments:**
- Use single-line comments (//) for implementation details
- Explain complex logic or business rules
- Comment validation steps and important operations

### 2. Class Documentation

**Class Documentation Standards:**

**Class-Level Documentation:**
- Provide comprehensive description of class purpose and responsibilities
- Explain the class's role in the application architecture
- Document key methods and functionality provided
- Include usage examples when helpful

**Constructor Documentation:**
- Document constructor parameters and their purposes
- Use [ClassName] syntax for type references
- Explain dependency requirements

**Documentation Structure:**
- Brief class description
- Detailed explanation of functionality
- Architecture context (e.g., "abstracts underlying data sources")
- Usage examples showing typical implementation patterns
- Constructor documentation with parameter descriptions

**Example Pattern:**
- Show typical instantiation: `AuthRepository(dataSource: authDataSource)`
- Demonstrate common method calls: `await repository.signIn(authModel)`

## Performance Standards

### 1. Widget Performance

**Widget Performance Standards:**

**Const Constructor Usage:**
- Always use const constructors when widget properties are compile-time constants
- Include super.key parameter for proper widget identification
- Mark fields as final for immutability

**Performance Optimization:**
- Use const constructors to enable widget caching and reduce rebuilds
- Declare required parameters appropriately
- Follow immutable widget pattern for better performance
**Advanced Performance Techniques:**
- Use RepaintBoundary for expensive widgets that don't need frequent repaints
- Wrap CustomPaint widgets with RepaintBoundary to isolate repainting
- Apply RepaintBoundary to complex widgets that have expensive build methods
- Use static text and const widgets where content doesn't change
- Example: ExpensiveWidget with RepaintBoundary wrapping CustomPaint(painter: ComplexPainter())

### 2. Memory Management

**Memory Management Standards:**

**Resource Management Pattern:**
- Declare resources as late variables (StreamSubscription, AnimationController)
- Initialize resources in initState() after calling super.initState()
- Always dispose of resources in dispose() method before calling super.dispose()

**Common Resources to Manage:**
- AnimationController - dispose() to free animation resources
- StreamSubscription - cancel() to stop listening and prevent memory leaks
- TextEditingController - dispose() to free text editing resources
- FocusNode - dispose() to clean up focus management

**Disposal Order:**
- Dispose custom resources first (controllers, subscriptions)
- Call super.dispose() last to ensure proper cleanup chain

## Security Standards

### 1. Sensitive Data Handling

**Sensitive Data Handling Standards:**

**Logging Security:**
- Never log sensitive information like passwords, tokens, or personal data
- Use generic log messages instead (e.g., "Authenticating user..." instead of actual password)
- Implement proper logging levels to control what gets logged in production

**Secure Storage Implementation:**
- Use FlutterSecureStorage for sensitive data storage
- Configure platform-specific security options:
  - Android: encryptedSharedPreferences: true
  - iOS: accessibility: KeychainAccessibility.first_unlock
- Store tokens and credentials using secure storage methods
- Use descriptive keys for stored values (e.g., 'auth_token')

**Security Best Practices:**
- Never store sensitive data in plain text
- Use secure storage APIs provided by the platform
- Implement proper access controls for stored data

### 2. Input Validation

**Input Validation Standards:**

**Validation Pattern:**
- Create InputValidator class with static validation methods
- Return String? for validation results (null = valid, String = error message)
- Check for null and empty values first
- Apply specific validation rules for each input type

**Email Validation:**
- Check for null/empty values
- Use RegExp for email format validation
- Return descriptive error messages for user feedback

**Password Validation:**
- Validate required field constraints
- Implement minimum length requirements (e.g., 8 characters)
- Consider additional complexity requirements as needed

**Validation Best Practices:**
- Use consistent return types across all validators
- Provide clear, user-friendly error messages
- Implement validation rules that match business requirements
- Use regular expressions for format validation where appropriate

## Accessibility Standards

### 1. Semantic Labels

**Semantic Labels for Accessibility:**

**Semantics Widget Usage:**
- Wrap interactive widgets with Semantics widget for screen reader support
- Provide descriptive label property (e.g., 'Login button')
- Include helpful hint property for user guidance (e.g., 'Double tap to login')
- Set appropriate semantic properties (button: true for buttons)

**Accessibility Best Practices:**
- Use clear, descriptive labels that explain widget purpose
- Provide hints for complex interactions
- Set semantic flags that match widget behavior
- Ensure all interactive elements have proper semantic information

### 2. Focus Management

**Focus Management for Accessibility:**

**Focus Node Management:**
- Create FocusNode instances for each input field (_emailFocus, _passwordFocus)
- Assign focusNode property to TextField widgets
- Implement logical focus flow between form fields

**Text Input Actions:**
- Use TextInputAction.next for fields that should advance to next field
- Use TextInputAction.done for final fields in forms
- Implement onSubmitted callback to handle focus transitions

**Focus Flow Implementation:**
- Use requestFocus() method to move focus programmatically
- Create logical tab order that matches visual layout
- Ensure all form fields are accessible via keyboard navigation

**Accessibility Benefits:**
- Enables keyboard navigation for users with disabilities
- Provides logical flow through form elements
- Improves overall user experience for all input methods

## Code Review Guidelines

### 1. Review Checklist

- [ ] Code follows established patterns and conventions
- [ ] All public APIs are documented
- [ ] Tests are included for new functionality
- [ ] Error handling is implemented properly
- [ ] Performance considerations are addressed
- [ ] Security best practices are followed
- [ ] Accessibility requirements are met

### 2. Review Comments

**Review Comment Examples:**

**Performance Suggestions:**
- Recommend const constructors for better performance (e.g., const MyWidget({super.key}))
- Suggest performance optimizations where applicable

**Code Readability:**
- Recommend extracting complex logic into separate methods (_handleComplexLogic)
- Suggest meaningful method and variable names
- Propose code organization improvements

**Best Practices:**
- Provide constructive feedback with specific examples
- Explain the reasoning behind suggestions
- Focus on maintainability and code quality improvements

## Tools and Automation

### 1. Static Analysis

**Static Analysis Configuration:**

**Analysis Options Setup:**
- Include flutter_lints package for standard Flutter linting rules
- Exclude generated files from analysis (*.g.dart, *.freezed.dart)
- Configure custom linter rules for project-specific requirements

**Recommended Linter Rules:**
- prefer_const_constructors - Enforce const constructors for performance
- prefer_const_literals_to_create_immutables - Use const for immutable collections
- avoid_print - Prevent print statements in production code
- prefer_single_quotes - Maintain consistent string quote style

**Configuration Benefits:**
- Ensures consistent code quality across the team
- Catches common issues during development
- Enforces project-specific coding standards automatically

### 2. Code Formatting

**Code Formatting and Analysis Commands:**

**Formatting Commands:**
- `dart format lib --line-length=100 --set-exit-if-changed` - Format library code with 100 character line limit
- `dart format test --line-length=100 --set-exit-if-changed` - Format test code with same line length
- Use --set-exit-if-changed flag to fail CI if formatting is needed

**Analysis Commands:**
- `flutter analyze` - Run static analysis to catch potential issues
- `flutter test` - Execute all unit and widget tests

**Development Workflow:**
- Run formatting before committing code
- Execute analysis to catch issues early
- Run tests to ensure functionality is maintained
- Integrate these commands into CI/CD pipeline for automated quality checks

## Related Documentation

- [Setup Guide](setup-guide.md)
- [Testing Guide](testing-guide.md)
- [Architecture Overview](../architecture/overview.md)
- [State Management Guide](../architecture/state-management.md)
