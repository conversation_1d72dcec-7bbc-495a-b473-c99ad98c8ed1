# Build & Deployment

## Overview

This document covers the build process, deployment strategies, and release management for the NSP mobile application. It includes configuration for different environments, build optimization, and distribution workflows.

## Build Configuration

### Environment Setup

The application supports multiple environments with different configurations:

**Environment Configuration Pattern:**

**Environment Variables Access:**
- Use EnvironmentConfigs class to centralize environment variable access
- Access variables using String.fromEnvironment() with compile-time constants
- Define getters for each environment-specific value (baseUrl, environment, qiwaClientId, qiwaRedirectUri, sentryDsn)

**Configuration Benefits:**
- Centralized environment management
- Type-safe access to environment variables
- Easy to modify environment settings
- Supports multiple deployment environments (dev, staging, production)
- Secure handling of sensitive configuration data

### Build Flavors

#### Android Flavors

**Android Build Flavors Configuration:**

The Android build configuration defines multiple product flavors to support different deployment environments. This setup enables building different app variants with environment-specific configurations and branding.

**Flavor Configuration:**
- **Demo Flavor**: Development environment with ".demo" suffix and "NSP Demo" app name for testing
- **Stage Flavor**: Staging environment with ".stage" suffix and "NSP Stage" app name for QA testing
- **UAT Flavor**: User acceptance testing environment with ".uat" suffix and "NSP UAT" app name
- **Production Flavor**: Live environment with no suffix and "NSP" app name for end users

**Build System Features:**
- **Application ID Suffixes**: Unique package identifiers allowing multiple app versions on same device
- **Version Name Suffixes**: Clear version identification for different environments
- **Resource Values**: Environment-specific app names displayed to users
- **Flavor Dimensions**: Organized flavor management using "environment" dimension for clean configuration

#### iOS Configuration

**iOS Configuration Structure:**

The iOS Info.plist configuration defines app metadata and capabilities for different build environments. This configuration supports environment-specific app naming and deep linking functionality.

**Configuration Elements:**
- **Bundle Display Name**: Environment-specific app name using build configuration variables
- **Bundle Identifier**: Unique app identifier using Xcode's product bundle identifier system
- **URL Schemes**: Deep linking configuration with "nsp" scheme for app-to-app navigation
- **Deep Link Setup**: Enables the app to handle custom URL schemes for external navigation

**iOS Build Features:**
- **Variable Substitution**: Uses Xcode build settings for environment-specific values
- **Deep Linking Support**: Configured URL schemes for seamless app integration
- **Bundle Management**: Proper bundle identifier configuration for App Store distribution

## Build Commands

### Development Builds

**Debug Build for Development:**
Execute `flutter run --flavor demo --dart-define=BASE_URL=https://dev-api.nsp.com --dart-define=ENVIRONMENT=development` for development with debugging capabilities

**Profile Build for Performance Testing:**
Run `flutter run --profile --flavor demo --dart-define=BASE_URL=https://dev-api.nsp.com` to test performance characteristics

**Release Build for Testing:**
Use `flutter run --release --flavor demo --dart-define=BASE_URL=https://dev-api.nsp.com` for release mode testing

### Production Builds

#### Android APK

**Build APK for Specific Flavor:**
Execute `flutter build apk --flavor prod --dart-define=BASE_URL=https://api.nsp.com --dart-define=ENVIRONMENT=production` for production APK

**Build APK with Obfuscation:**
Run `flutter build apk --release --obfuscate --split-debug-info=build/debug-info --flavor prod --dart-define=BASE_URL=https://api.nsp.com` for secure release builds

**Build App Bundle (Play Store Recommended):**
Use `flutter build appbundle --flavor prod --dart-define=BASE_URL=https://api.nsp.com --dart-define=ENVIRONMENT=production` for Play Store distribution

#### iOS IPA

**Build iOS App:**
Execute `flutter build ios --flavor prod --dart-define=BASE_URL=https://api.nsp.com --dart-define=ENVIRONMENT=production` for iOS application build

**Build and Archive for App Store:**
Run `flutter build ipa --flavor prod --dart-define=BASE_URL=https://api.nsp.com --dart-define=ENVIRONMENT=production` for App Store submission

### Build Scripts

#### Build Script for All Environments

**Multi-Environment Build Script (build_all.sh):**

**Script Structure:**
- **Error Handling**: Uses `set -e` to exit on any command failure
- **Demo Build**: Builds APK with demo flavor using dev-api.nsp.com base URL and demo environment variables
- **Stage Build**: Creates stage APK with stage-api.nsp.com and stage-specific configuration
- **UAT Build**: Generates UAT APK with uat-api.nsp.com and UAT environment settings
- **Production Build**: Creates production app bundle with api.nsp.com, obfuscation enabled, and debug info splitting

**Build Features:**
- **Environment-Specific URLs**: Each flavor uses appropriate API base URL
- **Client ID Management**: Uses environment variables for Qiwa client IDs
- **Security**: Production builds include code obfuscation and debug info separation
- **Progress Logging**: Displays build progress for each environment

## Code Signing

### Android Signing

#### Keystore Configuration

**Android Keystore Properties (android/key.properties):**
- **storePassword**: Password for the keystore file
- **keyPassword**: Password for the specific key alias
- **keyAlias**: Alias name for the signing key
- **storeFile**: Path to the keystore file (.jks format)

#### Build Configuration

**Android Signing Configuration:**

The Android signing configuration defines how the app is signed for release builds, ensuring app authenticity and enabling secure distribution through the Google Play Store.

**Signing Configuration:**
- **Release Signing**: Uses keystore properties for production app signing
- **Key Management**: Secure handling of keystore alias, passwords, and file paths
- **Build Types**: Release build configuration with signing and optimization settings
- **ProGuard Integration**: Code obfuscation and optimization for release builds

**Security Features:**
- **Keystore Protection**: Secure storage of signing credentials outside version control
- **Code Minification**: Reduces app size and improves security through code obfuscation
- **Optimization**: ProGuard rules for optimal release build performance
- **Conditional Configuration**: Safe handling of missing keystore files during development

### iOS Signing

#### Automatic Signing (Recommended)

**iOS Automatic Signing Configuration (ios/Runner.xcodeproj/project.pbxproj):**
- **CODE_SIGN_STYLE**: Set to "Automatic" for Xcode-managed signing
- **DEVELOPMENT_TEAM**: Your Apple Developer Team ID

#### Manual Signing

**iOS Manual Signing Configuration:**
- **CODE_SIGN_STYLE**: Set to "Manual" for custom provisioning profiles
- **PROVISIONING_PROFILE_SPECIFIER**: Name of your specific provisioning profile

## Build Optimization

### 1. Code Obfuscation

**Code Obfuscation Commands:**
- **Android APK**: Use `flutter build apk --release --obfuscate --split-debug-info=build/debug-info` for obfuscated Android builds
- **iOS IPA**: Use `flutter build ipa --release --obfuscate --split-debug-info=build/debug-info` for obfuscated iOS builds

### 2. Tree Shaking

**Tree Shaking Optimization:**

**Code Optimization Techniques:**
- Use const constructors wherever possible (const MyWidget({super.key})) to enable compile-time optimizations
- Import specific classes instead of entire libraries using show keyword
- Example: `import 'package:flutter/material.dart' show Widget, StatelessWidget;`

**Tree Shaking Benefits:**
- Removes unused code from final build
- Reduces app bundle size
- Improves app startup performance
- Eliminates dead code automatically during compilation

### 3. Asset Optimization

**Asset Configuration in pubspec.yaml:**
- **Asset Directories**: Include assets/images/ and assets/translations/ directories
- **Multi-Resolution Assets**: Provide logo.png in standard, 2.0x, and 3.0x resolutions for different screen densities
- **Optimization Benefits**: Reduces app size by including only necessary assets and appropriate resolutions

### 4. Build Size Analysis

**Build Size Analysis Commands:**
- **APK Size Analysis**: Run `flutter build apk --analyze-size` to analyze Android app size
- **iOS Size Analysis**: Execute `flutter build ios --analyze-size` to analyze iOS app size
- **Detailed Analysis**: Use `flutter build apk --analyze-size --target-platform android-arm64` for platform-specific size reports

## Continuous Integration/Continuous Deployment

### GitHub Actions Workflow

**CI/CD Workflow Configuration (.github/workflows/build-and-deploy.yml):**

**Workflow Structure:**
- **Name**: "Build and Deploy" workflow for automated CI/CD
- **Triggers**: Runs on push to main/develop branches and pull requests to main
- **Jobs**: Three main jobs - test, build-android, and build-ios

**Test Job (ubuntu-latest):**
- **Setup**: Uses actions/checkout@v3 and subosito/flutter-action@v2 with Flutter 3.x
- **Dependencies**: Runs flutter pub get to install dependencies
- **Code Generation**: Executes flutter packages pub run build_runner build
- **Testing**: Runs flutter test --coverage for comprehensive testing
- **Coverage**: Uploads coverage reports using codecov/codecov-action@v3

**Android Build Job (ubuntu-latest):**
- **Dependencies**: Requires test job completion, runs only on main branch
- **Environment**: Sets up Java 11 with Zulu distribution
- **Build Process**: Creates production APK with environment-specific secrets
- **Artifacts**: Uploads APK to GitHub Actions artifacts

**iOS Build Job (macos-latest):**
- **Dependencies**: Requires test job completion, runs only on main branch
- **Build Process**: Creates iOS build with production configuration and no code signing
- **Platform**: Uses macOS runner for iOS-specific build requirements

### Fastlane Configuration

#### Android Fastlane

**Android Fastlane Configuration (android/fastlane/Fastfile):**

**Platform Setup:**
- **Default Platform**: Set to android for Android-specific automation
- **Internal Testing Lane**: Builds production bundle and uploads to Play Store internal track
- **Production Lane**: Builds production bundle and uploads to Play Store production track

**Lane Configuration:**
- **Gradle Task**: Uses "bundle" task with "prod" flavor and "Release" build type
- **Upload Target**: Uploads AAB file from build/app/outputs/bundle/prodRelease/ directory
- **Track Management**: Supports both internal testing and production release tracks

#### iOS Fastlane

**iOS Fastlane Configuration (ios/fastlane/Fastfile):**

**Platform Setup:**
- **Default Platform**: Set to ios for iOS-specific automation
- **Beta Lane**: Builds app and uploads to TestFlight for beta testing
- **Release Lane**: Builds app and uploads to App Store for production release

**Lane Configuration:**
- **Build Settings**: Uses "Runner" scheme with "Release" configuration and "app-store" export method
- **TestFlight Upload**: Skips waiting for build processing to speed up automation
- **App Store Upload**: Configured for manual review submission and release control

## Release Management

### Version Management

#### Semantic Versioning

**Version Format in pubspec.yaml:**
- **Semantic Version**: 1.2.3 format where 1=major, 2=minor, 3=patch
- **Build Number**: +45 represents the build number for app stores
- **Usage**: version: 1.2.3+45 combines semantic versioning with build tracking

#### Version Bump Script

**Version Bump Script (bump_version.sh):**

**Script Functionality:**
- **Input Parameter**: Accepts version type (major, minor, patch) as command line argument
- **Current Version Extraction**: Uses grep and sed to extract current version and build number from pubspec.yaml
- **Version Calculation**: Implements case statement to increment appropriate version component based on semantic versioning
- **Build Number Increment**: Automatically increments build number for each version update
- **File Update**: Uses sed to update pubspec.yaml with new version and build number
- **Feedback**: Displays version change confirmation message

**Version Logic:**
- **Major**: Increments first number, resets minor and patch to 0
- **Minor**: Increments second number, resets patch to 0
- **Patch**: Increments third number only

### Release Checklist

#### Pre-Release
- [ ] All tests passing
- [ ] Code review completed
- [ ] Version number updated
- [ ] Changelog updated
- [ ] Build configurations verified
- [ ] Environment variables set
- [ ] Code signing certificates valid

#### Release Process
- [ ] Create release branch
- [ ] Build and test all flavors
- [ ] Upload to distribution platforms
- [ ] Verify app functionality
- [ ] Update release notes
- [ ] Tag release in Git
- [ ] Merge to main branch

#### Post-Release
- [ ] Monitor crash reports
- [ ] Check app store reviews
- [ ] Monitor performance metrics
- [ ] Update documentation
- [ ] Plan next release

## Distribution

### Internal Distribution

#### Firebase App Distribution

**Firebase CLI Setup and Distribution:**
- **Install Firebase CLI**: Run `npm install -g firebase-tools` to install Firebase command line tools
- **Deploy APK**: Use `firebase appdistribution:distribute` command with APK path, app ID, target groups, and release notes
- **Configuration**: Specify app ID (1:123456789:android:abcdef format), target groups ("internal-testers"), and descriptive release notes

### Store Distribution

#### Google Play Store

1. **Upload to Play Console**
   - Build App Bundle (AAB)
   - Upload to appropriate track (internal/alpha/beta/production)
   - Fill in release notes
   - Submit for review

2. **Release Tracks**
   - **Internal Testing**: Quick testing with internal team
   - **Alpha**: Closed testing with limited users
   - **Beta**: Open testing with broader audience
   - **Production**: Live release to all users

#### Apple App Store

1. **Upload to App Store Connect**
   - Build IPA file
   - Upload via Xcode or Transporter
   - Fill in app metadata
   - Submit for review

2. **TestFlight**
   - Internal testing with team members
   - External testing with beta users
   - Automatic distribution to testers

## Monitoring and Analytics

### Crash Reporting

**Crash Reporting Setup:**

**Firebase Crashlytics Integration:**
- Initialize crash reporting in main() function after WidgetsFlutterBinding.ensureInitialized()
- Enable crashlytics collection only for non-debug builds using kDebugMode check
- Configure FlutterError.onError to capture Flutter-specific errors
- Set up PlatformDispatcher.instance.onError for platform-level error handling

**Error Handling Configuration:**
- Use FirebaseCrashlytics.instance.recordFlutterFatalError() for Flutter errors
- Use FirebaseCrashlytics.instance.recordError() for general errors with stack traces
- Mark errors as fatal: true for critical issues

**Implementation Benefits:**
- Automatic crash detection and reporting
- Comprehensive error tracking across Flutter and platform layers
- Production-only error reporting to avoid debug noise
- Real-time crash notifications for quick issue resolution

### Performance Monitoring

**Performance Monitoring Implementation:**

**Analytics Tracking Class:**
- Create PerformanceMonitor class with static methods for consistent tracking
- Implement trackScreenView() method for screen navigation analytics
- Implement trackUserAction() method for custom event tracking

**Firebase Analytics Integration:**
- Use FirebaseAnalytics.instance.logScreenView() for screen tracking with screenName and screenClass parameters
- Use FirebaseAnalytics.instance.logEvent() for custom events with name and parameters
- Track user interactions and app performance metrics

**Monitoring Benefits:**
- Real-time performance insights
- User behavior analytics
- Screen navigation tracking
- Custom event monitoring for business metrics
- Data-driven app improvement decisions

## Troubleshooting Build Issues

### Common Build Problems

1. **Gradle Build Failures**
   **Clean and Rebuild Process**: Navigate to android directory, run `./gradlew clean`, return to project root, then execute `flutter clean` and `flutter pub get`

2. **iOS Build Failures**
   **Clean iOS Build Process**: Navigate to ios directory, remove Pods and Podfile.lock, run `pod install`, return to project root, then execute `flutter clean` and `flutter pub get`

3. **Code Generation Issues**
   **Regenerate Code Process**: Run `flutter packages pub run build_runner clean` followed by `flutter packages pub run build_runner build --delete-conflicting-outputs`

4. **Signing Issues**
   - Verify certificates are valid
   - Check provisioning profiles
   - Ensure bundle IDs match

## Related Documentation

- [Setup Guide](setup-guide.md)
- [Testing Guide](testing-guide.md)
- [Environment Configuration](../core/environment-config.md)
- [Troubleshooting](../troubleshooting/common-issues.md)
