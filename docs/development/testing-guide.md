# Testing Guide

## Overview

The NSP mobile application follows a comprehensive testing strategy that includes unit tests, widget tests, integration tests, and golden tests. This guide covers testing patterns, best practices, and implementation details.

## Testing Strategy

### Testing Pyramid

**Testing Hierarchy Structure:**
- **Top Level**: E2E Tests (End-to-end testing for complete user flows)
- **Second Level**: Integration Tests (Testing component interactions)
- **Third Level**: Widget Tests and BLoC Tests (UI component and state management testing)
- **Base Level**: Unit Tests and Repository Tests (Individual function and data layer testing)

### Test Types

1. **Unit Tests**: Test individual functions, classes, and business logic
2. **Widget Tests**: Test UI components and their interactions
3. **BLoC Tests**: Test state management logic
4. **Integration Tests**: Test complete user flows
5. **Golden Tests**: Test UI visual consistency

## Test Structure

### Project Test Organization

**Test Directory Structure:**
- **test/core/** - Core functionality tests (shared utilities and theme)
- **test/features/** - Feature-specific tests organized by feature (auth, catalog, etc.)
  - **auth/auth_bloc/** - Authentication BLoC tests
  - **auth/login_page/** - Login page widget tests
  - **auth/shared/** - Shared authentication test utilities
- **test/golden/** - Visual regression tests (widgets and pages)
- **test/integration/** - End-to-end integration tests
- **test/mocks/** - Mock implementations for testing
- **test/test_helpers/** - Common test utilities and helpers

## Unit Testing

### Testing Utilities and Extensions

Shared utilities provide consistent test setup and reduce boilerplate code across all test files.

**Test Helper Implementation:**

The TestHelpers class provides two essential static methods for test setup:

- **createTestApp()**: Creates a standard MaterialApp wrapper with optional home widget parameter, configured with the application's light theme for consistent testing environment
- **createTestAppWithRouter()**: Creates a MaterialApp.router wrapper that accepts a GoRouter configuration, enabling navigation testing while maintaining theme consistency

This utility class centralizes test app creation, ensuring all widget tests use the same theme and configuration as the production application.

**Test Helper Benefits:**
- **Consistent Setup**: Standardized app configuration across all tests
- **Theme Integration**: Ensures tests use the same theme as production
- **Router Support**: Simplified setup for navigation testing
- **Reduced Boilerplate**: Eliminates repetitive test setup code

### Repository Testing

Repository tests verify data layer logic and ensure proper interaction with external dependencies.

**Repository Test Structure:**

The AuthRepository test demonstrates comprehensive repository testing patterns:

- **Mock Generation**: Uses @GenerateNiceMocks annotation to create mock objects for AuthDataSource and AuthTokenProvider dependencies
- **Test Setup**: Establishes test group with proper setUp() method that initializes mock dependencies and creates repository instance with injected mocks
- **Test Implementation**: Follows Arrange-Act-Assert pattern where:
  - Arrange phase creates test data (AuthModel with token and verifier) and configures mock behavior using when().thenAnswer()
  - Act phase calls the repository method under test (signIn)
  - Assert phase verifies both the data source interaction and token provider calls using verify() assertions

This pattern ensures the repository correctly orchestrates calls to its dependencies and handles the authentication flow properly.

**Repository Testing Principles:**
- **Mock Dependencies**: Use Mockito to mock external dependencies
- **Verify Interactions**: Ensure repository calls dependencies correctly
- **Test Error Handling**: Verify proper exception handling and propagation
- **Arrange-Act-Assert**: Follow clear test structure for readability

## BLoC Testing

### BLoC Test Setup

**BLoC Test Implementation:**

The AuthBloc test showcases comprehensive BLoC testing using the bloc_test package:

- **Dependencies Setup**: Imports bloc_test, flutter_test, and mockito packages, with @GenerateNiceMocks creating mocks for AuthRepository and UserBloc
- **Test Configuration**: Establishes test group with setUp() method initializing all required mock dependencies (AuthRepository, UserBloc, AuthTokenProvider)
- **Error Case Testing**: Uses blocTest() to verify error handling when authentication token is null:
  - build() creates AuthBloc instance with injected mocks
  - act() triggers Authenticate event with invalid auth model
  - expect() verifies state sequence: AuthLoading followed by AuthError with specific message
  - verify() ensures UserBloc is never called during error scenarios
- **Success Case Testing**: Tests successful authentication flow:
  - Triggers Authenticate event with valid auth model
  - Expects AuthLoading followed by AuthSuccess states
  - Verifies UserBloc receives resetUserData event upon successful authentication

This pattern ensures BLoC state transitions and side effects are properly tested.

### Testing BLoC State Transitions

**BLoC State Transition Testing:**

**Success Case Testing:**
- Use blocTest to test TrainingsBloc state transitions
- Mock repository methods with when().thenAnswer() for successful responses
- Test that LoadTrainings event triggers TrainingsLoading followed by TrainingsLoaded states
- Verify repository method calls using verify() to ensure proper interaction

**Error Case Testing:**
- Mock repository to throw exceptions using when().thenThrow()
- Test that failures result in TrainingsLoading followed by TrainingsError states
- Verify error messages are properly propagated to the error state

**BLoC Testing Pattern:**
- build: Create BLoC instance with mocked dependencies
- act: Trigger the event being tested (e.g., LoadTrainings)
- expect: Define expected state sequence
- verify: Confirm repository interactions occurred as expected

**Testing Benefits:**
- Ensures proper state management flow
- Validates error handling behavior
- Confirms repository integration works correctly

## Widget Testing

### Basic Widget Tests

**Basic Widget Testing Structure:**

**Test Organization:**
- Group related tests using group() for AppButton widget
- Use testWidgets() for widget-specific tests
- Follow AAA pattern (Arrange, Act, Assert) in each test

**Text Display Testing:**
- Test that button displays correct text using find.text()
- Wrap widget in MaterialApp and Scaffold for proper context
- Use expect() with findsOneWidget matcher

**Interaction Testing:**
- Test button tap functionality using tester.tap()
- Use boolean flags to verify callback execution
- Test user interactions and state changes

**Styling Testing:**
- Test custom styling properties (backgroundColor, height)
- Use tester.widget() to access widget properties
- Verify decoration and styling attributes
- Test that custom properties are properly applied

**Widget Testing Best Practices:**
- Always wrap widgets in MaterialApp for proper theming
- Use descriptive test names that explain expected behavior
- Test both positive and edge cases
- Verify widget properties and interactions thoroughly

### Testing with BLoC

**Testing Widgets with BLoC:**

**BLoC State Testing:**
- Create MockAuthBloc instances for testing different states
- Use when().thenReturn() to mock specific BLoC states
- Provide mocked BLoC using BlocProvider.value()

**Loading State Testing:**
- Mock AuthBloc to return AuthLoading state
- Verify that CircularProgressIndicator is displayed
- Test loading UI behavior and indicators

**Error State Testing:**
- Mock AuthBloc to return AuthError state with specific error message
- Verify that error message is displayed correctly
- Test error handling and user feedback

**Widget-BLoC Integration Testing:**
- Wrap widgets with BlocProvider.value() for dependency injection
- Test how widgets respond to different BLoC states
- Verify UI updates based on state changes
- Ensure proper separation between UI and business logic

**Testing Benefits:**
- Validates state-driven UI behavior
- Ensures proper error handling display
- Confirms BLoC integration works correctly

## Golden Tests

### Setting Up Golden Tests

**Golden Test Setup for Widgets:**

**Golden Test Structure:**
- Use golden_toolkit package for visual regression testing
- Create DeviceBuilder to test across different device configurations
- Use testGoldens() for golden file comparison tests

**Multi-Scenario Testing:**
- Add multiple scenarios using addScenario() for different widget states
- Test primary, secondary, and disabled button variants
- Use descriptive names for each scenario (primary_button, secondary_button, disabled_button)

**Device Configuration:**
- Override devices for all scenarios using overrideDevicesForAllScenarios()
- Test on specific device types (Device.phone) for consistency
- Ensure consistent rendering across different screen sizes

**Golden File Management:**
- Use screenMatchesGolden() to compare with stored golden files
- Name golden files descriptively (app_button_variants)
- Update golden files when UI changes are intentional

**Testing Benefits:**
- Catches unintended visual changes
- Ensures consistent UI across different states
- Provides visual documentation of component variations

### Page Golden Tests

**Page-Level Golden Testing:**

**Page Golden Test Setup:**
- Test complete pages using pumpWidgetBuilder() with proper wrappers
- Load app fonts using loadAppFonts() for consistent text rendering
- Use materialAppWrapper() with theme and localization configuration

**Theme and Localization:**
- Configure AppTheme().light() for consistent theming
- Include LocaleKeys.delegate for proper localization testing
- Ensure fonts and themes match production environment

**State-Based Testing:**
- Test different page states (normal, error) using mocked BLoCs
- Mock specific states (AuthError) to test error UI rendering
- Use BlocProvider.value() to inject mocked state

**Golden File Organization:**
- Use descriptive names for golden files (login_page, login_page_error)
- Separate golden files for different page states
- Maintain clear naming convention for easy identification

**Testing Benefits:**
- Validates complete page layouts and styling
- Ensures consistent theming across pages
- Catches layout regressions in complex UI components

## Integration Testing

### Setting Up Integration Tests

**Integration Testing Setup:**

**Integration Test Configuration:**
- Use IntegrationTestWidgetsFlutterBinding.ensureInitialized() for proper setup
- Import main.dart as app for testing the complete application
- Group related integration tests for better organization

**Authentication Flow Testing:**
- Start app using app.main() and wait for settling with pumpAndSettle()
- Test complete user journey from get started to login
- Verify navigation between pages using find.text() and find.byType()
- Note: WebView authentication may require mocking or test environment

**Course Browsing Flow Testing:**
- Test navigation through catalog and course details
- Verify page transitions and widget presence
- Test user interactions like tapping on course cards
- Validate enrollment flow and success feedback

**Integration Testing Patterns:**
- Use pumpAndSettle() to wait for animations and async operations
- Test complete user workflows rather than isolated components
- Verify navigation and state changes across multiple screens
- Include realistic user interaction scenarios

**Testing Considerations:**
- May require mocking external services (WebView, APIs)
- Test with realistic data and user scenarios
- Ensure tests are stable and repeatable

## Test Data and Mocks

### Creating Test Data

**Test Data Factory Pattern:**

**Centralized Test Data:**
- Create TestData class with static factory methods for consistent test data
- Provide both valid and invalid data scenarios (validAuthModel, invalidAuthModel)
- Include realistic test values that represent actual application data

**Model Factory Methods:**
- Create factory methods for each domain model (AuthModel, TrainingModel, TrainingProviderModel, SectorModel)
- Use const constructors where possible for performance
- Include related objects (provider, sectors) for complete data structures

**Test Data Benefits:**
- Ensures consistency across all tests
- Easy to modify test data when models change
- Provides both positive and negative test cases
- Reduces duplication of test data setup

**Data Structure Examples:**
- AuthModel with token, codeVerifier, and codeChallenge fields
- TrainingModel with id, title, description, durationHours, provider, and sectors
- Related models like TrainingProviderModel and SectorModel

**Usage Pattern:**
- Import TestData class in test files
- Use static methods to get pre-configured test objects
- Modify specific fields when testing edge cases

### Mock Generators

**Mock Generation Configuration:**

**Automatic Mock Generation:**
- Use @GenerateNiceMocks annotation for automatic mock creation
- Specify MockSpec for each class that needs mocking
- Import generated mocks from .mocks.dart file

**Mock Categories:**
- **Repositories**: AuthRepository, TrainingsRepository for data layer testing
- **Data Sources**: AuthDataSource for API interaction testing
- **Providers**: AuthTokenProvider for token management testing
- **BLoCs**: UserBloc for state management testing
- **External Dependencies**: Dio for HTTP client testing, GoRouter for navigation testing

**Mock Benefits:**
- Automatic generation reduces boilerplate code
- Type-safe mocks with proper method signatures
- Easy to maintain when interfaces change
- Consistent mocking approach across the project

## Test Coverage

### Generating Coverage Reports

**Coverage Report Generation Commands:**

**Basic Coverage Generation:**
- `flutter test --coverage` - Run all tests and generate coverage data
- `remove_from_coverage -f coverage/lcov.info -r '\.g\.dart$'` - Exclude generated files from coverage
- `genhtml coverage/lcov.info -o coverage/html` - Generate HTML coverage report
- `open coverage/html/index.html` - Open coverage report in browser

**Coverage Workflow:**
- Run tests with coverage flag to collect data
- Filter out generated files to focus on actual code
- Generate visual HTML report for easy analysis
- Review coverage metrics and identify untested code

### Coverage Configuration

**Coverage Configuration:**

**Exclusion Patterns:**
- Exclude generated files (*.g.dart, *.freezed.dart) from coverage analysis
- Exclude generated directories (/generated/, /l10n/) that contain auto-generated code
- Exclude test directories (/test/) to focus on production code coverage
- Use glob patterns for flexible file matching

**Configuration Benefits:**
- Focuses coverage metrics on actual application code
- Prevents generated code from skewing coverage percentages
- Provides more accurate representation of test coverage

## Testing Best Practices

### 1. Test Organization

**Test Organization Best Practices:**

**Hierarchical Test Structure:**
- Use nested group() calls to organize tests by feature and functionality
- Group tests by BLoC class (AuthBloc) at the top level
- Create sub-groups for specific events (Authenticate Event, Logout Event)
- Use descriptive group names that clearly indicate what's being tested

**Test Naming Convention:**
- Use descriptive test names that explain expected behavior
- Include conditions and expected outcomes in test descriptions
- Follow pattern: "should [expected behavior] when [condition]"

**Organization Benefits:**
- Easy to locate specific tests when debugging
- Clear test structure for new team members
- Better test reporting and failure identification
- Logical grouping of related functionality

### 2. Descriptive Test Names

**Descriptive Test Naming Examples:**

**Good Test Names:**
- Use specific, descriptive names that explain the expected behavior
- Include conditions and expected outcomes
- Example: "should emit AuthSuccess when valid credentials are provided"

**Bad Test Names:**
- Avoid vague or generic test names
- Don't use abbreviations or unclear descriptions
- Example: "auth test" (too vague and uninformative)

**Naming Best Practices:**
- Start with "should" to indicate expected behavior
- Include the condition being tested ("when valid credentials are provided")
- Specify the expected outcome ("emit AuthSuccess")
- Make test names readable as documentation

### 3. AAA Pattern (Arrange, Act, Assert)

**AAA Pattern Implementation:**

**Arrange Section:**
- Set up test data and mock expectations
- Create expected results (expectedUser with id and name)
- Configure mock behavior using when().thenAnswer()

**Act Section:**
- Execute the method or function being tested
- Call the actual implementation (repository.getUser())
- Capture the result for assertion

**Assert Section:**
- Verify the expected outcome using expect() assertions
- Check that the result matches expectations (equals(expectedUser))
- Verify mock interactions using verify().called() to ensure proper method calls

**AAA Benefits:**
- Clear separation of test phases
- Easy to understand test structure
- Consistent testing approach across the codebase
- Makes tests more maintainable and readable

### 4. Mock Verification

**Mock Verification Best Practices:**

**Parameter Verification:**
- Verify that methods are called with correct parameters
- Use verify() to check specific method calls with expected arguments
- Test that TrainingFilters are passed correctly to repository methods

**Interaction Verification:**
- Use verify().called(1) to ensure methods are called exactly once
- Use verifyNoMoreInteractions() to ensure no unexpected method calls
- Verify the complete interaction pattern with mocked dependencies

**Verification Benefits:**
- Ensures proper integration between layers
- Validates that parameters are passed correctly
- Catches issues with method call patterns
- Provides confidence in component interactions

### 5. Error Testing

**Error Testing Patterns:**

**Exception Setup:**
- Use when().thenThrow() to simulate error conditions
- Create realistic exceptions (DioException with proper configuration)
- Test specific error types (connectionTimeout, network errors)

**Error Assertion:**
- Use expect() with throwsA() matcher to verify exception throwing
- Use isA<ExceptionType>() to check for specific exception types
- Combine Act and Assert sections for exception testing

**Error Testing Benefits:**
- Ensures proper error handling throughout the application
- Validates that exceptions are propagated correctly
- Tests resilience against network and API failures
- Confirms error handling doesn't break application flow

**Error Scenarios to Test:**
- Network timeouts and connection failures
- API server errors and invalid responses
- Authentication and authorization failures
- Data parsing and validation errors

## Continuous Integration

### GitHub Actions Configuration

**CI Configuration File**: .github/workflows/test.yml
**Workflow Setup**: Defines a "Test" workflow that runs on push and pull_request events using ubuntu-latest runner.
**Steps Configuration**:
- Checkout code using actions/checkout@v3
- Setup Flutter environment with subosito/flutter-action@v2 using Flutter version 3.x
- Install dependencies with `flutter pub get`
- Run code generation with `flutter packages pub run build_runner build`
- Execute tests with coverage using `flutter test --coverage`
- Upload coverage results to Codecov using codecov/codecov-action@v3 with coverage/lcov.info file

## Related Documentation

- [Setup Guide](setup-guide.md)
- [Coding Standards](coding-standards.md)
- [BLoC State Management](../architecture/state-management.md)
- [API Testing](../api/api-architecture.md)
