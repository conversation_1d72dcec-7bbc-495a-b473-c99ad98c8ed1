# Development Setup Guide

## Prerequisites

Before setting up the NSP mobile application, ensure you have the following tools installed:

### Required Software

1. **Flutter SDK** (Latest stable version)
   - Download from [flutter.dev](https://flutter.dev/docs/get-started/install)
   - Add Flutter to your PATH
   - Run `flutter doctor` to verify installation

2. **Dart SDK** (Included with Flutter)
   - Verify with `dart --version`

3. **Git**
   - Download from [git-scm.com](https://git-scm.com/)
   - Configure with your credentials

4. **IDE/Editor**
   - **Android Studio** (Recommended) with Flutter plugin
   - **VS Code** with Flutter and Dart extensions
   - **IntelliJ IDEA** with Flutter plugin

### Platform-Specific Requirements

#### Android Development
1. **Android Studio** with Android SDK
2. **Android SDK** (API level 21 or higher)
3. **Android Emulator** or physical device
4. **Java Development Kit (JDK)** 8 or higher

#### iOS Development (macOS only)
1. **Xcode** (latest version)
2. **iOS Simulator** or physical device
3. **CocoaPods** (`sudo gem install cocoapods`)

## Project Setup

### 1. Clone the Repository

**Repository Setup**: Use `git clone <repository-url>` to clone the repository, then navigate to the project directory with `cd nsp-mobile-app`.

### 2. Install Dependencies

**Flutter Dependencies**: Run `flutter pub get` to install all Flutter dependencies listed in pubspec.yaml.
**iOS Dependencies**: On macOS, navigate to the ios directory and run `pod install` to install CocoaPods dependencies, then return to the project root.

### 3. Environment Configuration

Create environment configuration files for different environments:

#### Development Environment
**File**: Create `.env.dev` file
**Configuration**: Set BASE_URL to https://dev-api.nsp.com, QIWA_CLIENT_ID to dev_client_id, QIWA_REDIRECT_URI to nsp://dev/callback, and ENVIRONMENT to development.

#### Staging Environment
**File**: Create `.env.stage` file
**Configuration**: Set BASE_URL to https://stage-api.nsp.com, QIWA_CLIENT_ID to stage_client_id, QIWA_REDIRECT_URI to nsp://stage/callback, and ENVIRONMENT to staging.

#### Production Environment
**File**: Create `.env.prod` file
**Configuration**: Set BASE_URL to https://api.nsp.com, QIWA_CLIENT_ID to prod_client_id, QIWA_REDIRECT_URI to nsp://callback, and ENVIRONMENT to production.

### 4. Code Generation

Generate required code files:

**Dependency Injection**: Run `flutter packages pub run build_runner build` to generate dependency injection code.
**Localization**: Use `flutter pub run easy_localization:generate --source-dir assets/translations` and `flutter pub run easy_localization:generate -f keys -o locale_keys.g.dart --source-dir assets/translations` to generate localization files.
**Clean Regeneration**: Use `flutter packages pub run build_runner clean` followed by `flutter packages pub run build_runner build --delete-conflicting-outputs` to clean and regenerate files when needed.

## IDE Configuration

### Android Studio Setup

1. **Install Flutter Plugin**:
   - Go to `File > Settings > Plugins`
   - Search for "Flutter" and install
   - Restart Android Studio

2. **Configure Dart Analysis**:
   - Go to `File > Settings > Languages & Frameworks > Dart`
   - Enable "Enable Dart support"
   - Set Dart SDK path

3. **Code Style Settings**:
   - Go to `File > Settings > Editor > Code Style > Dart`
   - Set line length to 100
   - Enable "Format code on save"
   - Enable "Organize imports on save"

4. **Useful Plugins**:
   - Flutter Inspector
   - Dart
   - BLoC (for BLoC code generation)
   - Rainbow Brackets
   - GitToolBox

### VS Code Setup

1. **Install Extensions**:
   - Flutter
   - Dart
   - Bloc
   - GitLens
   - Error Lens

2. **Configure Settings** - Create `.vscode/settings.json` for optimal development experience:

**VS Code Settings Configuration:**

Create a `.vscode/settings.json` file with the following development optimizations:
- **Line Length Enforcement**: Sets Dart line length to 100 characters for consistent code formatting across the team
- **Automatic Formatting**: Enables format-on-save functionality to maintain code style without manual intervention
- **Import Organization**: Automatically organizes and removes unused imports when saving files, keeping the codebase clean

This configuration ensures consistent development experience and code quality across all team members.

**Key Settings:**
- **Line Length**: Enforces 100-character limit for consistency
- **Format on Save**: Automatically formats code when saving files
- **Organize Imports**: Automatically sorts and removes unused imports

3. **Launch Configuration** - Create `.vscode/launch.json` for easy debugging:

**VS Code Launch Configuration:**

Create a `.vscode/launch.json` file to enable easy debugging with environment-specific settings:
- **Configuration Name**: "Flutter (Development)" for clear identification in the debug dropdown
- **Dart Type**: Specifies this as a Dart/Flutter debugging configuration
- **Environment Arguments**: Includes dart-define parameters for BASE_URL pointing to development API and demo flavor selection
- **One-Click Debugging**: Allows developers to start debugging with proper environment variables without manual command-line setup

This configuration streamlines the development workflow by providing pre-configured launch options for different environments.

**Configuration Benefits:**
- **Environment-specific**: Different configs for dev, staging, production
- **Easy Debugging**: One-click launch with proper environment variables
- **Consistent Setup**: Standardized development environment across team

## Running the Application

### Development Mode

**Development Configuration**: Run the app with development settings using `flutter run --dart-define=BASE_URL=https://dev-api.nsp.com --dart-define=ENVIRONMENT=development --flavor=demo`.
**Device Targeting**: Specify a particular device using `flutter run -d <device-id>` with appropriate dart-define parameters and flavor.
**Hot Reload**: Enable hot reload (default behavior) with `flutter run --hot` for rapid development iterations.

### Different Flavors

The app supports multiple flavors for different environments:

**Demo Flavor**: Use `flutter run --flavor=demo --dart-define=ENVIRONMENT=demo` for demo environment.
**Stage Flavor**: Use `flutter run --flavor=stage --dart-define=ENVIRONMENT=stage` for staging environment.
**UAT Flavor**: Use `flutter run --flavor=uat --dart-define=ENVIRONMENT=uat` for user acceptance testing.
**Production Flavor**: Use `flutter run --flavor=prod --dart-define=ENVIRONMENT=production` for production environment.

### Debug vs Release Mode

**Debug Mode**: Run `flutter run` (default mode) for development with debugging capabilities.
**Profile Mode**: Use `flutter run --profile` for performance testing without debug overhead.
**Release Mode**: Execute `flutter run --release` for optimized production-like builds.

## Development Tools

### 1. Flutter Inspector

Access Flutter Inspector for widget debugging:
- In Android Studio: `View > Tool Windows > Flutter Inspector`
- In VS Code: `Ctrl+Shift+P > Flutter: Open Widget Inspector`

### 2. Flutter DevTools

Launch DevTools for advanced debugging:

**DevTools Activation**: Use `flutter pub global activate devtools` to install DevTools globally, then run `flutter pub global run devtools` to launch.
**IDE Integration**: Alternatively, run `flutter run` and press 'v' in the terminal to launch DevTools with IDE integration.

### 3. Alice HTTP Inspector

Alice is integrated for HTTP traffic inspection in debug mode:
- Shake the device or use the notification to open Alice
- View all HTTP requests and responses
- Inspect headers, body, and timing

### 4. Hot Reload and Hot Restart

**Hot Reload**: Press 'r' to hot reload (preserves application state).
**Hot Restart**: Press 'R' to hot restart (resets application state).
**Quit**: Press 'q' to quit the running application.

## Testing Setup

### Running Tests

**All Tests**: Execute `flutter test` to run all unit and widget tests.
**Coverage Testing**: Use `flutter test --coverage` to run tests and generate coverage reports.
**Specific Tests**: Run individual test files with `flutter test test/features/auth/auth_bloc_test.dart`.
**Watch Mode**: Enable continuous testing with `flutter test --watch` for automatic re-runs on file changes.

### Golden Tests

**Update Golden Files**: Use `flutter test --update-goldens` to regenerate golden test reference images.
**Run Golden Tests**: Execute `flutter test test/golden/` to run visual regression tests.

### Integration Tests

**Integration Testing**: Run end-to-end tests with `flutter drive --target=test_driver/app.dart`.

## Code Quality Tools

### 1. Static Analysis

**Dart Analyzer**: Run `flutter analyze` to perform static code analysis and identify potential issues.
**Custom Analysis**: Use `flutter analyze --options analysis_options.yaml` to run analysis with custom configuration.

### 2. Code Formatting

**Format All Files**: Use `dart format lib --line-length=100 --set-exit-if-changed` for library code and `dart format test --line-length=100 --set-exit-if-changed` for test code.
**Format Specific File**: Format individual files with `dart format lib/main.dart`.

### 3. Import Organization

**VS Code**: Use Ctrl+Shift+P and select "Dart: Organize Imports" to organize import statements.
**Auto-Organization**: Enable auto-organize on save feature in IDE settings for automatic import management.

## Git Hooks Setup

The project uses LeftHook for Git hooks management:

### 1. Install LeftHook

**macOS Installation**: Use `brew install lefthook` to install via Homebrew.
**Alternative Installation**: Download LeftHook from GitHub releases for other platforms.

### 2. Install Hooks

**Hook Installation**: Run `lefthook install` to set up Git hooks in the project.

### 3. Hook Configuration

The `.lefthook.yml` file configures:
- Pre-commit: Code formatting and analysis
- Pre-push: Running tests
- Commit message validation

## Troubleshooting

### Common Issues

#### 1. Flutter Doctor Issues

**Installation Check**: Run `flutter doctor` to verify Flutter installation and identify missing components.
**Android Licenses**: Use `flutter doctor --android-licenses` to accept Android SDK licenses.
**iOS Setup**: On macOS, run `sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer` to fix Xcode path issues.

#### 2. Dependency Issues

**Clean Dependencies**: Use `flutter clean` followed by `flutter pub get` to clean and reinstall dependencies.
**Cache Repair**: Run `flutter pub cache repair` to fix corrupted pub cache.

#### 3. Build Issues

**Clean Build**: Execute `flutter clean` to remove build artifacts.
**iOS Pods**: On macOS, navigate to ios directory, remove Pods and Podfile.lock, then run `pod install`.
**Android Build**: Navigate to android directory and run `./gradlew clean` to reset Android build.

#### 4. Code Generation Issues

**Clean Generated Files**: Use `flutter packages pub run build_runner clean` to remove generated files.
**Force Regeneration**: Run `flutter packages pub run build_runner build --delete-conflicting-outputs` to force regeneration.

### Platform-Specific Issues

#### Android

**SDK Check**: Run `flutter doctor` to verify Android SDK installation.
**SDK Update**: Open Android Studio, navigate to SDK Manager, and update Android SDK components.
**Gradle Issues**: Navigate to android directory and run `./gradlew clean` to fix Gradle-related problems.

#### iOS (macOS only)

**CocoaPods Update**: Use `sudo gem install cocoapods` to update CocoaPods to the latest version.
**Pod Reinstall**: Navigate to ios directory, remove Pods and Podfile.lock, then run `pod install` to reinstall dependencies.
**Xcode Issues**: Run `sudo xcode-select --install` to install or update Xcode command line tools.

## Performance Optimization

### Development Mode Optimizations

1. **Enable Hot Reload**: Faster development iterations
2. **Use Flutter Inspector**: Debug widget trees efficiently
3. **Profile Mode**: Test performance without debug overhead

### Build Optimizations

**Optimized Build**: Use `flutter build apk --release --obfuscate --split-debug-info=build/debug-info` for production builds with code obfuscation and debug info separation.
**Architecture-Specific**: Build for specific architectures using `flutter build apk --target-platform android-arm64` to reduce APK size.

## Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `BASE_URL` | API base URL | `https://api.nsp.com` |
| `ENVIRONMENT` | Environment name | `development` |
| `QIWA_CLIENT_ID` | Qiwa OAuth client ID | `client_123` |
| `QIWA_REDIRECT_URI` | OAuth redirect URI | `nsp://callback` |

### Optional Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `LOG_LEVEL` | Logging level | `info` |
| `CACHE_DURATION` | Cache duration in minutes | `5` |

## Next Steps

After completing the setup:

1. **Read the Architecture Documentation**: Understand the app structure
2. **Review Coding Standards**: Follow the established patterns
3. **Run Tests**: Ensure everything works correctly
4. **Start Development**: Begin implementing features

## Related Documentation

- [Coding Standards](coding-standards.md)
- [Testing Guide](testing-guide.md)
- [Build & Deployment](build-deployment.md)
- [Architecture Overview](../architecture/overview.md)
