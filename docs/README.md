# National Skills Platform (NSP) - Documentation Hub

Welcome to the comprehensive documentation for the National Skills Platform Flutter mobile application. This documentation provides detailed information about the app's architecture, features, development practices, and deployment procedures.

## 📱 About NSP Mobile App

The National Skills Platform mobile application is a Flutter-based learning management system that enables users to:

### Core Capabilities
- **Course Discovery**: Browse and search training courses and learning tracks
- **Learning Management**: Enroll, track progress, and complete courses
- **Assessment System**: Take qualification tests and interactive quizzes
- **Progress Tracking**: Monitor learning journey and achievements
- **Profile Management**: Manage personal information and preferences
- **Offline Support**: Access downloaded content without internet connection

### Target Users
- **Trainees**: Individuals seeking skill development and certification
- **Organizations**: Companies managing employee training programs
- **Instructors**: Content creators and training providers

## 🏗️ Architecture Overview

The app follows **Clean Architecture** principles with modern Flutter best practices:

### Core Technologies
- **Framework**: Flutter 3.x with Dart
- **State Management**: BLoC pattern with flutter_bloc
- **Dependency Injection**: GetIt + Injectable for service location
- **Navigation**: GoRouter for declarative routing
- **Networking**: Dio HTTP client with custom interceptors
- **Local Storage**: Hive for caching and Flutter Secure Storage for sensitive data

### Architectural Patterns
- **Clean Architecture**: Separation of concerns across presentation, domain, and data layers
- **Feature-Driven Development**: Modular structure organized by business features
- **Repository Pattern**: Abstraction layer for data access
- **SOLID Principles**: Maintainable and testable code structure

## 📚 Documentation Structure

### 🏛️ Architecture Documentation
- [**Architecture Overview**](architecture/overview.md) - High-level system design and principles
- [**Clean Architecture**](architecture/clean-architecture.md) - Layer implementation and patterns
- [**State Management**](architecture/state-management.md) - BLoC patterns and best practices
- [**Dependency Injection**](architecture/dependency-injection.md) - GetIt and Injectable setup
- [**Data Flow**](architecture/data-flow.md) - Request-response cycles and state propagation

### 🎯 Feature Documentation
- [**Feature Overview**](features/feature-overview.md) - All features and responsibilities
- [**Authentication**](features/auth.md) - Qiwa integration and auth flow
- [**Catalog**](features/catalog.md) - Course browsing and filtering
- [**Course Details**](features/course-details.md) - Course information display
- [**Home**](features/home.md) - Dashboard functionality
- [**My Learnings**](features/my-learnings.md) - User's enrolled courses
- [**Profile**](features/profile.md) - User profile management
- [**Qualification Test**](features/qualification-test.md) - Testing system
- [**Quiz**](features/quiz.md) - Quiz functionality
- [**Search**](features/search.md) - Search implementation
- [**Training Consumption**](features/training-consumption.md) - Course taking experience

### 🔧 Core Documentation
- [**Shared Utilities**](core/shared-utilities.md) - Core utilities and helpers
- [**Theme System**](core/theme-system.md) - Theming, colors, and typography
- [**Constants**](core/constants.md) - Application constants and configurations
- [**Error Handling**](core/error-handling.md) - Error management strategies
- [**Environment Config**](core/environment-config.md) - Environment setup

### 🧭 Navigation Documentation
- [**Routing System**](navigation/routing-system.md) - GoRouter implementation
- [**Navigation Patterns**](navigation/navigation-patterns.md) - Best practices
- [**Deep Linking**](navigation/deep-linking.md) - Deep linking configuration

### 🌐 API Documentation
- [**API Architecture**](api/api-architecture.md) - API communication setup
- [**Interceptors**](api/interceptors.md) - Request/response interceptors
- [**Data Sources**](api/data-sources.md) - Data source implementations
- [**Repositories**](api/repositories.md) - Repository pattern usage
- [**Models**](api/models.md) - Data models and serialization

### 🎨 UI Components Documentation
- [**Design System**](ui-components/design-system.md) - Design system overview
- [**Shared Widgets**](ui-components/shared-widgets.md) - Reusable UI components
- [**Theming**](ui-components/theming.md) - Theme implementation
- [**Localization**](ui-components/localization.md) - Multi-language support

### 👨‍💻 Development Documentation
- [**Setup Guide**](development/setup-guide.md) - Development environment setup
- [**Coding Standards**](development/coding-standards.md) - Code style and conventions
- [**Testing Guide**](development/testing-guide.md) - Testing strategies
- [**Build & Deployment**](development/build-deployment.md) - Build process
- [**Git Workflow**](development/git-workflow.md) - Git workflow and conventions

### 📱 Native Integration Documentation
- [**Plugins**](native-integration/plugins.md) - Flutter plugins usage

### 🔧 Troubleshooting Documentation
- [**Common Issues**](troubleshooting/common-issues.md) - Common problems and solutions
- [**Debugging**](troubleshooting/debugging.md) - Debugging strategies

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (latest stable version)
- Dart SDK (included with Flutter)
- Android Studio or VS Code with Flutter extensions
- Git for version control

### Setup Steps
1. **Clone the repository**
   Use `git clone <repository-url>` to clone the repository, then navigate to the project directory with `cd nsp-mobile-app`.

2. **Install dependencies**
   Run `flutter pub get` to install all required Flutter dependencies.

3. **Generate code**
   Execute `flutter packages pub run build_runner build` to generate necessary code files.

4. **Run the app**
   Launch the application using `flutter run --flavor demo --dart-define=BASE_URL=https://dev-api.nsp.com --dart-define=ENVIRONMENT=development`.

### Next Steps
1. **Setup**: Follow the detailed [Setup Guide](development/setup-guide.md)
2. **Architecture**: Read the [Architecture Overview](architecture/overview.md)
3. **Features**: Explore [Feature Overview](features/feature-overview.md)
4. **Development**: Check [Coding Standards](development/coding-standards.md)

## 📋 Documentation Maintenance

### Guidelines for Contributors

1. **Keep documentation up-to-date** with code changes
2. **Use clear, concise language** suitable for developers
3. **Include code examples** from the actual codebase
4. **Add cross-references** to related documentation
5. **Update this index** when adding new documentation

### Documentation Standards

- Use consistent markdown formatting
- Include table of contents for longer documents
- Add code examples with syntax highlighting
- Use Mermaid diagrams for complex flows
- Include "Related Documentation" sections

### Review Process

- Documentation changes should be reviewed alongside code changes
- Ensure examples are tested and working
- Verify all internal links are functional
- Check for consistency with existing documentation

## 🤝 Contributing

When contributing to the NSP mobile app:

1. Read the relevant feature documentation
2. Follow the coding standards
3. Write tests for new functionality
4. Update documentation for any changes
5. Follow the git workflow guidelines

## 📞 Support

For questions about this documentation or the NSP mobile app:

1. Check the [Troubleshooting](troubleshooting/common-issues.md) section
2. Review the [Debugging Guide](troubleshooting/debugging.md)
3. Consult the relevant feature documentation
4. Contact the development team

---

**Last Updated**: [Current Date]
**Version**: 1.0.0
**Maintained by**: NSP Development Team
