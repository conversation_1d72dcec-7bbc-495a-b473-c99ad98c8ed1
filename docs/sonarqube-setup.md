# SonarQube Setup for Flutter Projects

This document explains how to properly configure SonarQube analysis for Flutter projects to avoid dependency resolution issues.

## Prerequisites

1. **Flutter SDK** - Ensure Flutter is installed and in your PATH
2. **SonarQube Server** - Running SonarQube instance
3. **SonarScanner** - SonarQube scanner tool

## Installation

### Install SonarScanner

**macOS:**
```bash
brew install sonar-scanner
```

**Linux:**
```bash
# Download from https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/
# Extract and add to PATH
```

**Windows:**
```bash
# Download from https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/
# Extract and add to PATH
```

## Configuration

### 1. Project Configuration

The `sonar-project.properties` file has been configured with proper Flutter/Dart settings:

- Correct source and test directories
- Proper exclusions for generated files
- Dart-specific analyzer configuration
- Coverage report paths

### 2. Environment Variables

Set these environment variables for your SonarQube instance:

```bash
export SONAR_HOST_URL="http://your-sonarqube-server:9000"
export SONAR_TOKEN="your-sonar-token"
export SONAR_PROJECT_KEY="your-project-key"
```

## Running Analysis

### Local Analysis

1. **Prepare the project:**
   ```bash
   ./scripts/prepare-sonar-analysis.sh
   ```

2. **Run analysis:**
   ```bash
   ./scripts/run-sonar-analysis.sh
   ```

### CI/CD Analysis

Use the provided GitHub Actions workflow (`.github/workflows/sonar-analysis.yml`) or adapt it for your CI/CD system.

## Troubleshooting

### Common Issues

1. **"Target of URI doesn't exist" errors:**
   - Ensure `flutter pub get` has been run
   - Check that all dependencies are properly installed
   - Verify generated files exist (run build_runner if needed)

2. **"Undefined class/method" errors:**
   - Make sure Flutter SDK is in PATH during analysis
   - Verify all imports are correct
   - Check that analysis_options.yaml is properly configured

3. **Coverage issues:**
   - Run `flutter test --coverage` before analysis
   - Ensure coverage/lcov.info file exists
   - Check coverage exclusions in sonar-project.properties

### Verification Steps

Before running SonarQube analysis, verify:

1. ✅ Flutter analyze passes: `flutter analyze`
2. ✅ Dependencies are installed: `flutter pub get`
3. ✅ Generated files exist: `flutter pub run build_runner build`
4. ✅ Tests run successfully: `flutter test`

## Best Practices

1. **Always run preparation script** before SonarQube analysis
2. **Include coverage** by running tests with `--coverage` flag
3. **Exclude generated files** from analysis (already configured)
4. **Use consistent Flutter version** across environments
5. **Cache dependencies** in CI/CD for faster builds

## Integration with Different CI/CD Systems

### GitLab CI
```yaml
sonar-analysis:
  stage: analysis
  image: cirrusci/flutter:stable
  before_script:
    - ./scripts/prepare-sonar-analysis.sh
  script:
    - ./scripts/run-sonar-analysis.sh
```

### Jenkins
```groovy
pipeline {
    agent any
    stages {
        stage('Prepare') {
            steps {
                sh './scripts/prepare-sonar-analysis.sh'
            }
        }
        stage('SonarQube Analysis') {
            steps {
                sh './scripts/run-sonar-analysis.sh'
            }
        }
    }
}
```

## Support

If you encounter issues:

1. Check Flutter and SonarScanner versions
2. Verify environment variables are set correctly
3. Ensure SonarQube server is accessible
4. Review SonarQube logs for detailed error messages
