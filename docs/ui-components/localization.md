# Localization

This document covers the localization implementation in the NSP Flutter application, including multi-language support, translation management, and RTL (Right-to-Left) layout handling.

## Table of Contents

- [Overview](#overview)
- [Localization Setup](#localization-setup)
- [Translation Management](#translation-management)
- [Usage Patterns](#usage-patterns)
- [RTL Support](#rtl-support)
- [Code Generation](#code-generation)
- [Best Practices](#best-practices)
- [Maintenance](#maintenance)

## Overview

The NSP app supports multiple languages with full localization using the `easy_localization` package. The app currently supports Arabic (AR) and English (EN) with proper RTL layout support for Arabic.

### Key Features

- **Multi-language Support**: Arabic and English
- **RTL Layout**: Proper right-to-left layout for Arabic
- **Type-safe Translations**: Generated locale keys for compile-time safety
- **Dynamic Language Switching**: Runtime language changes
- **Fallback Support**: English as fallback language
- **Asset-based Translations**: JSON-based translation files

## Localization Setup

### Package Configuration

The app uses `easy_localization` for internationalization:

```yaml
# pubspec.yaml
dependencies:
  easy_localization: ^3.0.3

dev_dependencies:
  easy_localization_loader: ^2.0.0
```

### Main App Configuration

**Main App Localization Setup:**

The main application entry point configures EasyLocalization with comprehensive multi-language support. This setup ensures the app can seamlessly switch between English and Arabic languages.

**Configuration Components:**
- **Initialization**: EasyLocalization is initialized before the app starts to ensure translations are ready
- **Supported Locales**: Defines English (en) and Arabic (ar) as supported languages using Constants for consistency
- **Translation Path**: Points to 'assets/translations' directory containing JSON translation files
- **Fallback Locale**: Uses English as the default fallback when translations are missing
- **Asset Loader**: Uses CodegenLoader for efficient, compile-time embedded translations

**MaterialApp Integration:**
- **Localization Delegates**: Integrates EasyLocalization delegates with MaterialApp
- **Supported Locales**: Passes supported locales from EasyLocalization context
- **Current Locale**: Uses context.locale for automatic locale detection and switching
- **Automatic RTL**: MaterialApp automatically handles RTL layout for Arabic content

### Supported Locales

**Locale Constants Implementation:**

The Constants class defines static locale codes with localeEN set to 'en' for English and localeAR set to 'ar' for Arabic language support.

**Supported Locales Configuration**: supportedLocales list contains Locale objects constructed from Constants.localeEN and Constants.localeAR for EasyLocalization configuration.

**Type Safety**: Using static constants prevents typos and provides compile-time safety for locale references throughout the application.

## Translation Management

### Translation Files Structure

```
assets/translations/
├── en.json    # English translations
└── ar.json    # Arabic translations
```

### English Translation File (en.json)

**English Translation File Structure:**

The English translation file (en.json) contains all English text content organized in a hierarchical JSON structure. This file serves as the primary translation source and fallback for the application.

**Content Organization:**
- **Header Section**: Platform branding, authentication, and navigation text
- **Home Section**: Welcome messages, featured content labels, and main dashboard text
- **Catalog Section**: Course browsing interface, filtering options, and search-related text
- **Language Section**: Language selection labels for both English and Arabic contexts
- **Error Handling**: Generic error messages with parameter placeholders for dynamic content

**Key Features:**
- Hierarchical structure using nested JSON objects for logical grouping
- Parameter placeholders using {} syntax for dynamic content insertion
- Consistent naming convention with descriptive keys
- Cross-language reference keys for language selection interface
- Comprehensive error messaging with support for error codes and user guidance

### Arabic Translation File (ar.json)

**Arabic Translation File Structure:**

The Arabic translation file (ar.json) provides complete Arabic translations with proper RTL text formatting and cultural adaptation. This file mirrors the English structure while ensuring culturally appropriate Arabic content.

**Content Organization:**
- **Header Section**: Arabic platform branding, authentication terms, and navigation labels
- **Home Section**: Arabic welcome messages, featured content descriptions, and dashboard elements
- **Catalog Section**: Arabic course browsing terms, filtering vocabulary, and search interface text
- **Language Section**: Bilingual language selection labels maintaining consistency across locales
- **Error Handling**: Culturally appropriate Arabic error messages with parameter support

**Arabic-Specific Features:**
- Proper Arabic typography and text formatting
- Culturally adapted messaging that resonates with Arabic-speaking users
- Maintains same JSON structure as English for consistency
- Supports parameter placeholders for dynamic Arabic content
- Professional Arabic terminology for educational and technical contexts

## Usage Patterns

### Basic Translation

**Generated Locale Keys Structure:**

The LocaleKeys class is automatically generated from translation JSON files, providing type-safe access to all translation keys. This generated class ensures compile-time validation and prevents runtime errors from typos.

**Key Generation Features:**
- **Hierarchical Mapping**: JSON structure is flattened into dot-notation constants (e.g., 'header.platformTitle')
- **Type Safety**: Static constants prevent runtime errors from incorrect key names
- **IDE Support**: Provides autocomplete and refactoring support for translation keys
- **Consistent Naming**: Follows snake_case convention for Dart constants
- **Automatic Updates**: Regenerated whenever translation files change

**Usage Benefits:**
- Compile-time validation of translation key existence
- Easy refactoring and renaming of translation keys
- IDE autocomplete for faster development
- Prevention of runtime translation errors
- Clear mapping between JSON structure and Dart constants

### Using Translations in Widgets

**Translation Widget Implementation:**

The WelcomeWidget class demonstrates comprehensive translation usage patterns in Flutter widgets using EasyLocalization.

**Simple Translation**: Text widget uses LocaleKeys.home_welcome.tr() for basic translation without parameters, automatically selecting appropriate language based on current locale.

**Parameterized Translation**: Text widget uses LocaleKeys.error_body.tr(args: ['12345']) demonstrating parameter passing for dynamic content insertion in translations.

**Conditional Translation**: Text widget implements locale-specific logic using context.locale.languageCode comparison with Constants.localeAR to display different translations based on current language setting.

**Column Layout**: Column widget organizes multiple translated text elements vertically, showcasing different translation patterns in a single widget structure.

### Translation with Parameters

**Parameter Translation Implementation:**

**Single Parameter**: Translation key "welcome_user" with "Welcome, {}" format uses placeholder {} for dynamic content insertion, accessed via LocaleKeys.welcome_user.tr(args: [userName]).

**Multiple Parameters**: Translation key "training_info" with "Training {} has {} lessons" format uses multiple {} placeholders, accessed via LocaleKeys.training_info.tr(args: [trainingName, lessonCount.toString()]).

**Parameter Order**: Arguments array order must match placeholder order in translation strings for correct content substitution.

### Pluralization

**Pluralization Implementation:**

**Plural Forms Structure**: Translation key "items" contains nested object with "zero" (No items), "one" (1 item), and "other" ({} items) forms for different quantity contexts.

**Plural Usage**: Text widget uses LocaleKeys.items.plural(itemCount) method which automatically selects appropriate plural form based on itemCount value.

**Automatic Selection**: EasyLocalization automatically chooses correct plural form based on language rules and provided count parameter.

### Gender Support

**Gender-Specific Translation Implementation:**

The translation system supports gender-specific content through nested JSON objects with male, female, and other keys. Each gender variant contains appropriate greeting text with parameter placeholders for dynamic content insertion.

**Usage Pattern**: Text widgets utilize LocaleKeys.greeting.tr() method with gender parameter and args array to display contextually appropriate greetings based on user gender and name.

## RTL Support

### Automatic Layout Direction

The app automatically handles RTL layout for Arabic:

**Automatic RTL Support Implementation:**

The MyApp class extends StatelessWidget and configures MaterialApp with automatic RTL support through EasyLocalization integration. The build method returns a MaterialApp widget configured with localizationsDelegates, supportedLocales, and locale properties from the context. It includes a builder function that wraps content in a Directionality widget, automatically setting TextDirection.rtl for Arabic (Constants.localeAR) and TextDirection.ltr for other languages.

### RTL-Aware Widgets

**RTL-Aware Widget Implementation:**

The RTLAwareWidget class demonstrates locale-aware UI construction by checking the current language code against Constants.localeAR to determine text direction. The build method creates a Row widget with conditional icon placement - displaying Icons.arrow_forward before text for LTR languages and Icons.arrow_back after text for RTL languages, ensuring proper visual flow for both reading directions.

### Manual Direction Control

**Manual Direction Control Patterns:**

**LTR Content Override**: Use Directionality widget with TextDirection.ltr to force left-to-right layout for specific content like email addresses, phone numbers, or URLs that should maintain LTR orientation regardless of the app's current locale.

**RTL Content Override**: Use Directionality widget with TextDirection.rtl to force right-to-left layout for Arabic text within mixed-language content, ensuring proper text flow and readability for Arabic content in predominantly LTR interfaces.

## Code Generation

### Generating Locale Keys

The app uses code generation for type-safe translation keys:

**Basic Key Generation:**
Execute `flutter packages pub run easy_localization:generate -S assets/translations -O lib/generated` to generate locale keys from translation files

**Advanced Key Generation:**
Run `flutter packages pub run easy_localization:generate` with parameters: source directory as assets/translations, output directory as lib/generated, output file as locale_keys.g.dart, and format as keys

### Generated Code Structure

**Generated LocaleKeys Class Structure:**

The complete LocaleKeys class is automatically generated from translation JSON files, creating a comprehensive mapping of all available translation keys. This generated class provides the foundation for type-safe localization throughout the application.

**Class Organization:**
- **Header Section Keys**: Platform title, authentication actions, and navigation elements
- **Home Section Keys**: Welcome messages, featured content, and dashboard elements
- **Catalog Section Keys**: Course browsing, filtering, and search functionality
- **Section Grouping**: Parent keys for each major section (header, home, catalog)

**Generated Features:**
- Automatic generation from JSON structure with no manual editing required
- Hierarchical organization matching the JSON file structure
- Static constants for compile-time validation and performance
- Consistent naming convention using underscore notation
- Complete coverage of all translation keys across both language files

### Asset Loader

**CodegenLoader Asset Loader:**

The CodegenLoader class provides efficient, compile-time embedded translation loading for the EasyLocalization system. This generated loader eliminates the need for runtime file reading and improves app startup performance.

**Loader Architecture:**
- **AssetLoader Implementation**: Extends EasyLocalization's AssetLoader interface for seamless integration
- **Embedded Data**: Contains all translation data compiled directly into the app binary
- **Locale Mapping**: Maps locale strings ('en', 'ar') to their respective translation data structures
- **Synchronous Loading**: Returns translations immediately without file I/O operations

**Performance Benefits:**
- No runtime file system access required for translation loading
- Faster app startup with embedded translation data
- Reduced bundle size compared to separate JSON asset files
- Compile-time validation of translation data integrity
- Efficient memory usage with static constant data structures

## Best Practices

### 1. Key Naming Convention

**Key Naming Convention Examples:**

**✅ Good Practice**: Use hierarchical, descriptive keys like catalog_filter_sort_newest and profile_settings_language that clearly indicate the feature area, component, and specific purpose of the translation.

**❌ Bad Practice**: Avoid flat, unclear keys like text1 or msg that provide no context about their usage or location within the application.

### 2. Parameter Usage

**Parameter Usage Best Practices:**

**✅ Good Practice**: Use clear parameter placeholders with descriptive names like {appName} and {userName} in translation strings. Implement usage with Text widget calling LocaleKeys.welcome_message.tr(args: [appName, userName]) for clear parameter mapping.

**❌ Bad Practice**: Avoid unclear parameter order with generic placeholders like {} that make it difficult to understand which argument corresponds to which placeholder.

### 3. Context-Aware Translations

**Context-Aware Translation Examples:**

**✅ Good Practice**: Create context-specific translations like button_save, button_save_changes, and button_save_draft that provide clear, contextual meaning for different use cases within the application.

**❌ Bad Practice**: Avoid generic translations like save that are used everywhere without context, making it difficult to provide appropriate translations for different scenarios.

### 4. RTL Considerations

**RTL Layout Considerations:**

**✅ Good Practice - Directional Layout**: Implement DirectionalWidget class that extends StatelessWidget with build method returning Row widget configured with textDirection property that dynamically switches between TextDirection.rtl for Arabic and TextDirection.ltr for other languages.

**✅ Good Practice - Locale-Specific Icons**: Use conditional Icon widgets that display Icons.arrow_back for Arabic locale and Icons.arrow_forward for other locales, ensuring visual elements align with reading direction expectations.

### 5. Language Switching

**Language Switching Implementation:**

The LanguageSwitcher class extends StatelessWidget and provides a DropdownButton<Locale> for language selection. The build method creates a dropdown with current locale as value, maps supportedLocales to DropdownMenuItem widgets with localized language names, and handles onChanged events by calling context.setLocale(locale) to switch the app language.

The _getLanguageName helper method uses a switch statement to return appropriate localized language names using LocaleKeys.languages_english_en.tr() for English and LocaleKeys.languages_arabic_ar.tr() for Arabic, with fallback to languageCode for unsupported locales.

## Maintenance

### Adding New Translations

1. **Add to JSON files**: Update both `en.json` and `ar.json`
2. **Regenerate keys**: Run code generation command
3. **Update usage**: Use new generated keys in code
4. **Test both languages**: Verify translations in both locales

### Translation Validation

**Translation Validation Utility:**

The TranslationValidator class provides static validateTranslations method to identify missing translations between language files. It extracts keys from englishTranslations and arabicTranslations using _extractKeys helper, then calculates differences using Set.difference() to find missingInArabic and missingInEnglish keys. The method prints diagnostic messages for any missing translations to help maintain translation completeness across all supported languages.

### Performance Optimization

**Performance Optimization Utility:**

The TranslationPreloader class provides static preloadTranslations method for improving app startup performance. It calls EasyLocalization.ensureInitialized() to initialize the localization system, then iterates through supportedLocales to preload translation data using EasyLocalization.of(context)?.loadTranslations() for each locale, reducing runtime translation loading delays.

### Testing Localization

**Localization Testing Pattern:**

The testWidgets function demonstrates comprehensive localization testing by wrapping the test widget in EasyLocalization with supportedLocales for English and Arabic, specifying translation path and fallbackLocale. The test pumps a MaterialApp configured with localizationDelegates and supportedLocales, then verifies English text display using expect(find.text('Welcome'), findsOneWidget).

For Arabic testing, it uses tester.binding.setLocale('ar', 'SA') to switch locale, calls tester.pump() to rebuild the widget tree, and verifies Arabic text display with expect(find.text('مرحباً'), findsOneWidget) to ensure proper translation switching functionality.

## Related Documentation

- [Theming](theming.md) - Theme system with locale-aware fonts
- [Shared Widgets](shared-widgets.md) - Localized UI components
- [Development Standards](../development/coding-standards.md) - Code style guidelines
- [Testing Guide](../development/testing-guide.md) - Testing localized content

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
