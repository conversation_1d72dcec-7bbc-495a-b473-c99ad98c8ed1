# Theming

This document covers the theming implementation in the NSP Flutter application, including color systems, typography, locale-aware font selection, and theme customization patterns.

## Table of Contents

- [Overview](#overview)
- [Theme Architecture](#theme-architecture)
- [Color System](#color-system)
- [Typography System](#typography-system)
- [Locale-Aware Theming](#locale-aware-theming)
- [Theme Usage](#theme-usage)
- [Custom Components](#custom-components)
- [Best Practices](#best-practices)

## Overview

The NSP app implements a comprehensive theming system that provides consistent styling across the application. The theme system supports multiple languages with appropriate fonts and maintains design system standards.

### Key Features

- **Consistent Color Palette**: Centralized color definitions
- **Typography System**: Comprehensive text styles with extensions
- **Locale-Aware Fonts**: Automatic font selection based on language
- **Material 3 Support**: Modern Material Design implementation
- **Accessibility**: High contrast ratios and semantic colors

## Theme Architecture

### Directory Structure

```
core/theme/
├── app_colors.dart      # Color palette definitions
├── app_text_theme.dart  # Typography system with extensions
├── app_theme.dart       # Main theme configuration
└── theme.dart           # Export file
```

### Theme Components

1. **AppColors**: Centralized color palette
2. **AppTextTheme**: Typography system with style extensions
3. **AppTheme**: Main theme configuration
4. **Context Extensions**: Easy access to theme properties

## Color System

### Color Palette

**Application Color Palette:**

The AppColors class defines the complete color system with organized categories:
- **Primary Colors**: greenAccentPrimary (0xFF00B050) for main brand actions and greenAccentSecondary (0xFF92D050) for secondary brand elements
- **Neutral Colors**: neutralBlack, neutralWhite, and neutralGrey (0xFF6B7280) for basic UI elements and backgrounds
- **UI Background Colors**: uiBackgroundPrimary (0xFFF9FAFB) for main surfaces and uiBackgroundSecondary (0xFFF3F4F6) for secondary surfaces
- **Semantic Colors**: semanticSuccess (green), semanticWarning (amber), semanticError (red), and semanticInfo (blue) for status communication
- **Text Colors**: textPrimary (0xFF111827) for main content, textSecondary (0xFF6B7280) for supporting text, and textTertiary (0xFF9CA3AF) for subtle text
- **Static Constants**: All colors defined as static const Color values for compile-time optimization and consistency

This color system ensures consistent visual hierarchy and semantic meaning throughout the application.

### Color Usage Guidelines

**Color Usage Implementation Patterns:**

**Primary Actions**: Container widgets use AppColors.greenAccentPrimary for primary actions and branding elements, ensuring consistent brand identity throughout the application.

**Success States**: Icon widgets with Icons.check use AppColors.semanticSuccess color for positive feedback and completion indicators.

**Error States**: Text widgets displaying error messages use AppColors.semanticError color for clear error communication and visual hierarchy.

**Background Surfaces**: Scaffold widgets use AppColors.uiBackgroundPrimary for consistent page backgrounds across the application.

## Typography System

### Text Style Hierarchy

**Typography System Implementation:**

The text theme system provides comprehensive typography with locale-aware font selection:
- **ContextExtension**: Adds textTheme getter to BuildContext for easy access to _AppTextStyles instance
- **_AppTextStyles Class**: Private class that accepts BuildContext for locale detection and provides complete text style hierarchy
- **Heading Styles**: h1 (32px, w700), h2 (24px, w600), h3 (20px, w600), h4 (18px, w600) for content hierarchy
- **Body Text Styles**: textLarge (18px), textMedium (16px), textSmall (14px), textXSmall (12px) for content text
- **Base Style Logic**: _baseStyle provides foundation with locale-aware font family selection, primary text color, and 1.5 line height
- **Locale Detection**: _isLocaleArabic checks context.locale against Constants.localeAR for Arabic font switching
- **Font Family Selection**: Automatically chooses AppTheme.arabicFontFamily for Arabic locale or AppTheme.fontFamily for other locales

This system ensures consistent typography with proper internationalization support and easy access throughout the application.

### Style Extensions

The typography system includes convenient extensions for common style modifications:

**TextStyleExtensions Implementation:**

The TextStyleExtensions provides convenient style modifications through extension methods on TextStyle:

**Font Weight Extensions**: bold (w700), semiBold (w600), medium (w500), and regular (w400) getters use copyWith to modify fontWeight property for typography hierarchy.

**Color Extensions**: Comprehensive color getters including white/black for neutrals, greyPrimary/greySecondary for text hierarchy, accentGreenPrimary for branding, and semantic colors (success, error, warning) for state indication.

**Text Decoration Extensions**: underline and lineThrough getters apply TextDecoration properties for emphasis and strikethrough effects.

**RTL Support Extensions**: ltr and rtl getters set textDirection property for explicit text direction control in multilingual applications.

**Chaining Pattern**: All extensions return TextStyle allowing method chaining for complex style combinations like context.textTheme.h1.bold.success.underline.

### Usage Examples

**Typography Usage Patterns:**

**Heading Styling**: Text widget with 'Welcome to NSP' uses context.textTheme.h1.bold.accentGreenPrimary for large, bold, brand-colored headings.

**Semantic Text**: Text widget with 'Success message' uses context.textTheme.textMedium.success for medium-sized text with semantic success color.

**Secondary Information**: Text widget with 'Additional information' uses context.textTheme.textSmall.greySecondary for smaller, less prominent text.

**Complex Styling**: Text widget with 'Important notice' demonstrates chaining with context.textTheme.textLarge.semiBold.error.underline for large, semi-bold, red, underlined text.

## Locale-Aware Theming

### Font Selection

The theme system automatically selects appropriate fonts based on the current locale:

**Theme Configuration with Locale Support:**

The AppTheme class provides comprehensive theme configuration with automatic locale-based font selection:
- **Font Constants**: fontFamily ('Noto Sans') for Latin scripts and arabicFontFamily ('Tajawal') for Arabic text rendering
- **Locale Detection**: Retrieves currentLocale from Hive storage with fallback to Constants.localeEN for default English
- **Language Check**: isLocaleEnglish boolean determines font family selection based on locale comparison
- **Material 3 Integration**: Uses ThemeData.light(useMaterial3: true) as foundation for modern Material Design components
- **Text Theme Application**: Applies locale-appropriate font family to both textTheme and primaryTextTheme for comprehensive coverage
- **Dynamic Font Switching**: Automatically selects Noto Sans for English and Tajawal for Arabic based on stored locale preference
- **Theme Extension**: copyWith() allows additional theme customization while maintaining font family logic

This implementation ensures proper typography rendering across different languages with automatic font family selection.

### Font Families

- **English (Noto Sans)**: Clean, modern sans-serif font optimized for Latin scripts
- **Arabic (Tajawal)**: Arabic-optimized font with proper RTL support and Arabic character rendering

### RTL Support

**RTL Support Implementation:**

**Locale Detection**: _isLocaleArabic getter compares context.locale with Constants.localeAR Locale for Arabic language detection.

**Base Style Configuration**: _baseStyle getter provides TextStyle with conditional font family selection (AppTheme.arabicFontFamily for Arabic, AppTheme.fontFamily for others) and automatic text direction setting.

**Manual Direction Control**: Text widgets can override automatic direction using .ltr extension for left-to-right content or .rtl extension for right-to-left content like Arabic text.

**Automatic Adaptation**: System automatically handles font family and text direction based on current locale while allowing manual overrides for mixed-language content.

## Theme Usage

### Accessing Theme Properties

**Theme Access Implementation:**

The MyWidget class demonstrates proper theme property access patterns using context-based theme system.

**Container Styling**: Container uses AppColors.uiBackgroundPrimary for consistent background color across the application.

**Typography Access**: Text widgets access theme typography through context.textTheme with h2.bold for title and textMedium.greyPrimary for subtitle, ensuring consistent text hierarchy.

**Column Layout**: Column widget organizes text elements vertically with proper theme-aware styling for structured content presentation.

### Theme-Aware Components

**ThemedCard Implementation:**

The ThemedCard class demonstrates comprehensive theme-aware component design with conditional styling based on state.

**Constructor Parameters**: Required title and content strings, optional isHighlighted boolean (defaults to false) for state-based styling variations.

**Conditional Styling**: Container decoration uses isHighlighted to switch between highlighted state (greenAccentPrimary with 0.1 opacity background, greenAccentPrimary border) and normal state (neutralWhite background, textTertiary border).

**Layout Structure**: Container with 16px padding contains Column with CrossAxisAlignment.start for left-aligned content layout.

**Typography Hierarchy**: Title uses context.textTheme.h4.semiBold with conditional color (greenAccentPrimary when highlighted, textPrimary otherwise), content uses textMedium.greyPrimary for consistent secondary text.

**Spacing**: SizedBox with 8px height provides proper spacing between title and content elements for visual hierarchy.

## Custom Components

### Themed Button Component

**Themed Button Component Implementation:**

The AppButton class provides a reusable button component with comprehensive theming support:
- **Constructor Parameters**: Required onTap callback, optional backgroundColor (defaults to greenAccentPrimary), buttonText, textStyle, height (48px default), and borderRadius (8.0 default)
- **Property Types**: String? buttonText for optional text, VoidCallback onTap for tap handling, Color? backgroundColor for customization, TextStyle? textStyle for text styling override
- **UI Structure**: GestureDetector wraps Container with height, padding (12px all sides), BoxDecoration with circular border radius and background color
- **Text Rendering**: Center-aligned Text widget using buttonText with fallback to empty string and textStyle with getTextStyle fallback
- **Dynamic Styling**: getTextStyle method provides context-aware text styling based on background color
- **Color Logic**: Returns white semibold small text for greenAccentPrimary background, otherwise returns default semibold small text
- **Theme Integration**: Uses context.textTheme for consistent typography and style extensions (semiBold, white)

This component ensures consistent button styling while allowing customization for different use cases throughout the application.

### Theme-Responsive Input

**ThemedInput Implementation:**

The ThemedInput class provides a comprehensive input component with theme integration and error state handling.

**Constructor Parameters**: Optional label and hint strings, hasError boolean (defaults to false), and TextEditingController for input management.

**Label Rendering**: Conditional label display using spread operator with Text widget styled with context.textTheme.textSmall.semiBold and 8px spacing.

**TextField Configuration**: TextField uses controller parameter, context.textTheme.textMedium for input text styling, and comprehensive InputDecoration for visual styling.

**Error State Handling**: Border colors conditionally switch between semanticError (error state) and textTertiary (normal) or greenAccentPrimary (focused) based on hasError boolean.

**Decoration Properties**: InputDecoration includes hintStyle with greySecondary color, 8px border radius, filled background with uiBackgroundPrimary color, and 2px focused border width.

**Layout Structure**: Column with CrossAxisAlignment.start organizes label and input field with proper spacing and alignment for consistent form layouts.

## Best Practices

### 1. Consistent Color Usage

**Good Practice**: Container uses AppColors.semanticSuccess and Text uses AppColors.semanticError for semantic color consistency across the application.

**Bad Practice**: Using hardcoded Color(0xFF00FF00) or Colors.red bypasses the design system and creates inconsistent color usage.

### 2. Typography Hierarchy

**Good Practice**: Text widgets use context.textTheme.h2.bold for titles and context.textTheme.textMedium for body text, maintaining consistent typography hierarchy.

**Bad Practice**: Custom TextStyle with hardcoded fontSize and fontWeight values bypasses the typography system and creates inconsistent text styling.

### 3. Locale-Aware Styling

**Good Practice**: Text uses context.textTheme.textMedium allowing the system to automatically handle font family selection based on current locale.

**Bad Practice**: Hardcoding fontFamily to 'Noto Sans' prevents automatic locale-based font switching and breaks internationalization support.

### 4. Theme Extensions

**Good Practice**: Text uses context.textTheme.textMedium.success.bold leveraging style extensions for clean, readable style composition.

**Bad Practice**: Manual copyWith with explicit color and fontWeight properties creates verbose code and bypasses the extension system benefits.

### 5. Responsive Design

**ResponsiveText Implementation:**

The ResponsiveText class demonstrates responsive design by adapting text styles based on screen size using MediaQuery for device detection.

**Screen Size Detection**: Uses MediaQuery.of(context).size.width > 600 to determine tablet vs mobile device for appropriate text sizing.

**Conditional Styling**: Text widget conditionally applies context.textTheme.h2.bold for tablets or context.textTheme.h3.bold for mobile devices, ensuring optimal readability across screen sizes.

### 6. Accessibility

**Accessibility Best Practices:**

**Semantic Markup**: Semantics widget wraps Text with descriptive label ('Success message') for screen reader accessibility while using context.textTheme.textMedium.success for semantic color indication.

**High Contrast Colors**: Text widget uses AppColors.textPrimary for sufficient color contrast ensuring readability for users with visual impairments and meeting accessibility standards.

## Related Documentation

- [Design System](design-system.md) - Overall design system guidelines
- [Shared Widgets](shared-widgets.md) - Reusable UI components
- [Localization](localization.md) - Multi-language support
- [Development Standards](../development/coding-standards.md) - Code style guidelines

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
