# Shared UI Components

## Overview

The NSP mobile application includes a comprehensive library of reusable UI components that ensure consistency across the app. These components follow the design system and provide a unified user experience.

## Component Library Structure

**Shared UI Components Directory (features/shared/ui_components/):**
- **app_button.dart** - Primary button component with consistent styling
- **app_input.dart** - Input field component for forms
- **app_checkbox.dart** - Custom checkbox with app theming
- **app_chip.dart** - Chip/tag component for selections
- **app_custom_radio.dart** - Custom radio button implementation
- **app_divider.dart** - Divider component for visual separation
- **app_loading_overlay.dart** - Loading overlay for async operations
- **app_shimmer.dart** - Shimmer loading effect for content placeholders
- **app_tab_bar.dart** - Custom tab bar component
- **nsp_app_bar.dart** - Application-specific app bar
- **toast_messages.dart** - Toast notification system
- **ui_components.dart** - Central export file for all components

## Core Components

### 1. AppButton

The primary button component provides consistent styling, accessibility, and behavior across the application.

**AppButton Component Structure:**

The AppButton class provides a comprehensive, customizable button component that serves as the foundation for all button interactions throughout the application. This component integrates seamlessly with the app's design system and theme.

**Component Architecture:**
- **StatelessWidget Implementation**: Efficient rendering with immutable properties for optimal performance
- **Required Callback**: onTap parameter ensures all buttons have defined interaction behavior
- **Theme Integration**: Uses AppColors.greenAccentPrimary as default background color
- **Flexible Content**: Supports button text, leading icons, and trailing icons for various use cases
- **Consistent Dimensions**: Default 48px height and 8px border radius following design system standards

**Key Design Features:**
- **Accessibility Compliance**: Proper touch targets and semantic labeling for screen readers
- **Visual Consistency**: Maintains design system compliance across all button instances
- **Customization Options**: Extensive customization while preserving design consistency
- **Performance Optimization**: Efficient widget composition and rendering

**Key Features:**
- **Consistent Styling**: Follows app design system with default colors and dimensions
- **Flexible Content**: Supports text, leading/trailing icons, and custom layouts
- **Accessibility**: Built-in semantic labels and touch target sizing
- **Customizable**: Configurable colors, borders, padding, and dimensions

#### Usage Examples

**AppButton Usage Examples**: Primary button with onTap callback and buttonText property. Secondary button with transparent backgroundColor, borderColor using AppColors.greyTertiary, and textStyle using context.textTheme.textMedium.greyPrimary. Button with leading Icon using Icons.share and white color. Full-width button with width set to double.infinity and margin using EdgeInsets.symmetric with 16px horizontal spacing.

### 2. AppInputField

A comprehensive input field component with built-in validation, formatting, and accessibility features.

**AppInputField Component Architecture:**

The AppInputField class provides a comprehensive, stateful text input component with built-in validation, formatting, and accessibility features. This component serves as the foundation for all form inputs throughout the application.

**Core Features:**
- **Stateful Management**: StatefulWidget implementation for dynamic input state handling
- **Text Input Control**: Controller-based text input with change detection and validation
- **Security Support**: Password obscuring functionality for sensitive input fields
- **Keyboard Integration**: Configurable keyboard types for optimal user experience
- **Icon Integration**: Prefix and suffix icon support for enhanced visual feedback and functionality

**Input Capabilities:**
- **Validation Framework**: Built-in validator function support for form validation
- **Label System**: Configurable label text for accessibility and user guidance
- **Hint Text**: Default empty string hint text with customization options
- **Change Handling**: Real-time input change detection for dynamic UI updates

**Key Features:**
- **Validation Support**: Built-in form validation with custom error messages
- **Input Formatting**: Support for input formatters and keyboard types
- **Icon Integration**: Prefix and suffix icon support for enhanced UX
- **Accessibility**: Proper labeling and semantic structure for screen readers

#### Usage Examples

**AppInputField Usage Examples**: Basic text input with controller, hintText, and label properties. Password input with obscureText set to true and suffixIcon using IconButton with visibility toggle logic. Search input with prefixIcon using Icons.search and onChanged callback. Validated input with keyboardType set to TextInputType.emailAddress and validator function checking for empty values and email format.

### 3. AppChip

Chip component for tags, filters, and selections.

**AppChip Component Architecture:**

The AppChip class provides a customizable chip component for displaying tags, filters, and selectable items throughout the application. This component integrates with the app's design system and provides consistent chip styling with accessibility support.

**Component Structure:**
- **StatelessWidget Implementation**: Efficient rendering with immutable properties for optimal performance
- **Required Text**: Mandatory text parameter ensuring all chips display meaningful content
- **Accessibility Integration**: Semantics wrapper with button role and label for screen reader support
- **Gesture Detection**: GestureDetector wrapper for tap handling and user interaction

**Visual Design:**
- **Consistent Padding**: Standard 16px horizontal and 7px vertical padding for optimal content spacing
- **Border Radius**: 8px border radius maintaining design system consistency
- **Theme Integration**: Uses AppColors.accentExtraLight background and AppColors.accentLight border by default
- **Typography**: Integrates with context.textTheme.textXSmall.medium for consistent text styling

**Accessibility Features:**
- **Semantic Button**: Automatically identifies as button when onTap is provided
- **Screen Reader Support**: Proper label assignment for accessibility tools
- **Interactive States**: Clear indication of interactive vs display-only chips

#### Usage Examples

**AppChip Usage Examples**: Basic chip with text property. Interactive chip with onTap callback, backgroundColor using AppColors.greenAccentPrimary, and white textColor. Chip list using Wrap widget with 8px spacing, mapping categories to AppChip widgets with conditional backgroundColor based on selection state.

### 4. AppCheckBox

Custom checkbox component with consistent styling.

**AppCheckBox Component Architecture:**

The AppCheckBox class provides a custom checkbox component with internal state management and consistent styling. This component offers a branded alternative to Flutter's default checkbox with enhanced visual design and self-contained state.

**Component Features:**
- **StatefulWidget Implementation**: Internal state management for checkbox value with setState updates
- **Initial Value Support**: Configurable initial checkbox state with default false value
- **Change Callback**: Optional onChanged function parameter for parent component notification
- **Self-Contained State**: Internal checkBoxState management with automatic UI updates

**Visual Design:**
- **Consistent Sizing**: Fixed 18x18 pixel dimensions for uniform checkbox appearance
- **Theme Integration**: Uses AppColors.greenAccentPrimary for checked state and white for unchecked
- **Border Styling**: 4px border radius with conditional border based on state
- **Icon Integration**: Custom check icon from AssetsPath with theme-appropriate coloring

**Interaction Behavior:**
- **Tap Handling**: GestureDetector toggles internal state and triggers callback
- **State Updates**: setState automatically updates UI when checkbox state changes
- **Parent Notification**: Optional callback notifies parent components of state changes

### 5. AppShimmer

Shimmer loading effect for better perceived performance.

**AppShimmer Component Architecture:**

The AppShimmer class provides a shimmer loading effect wrapper that enhances perceived performance during data loading states. This component uses the Skeletonizer package to create smooth, animated loading placeholders.

**Component Features:**
- **StatelessWidget Implementation**: Efficient rendering with immutable properties for optimal performance
- **Required Child Widget**: Wraps any widget to provide shimmer effect during loading states
- **Loading State Control**: Boolean isLoading parameter controls when shimmer effect is active
- **Skeletonizer Integration**: Uses external Skeletonizer package for professional shimmer animations

**Shimmer Configuration:**
- **Base Color**: Light grey (Colors.grey[300]) as the primary shimmer background color
- **Highlight Color**: Very light grey (Colors.grey[100]) for the shimmer highlight effect
- **Animation Duration**: 1500 milliseconds for smooth, not-too-fast shimmer animation
- **Conditional Activation**: Shimmer only appears when isLoading is true

**Usage Benefits:**
- **Improved UX**: Provides visual feedback during loading states instead of blank screens
- **Performance Perception**: Makes loading feel faster through engaging animations
- **Flexible Application**: Can wrap any widget to provide loading state visualization

#### Usage Examples

**AppShimmer Implementation Patterns:**

**Course Cards Shimmer**: AppShimmer wraps ListView.builder with isLoading property bound to state condition (state is CoursesLoading), dynamically adjusting itemCount between skeleton count (5) and actual courses.length, conditionally rendering CourseCardSkeleton during loading or CourseCard with actual data.

**Text Content Shimmer**: AppShimmer wraps Column containing Container widgets with specified heights (20px, 16px) and white color for skeleton text representation, separated by SizedBox for spacing, creating realistic text loading placeholders.

**Conditional Rendering**: Both patterns use isLoading boolean to control shimmer activation, seamlessly transitioning between skeleton states and actual content when data becomes available, providing smooth loading experience.

### 6. AppTabBar

Custom tab bar component with consistent styling.

**AppTabBar Component Architecture:**

The AppTabBar class provides a custom tab bar component with consistent styling and theme integration. This component wraps Flutter's TabBar with branded styling and enhanced user experience features.

**Component Features:**
- **StatelessWidget Implementation**: Efficient rendering with immutable properties for optimal performance
- **Required Dependencies**: TabController for state management, tab list for content, and onTabTapped callback
- **Container Wrapper**: Styled container with 8px border radius and fixed 44px height
- **Theme Integration**: Uses app's text theme and color system for consistent appearance

**Tab Configuration:**
- **Indicator Styling**: Full-tab indicator size with AppColors.greenAccentPrimary color
- **Text Styling**: Different styles for selected (accentGreenPrimary.medium) and unselected (textSmall.medium) tabs
- **Interaction Handling**: Custom onTap callback with disabled splash effects for clean interaction
- **Transparent Overlays**: Removes default Material overlay colors for custom touch feedback

**Visual Features:**
- **Consistent Height**: Fixed 44px height for uniform tab bar appearance
- **Rounded Corners**: 8px border radius matching design system standards
- **Dynamic Tabs**: Automatically generates Tab widgets from provided string list
- **Clean Interactions**: NoSplash factory and transparent overlays for refined user experience

## Loading Components

### 1. AppLoadingOverlay

Full-screen loading overlay with spinner.

**AppLoadingOverlay Implementation:**

The AppLoadingOverlay class extends StatelessWidget with required child and isLoading parameters, providing full-screen loading state management.

**Stack-Based Layout**: Uses Stack widget to layer loading overlay on top of child content, with conditional Container rendering based on isLoading boolean state for overlay visibility control.

**Overlay Styling**: Container uses black color with 0.3 opacity for semi-transparent background, centered CircularProgressIndicator with AppColors.greenAccentPrimary color using AlwaysStoppedAnimation for consistent brand theming.

**Conditional Rendering**: Implements if (isLoading) condition within Stack children array to show/hide overlay, ensuring overlay only appears during loading states without affecting underlying content layout.

### 2. Skeleton Components

**CourseCardSkeleton Implementation:**

The CourseCardSkeleton class extends StatelessWidget providing placeholder content during loading states for course cards.

**Card Structure**: Uses Card widget with 16px padding containing Column with CrossAxisAlignment.start for left-aligned skeleton elements mimicking actual course card layout.

**Image Placeholder**: First Container with 120px height, Colors.grey[300] background, and 8px border radius represents course image placeholder with rounded corners matching design system.

**Text Placeholders**: Two additional Containers simulate text content - first with 20px height and full width for title, second with 16px height and 200px width for description, both using Colors.grey[300] for consistent skeleton appearance.

**Spacing**: SizedBox widgets with 12px and 8px heights provide proper spacing between skeleton elements, maintaining visual hierarchy similar to actual course card content.

## Feedback Components

### 1. Toast Messages

**Toast Message Implementation**

The toast messaging system provides a centralized way to display temporary notifications to users. The implementation includes:

**Core Function**: A main `showAppToast` function that accepts a BuildContext, required message string, optional toast type (defaulting to info), and optional duration (defaulting to 3 seconds). The function utilizes the styled_toast package to display messages with slide animations from top to bottom.

**Animation Behavior**: Toast messages slide down from the top of the screen when appearing and slide back up when disappearing, positioned at the top of the screen for optimal visibility.

**Color System**: A helper function determines the background color based on toast type using a switch statement that maps:
- Success type to green status color
- Error type to warning status color
- Warning type to orange accent color
- Info type to green accent color

**Type Safety**: An enum defines four toast types (success, error, warning, info) ensuring type-safe usage throughout the application.

**Styling**: Toast messages use medium text style from the app's theme system with white text color for optimal contrast against colored backgrounds.

### 2. Error States

**ErrorStateWidget Implementation:**

The ErrorStateWidget class extends StatelessWidget with required message parameter and optional onRetry callback for error state display.

**Centered Layout**: Uses Center widget with 24px padding containing Column with MainAxisAlignment.center for vertically centered error presentation.

**Error Icon**: Displays Icons.error_outline with 64px size and AppColors.greySecondary color for consistent error state visual indication.

**Message Display**: Text widget shows error message using context.textTheme.textMedium.greyPrimary style with center alignment for readable error communication.

**Conditional Retry**: If onRetry callback provided, renders SizedBox spacing (24px) followed by AppButton with 'Retry' text and onRetry callback, enabling user recovery actions.

**Spread Operator**: Uses spread operator (...) with conditional list to cleanly include retry button and spacing only when onRetry is not null.

### 3. Empty States

**EmptyStateWidget Implementation:**

The EmptyStateWidget class extends StatelessWidget with required title and description parameters, plus optional action widget and icon for customizable empty state display.

**Centered Layout**: Uses Center widget with 24px padding containing Column with MainAxisAlignment.center for vertically centered empty state presentation.

**Icon Display**: Shows provided icon or defaults to Icons.inbox_outlined with 64px size and AppColors.greySecondary color for consistent empty state visual indication.

**Title and Description**: Two Text widgets display title using context.textTheme.h4.greyPrimary style and description using context.textTheme.textMedium.greySecondary style, both center-aligned for clear hierarchy.

**Conditional Action**: If action widget provided, renders SizedBox spacing (24px) followed by the action widget, enabling custom buttons or links for user guidance.

**Flexible Design**: Optional icon parameter allows customization for different empty states (inbox, search, favorites) while maintaining consistent layout and styling patterns.

## Navigation Components

### 1. NSP App Bar

**NSP App Bar Implementation**

The NSP App Bar is a custom application bar component that implements Flutter's PreferredSizeWidget interface for consistent sizing. The implementation features:

**Base Structure**: A StatelessWidget that extends the standard Flutter AppBar with custom styling including white background color and zero elevation for a flat design aesthetic.

**Branding**: Displays the application logo as the title using a dedicated AppBarLogo widget component for consistent brand representation across the app.

**Authentication-Aware Actions**: Uses a StreamBuilder to listen to authentication state changes from the AuthTokenProvider service via dependency injection (GetIt). The stream provides real-time updates when users log in or out.

**Conditional Notifications**: When the user is authenticated, displays a notifications icon button with outlined style. When not authenticated, renders an empty SizedBox to maintain layout consistency without showing the notification button.

**Event Handling**: The notifications button triggers a private method to show notifications when pressed, providing access to user notifications and alerts.

**Size Specification**: Implements the preferredSize getter returning standard toolbar height to ensure proper integration with Flutter's app bar system and consistent sizing across different screens.

### 2. App Back Button

**AppBackButton Implementation:**

The AppBackButton class extends StatelessWidget with optional onPressed callback and color parameters for customizable back navigation.

**Icon Configuration**: Uses IconButton with Icons.arrow_back_ios icon, applying provided color or defaulting to AppColors.greenAccentPrimary for consistent brand theming.

**Navigation Handling**: Accepts optional onPressed callback or defaults to router.pop() for standard back navigation behavior, providing flexibility for custom navigation logic.

**Parameter Flexibility**: Optional parameters allow customization of both appearance (color) and behavior (onPressed) while maintaining sensible defaults for common use cases.

## Component Guidelines

### 1. Accessibility

All components include proper accessibility support:

**Semantic Labels Implementation:**

**Button Semantics**: Semantics widget wraps AppButton with button property set to true and descriptive label ('Login button') for screen reader identification, ensuring proper accessibility for interactive elements.

**Text Field Semantics**: Semantics widget wraps AppInputField with textField property set to true and descriptive label ('Email address') for form field identification, providing clear context for assistive technologies.

**Screen Reader Support**: Both implementations provide essential accessibility metadata enabling screen readers to properly announce component types and purposes to users with visual impairments.

### 2. Responsive Design

Components adapt to different screen sizes:

**ResponsiveButton Implementation:**

The ResponsiveButton class demonstrates responsive design patterns using MediaQuery for screen size detection and conditional styling.

**Screen Size Detection**: Uses MediaQuery.of(context).size.width to get screen width, then applies isTablet boolean logic (screenWidth > 768) for tablet detection.

**Adaptive Sizing**: AppButton height adjusts between 56px for tablets and 48px for phones, while fontSize scales between 18px for tablets and 16px for phones using copyWith method.

**Responsive Typography**: Combines context.textTheme.textMedium with copyWith to override fontSize based on device type, maintaining design consistency across different screen sizes.

### 3. Theme Integration

Components automatically adapt to theme changes:

**ThemedCard Implementation:**

The ThemedCard class demonstrates automatic theme adaptation using Flutter's Theme system for consistent styling across light and dark modes.

**Theme-Aware Colors**: Uses Theme.of(context).cardColor for background and Theme.of(context).shadowColor for shadow, ensuring automatic adaptation to current theme without hardcoded colors.

**Consistent Styling**: Container decoration includes 12px border radius for rounded corners and BoxShadow with 0.1 opacity, 8px blur radius, and Offset(0, 2) for subtle elevation effect.

**Dynamic Theming**: All color values derive from current theme context, enabling seamless transitions between light and dark themes without component modifications.

## Testing Components

### Widget Testing

**Widget Testing Implementation:**

The AppButton widget test demonstrates comprehensive testing patterns for interactive components using Flutter's testing framework.

**Test Setup**: testWidgets function creates isolated test environment with MaterialApp and Scaffold wrapper containing AppButton with onTap callback that sets wasTapped boolean flag.

**User Interaction Simulation**: Uses tester.tap() with find.text('Test Button') to simulate user tap interaction on the button widget.

**Assertion Verification**: expect(wasTapped, isTrue) verifies that the onTap callback was properly triggered, ensuring button functionality works correctly.

**Test Structure**: Follows standard widget testing pattern with setup (pumpWidget), action (tap), and verification (expect) for reliable component testing.

### Golden Tests

**Golden Testing Implementation:**

The AppButton golden test demonstrates visual regression testing using Flutter's golden file comparison system.

**Test Environment**: testWidgets creates MaterialApp with AppTheme().light() and Scaffold containing Column with multiple AppButton variants for comprehensive visual testing.

**Button Variants**: Tests both primary button (default styling) and secondary button (transparent background with AppColors.greyTertiary border) to verify different visual states.

**Visual Comparison**: expectLater with find.byType(Scaffold) and matchesGoldenFile('app_button_variants.png') compares current rendering against stored golden file reference.

**Regression Detection**: Golden tests automatically detect visual changes in component rendering, ensuring UI consistency across code changes and preventing unintended visual regressions.

## Best Practices

### 1. Consistency
- Use shared components consistently across the app
- Follow the established design patterns
- Maintain consistent spacing and sizing

### 2. Reusability
- Create generic components that can be customized
- Use composition over inheritance
- Provide sensible defaults

### 3. Performance
- Optimize component rendering
- Use const constructors where possible
- Implement proper disposal for stateful components

### 4. Accessibility
- Include semantic labels and hints
- Support screen readers
- Ensure proper focus management

## Related Documentation

- [Design System](design-system.md)
- [Theme System](../core/theme-system.md)
- [Testing Guide](../development/testing-guide.md)
- [Accessibility Guidelines](../development/coding-standards.md)
