# Design System

## Overview

The NSP mobile application follows a comprehensive design system that ensures consistency, accessibility, and maintainability across all user interfaces. This document outlines the design principles, component library, and implementation guidelines.

## Design Principles

### 1. Consistency
- **Visual Consistency**: Uniform appearance across all screens and components
- **Behavioral Consistency**: Predictable interactions and navigation patterns
- **Content Consistency**: Consistent tone, terminology, and information architecture

### 2. Accessibility
- **Inclusive Design**: Accessible to users with diverse abilities and needs
- **WCAG Compliance**: Follows Web Content Accessibility Guidelines
- **Screen Reader Support**: Proper semantic markup and labels
- **Color Contrast**: Meets accessibility contrast requirements

### 3. Usability
- **User-Centered**: Designed with user needs and goals in mind
- **Intuitive Navigation**: Clear and logical navigation patterns
- **Efficient Interactions**: Minimal steps to complete tasks
- **Error Prevention**: Design that prevents user errors

### 4. Performance
- **Optimized Assets**: Efficient use of images and resources
- **Fast Loading**: Quick response times and smooth animations
- **Memory Efficient**: Minimal memory footprint
- **Battery Conscious**: Optimized for mobile device constraints

## Color System

### Primary Colors

The primary color palette establishes the visual identity and brand recognition throughout the application.

**Brand Color Palette Definition:**

The AppColors class establishes the primary brand color system:
- **greenAccentPrimary (0xFF0060FF)**: Main brand color used for primary actions, buttons, and key UI elements
- **greenAccentSecondary (0xFF80A9AC)**: Secondary brand color for supporting elements and hover states
- **greenAccentTertiary (0xFF204D88)**: Tertiary brand color for accent elements and subtle highlights
- **greenAccentDark (0xFF173255)**: Dark variant for high contrast situations and accessibility compliance
- **Static Constants**: All colors defined as static const Color values for compile-time optimization and memory efficiency

This color hierarchy provides a cohesive brand identity with appropriate contrast levels for different UI contexts.


**Usage Guidelines:**
- **Primary**: Main actions, links, active states, primary buttons
- **Secondary**: Secondary actions, hover states, supporting elements
- **Tertiary**: Subtle accents, borders, dividers
- **Dark Variants**: Text on light backgrounds, emphasis elements

**Accessibility:** All primary colors meet WCAG AA contrast requirements when used appropriately.

### Secondary Colors

**Secondary Color Palette**: The AppColors class defines orange accent colors including orangeAccentPrimary (0xFF0060FF) for warnings and attention, orangeAccentSecondary (0xFFF1AA61) for secondary orange elements, orangeAccentTertiary (0xFFC87409) for tertiary orange, and orangeAccentLight (0xFFFDF4EA) for light orange backgrounds.

#### Usage Guidelines
- **Orange Primary**: Warnings, important notifications, progress indicators
- **Orange Secondary**: Hover states, secondary warnings
- **Orange Light**: Background for warning sections

### Neutral Colors

**Neutral Color System**: The AppColors class provides neutral colors including neutralWhite (0xFFFFFFFF) for pure white, neutralBlack (0xFF072527) for primary text, and a comprehensive grey scale with greyPrimary (0xFF575758) for primary grey text, greySecondary (0xFF959595) for secondary text, greyTertiary (0xFFD9D9D9) for borders and dividers, greyLight (0xFFEDEDED) for light backgrounds, and greyExtraLight (0xFFF2F2F2) for extra light backgrounds.

### Status Colors

**Status Color Palette**: The AppColors class defines status colors including success colors with statusSuccessDark (0xFF1E4620), statusSuccess (0xFF068463) for success actions, and statusSuccessLight (0xFFEAF6EA) for success backgrounds. Warning and error colors include statusWarningDark (0xFF5F2120), statusWarning (0xFFF05667) for error actions, and statusWarningLight (0xFFFEF4F4) for error backgrounds.

### Background Colors

**Background Color System**: The AppColors class provides background colors including uiBackgroundPrimary (0xFFFAFAFA) for main app backgrounds, uiBackgroundSecondary (0xFFFEFEFE) for card backgrounds, accentLight (0xFFE2E7E8) for light accent backgrounds, and accentExtraLight (0xFFF3F7F7) for extra light accent backgrounds.

## Typography System

### Font Families

**Font Family Configuration**: The AppTheme class defines font families with fontFamily set to 'Noto Sans' for English text and arabicFontFamily set to 'Tajawal' for Arabic text, providing locale-appropriate typography.

### Type Scale

#### Headings
- **H1**: 32px/33px (EN/AR), Bold - Page titles, main headings
- **H2**: 28px/29px (EN/AR), Bold - Section headers
- **H3**: 24px/25px (EN/AR), SemiBold - Subsection headers
- **H4**: 20px/21px (EN/AR), SemiBold - Component titles
- **H5**: 18px/19px (EN/AR), Medium - Small headers
- **H6**: 16px/17px (EN/AR), Medium - Smallest headers

#### Body Text
- **Text Large**: 18px/19px (EN/AR), Regular - Large body text, important content
- **Text Medium**: 16px/17px (EN/AR), Regular - Standard body text
- **Text Small**: 14px/15px (EN/AR), Regular - Small body text, metadata
- **Text XSmall**: 12px/13px (EN/AR), Regular - Caption text, labels

### Typography Usage

The typography system provides locale-aware text styling with automatic font selection and sizing adjustments.

**Locale-Aware Typography System:**

The text theme system provides intelligent typography with automatic locale adjustments:
- **ContextExtension**: Adds textTheme getter to BuildContext for easy access to _AppTextStyles instance
- **_AppTextStyles Class**: Private class that handles locale detection and provides text style hierarchy
- **Locale Detection**: _isLocaleArabic checks context.locale against Constants.localeAR for Arabic language identification
- **Adaptive Sizing**: h1 style uses 33px for Arabic (better readability) and 32px for other languages with FontWeight.w700
- **Medium Text Adaptation**: textMedium uses 17px for Arabic and 16px for other languages with FontWeight.w400
- **Dynamic Adjustment**: Automatically adjusts font sizes based on current locale for optimal readability across languages

This system ensures proper typography rendering with locale-specific optimizations for enhanced user experience.

**Typography Features:**
- **Locale-Aware**: Automatic font family and size selection based on language
- **Context Extension**: Easy access via `context.textTheme.h1`
- **Consistent Scaling**: Proportional sizing across different text styles
- **Accessibility**: Optimized for readability and screen reader compatibility

### Text Style Modifiers

Text style modifiers provide a fluent API for customizing typography with consistent weight and color variations.

**Text Style Extension System:**

The TextStyleModifiers extension provides convenient style modifications for consistent typography:
- **Weight Modifiers**: bold (FontWeight.w700), semiBold (FontWeight.w600), and medium (FontWeight.w500) for typography hierarchy
- **Color Modifiers**: greyPrimary for secondary text, accentGreenPrimary for brand-colored text, and statusWarning for warning states
- **Extension Pattern**: Uses copyWith() method to create new TextStyle instances with modified properties
- **Chainable Methods**: Allows method chaining for complex style combinations (e.g., textMedium.bold.accentGreenPrimary)
- **Semantic Naming**: Uses descriptive names that reflect purpose rather than specific values for maintainability
- **Consistent Application**: Ensures uniform text styling across the application with predefined modifiers

This extension system simplifies text styling while maintaining design consistency and reducing code duplication.

**Modifier Benefits:**
- **Fluent API**: Chain modifiers for complex styling: `context.textTheme.h1.bold.greyPrimary`
- **Consistent Colors**: Predefined color modifiers ensure design system compliance
- **Type Safety**: Compile-time checking prevents invalid style combinations
- **Reusability**: Common style patterns available throughout the app

## Spacing System

### Spacing Scale

**Spacing Scale System**: The AppSpacing class provides a consistent spacing scale based on a 4px unit, with xs (4px), sm (8px), md (12px), lg (16px), xl (24px), xxl (32px), and xxxl (48px) values for consistent layout spacing throughout the application.

### Usage Guidelines

**Consistent Spacing Usage**: Apply spacing using Padding with EdgeInsets.all(AppSpacing.lg) for 16px padding, SizedBox with AppSpacing.md for 12px vertical spacing between elements, and AppSpacing.xl for 24px spacing before action buttons to maintain consistent layout rhythm.

## Component Library

### 1. Buttons

#### Primary Button
**Primary Button Configuration**: AppButton with onTap callback, buttonText property, backgroundColor set to AppColors.greenAccentPrimary, and textStyle using context.textTheme.textMedium.bold with white color for primary actions.

#### Secondary Button
**Secondary Button Configuration**: AppButton with transparent backgroundColor, borderColor set to AppColors.greenAccentPrimary, and textStyle using context.textTheme.textMedium.bold.accentGreenPrimary for secondary actions.

#### Destructive Button
**Destructive Button Configuration**: AppButton with backgroundColor set to AppColors.statusWarning and white text color for delete or destructive actions.

### 2. Input Fields

#### Standard Input
**Standard Input Configuration**: AppInputField with controller for text management, hintText for placeholder text, label for field identification, and validator function for input validation.

#### Password Input
**Password Input Configuration**: AppInputField with obscureText set to true for password masking, suffixIcon with IconButton for visibility toggle using Icons.visibility and Icons.visibility_off, and setState to manage visibility state.

### 3. Cards and Containers

#### Standard Card
**Standard Card Configuration**: Card widget with elevation of 2, RoundedRectangleBorder shape with 12px border radius, and Padding using AppSpacing.lg for consistent internal spacing.

#### Elevated Container
**Elevated Container Configuration**: Container with BoxDecoration including white background color, 12px border radius, and BoxShadow with black color at 0.1 opacity, 8px blur radius, and Offset(0, 2) for subtle elevation effect.

### 4. Navigation Components

#### Tab Bar
**Tab Bar Configuration**: AppTabBar with tabController for tab management, tabList containing tab labels as string array, and onTabTapped callback function receiving index parameter for tab change handling.

#### Bottom Navigation
**Bottom Navigation Configuration**: BottomNavigationBar with BottomNavigationBarType.fixed, selectedItemColor using AppColors.greenAccentPrimary, unselectedItemColor using AppColors.greySecondary, and BottomNavigationBarItem array with icon and label properties.

## Layout Patterns

### 1. Screen Layout

**Standard Screen Layout Pattern**: The StandardScreenLayout class extends StatelessWidget with required title and body parameters, optional actions and floatingActionButton. It returns a Scaffold with AppBar containing title Text, actions list, white backgroundColor, AppColors.neutralBlack foregroundColor, and zero elevation. The body is wrapped in SafeArea for proper screen boundaries.

### 2. List Layout

**Standard List Layout Pattern**: The StandardListLayout class extends StatelessWidget with required items list and itemBuilder function, optional onRefresh callback and emptyState widget. It conditionally returns emptyState for empty lists, creates ListView.builder with itemCount, itemBuilder, and vertical padding using AppSpacing.md, and wraps with RefreshIndicator when onRefresh is provided.

## Responsive Design

### 1. Breakpoints

**Responsive Breakpoints System**: The Breakpoints class defines mobile (480px), tablet (768px), and desktop (1024px) breakpoints. The ResponsiveExtension on BuildContext provides isMobile, isTablet, and isDesktop getters using MediaQuery.of(this).size.width comparisons, plus screenWidth and screenHeight getters for responsive design.

### 2. Responsive Components

**Responsive Container Component**: The ResponsiveContainer class extends StatelessWidget with required child and optional width parameters for different screen sizes. It uses context.isMobile, context.isTablet conditions to set width with fallback values (double.infinity for mobile, 600 for tablet, 800 for desktop) and returns a Container with calculated width.

## Accessibility Guidelines

### 1. Semantic Markup

**Accessible Card Component**: The AccessibleCard class extends StatelessWidget with required title and content strings, optional onTap callback. It wraps content in Semantics widget with label, hint, and button properties, uses Card with InkWell for touch feedback, Padding with AppSpacing.lg, Column with CrossAxisAlignment.start, title Text using context.textTheme.h4.bold, SizedBox spacing, and content Text with greySecondary styling.

### 2. Color Contrast

**Color Contrast Checker Utility**: The ContrastChecker class provides static methods meetsWCAGAA() and meetsWCAGAAA() that calculate contrast ratios using _calculateContrastRatio() and compare against WCAG standards (4.5 for AA, 7.0 for AAA). The private _calculateContrastRatio() method computes luminance values and applies the contrast ratio formula (lighter + 0.05) / (darker + 0.05).

## Animation Guidelines

### 1. Standard Animations

**Animation System Configuration**: The AppAnimations class defines duration constants (fast: 200ms, normal: 300ms, slow: 500ms), curve constants (easeIn, easeOut, easeInOut), and static methods for common animations. The fadeIn() method returns a Tween<double> from 0.0 to 1.0 with CurvedAnimation, and slideUp() returns a Tween<Offset> from (0.0, 1.0) to Offset.zero with easeOut curve.

### 2. Page Transitions

**Page Transition System**: The AppPageTransitions class provides a static slideTransition<T>() method that returns PageRouteBuilder<T> with configurable begin Offset (default: 1.0, 0.0) and duration (default: AppAnimations.normal). It uses SlideTransition with Tween<Offset> from begin to Offset.zero, animated with CurvedAnimation using AppAnimations.easeOut curve.

## Design Tokens

### 1. Token Structure

**Design Tokens System**: The DesignTokens class provides structured design tokens including spacing Map with values from 'xs' (4.0) to 'xxl' (32.0), borderRadius Map with values from 'sm' (4.0) to 'round' (999.0), and shadows Map containing BoxShadow lists for 'sm', 'md', and 'lg' with varying blurRadius (2, 4, 8) and offset values (0,1), (0,2), (0,4) using Color(0x1A000000) for consistent shadow appearance.

## Implementation Guidelines

### 1. Component Creation

When creating new components:

1. **Follow naming conventions**: Use descriptive, consistent names
2. **Implement accessibility**: Include semantic markup and labels
3. **Support theming**: Use design tokens and theme colors
4. **Document usage**: Provide clear documentation and examples
5. **Test thoroughly**: Include unit tests and accessibility tests

### 2. Design System Maintenance

1. **Regular audits**: Review components for consistency and usage
2. **Version control**: Track changes and maintain backwards compatibility
3. **Documentation updates**: Keep documentation current with changes
4. **Team communication**: Share updates and changes with the team

### 3. Quality Assurance

1. **Visual testing**: Use golden tests for visual regression testing
2. **Accessibility testing**: Test with screen readers and accessibility tools
3. **Performance testing**: Monitor component performance and optimization
4. **Cross-platform testing**: Ensure consistency across platforms

## Related Documentation

- [Theme System](../core/theme-system.md)
- [Shared Widgets](shared-widgets.md)
- [Coding Standards](../development/coding-standards.md)
- [Testing Guide](../development/testing-guide.md)
