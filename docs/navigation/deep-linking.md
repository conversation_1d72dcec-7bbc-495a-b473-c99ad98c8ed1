# Deep Linking

This document covers the deep linking implementation in the NSP Flutter application, including URL scheme configuration, route handling, and best practices for creating linkable content.

## Table of Contents

- [Overview](#overview)
- [URL Structure](#url-structure)
- [Route Configuration](#route-configuration)
- [Platform Configuration](#platform-configuration)
- [Deep Link Handling](#deep-link-handling)
- [Query Parameters](#query-parameters)
- [Authentication Handling](#authentication-handling)
- [Testing Deep Links](#testing-deep-links)
- [Best Practices](#best-practices)

## Overview

Deep linking allows users to navigate directly to specific content within the NSP app through URLs. This enables sharing of specific courses, lessons, or app states, and supports features like email links, push notifications, and web integration.

### Key Features

- **Direct Content Access**: Link directly to courses, lessons, or specific app sections
- **State Preservation**: Maintain navigation state when following deep links
- **Authentication Handling**: Graceful handling of protected content
- **Query Parameter Support**: Pass additional context through URL parameters
- **Cross-Platform Support**: Works on both Android and iOS

## URL Structure

### Base URL Pattern

**URL Format**: `nsp://app/{route}?{parameters}` where route represents the destination and parameters provide additional context

### Route Hierarchy

**Route Structure:**
- **/** - Root/Home page
- **home** - Home page
- **catalog** - Course catalog (with optional ?tab=trainings parameter)
- **training/{id}** - Training details with specific ID
  - **lesson/{lessonId}** - Specific lesson within training
- **learning-track/{id}** - Learning track details with specific ID
- **my-learnings** - User's enrolled courses
- **profile** - User profile page
- **search** - Search page (with optional ?q={query} parameter)
- **login** - Authentication page

## Route Configuration

### Basic Route Setup

**GoRouter Deep Link Configuration:**

The GoRouter setup demonstrates comprehensive deep linking with parameterized routes:
- **Root Configuration**: GoRouter with initialLocation set to Routes.rootPage.path for app startup
- **Base Route Structure**: Root GoRoute with path '/', name from Routes.rootPage.name, and RootPage builder
- **Nested Route Pattern**: Child routes defined within routes array for hierarchical URL structure
- **Training Details Route**: Path '/training/:trainingId' with parameter extraction using state.pathParameters['trainingId'] and TrainingDetailsPage builder
- **Learning Track Route**: Path '/learning-track/:learningTrackId' with similar parameter handling for LearningTrackDetailsPage
- **Parameter Handling**: Uses state.pathParameters to extract URL parameters with null assertion operator for required parameters
- **Builder Functions**: Each route includes builder function accepting context and state for page construction

This configuration enables direct navigation to specific content via URLs with proper parameter extraction.

        // Catalog with optional tab parameter
        GoRoute(
          path: '/catalog',
          name: Routes.catalogPage.name,
          builder: (context, state) {
            final tab = state.uri.queryParameters[RouterQueryConstants.tab];
            return CatalogPage(tab: tab);
          },
        ),

        // Search with query parameter
        GoRoute(
          path: '/search',
          name: Routes.searchPage.name,
          builder: (context, state) {
            final query = state.uri.queryParameters['q'];
            return SearchPage(initialQuery: query);
          },
        ),
      ],
    ),
  ],
);

### Nested Route Parameters

**Nested Route Parameters Implementation:**

- **Complex Route Definition**: Create GoRoute with path '/training/:trainingId/lesson/:lessonId' and name Routes.lessonPage.name
- **Parameter Extraction**: Extract trainingId and lessonId from state.pathParameters and sectionId from state.uri.queryParameters
- **Page Construction**: Pass extracted parameters to LessonPage constructor for proper page initialization
- **Deep Link Usage**: Use router.go('/training/123/lesson/456?section=intro') to navigate directly to specific lesson with section parameter

## Platform Configuration

### Android Configuration

#### AndroidManifest.xml

**Android Manifest Deep Link Configuration:**

The AndroidManifest.xml activity configuration enables deep linking support:
- **Activity Declaration**: MainActivity with android:exported="true" for external access and android:launchMode="singleTop" to prevent duplicate instances
- **Launch Theme**: Uses @style/LaunchTheme for consistent app startup appearance
- **Standard Launch Intent**: First intent-filter with MAIN action and LAUNCHER category for normal app launch from home screen
- **Deep Link Intent Filter**: Second intent-filter with android:autoVerify="true" for automatic link verification
- **View Action**: android.intent.action.VIEW action for handling URL clicks
- **Categories**: DEFAULT and BROWSABLE categories to enable browser and system link handling
- **Custom Scheme**: data element with android:scheme="nsp" for custom URL scheme (nsp://...)

This configuration allows the app to handle both standard launches and custom URL scheme deep links.

    <!-- HTTPS deep links -->
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="https"
              android:host="nsp.gov.sa" />
    </intent-filter>
</activity>

<!-- Queries for external app integration -->
<queries>
    <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="https" />
    </intent>
    <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="sms" />
    </intent>
    <intent>
        <action android:name="android.intent.action.VIEW" />
        <data android:scheme="tel" />
    </intent>
</queries>

### iOS Configuration

#### Info.plist

**iOS Deep Link Configuration:**
- **CFBundleURLTypes**: Array containing URL scheme configurations
- **First Scheme**: "nsp.deep.link" identifier with "nsp" scheme for custom URL handling
- **Second Scheme**: "nsp.https.link" identifier with "https" scheme for web links
- **Associated Domains**: "applinks:nsp.gov.sa" for universal links support

## Deep Link Handling

### Initial Route Handling

**Initial Route Handling Implementation:**

- **RootPage Class**: Create StatelessWidget with StreamBuilder<bool> listening to GetIt.instance<AuthTokenProvider>().authStateStream
- **Authentication Check**: Return BuildLoader if no data, otherwise call _handleInitialRoute with isAuthenticated state
- **Route Protection Logic**: In _handleInitialRoute, get currentLocation from GoRouterState.of(context).uri.toString() and check _isProtectedRoute
- **Unauthenticated Redirect**: Store intended destination with _storeIntendedDestination and redirect to Routes.login.name using WidgetsBinding.instance.addPostFrameCallback
- **Protected Route Detection**: _isProtectedRoute method checks if route starts with any path in protectedRoutes array ('/my-learnings', '/profile', '/training/', '/learning-track/')
- **Destination Storage**: _storeIntendedDestination uses GetIt.instance<FlutterSecureStorage>().write with key 'intended_destination' for later retrieval

### Post-Authentication Navigation

**Post-Authentication Navigation Implementation:**

- **AuthBloc Method**: Create _handleSuccessfulLogin method in AuthBloc that reads intended destination from _secureStorage with key 'intended_destination'
- **Destination Check**: If intendedDestination exists, delete it from storage and navigate using router.go(intendedDestination)
- **Default Navigation**: If no stored destination, navigate to default route using router.goNamed(Routes.homePage.name)

## Query Parameters

### Supported Query Parameters

| Parameter | Purpose | Example |
|-----------|---------|---------|
| `tab` | Specify catalog tab | `/catalog?tab=trainings` |
| `q` | Search query | `/search?q=flutter` |
| `section` | Specific section | `/training/123?section=intro` |
| `filter` | Apply filters | `/catalog?filter=popular` |
| `sort` | Sort order | `/catalog?sort=newest` |

### Query Parameter Handling

**Query Parameter Handling Implementation:**

- **CatalogPage Class**: Create StatefulWidget with optional tab parameter and build method that gets GoRouterState.of(context)
- **Parameter Extraction**: Extract filter and sort from state.uri.queryParameters for additional query parameter handling
- **BLoC Integration**: Create BlocProvider with CatalogBloc and add LoadCatalog event using tab ?? 'trainings', filter, and sort parameters

### Building Deep Links

**Building Deep Links Implementation:**

- **DeepLinkBuilder Class**: Create static methods for building different types of deep links with proper URI construction
- **Training Link Builder**: buildTrainingLink method creates Uri with path '/training/$trainingId' and optional section query parameter
- **Search Link Builder**: buildSearchLink method creates Uri with path '/search' and query parameters for 'q' and optional 'filter'
- **Catalog Link Builder**: buildCatalogLink method creates Uri with path '/catalog' and optional tab and filter query parameters using RouterQueryConstants.tab
- **Usage Examples**: Use DeepLinkBuilder.buildTrainingLink('123', section: 'intro') and DeepLinkBuilder.buildSearchLink('flutter', filter: 'popular') for link generation

## Authentication Handling

### Protected Route Middleware

**Protected Route Middleware Implementation:**

- **AuthGuard Class**: Create static isProtectedRoute method that checks if route starts with any path in protectedPaths array
- **Route Protection Check**: protectedPaths includes '/my-learnings', '/profile', '/training/', '/learning-track/' for authentication-required routes
- **Protected Route Handler**: handleProtectedRoute method gets token from GetIt.instance<AuthTokenProvider>().getToken()
- **Authentication Logic**: If token is null, store route in FlutterSecureStorage with key 'intended_destination' and return Routes.login.name, otherwise return null to allow access

### Route Guard Implementation

**Route Guard Implementation:**

- **GoRoute Configuration**: Define GoRoute with path '/training/:trainingId' and name Routes.trainingDetailsPage.name
- **Redirect Middleware**: Use redirect callback that gets route from state.uri.toString() and calls AuthGuard.handleProtectedRoute(route)
- **Builder Logic**: Extract trainingId from state.pathParameters['trainingId'] and return TrainingDetailsPage with trainingId parameter

## Testing Deep Links

### Android Testing

**Android Deep Link Testing Commands:**
- **Custom Scheme Test**: Use `adb shell am start -W -a android.intent.action.VIEW -d "nsp://app/training/123" com.example.nsp` to test custom URL schemes
- **HTTPS Link Test**: Use `adb shell am start -W -a android.intent.action.VIEW -d "https://nsp.gov.sa/training/123" com.example.nsp` to test universal links

### iOS Testing

**iOS Deep Link Testing Commands:**
- **Custom Scheme Test**: Use `xcrun simctl openurl booted "nsp://app/training/123"` to test custom URL schemes in simulator
- **Universal Link Test**: Use `xcrun simctl openurl booted "https://nsp.gov.sa/training/123"` to test universal links in simulator

### Flutter Testing

**Flutter Testing Implementation:**

- **Test Setup**: Create testWidgets with GoRouter configuration containing GoRoute with path '/training/:trainingId'
- **Router Configuration**: Define builder that extracts trainingId from state.pathParameters and returns TrainingDetailsPage
- **Widget Testing**: Use tester.pumpWidget with MaterialApp.router and routerConfig: router for testing setup
- **Navigation Testing**: Call router.go('/training/123') followed by tester.pumpAndSettle() to complete navigation
- **Verification**: Use expect(find.byType(TrainingDetailsPage), findsOneWidget) to verify correct page is displayed

## Best Practices

### 1. URL Design

**URL Design Best Practices:**

- **✅ Good Practice**: Use clear, hierarchical URLs like '/training/123', '/training/123/lesson/456', and '/catalog?tab=trainings&filter=popular' for intuitive navigation
- **❌ Bad Practice**: Avoid unclear or flat URLs like '/page?id=123&type=training' or '/training_123_lesson_456' that are hard to understand and maintain

### 2. Parameter Validation

**Parameter Validation Best Practices:**

- **✅ Good Practice**: Validate parameters by checking if trainingId is null or empty, returning ErrorPage with appropriate message for invalid cases
- **❌ Bad Practice**: Avoid assuming parameters are valid with direct non-null assertion (!) which can throw exceptions and crash the app

### 3. Error Handling

**Error Handling Best Practices:**

- **Graceful Error Handling**: Use try-catch block around parameter extraction and page construction
- **Error Page**: Return ErrorPage with descriptive message 'Failed to load training' and onRetry callback that navigates to Routes.catalogPage.name for recovery

### 4. State Preservation

**State Preservation Best Practices:**

- **StatefulShellRoute Configuration**: Use StatefulShellRoute.indexedStack with AppBottomNavBar builder to preserve navigation state across tabs
- **Nested Route Structure**: Define nested routes within StatefulShellBranch where child routes like '/training/:trainingId' preserve parent state
- **State Management**: Nested routes maintain parent navigation context while allowing deep linking to specific content within tabs

### 5. Analytics Integration

**Analytics Integration Best Practices:**

- **DeepLinkAnalytics Class**: Create static trackDeepLinkUsage method that tracks analytics.track('deep_link_opened') with route, parameters, and timestamp data
- **Route Handler Integration**: Call DeepLinkAnalytics.trackDeepLinkUsage in GoRoute builder with route pattern and extracted parameters for usage tracking
- **Data Collection**: Track route patterns like '/training/:trainingId' with actual parameter values for analytics insights

## Related Documentation

- [Routing System](routing-system.md) - GoRouter configuration and setup
- [Navigation Patterns](navigation-patterns.md) - Navigation best practices
- [Authentication](../features/auth.md) - User authentication flow
- [Build & Deployment](../development/build-deployment.md) - Platform-specific configuration

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
