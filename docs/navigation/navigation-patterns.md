# Navigation Patterns

Navigation patterns in the NSP app using GoRouter. This guide shows you the most common navigation scenarios and how to implement them correctly.

## Quick Navigation Guide

| Need to... | Use This | Example |
|------------|----------|---------|
| **Go to a page** | `context.pushNamed()` | `context.pushNamed('/training/123')` |
| **Replace current page** | `context.pushReplacementNamed()` | Next lesson in sequence |
| **Go back** | `context.pop()` | Close modal, back button |
| **Pass data** | `extra` parameter | `extra: trainingId` |
| **Switch bottom tab** | `navigationShell.goBranch()` | Switch to catalog tab |

## Common Navigation Patterns

### 1. Simple Page Navigation

**Simple Page Navigation Examples:**

- **Training Details Navigation**: Use context.pushNamed with Routes.trainingDetails.name and pass trainingId via pathParameters map
- **Data-Rich Navigation**: Use context.pushNamed with Routes.lessonPage.name and pass complex objects like LessonParams via the extra parameter
- **Back Navigation**: Use context.pop() for simple back navigation

### 2. Bottom Tab Navigation

The NSP app has 4 main tabs that preserve their navigation state:

**Bottom Tab Navigation Examples:**

- **Tab Switching**: Use navigationShell.goBranch(1) to switch to specific tab (e.g., Catalog tab at index 1)
- **Tab Reset**: Use navigationShell.goBranch with currentIndex and initialLocation: true to reset active tab to its root page

**Tab Indices:**
- 0: Home
- 1: Catalog
- 2: My Learnings
- 3: Profile

### 3. Modal Presentations

For overlays, forms, and temporary content:

**Modal Presentation Examples:**

- **Login Modal**: Use context.pushNamed with Routes.login.name for slide-up modal presentation
- **Full-Screen Details**: Use context.pushNamed with Routes.trainingDetails.name and pathParameters for full-screen modal content

### 4. Sequential Navigation (Lessons)

For multi-step flows like course lessons:

**Sequential Navigation Examples:**

- **Next Lesson**: Use context.pushReplacementNamed with Routes.videoLesson.name and LessonParams containing nextLesson and LessonNavigationType.forward for right-slide transition
- **Previous Lesson**: Use context.pushReplacementNamed with Routes.articleLesson.name and LessonParams containing previousLesson and LessonNavigationType.backward for left-slide transition

## Navigation Patterns

### 1. Bottom Navigation Pattern

The main app navigation uses a bottom navigation bar with four tabs, each maintaining its own navigation stack.

**Bottom Navigation Bar Implementation:**

The AppBottomNavBar class provides the main navigation interface with intelligent state management:
- **Class Structure**: Extends StatelessWidget with StatefulNavigationShell parameter for managing tab navigation
- **Branch Navigation Method**: _goBranch handles tab switching with special logic for Home tab that triggers TrainingsBloc refresh via GetIt dependency injection
- **State Reset Logic**: When navigating to Home tab (index 0), automatically refreshes training data to ensure fresh content
- **Initial Location Support**: Uses initialLocation parameter to reset tab to root when tapping the currently active tab
- **UI Structure**: Returns Scaffold with navigationShell as body and BottomNavigationBar with fixed type, current index tracking, and tap handling
- **Tab Configuration**: BottomNavigationBar items array contains navigation items for Home, Catalog, My Learnings, and Profile tabs

This implementation ensures smooth tab navigation with proper state management and data refresh capabilities.

### 2. Modal Presentation Pattern

For forms, details, and temporary content that should overlay the main interface.

**Modal Presentation Pattern Implementation:**

- **Slide-Up Modal**: Use router.pushNamed with Routes.loginPage.name and pageBuilder returning SlideBottomToTopTransition wrapping LoginPage widget
- **Full-Screen Modal**: Use router.pushNamed with Routes.trainingDetailsPage.name and pass trainingId via extra parameter

### 3. Sequential Flow Pattern

For multi-step processes like course consumption or onboarding.

**Sequential Flow Pattern Implementation:**

- **Forward Navigation**: Use router.pushReplacementNamed with Routes.videoLessonPage.name and LessonParams containing nextLesson and LessonNavigationType.forward
- **Backward Navigation**: Use router.pushReplacementNamed with Routes.articlePage.name and LessonParams containing previousLesson and LessonNavigationType.backward

### 4. Contextual Navigation Pattern

Navigation that adapts based on user state or content type.

**Contextual Navigation Pattern Implementation:**

- **Authentication-Based Navigation**: Use conditional logic to check isAuthenticated state and navigate to Routes.myLearningPage.name or Routes.loginPage.name accordingly
- **Content-Type Navigation**: Use switch statement on lessonType enum to navigate to appropriate routes (Routes.videoLessonPage.name, Routes.articlePage.name, or Routes.quizPage.name) with lessonParams via extra parameter

## Custom Transitions

### 1. Slide Bottom to Top

Used for modal presentations and overlays.

**Slide Bottom to Top Transition Class:**

The SlideBottomToTopTransition creates smooth modal-style animations for overlay presentations:
- **Class Structure**: Extends CustomTransitionPage<void> with required child parameter using super constructor
- **Animation Configuration**: Uses transitionsBuilder callback to define custom transition behavior
- **Offset Definition**: Sets begin offset (0.0, 1.0) for bottom starting position and end offset (Offset.zero) for final position
- **Curve Application**: Applies Curves.easeInOut for natural acceleration and deceleration
- **Tween Chain**: Creates Tween with begin/end offsets and chains with CurveTween for smooth interpolation
- **Animation Driver**: Uses animation.drive(tween) to create offsetAnimation for position control
- **Slide Transition**: Returns SlideTransition widget with position animation driving the child widget movement

This transition provides elegant bottom-to-top slide effects ideal for modal presentations and overlay screens.

### 2. Multi-Directional Transition

Adapts animation direction based on navigation context.

**Multi-Directional Transition Implementation:**

The MultiTransition class provides context-aware animations that adapt based on navigation direction:
- **Class Structure**: Extends CustomTransitionPage<void> with required child and forwardType parameters, includes 300ms reverse transition duration
- **Animation Status Detection**: Checks animation.status for AnimationStatus.reverse to determine if this is a pop/close action
- **Reverse Animation Logic**: For pop actions, always slides down (0.0, 1.0 to Offset.zero) regardless of forward type for consistent close behavior
- **Forward Animation Switch**: Uses switch statement on forwardType to determine animation direction:
  - **LessonNavigationType.forward**: Slides from right (1.0, 0.0) for next lesson navigation
  - **LessonNavigationType.backward**: Slides from left (-1.0, 0.0) for previous lesson navigation
  - **Default case**: Slides up from bottom (0.0, 1.0) for standard navigation
- **Curve Application**: All animations use CurvedAnimation with Curves.easeInOut for smooth transitions
- **Type Property**: Stores LessonNavigationType forwardType as final field for animation direction control

This transition system provides intuitive directional feedback for sequential content navigation like lesson flows.

### 3. Transition Usage Examples

**Transition Usage Examples:**

- **Modal Presentation**: Use pageBuilder returning SlideBottomToTopTransition with LoginPage child for slide-up modal effect
- **Contextual Lesson Navigation**: Use pageBuilder extracting LessonParams from state.extra and returning MultiTransition with ArticleLesson child and forwardType from lessonParams.lessonNavigationType
- **Standard Page Transition**: Use builder returning HomePage widget for default transition behavior

## Bottom Navigation

### StatefulShellRoute Configuration

**StatefulShellRoute Configuration for Bottom Navigation:**

The StatefulShellRoute.indexedStack creates a comprehensive bottom navigation system with four main tabs:
- **Builder Configuration**: Uses AppBottomNavBar as the shell widget that wraps the navigationShell for consistent UI
- **Home Tab Branch**: Contains HomePage with AuthStateEnum handling and nested SectorsPage route for sector browsing
- **Catalog Tab Branch**: Features CatalogPage with tab query parameter support for filtering content types
- **My Learnings Tab Branch**: Provides MyLearningsPage for user's enrolled and completed courses
- **Profile Tab Branch**: Contains ProfilePage for user account management and settings
- **Navigator Keys**: Each branch uses dedicated navigator keys (_homePageNavigatorKey, _catalogNavigatorKey, etc.) for independent navigation stacks
- **Route Structure**: Each branch defines GoRoute configurations with path, name, and builder properties for proper page construction
- **Data Handling**: Routes extract data from state.extra and state.uri.queryParameters with appropriate fallback values

This configuration ensures each tab maintains its own navigation history while preserving the bottom navigation bar across all screens.

### Tab State Management

**Tab State Management Examples:**

- **Tab Navigation**: Use navigationShell.goBranch(tabIndex) to switch to specific tab by index
- **Tab Reset**: Use navigationShell.goBranch with tabIndex and initialLocation: true to reset tab to its root page
- **Current Tab Check**: Access navigationShell.currentIndex to get the currently active tab index
- **Navigator Access**: Use _homePageNavigatorKey.currentState to access tab-specific navigator state

## Data Passing Patterns

### When to Use Each Method

| Data Type | Method | Example | Use Case |
|-----------|--------|---------|----------|
| **Simple ID** | Path parameters | `/training/123` | URLs, deep linking |
| **Optional filters** | Query parameters | `?tab=trainings&sort=newest` | Search, filters |
| **Complex objects** | Extra parameter | `LessonParams(lesson, section)` | Rich data, BLoCs |

### 1. Path Parameters (Simple Data)

**Best for**: IDs, simple values that should appear in URL

**Path Parameters Implementation:**

- **Navigation with ID**: Use context.pushNamed with Routes.trainingDetails.name and pathParameters map containing 'trainingId': '123'
- **Route Builder Reception**: Define GoRoute with path '/training/:trainingId' and builder extracting trainingId from state.pathParameters['trainingId'] to pass to TrainingDetailsPage constructor

### 2. Query Parameters (Optional Data)

**Best for**: Filters, search terms, optional settings

**Query Parameters Implementation:**

- **Navigation with Filters**: Use context.pushNamed with Routes.catalog.name and queryParameters map containing 'tab', 'filter', and 'sort' values
- **Parameter Reception**: Extract values from state.uri.queryParameters with null-coalescing operator for defaults (e.g., 'tab' ?? 'trainings')

### 3. Extra Objects (Complex Data)

**Best for**: Complex objects, BLoCs, multiple related values

**Extra Objects Implementation:**

- **Complex Data Passing**: Use context.pushNamed with Routes.videoLesson.name and extra parameter containing LessonParams object with lesson, section, trainingConsumptionBloc, and isCompleted properties
- **Safe Parameter Reception**: Cast state.extra as LessonParams? with null check, returning ErrorPage if null or VideoLessonPage with params if valid

### 4. Real-World Examples

**Real-World Navigation Examples:**

- **Training Card Tap**: Use onTap callback with context.pushNamed to Routes.trainingDetails.name, passing training.id via pathParameters
- **Catalog Tab Selection**: Use onTap callback with context.pushNamed to Routes.catalog.name, passing 'tab': 'learning-tracks' via queryParameters
- **Lesson Start**: Use onTap callback with context.pushNamed to Routes.articleLesson.name, passing LessonParams with firstLesson, section, and trainingConsumptionBloc via extra parameter

### 4. Type-Safe Data Passing

**Type-Safe Data Passing Implementation:**

- **Parameter Class Definition**: Create LessonParams class with final fields for lesson, section, trainingConsumptionBloc, and lessonNavigationType with const constructor and default value for lessonNavigationType
- **Route Builder Usage**: In pageBuilder, cast state.extra as LessonParams? with null validation, returning SlideBottomToTopTransition with ErrorPage if null, or MultiTransition with VideoLesson and forwardType from params.lessonNavigationType if valid

## Navigation Extensions

### Custom Navigation Methods

**Navigation Extension Methods for GoRouter:**

The Navigator extension enhances GoRouter with additional navigation utilities:
- **popUntil Method**: Accepts a predicate function that evaluates RouteMatch objects and continuously pops routes until the condition is satisfied, with canPop() safety checks and routerDelegate.currentConfiguration.last for current route access
- **popAllRoutes Method**: Provides a convenient way to clear the entire navigation stack by repeatedly calling pop() while canPop() returns true
- **Safety Mechanisms**: Both methods include canPop() validation to prevent navigation errors when no routes are available to pop
- **Flexible Logic**: popUntil allows custom conditions for determining when to stop popping routes, enabling complex navigation scenarios

These extensions provide commonly needed navigation stack manipulation operations that enhance GoRouter's built-in capabilities.

### Usage Examples

**Navigation Extension Usage Examples:**

- **Pop Until Home**: Use router.popUntil with predicate checking route.matchedLocation against Routes.homePage.path
- **Clear Stack and Navigate**: Use router.popAllRoutes() followed by router.goNamed(Routes.homePage.name) to clear navigation stack and go to specific page
- **Conditional Pop**: Use router.popUntil with predicate checking route.name against Routes.catalogPage.name

## Best Practices

### 1. Navigation State Management

**Navigation State Management Best Practices:**

- **✅ Good Practice**: Use StatefulShellRoute.indexedStack with builder returning AppBottomNavBar(navigationShell) and branches array for automatic state management
- **❌ Bad Practice**: Avoid manual bottom navigation state management with StatefulWidget and manual currentIndex tracking

### 2. Error Handling

**Error Handling Best Practices:**

- **✅ Good Practice**: Use nullable cast (state.extra as LessonParams?) with null check, returning SlideBottomToTopTransition with ErrorPage for invalid parameters
- **❌ Bad Practice**: Avoid direct casting (state.extra as LessonParams) without null checking, which can throw exceptions

### 3. Transition Consistency

**Transition Consistency Best Practices:**

- **Consistent Modal Transitions**: Use SlideBottomToTopTransition consistently for modal content like LoginPage
- **Context-Appropriate Transitions**: Use MultiTransition with lessonParams.lessonNavigationType for directional lesson navigation

### 4. Deep Linking Support

**Deep Linking Support Best Practices:**

- **Hierarchical URL Structure**: Use GoRoute with path '/training/:trainingId/lesson/:lessonId' and extract both trainingId and lessonId from state.pathParameters to pass to LessonPage constructor

## Common Navigation Scenarios

### 1. Authentication-Based Navigation

**Authentication-Based Navigation Implementation:**

- **Protected Page Navigation**: Create navigateToProtectedPage function that checks context.read<AuthBloc>().state is AuthSuccess and navigates to Routes.myLearnings.name if authenticated or Routes.login.name if not
- **Post-Login Redirect**: Use BlocListener<AuthBloc, AuthState> with listener checking for AuthSuccess state and calling context.pushReplacementNamed(Routes.home.name) to redirect after successful login

### 2. Lesson Flow Navigation

**Lesson Flow Navigation Implementation:**

- **Next Lesson Logic**: Create goToNextLesson function that calls getNextLesson() and uses context.pushReplacementNamed with getRouteForLessonType(nextLesson.type) and LessonParams containing lesson and LessonNavigationType.forward
- **Course Completion**: When nextLesson is null, use context.popUntil with predicate checking route.name == Routes.trainingDetails.name to return to course overview

### 3. Tab Navigation with State

**Tab Navigation with State Implementation:**

- **Tab Switch with Refresh**: Create goToCatalogTab function that calls navigationShell.goBranch(1) to switch to catalog tab and context.read<CatalogBloc>().add(RefreshCatalog()) to trigger data refresh
- **Smart Tab Handling**: Create onTabTap function that checks if index == navigationShell.currentIndex and calls goBranch with initialLocation: true for tab reset or goBranch(index) for tab switching

## Navigation Troubleshooting

### Common Issues & Solutions

| Problem | Cause | Solution |
|---------|-------|----------|
| **Page not found** | Wrong route name | Check `Routes.routeName.name` |
| **Data not passed** | Missing `extra` parameter | Add `extra: yourData` |
| **Back button doesn't work** | Using `pushReplacement` | Use `push` instead |
| **Tab state lost** | Not using `StatefulShellRoute` | Use proper tab configuration |
| **Deep link fails** | Route not defined | Add route to router configuration |

### Debug Navigation Issues

**Debug Navigation Issues Implementation:**

- **Route Debugging**: Create debugCurrentRoute function that gets GoRouterState.of(context).uri.toString() and prints current location with appPrint
- **Parameter Debugging**: Create debugNavigation function that prints state.path, state.pathParameters, state.uri.queryParameters, and state.extra for comprehensive navigation debugging
- **Safe Navigation**: Create safeNavigate function with try-catch block around context.pushNamed, logging errors with appPrint and falling back to context.goNamed(Routes.home.name)

### Navigation Best Practices

**Navigation Best Practices Implementation:**

- **Parameter Validation**: Always cast state.extra as LessonParams? with null check, returning ErrorPage with appropriate message if validation fails
- **Named Routes**: Use context.pushNamed(Routes.trainingDetails.name) instead of context.push('/training/123') for better maintainability and type safety
- **Back Button Handling**: Use WillPopScope with onWillPop callback that calls context.pop() and returns false to prevent default behavior while implementing custom back logic

## Related Documentation

- [Routing System](routing-system.md) - GoRouter configuration and setup
- [Deep Linking](deep-linking.md) - Deep linking implementation
- [State Management](../architecture/state-management.md) - BLoC patterns
- [UI Components](../ui-components/shared-widgets.md) - Reusable navigation components

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
