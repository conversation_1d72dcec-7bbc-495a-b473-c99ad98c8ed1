# Routing System with GoRouter

## Table of Contents
- [Overview](#overview)
- [Router Architecture](#router-architecture)
- [Navigation Patterns](#navigation-patterns)
- [Bottom Navigation](#bottom-navigation)
- [Custom Transitions](#custom-transitions)
- [Deep Linking](#deep-linking)
- [Route Guards](#route-guards)
- [Testing Strategy](#testing-strategy)

## Overview

The NSP mobile application uses **GoRouter** for declarative navigation management, providing a robust foundation for type-safe routing, deep linking support, and sophisticated navigation patterns. The system integrates seamlessly with the app's architecture while maintaining performance and user experience standards.

### Key Features
- **Type-Safe Navigation**: Enum-based route definitions prevent navigation errors
- **Deep Linking Support**: Comprehensive URL handling for external navigation
- **Custom Transitions**: Smooth animations and transitions between screens
- **Bottom Navigation**: Persistent navigation with independent tab stacks
- **Route Guards**: Authentication and role-based access control
- **State Management**: Integration with BLoC pattern for navigation state

## Router Architecture

### System Structure

```mermaid
graph TB
    subgraph "🧭 Navigation System"
        Router[GoRouter Core<br/>• Route Configuration<br/>• Type Safety<br/>• Deep Link Handling]

        subgraph "📱 Navigation Components"
            BottomNav[Bottom Navigation<br/>• StatefulShellRoute<br/>• Independent Stacks<br/>• Persistent UI]
            Transitions[Custom Transitions<br/>• Slide Animations<br/>• Multi-directional<br/>• Modal Presentations]
            Guards[Route Guards<br/>• Authentication<br/>• Role-based Access<br/>• Redirect Logic]
        end

        subgraph "🎯 Route Management"
            Routes[Routes Enum<br/>• Type Safety<br/>• Path Generation<br/>• Centralized Definitions]
            Params[Parameter Handling<br/>• Path Parameters<br/>• Query Parameters<br/>• Extra Objects]
            DeepLinks[Deep Linking<br/>• URL Structure<br/>• External Navigation<br/>• App State Restoration]
        end
    end

    subgraph "📄 Application Pages"
        Features[Feature Pages<br/>• Home<br/>• Catalog<br/>• Profile<br/>• Learning]
        Modals[Modal Pages<br/>• Login<br/>• Details<br/>• Forms]
    end

    Router --> BottomNav
    Router --> Transitions
    Router --> Guards

    Routes --> Params
    Routes --> DeepLinks

    BottomNav --> Features
    Transitions --> Modals
    Guards --> Features

    style Router fill:#e3f2fd
    style BottomNav fill:#f3e5f5
    style Transitions fill:#e8f5e8
    style Routes fill:#fff3e0
    style Features fill:#fce4ec
```

### Component Architecture

**Core Navigation Files**:
- **router.dart**: Main router configuration with route definitions and setup
- **root_page.dart**: Initial routing logic and app entry point handling
- **app_bottom_nav_bar.dart**: Bottom navigation implementation with shell routing
- **Custom Transitions**: Specialized animation classes for smooth page transitions

## Router Configuration

### Main Router Setup

The router configuration uses an enum-based approach for type-safe route management with GoRouter integration.

**Router Setup Architecture:**

The router configuration establishes a type-safe, centralized navigation system using GoRouter with enum-based route management and dependency injection integration.

**Router Components:**
- **Routes Enum**: Defines all available routes in the app for type-safe navigation
- **Dependency Injection**: Router registered as singleton with GetIt for global access
- **Debug Integration**: Alice HTTP inspector integration for development builds with conditional navigator key
- **Initial Location**: App starts at rootPage route for consistent initialization

**Route Management:**
- **Type Safety**: Enum-based route definitions prevent navigation errors and provide IDE autocomplete
- **Centralized Configuration**: All routes defined in single location for easy maintenance
- **Path Resolution**: Routes enum provides path property for URL generation
- **Global Access**: Router accessible throughout app via GetIt dependency injection

**Router Features:**
- **Type-Safe Routes**: Enum-based route definitions prevent typos and provide autocomplete
- **Debug Integration**: Alice HTTP inspector integration for development builds
- **Dependency Injection**: Router registered as singleton for global access
- **Initial Location**: Configurable starting route for the application

### Route Path Extension

The route extension provides automatic path generation from enum values with special handling for the root route.

**Route Path Extension Implementation:**

The RoutesExtension provides automatic path generation from enum values with intelligent handling:
- **Root Route Special Case**: Returns "/" for the rootPage route to handle the application's base URL
- **Automatic Path Generation**: Uses toString().split('.').last to convert enum names to URL paths (e.g., Routes.homePage becomes "homePage")
- **Consistent Naming**: Ensures URL paths match enum naming conventions without manual string management
- **Maintainability**: Eliminates the need to manually maintain separate path strings for each route

This extension simplifies route management by automatically generating consistent URL paths from the enum structure.

**Extension Benefits:**
- **Automatic Path Generation**: Converts enum names to URL paths automatically
- **Root Route Handling**: Special case for the root path ("/")
- **Consistency**: Ensures consistent naming between enum and URL paths
- **Maintainability**: Reduces manual path string management

## Bottom Navigation with StatefulShellRoute

### Shell Route Configuration

The shell route configuration enables persistent bottom navigation with independent navigation stacks for each tab.

**Shell Route Configuration Structure:**

The StatefulShellRoute.indexedStack creates a persistent bottom navigation system with the following architecture:
- **Builder Function**: Returns AppBottomNavBar widget that wraps the navigationShell for consistent bottom navigation UI
- **Branch Structure**: Each StatefulShellBranch represents a tab with its own navigation stack and dedicated navigator key
- **Route Hierarchy**: Each branch contains GoRoute definitions with path, name, and builder properties for page construction
- **State Handling**: HomePage builder extracts AuthStateEnum from state.extra with fallback to notLoggedIn default
- **Nested Routes**: Sub-routes like sectorsPage are nested within parent routes, maintaining hierarchical navigation
- **Data Passing**: Uses state.extra for passing complex objects like sector lists between routes

This configuration enables independent navigation stacks for each tab while preserving the bottom navigation bar across all screens.

**Shell Route Features:**
- **Persistent Navigation**: Bottom navigation bar remains visible across tab switches
- **Independent Stacks**: Each tab maintains its own navigation history
- **Nested Routes**: Support for sub-routes within each tab
- **State Preservation**: Tab content and scroll positions are preserved

### Bottom Navigation Implementation

```mermaid
graph TB
    subgraph "📱 Bottom Navigation Architecture"
        Shell[StatefulShellRoute<br/>• Persistent Navigation<br/>• Independent Stacks<br/>• State Preservation]

        subgraph "🏠 Navigation Tabs"
            Home[Home Tab<br/>• Dashboard<br/>• Featured Content<br/>• Quick Access]
            Catalog[Catalog Tab<br/>• Course Browse<br/>• Search & Filter<br/>• Learning Tracks]
            Learning[My Learnings Tab<br/>• Enrolled Courses<br/>• Progress Tracking<br/>• Achievements]
            Profile[Profile Tab<br/>• User Settings<br/>• Account Info<br/>• Preferences]
        end

        subgraph "🎯 Navigation Features"
            Icons[Dynamic Icons<br/>• Active/Inactive States<br/>• Visual Feedback<br/>• Asset Management]
            Labels[Localized Labels<br/>• Multi-language<br/>• Context Aware<br/>• Accessibility]
            Behavior[Navigation Behavior<br/>• Branch Switching<br/>• Stack Management<br/>• State Restoration]
        end
    end

    Shell --> Home
    Shell --> Catalog
    Shell --> Learning
    Shell --> Profile

    Home --> Icons
    Catalog --> Icons
    Learning --> Labels
    Profile --> Behavior

    style Shell fill:#e3f2fd
    style Home fill:#f3e5f5
    style Catalog fill:#e8f5e8
    style Learning fill:#fff3e0
    style Profile fill:#fce4ec
```

**Bottom Navigation Features**:

#### AppBottomNavBar Component
- **StatefulNavigationShell Integration**: Wraps the navigation shell for persistent bottom navigation
- **Fixed Tab Layout**: Uses BottomNavigationBarType.fixed for consistent four-tab layout
- **Dynamic Icon States**: Icons change appearance based on active/inactive tab states
- **Localized Labels**: Tab labels automatically translate based on current app locale

#### Navigation Behavior
- **Branch Navigation**: Uses goBranch method for efficient tab switching
- **Stack Preservation**: Each tab maintains independent navigation history
- **Initial Location Handling**: Smart handling of repeated tab taps for stack reset
- **State Management**: Preserves scroll positions and form data across tab switches

#### Tab Structure
- **Home Tab**: Dashboard with featured content and quick access to popular courses
- **Catalog Tab**: Course browsing with search, filters, and learning track exploration
- **My Learnings Tab**: Personal learning dashboard with progress tracking
- **Profile Tab**: User account management and application settings

## Navigation Patterns

### Navigation Flow Architecture

```mermaid
graph LR
    subgraph "🧭 Navigation Methods"
        Push[Push Navigation<br/>• Add to Stack<br/>• Preserve History<br/>• Back Button Support]
        Go[Go Navigation<br/>• Replace Current<br/>• Clear History<br/>• New Entry Point]
        Pop[Pop Navigation<br/>• Remove Current<br/>• Return to Previous<br/>• Result Passing]
    end

    subgraph "📊 Parameter Types"
        Path[Path Parameters<br/>• URL Segments<br/>• Required Values<br/>• Type Safety]
        Query[Query Parameters<br/>• Optional Values<br/>• Filter States<br/>• Search Terms]
        Extra[Extra Objects<br/>• Complex Data<br/>• Model Passing<br/>• State Transfer]
    end

    subgraph "🎯 Use Cases"
        Details[Detail Views<br/>• Course Details<br/>• User Profiles<br/>• Content Pages]
        Modals[Modal Screens<br/>• Forms<br/>• Confirmations<br/>• Overlays]
        Tabs[Tab Navigation<br/>• Bottom Tabs<br/>• Section Switching<br/>• State Preservation]
    end

    Push --> Path
    Go --> Query
    Pop --> Extra

    Path --> Details
    Query --> Modals
    Extra --> Tabs

    style Push fill:#e3f2fd
    style Go fill:#f3e5f5
    style Pop fill:#e8f5e8
    style Path fill:#fff3e0
    style Query fill:#fce4ec
    style Extra fill:#e0f2f1
```

### Navigation Method Patterns

#### Push Navigation
**Purpose**: Add new screen to navigation stack while preserving history
**Use Cases**:
- Detail views (course details, user profiles)
- Multi-step forms and wizards
- Drill-down navigation patterns
- Modal presentations

**Benefits**:
- Maintains navigation history for back button functionality
- Preserves previous screen state and scroll positions
- Supports complex navigation flows with multiple levels

#### Go Navigation
**Purpose**: Replace current screen with new destination
**Use Cases**:
- Tab switching in bottom navigation
- Login/logout flow transitions
- App state resets and fresh starts
- Error recovery navigation

**Benefits**:
- Clears navigation history for clean state
- Prevents unwanted back navigation
- Optimizes memory usage by removing unused screens

#### Pop Navigation
**Purpose**: Return to previous screen with optional result data
**Use Cases**:
- Form submission completion
- Modal dismissal with data
- Cancellation flows
- Result confirmation screens

**Benefits**:
- Efficient return to previous context
- Data passing between screens
- Maintains app flow continuity

### Parameter Passing Strategies

#### Path Parameters
**Implementation**: URL segment-based parameter passing
**Characteristics**:
- **Required Values**: Parameters must be provided for navigation
- **URL Visible**: Parameters appear in the URL structure
- **Type Safety**: Compile-time validation of parameter names
- **Deep Link Friendly**: Supports external URL navigation

**Best Practices**:
- Use for essential identifiers (IDs, slugs)
- Keep parameter names descriptive and consistent
- Validate parameter values in route builders
- Handle missing or invalid parameters gracefully

#### Query Parameters
**Implementation**: URL query string-based parameter passing
**Characteristics**:
- **Optional Values**: Parameters can be omitted without breaking navigation
- **Filter States**: Ideal for search filters and view options
- **URL Persistence**: State preserved in browser history
- **Multiple Values**: Support for arrays and complex filters

**Best Practices**:
- Use for optional configuration and filters
- Provide sensible defaults for missing parameters
- Keep query parameter names short and clear
- Validate and sanitize query parameter values

#### Extra Object Passing
**Implementation**: Direct object passing through navigation state
**Characteristics**:
- **Complex Data**: Support for full model objects and complex structures
- **Type Safety**: Compile-time type checking for passed objects
- **Memory Efficient**: Direct object references without serialization
- **State Transfer**: Ideal for maintaining complex application state

**Best Practices**:
- Use for complex objects that don't serialize well to URLs
- Ensure objects are immutable to prevent unintended modifications
- Provide fallback handling for null or missing extra data
- Consider memory implications for large objects

## Custom Transitions

### Transition Architecture

```mermaid
graph TB
    subgraph "🎬 Transition System"
        Base[CustomTransitionPage<br/>• Base Class<br/>• Animation Framework<br/>• Builder Pattern]

        subgraph "📱 Transition Types"
            Modal[Modal Transitions<br/>• Bottom to Top<br/>• Slide Up Effect<br/>• Modal Presentation]
            Directional[Directional Transitions<br/>• Left/Right Slide<br/>• Forward/Backward<br/>• Content Navigation]
            Fade[Fade Transitions<br/>• Opacity Animation<br/>• Smooth Blending<br/>• Subtle Changes]
        end

        subgraph "⚙️ Animation Properties"
            Curves[Animation Curves<br/>• Ease Curves<br/>• Natural Motion<br/>• Timing Control]
            Offsets[Position Offsets<br/>• Start/End Points<br/>• Direction Control<br/>• Smooth Movement]
            Duration[Timing Control<br/>• Animation Speed<br/>• Performance Balance<br/>• User Experience]
        end
    end

    Base --> Modal
    Base --> Directional
    Base --> Fade

    Modal --> Curves
    Directional --> Offsets
    Fade --> Duration

    style Base fill:#e3f2fd
    style Modal fill:#f3e5f5
    style Directional fill:#e8f5e8
    style Fade fill:#fff3e0
    style Curves fill:#fce4ec
```

### Transition Implementation Patterns

#### Slide Bottom to Top Transition
**Purpose**: Creates elegant modal-style presentations
**Implementation Details**:
- **CustomTransitionPage Extension**: Extends base transition class for consistent behavior
- **Offset Animation**: Animates from bottom (0.0, 1.0) to center (Offset.zero)
- **Curve Application**: Uses Curves.ease for natural, smooth animation timing
- **Modal Presentation**: Ideal for forms, details, and overlay screens

**Use Cases**:
- Login and registration forms
- Course detail modals
- Settings and configuration screens
- Confirmation dialogs and alerts

#### Multi-Directional Transition System
**Purpose**: Provides contextual navigation animations based on content flow
**Animation Types**:
- **Forward Navigation**: Right-to-left slide for progressing through content
- **Backward Navigation**: Left-to-right slide for returning to previous content
- **Neutral Transition**: Fade animation for non-directional navigation

**Implementation Features**:
- **Type-Based Animation**: LessonNavigationType enum determines animation direction
- **Consistent Timing**: Uniform animation duration across all transition types
- **Smooth Interpolation**: Tween-based animations with proper curve application
- **Performance Optimized**: Efficient animation rendering for smooth user experience

#### Animation Configuration
**Curve Selection**:
- **Ease Curves**: Natural acceleration and deceleration for comfortable viewing
- **Performance Balance**: Optimized timing for smooth animations without lag
- **User Experience**: Animation speed tuned for optimal user perception

**Position Management**:
- **Precise Offsets**: Carefully calculated start and end positions
- **Smooth Movement**: Linear interpolation between defined offset points
- **Direction Control**: Consistent directional behavior across similar transitions

## Deep Linking

### Deep Link Architecture

```mermaid
graph TB
    subgraph "🔗 Deep Link System"
        External[External Sources<br/>• Web Links<br/>• Email Links<br/>• Social Media<br/>• QR Codes]

        subgraph "📱 URL Structure"
            Root[Root Level<br/>nsp://app/<br/>• Home Dashboard<br/>• Entry Point]
            Features[Feature Routes<br/>• /catalog<br/>• /my-learnings<br/>• /profile]
            Details[Detail Routes<br/>• /training/:id<br/>• /lesson/:lessonId<br/>• /search?query=term]
        end

        subgraph "⚙️ Processing"
            Parser[URL Parser<br/>• URI Parsing<br/>• Path Extraction<br/>• Parameter Validation]
            Router[Route Resolution<br/>• Path Matching<br/>• Navigation Logic<br/>• State Restoration]
            Fallback[Fallback Handling<br/>• Invalid URLs<br/>• Missing Content<br/>• Error Recovery]
        end
    end

    External --> Root
    External --> Features
    External --> Details

    Root --> Parser
    Features --> Parser
    Details --> Parser

    Parser --> Router
    Router --> Fallback

    style External fill:#e3f2fd
    style Root fill:#f3e5f5
    style Features fill:#e8f5e8
    style Details fill:#fff3e0
    style Parser fill:#fce4ec
```

### URL Structure Hierarchy

#### Application URL Scheme
**Base URL**: `nsp://app/` - Custom scheme for NSP mobile application

#### Route Categories

**Root Routes**:
- **Home (`/`)**: Application dashboard and main entry point
- **Authentication**: Login and registration flows

**Feature Routes**:
- **Catalog (`/catalog`)**: Course browsing and discovery
- **My Learnings (`/my-learnings`)**: Personal learning dashboard
- **Profile (`/profile`)**: User account and settings

**Detail Routes**:
- **Training Details (`/catalog/training/:id`)**: Specific course information
- **Learning Track (`/catalog/learning-track/:id`)**: Learning path details
- **Training Consumption (`/training/:id/lesson/:lessonId`)**: Active learning session

**Search and Filter Routes**:
- **Search (`/catalog/search`)**: Course search interface
- **Filtered Results (`/catalog?filters=...`)**: Filtered course listings

### Deep Link Processing

#### URL Parsing Strategy
**URI Analysis**:
- **Scheme Validation**: Ensures URL uses correct application scheme
- **Path Segmentation**: Breaks URL into logical navigation components
- **Parameter Extraction**: Identifies path and query parameters
- **Validation**: Verifies parameter format and required values

#### Route Resolution Process
**Navigation Logic**:
- **Path Matching**: Compares incoming URL to defined route patterns
- **Parameter Binding**: Maps URL parameters to route parameters
- **State Restoration**: Restores application state based on URL context
- **Authentication Check**: Validates user access for protected routes

#### Error Handling
**Fallback Mechanisms**:
- **Invalid URLs**: Redirects to appropriate fallback pages
- **Missing Content**: Handles references to non-existent resources
- **Authentication Required**: Redirects to login for protected content
- **Network Issues**: Provides offline-friendly error handling

### Deep Link Use Cases

#### External Integration
**Marketing Campaigns**:
- **Course Promotion**: Direct links to specific courses from marketing materials
- **Email Campaigns**: Deep links in educational newsletters and announcements
- **Social Media**: Shareable links for course recommendations
- **QR Codes**: Physical materials linking to digital content

#### User Experience
**Seamless Navigation**:
- **Bookmark Support**: Users can bookmark specific courses and lessons
- **Share Functionality**: Easy sharing of interesting content with others
- **Resume Learning**: Direct links to continue interrupted learning sessions
- **Cross-Platform**: Consistent URL structure across web and mobile platforms

## Route Guards

### Security and Access Control

```mermaid
graph TB
    subgraph "🔐 Route Guard System"
        Request[Navigation Request<br/>• Route Target<br/>• User Context<br/>• Current State]

        subgraph "🛡️ Guard Types"
            Auth[Authentication Guard<br/>• Login Status<br/>• Token Validation<br/>• Session Check]
            Role[Role-Based Guard<br/>• User Permissions<br/>• Feature Access<br/>• Content Restrictions]
            State[State Guard<br/>• App State<br/>• Data Availability<br/>• Prerequisite Check]
        end

        subgraph "🎯 Actions"
            Allow[Allow Access<br/>• Continue Navigation<br/>• Load Destination<br/>• Maintain Flow]
            Redirect[Redirect<br/>• Alternative Route<br/>• Login Page<br/>• Error Page]
            Block[Block Access<br/>• Show Error<br/>• Return to Previous<br/>• Fallback Action]
        end
    end

    Request --> Auth
    Request --> Role
    Request --> State

    Auth --> Allow
    Auth --> Redirect
    Role --> Allow
    Role --> Block
    State --> Redirect

    style Request fill:#e3f2fd
    style Auth fill:#f3e5f5
    style Role fill:#e8f5e8
    style State fill:#fff3e0
    style Allow fill:#4CAF50,color:#fff
    style Redirect fill:#FF9800,color:#fff
    style Block fill:#F44336,color:#fff
```

### Guard Implementation Patterns

#### Authentication Guard
**Purpose**: Protects routes requiring user authentication
**Implementation Strategy**:
- **Token Validation**: Checks for valid authentication token in secure storage
- **Session Verification**: Validates active user session with backend
- **Redirect Logic**: Automatically redirects unauthenticated users to login
- **State Preservation**: Maintains intended destination for post-login navigation

**Protected Routes**:
- User profile and settings
- My learnings dashboard
- Course enrollment actions
- Personal progress tracking

#### Role-Based Access Guard
**Purpose**: Enforces user role and permission-based access control
**Access Control Logic**:
- **Role Verification**: Validates user role against route requirements
- **Permission Checking**: Ensures user has necessary permissions for features
- **Graceful Degradation**: Provides appropriate fallback for insufficient permissions
- **Dynamic Access**: Adapts access based on changing user roles

**Use Cases**:
- Trainee-specific features and content
- Administrative functions and settings
- Premium content and advanced features
- Organization-specific resources

#### State-Based Guards
**Purpose**: Ensures application state prerequisites are met
**State Validation**:
- **Data Availability**: Verifies required data is loaded before navigation
- **Network Connectivity**: Checks network status for online-only features
- **App Configuration**: Ensures proper app setup and configuration
- **User Onboarding**: Guides users through required setup steps

### Navigation Extensions

#### Enhanced Navigation Methods
**Stack Manipulation**:
- **popUntil Method**: Removes routes from stack until specified condition is met
- **popAllRoutes Method**: Clears entire navigation stack for fresh start
- **Safety Validation**: Includes canPop() checks to prevent navigation errors
- **Flexible Conditions**: Supports custom logic for complex navigation scenarios

**Advanced Navigation Patterns**:
- **Conditional Navigation**: Navigate based on dynamic conditions and state
- **Bulk Stack Operations**: Efficiently manage multiple route operations
- **Error Recovery**: Robust error handling for navigation failures
- **State Restoration**: Restore navigation state after app restart or crash

## Error Handling

### Error Management Strategy

```mermaid
graph TB
    subgraph "❌ Error Scenarios"
        NotFound[Route Not Found<br/>• Invalid URLs<br/>• Deleted Content<br/>• Typos]
        Auth[Authentication Errors<br/>• Expired Sessions<br/>• Invalid Tokens<br/>• Permission Denied]
        Network[Network Errors<br/>• Connection Issues<br/>• Timeout Errors<br/>• Server Problems]
    end

    subgraph "🛠️ Error Handling"
        Detection[Error Detection<br/>• Route Validation<br/>• State Checking<br/>• Exception Catching]
        Recovery[Error Recovery<br/>• Fallback Routes<br/>• Retry Logic<br/>• User Guidance]
        Feedback[User Feedback<br/>• Error Messages<br/>• Recovery Options<br/>• Support Links]
    end

    NotFound --> Detection
    Auth --> Detection
    Network --> Detection

    Detection --> Recovery
    Recovery --> Feedback

    style NotFound fill:#ffebee
    style Auth fill:#fff3e0
    style Network fill:#e8f5e8
    style Detection fill:#e3f2fd
    style Recovery fill:#f3e5f5
    style Feedback fill:#fce4ec
```

### Error Handling Patterns

#### Route Not Found Handling
**Error Page Implementation**:
- **Custom Error Builder**: GoRouter errorBuilder provides centralized error handling
- **Error Information Display**: Shows error details and attempted location
- **Recovery Actions**: Provides navigation options to return to valid routes
- **User-Friendly Messaging**: Clear, non-technical error explanations

**Fallback Strategies**:
- **Home Navigation**: Quick return to main application dashboard
- **Previous Route**: Option to return to last valid location
- **Search Suggestion**: Help users find intended content
- **Support Contact**: Easy access to help and support resources

#### Authentication Error Recovery
**Session Management**:
- **Automatic Retry**: Attempts to refresh expired authentication tokens
- **Graceful Degradation**: Redirects to login with preserved destination
- **State Preservation**: Maintains user's intended navigation target
- **Clear Messaging**: Explains authentication requirements to users

## Navigation State Management

### BLoC Integration Patterns

```mermaid
graph LR
    subgraph "🎯 Navigation State"
        NavCubit[Navigation Cubit<br/>• Route State<br/>• Loading States<br/>• Error Handling]

        subgraph "📊 State Types"
            Initial[Initial State<br/>• App Launch<br/>• Default Route<br/>• Clean State]
            Loading[Loading State<br/>• Route Transition<br/>• Data Loading<br/>• Progress Indication]
            Success[Success State<br/>• Navigation Complete<br/>• Route Active<br/>• Content Loaded]
            Error[Error State<br/>• Navigation Failed<br/>• Route Invalid<br/>• Recovery Options]
        end
    end

    NavCubit --> Initial
    NavCubit --> Loading
    NavCubit --> Success
    NavCubit --> Error

    style NavCubit fill:#e3f2fd
    style Initial fill:#f3e5f5
    style Loading fill:#fff3e0
    style Success fill:#4CAF50,color:#fff
    style Error fill:#F44336,color:#fff
```

### State Management Integration

#### Navigation Cubit Pattern
**State Coordination**:
- **Navigation Events**: Triggers navigation actions through BLoC events
- **Loading States**: Manages loading indicators during route transitions
- **Error Handling**: Captures and manages navigation errors
- **Success Tracking**: Confirms successful navigation completion

#### Navigation Listener Implementation
**Route Change Monitoring**:
- **Analytics Integration**: Tracks page views and navigation patterns
- **State Synchronization**: Updates app state based on route changes
- **Performance Monitoring**: Measures navigation timing and performance
- **User Behavior Tracking**: Collects navigation analytics for UX improvement

## Testing Strategy

### Navigation Testing Approach

```mermaid
graph TB
    subgraph "🧪 Testing Levels"
        Unit[Unit Tests<br/>• Route Logic<br/>• Guard Functions<br/>• Parameter Validation]
        Widget[Widget Tests<br/>• Navigation Actions<br/>• Route Rendering<br/>• State Changes]
        Integration[Integration Tests<br/>• Full Navigation Flows<br/>• Deep Link Handling<br/>• Error Scenarios]
    end

    subgraph "🎯 Test Scenarios"
        Routes[Route Testing<br/>• Valid Navigation<br/>• Parameter Passing<br/>• Guard Behavior]
        Transitions[Transition Testing<br/>• Animation Behavior<br/>• Performance Impact<br/>• Visual Consistency]
        DeepLinks[Deep Link Testing<br/>• URL Parsing<br/>• State Restoration<br/>• Error Handling]
    end

    Unit --> Routes
    Widget --> Transitions
    Integration --> DeepLinks

    style Unit fill:#e3f2fd
    style Widget fill:#f3e5f5
    style Integration fill:#e8f5e8
    style Routes fill:#fff3e0
    style Transitions fill:#fce4ec
    style DeepLinks fill:#e0f2f1
```

### Testing Methodologies

#### Router Testing
**Route Validation**:
- **Navigation Success**: Verify successful navigation to target routes
- **Parameter Handling**: Test path and query parameter processing
- **Guard Behavior**: Validate authentication and role-based access
- **Error Scenarios**: Test handling of invalid routes and parameters

#### Widget Testing
**UI Integration**:
- **Navigation Triggers**: Test button taps and gesture-based navigation
- **State Updates**: Verify UI updates following navigation events
- **Transition Animations**: Test custom transition implementations
- **Bottom Navigation**: Validate tab switching and state preservation

#### Integration Testing
**End-to-End Flows**:
- **Complete User Journeys**: Test full navigation flows from start to finish
- **Deep Link Handling**: Verify external URL processing and state restoration
- **Cross-Feature Navigation**: Test navigation between different app features
- **Error Recovery**: Test error handling and recovery mechanisms

## Related Documentation

- [Deep Linking Guide](deep-linking.md)
- [Navigation Patterns](navigation-patterns.md)
- [Architecture Overview](../architecture/overview.md)
- [State Management](../architecture/state-management.md)
- [Testing Guide](../development/testing-guide.md)
- [UI Components](../ui-components/shared-widgets.md)
