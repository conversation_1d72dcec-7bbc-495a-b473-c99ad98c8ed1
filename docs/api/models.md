# API Data Models

## Overview

The NSP mobile application uses a comprehensive set of data models to handle API communication, data serialization, and type-safe data transfer between layers. All models are built using Freezed for immutability and JSON serialization capabilities.

## Model Architecture

### Core Technologies

**Freezed**: Provides immutable data classes with built-in equality, toString, and copyWith methods.

**Freezed Model Structure Example:**

The AuthModel demonstrates the standard Freezed implementation pattern:
- **@freezed Annotation**: Enables code generation for immutable data classes with built-in equality, toString, and copyWith methods
- **Class Declaration**: Extends the generated _$AuthModel mixin for automatic method implementations
- **Factory Constructor**: Uses const factory pattern with named parameters for required and optional fields
- **Required Fields**: codeVerifier and codeChallenge are mandatory for OAuth PKCE flow
- **Optional Fields**: token is nullable using String? for authorization code
- **JSON Serialization**: Includes factory constructor for fromJson conversion using generated _$AuthModelFromJson method

This pattern ensures type safety, immutability, and automatic JSON serialization across all data models.

**Key Features:**
- **Immutability**: All model instances are immutable by default
- **JSON Serialization**: Automatic JSON conversion with code generation
- **Type Safety**: Compile-time type checking for all properties
- **Null Safety**: Proper handling of optional and required fields

## Authentication Models

### AuthModel

Handles OAuth authentication data including PKCE parameters.

**AuthModel Factory and Serialization:**

The AuthModel factory constructor and toJson method demonstrate OAuth authentication handling:
- **Factory Parameters**: Includes required codeVerifier and codeChallenge for PKCE security, optional token for authorization code
- **Default Values**: Uses @Default annotation to set role to Constants.TRAINEE as the standard user type
- **Custom toJson**: Implements manual JSON serialization mapping model fields to API-expected constants
- **Field Mapping**: Maps token to Constants.authCode, maintains codeVerifier and codeChallenge, includes role and mobile flag
- **API Compatibility**: Ensures JSON structure matches backend API expectations for authentication requests

This approach provides secure OAuth PKCE implementation with proper field mapping for API communication.

**Properties:**
- **codeVerifier**: PKCE code verifier for OAuth security
- **codeChallenge**: PKCE code challenge derived from verifier
- **token**: Authorization code received from OAuth provider
- **role**: User role (default: TRAINEE)

### RefreshTokenModel

Manages token refresh operations for maintaining user sessions.

**RefreshTokenModel Implementation:**

The RefreshTokenModel handles token refresh operations for session management:
- **Required Token**: refreshToken parameter is mandatory for obtaining new access tokens
- **Default Role**: Uses @Default annotation to set role to Constants.TRAINEE for standard user context
- **Simple Structure**: Minimal model focused specifically on token refresh functionality
- **Custom Serialization**: toJson method maps fields to API constants (refreshToken, role) and includes mobile flag
- **Session Continuity**: Enables seamless token refresh without requiring user re-authentication

This model ensures secure session management by providing the necessary data for token refresh API calls.

**Properties:**
- **refreshToken**: Token used to obtain new access tokens
- **role**: User role for authorization context

## User Models

### UserModel

Represents user profile information and account details.

**UserModel Structure:**

The UserModel represents comprehensive user profile information with proper type safety:
- **Required Fields**: id, name, and email are mandatory for user identification and communication
- **Optional Profile Data**: phoneNumber and profilePictureUrl are nullable for incomplete profiles
- **Timestamp Tracking**: lastLoginAt uses DateTime? for optional login tracking
- **Role Management**: roles field uses @Default([]) to provide empty list fallback for user permissions
- **Freezed Integration**: Standard @freezed annotation with _$UserModel mixin for code generation
- **JSON Support**: Includes fromJson factory constructor using generated _$UserModelFromJson method

This model provides a complete user profile structure with appropriate null safety and default values.

**Properties:**
- **id**: Unique user identifier
- **name**: User's full name
- **email**: Primary email address
- **phoneNumber**: Optional phone number
- **profilePictureUrl**: URL to user's profile image
- **lastLoginAt**: Timestamp of last login
- **roles**: List of user roles and permissions

## Course Models

### TrainingModel

Represents individual training courses with comprehensive metadata.

**TrainingModel Implementation:**

The TrainingModel represents individual training courses with comprehensive metadata:
- **Core Identification**: Required id, title, and description fields for basic course information
- **Visual Content**: Optional imageUrl for course thumbnails and visual representation
- **Duration and Provider**: Required duration (in minutes) and provider name for course details
- **Rating System**: Uses @Default(0.0) for rating field with double type supporting decimal ratings
- **Tagging System**: @Default([]) provides empty list fallback for searchable tags and categories
- **Status Tracking**: Required TrainingStatus enum for enrollment and completion state management
- **Standard Structure**: Follows Freezed pattern with @freezed annotation and generated fromJson method

This model provides complete course representation with proper defaults and type safety for the catalog system.

**Properties:**
- **id**: Unique training identifier
- **title**: Training course title
- **description**: Detailed course description
- **imageUrl**: Course thumbnail image
- **duration**: Course duration in minutes
- **provider**: Training provider name
- **rating**: Average user rating (0.0-5.0)
- **tags**: Searchable tags and categories
- **status**: Enrollment and completion status

### LearningTrackModel

Represents collections of related training courses.

**LearningTrackModel Structure:**

The LearningTrackModel represents collections of related training courses:
- **Basic Information**: Required id, title, and description for track identification and overview
- **Course Collection**: Required List<TrainingModel> trainings containing all courses in the learning path
- **Duration Calculation**: Required totalDuration field representing combined time of all included trainings
- **Progress Tracking**: @Default(0) for completedTrainings count with integer type for tracking user progress
- **Nested Models**: Includes complex TrainingModel objects within the trainings list for complete course data
- **Standard Pattern**: Uses @freezed annotation with _$LearningTrackModel mixin and generated fromJson method

This model enables structured learning paths with multiple courses and progress tracking capabilities.

**Properties:**
- **id**: Unique learning track identifier
- **title**: Learning track title
- **description**: Track overview and objectives
- **trainings**: List of included training courses
- **totalDuration**: Combined duration of all trainings
- **completedTrainings**: Number of completed courses

## Assessment Models

### QuestionModel

Represents quiz and test questions with multiple choice options.

**QuestionModel for Assessments:**

The QuestionModel represents quiz and test questions with multiple choice functionality:
- **Question Identity**: Required id and text fields for unique identification and question content
- **Answer Options**: Required List<AnswerOptionModel> options containing all possible answer choices
- **Optional Explanation**: Nullable explanation field for providing correct answer details after submission
- **Question Type**: @Default(QuestionType.multipleChoice) sets default question format with enum type safety
- **Nested Models**: Includes AnswerOptionModel objects for structured answer choice representation
- **Assessment Support**: Designed for quiz, test, and qualification assessment functionality

This model supports comprehensive question-based assessments with flexible question types and detailed answer options.

**Properties:**
- **id**: Unique question identifier
- **text**: Question content and instructions
- **options**: List of possible answers
- **explanation**: Optional explanation for correct answer
- **type**: Question type (multiple choice, true/false, etc.)

### AnswerOptionModel

Represents individual answer choices for questions.

**AnswerOptionModel Implementation:**

The AnswerOptionModel represents individual answer choices for assessment questions:
- **Option Identity**: Required id and text fields for unique identification and answer content display
- **Correctness Flag**: @Default(false) for isCorrect boolean indicating whether this option is the right answer
- **Simple Structure**: Minimal model focused on essential answer choice data without unnecessary complexity
- **Assessment Integration**: Designed to work with QuestionModel for complete question-answer functionality
- **Default Safety**: Uses false default for isCorrect to prevent accidental correct answer marking

This model provides clean answer choice representation for quiz and test question systems.

**Properties:**
- **id**: Unique option identifier
- **text**: Answer option text
- **isCorrect**: Whether this option is the correct answer

## Response Models

### ApiResponseModel

Generic wrapper for all API responses with consistent error handling.

**Generic API Response Model:**

The ApiResponseModel provides a standardized wrapper for all API responses with generic type support:
- **Success Indicator**: Required bool success field for immediate response status determination
- **Generic Data**: T? data field allows any type of response payload with null safety
- **User Messages**: Optional message field for human-readable response information
- **Error Identification**: Optional errorCode field for specific error categorization and debugging
- **Error Collection**: @Default([]) for errors list providing detailed validation or processing error messages
- **Generic Serialization**: Custom fromJson method accepts T Function(Object?) fromJsonT for type-safe deserialization
- **Type Safety**: Generic <T> parameter ensures compile-time type checking for response data

This model standardizes API response handling across all endpoints with consistent error management and type safety.

**Properties:**
- **success**: Indicates if the request was successful
- **data**: Response payload of generic type T
- **message**: Human-readable response message
- **errorCode**: Specific error code for debugging
- **errors**: List of validation or processing errors

### PaginatedResponseModel

Handles paginated API responses for large datasets.

**Paginated Response Model:**

The PaginatedResponseModel handles paginated API responses for large datasets with generic type support:
- **Data Collection**: Required List<T> data containing items for the current page with generic type safety
- **Page Information**: Required currentPage, totalPages, and totalItems fields for pagination navigation
- **Continuation Flag**: @Default(false) for hasMore boolean indicating if additional pages are available
- **Generic Support**: <T> parameter allows pagination of any data type (trainings, users, etc.)
- **Custom Deserialization**: fromJson method with T Function(Object?) fromJsonT parameter for type-safe item conversion
- **Navigation Support**: Provides all necessary information for implementing pagination UI controls

This model enables consistent pagination handling across all list-based API endpoints with proper type safety.

**Properties:**
- **data**: List of items for current page
- **currentPage**: Current page number (1-based)
- **totalPages**: Total number of available pages
- **totalItems**: Total count of all items
- **hasMore**: Whether more pages are available

## Enum Definitions

### Common Enums

**Common Enum Definitions:**

The application uses several enums for type-safe status and categorization management:
- **TrainingStatus Enum**: Defines course lifecycle states including available (open for enrollment), enrolled (user registered), inProgress (actively taking), completed (finished), and expired (no longer available)
- **QuestionType Enum**: Categorizes assessment question formats including multipleChoice (standard multiple choice), trueFalse (boolean questions), shortAnswer (brief text responses), and essay (long-form answers)
- **UserRole Enum**: Defines user permission levels including trainee (standard learner), instructor (course facilitator), and admin (system administrator)
- **Type Safety**: Enums provide compile-time validation preventing invalid status values
- **Serialization Support**: Automatic JSON conversion with proper string representation

These enums ensure consistent status management and type safety throughout the application.

**Enum Benefits:**
- **Type Safety**: Compile-time validation of status values
- **Serialization**: Automatic JSON conversion with proper naming
- **Extensibility**: Easy to add new status types
- **Documentation**: Self-documenting code with clear intent

## Model Validation

### Custom Validators

**Model Validation Extensions:**

Custom validation extensions provide business logic validation directly on model classes:
- **UserModelValidation Extension**: Adds validation methods to UserModel including isValidEmail (basic email format check with @ and . characters) and hasCompleteProfile (ensures name, email are non-empty and phoneNumber exists)
- **TrainingModelValidation Extension**: Provides TrainingModel validation including isAvailable (checks status equals TrainingStatus.available) and canEnroll (validates both available status and positive duration)
- **Extension Benefits**: Encapsulates validation logic within model context for better organization and reusability
- **Business Rules**: Implements domain-specific validation rules that go beyond basic type checking
- **Getter Pattern**: Uses getter syntax for clean, readable validation checks throughout the application

These extensions centralize validation logic and provide consistent business rule enforcement across the application.

**Validation Features:**
- **Business Logic**: Encapsulates validation rules within models
- **Reusability**: Consistent validation across the application
- **Readability**: Clear, self-documenting validation methods
- **Maintainability**: Centralized validation logic

## Best Practices

### 1. Model Design
- Use Freezed for all data models
- Implement proper null safety
- Include validation methods
- Use meaningful property names

### 2. JSON Serialization
- Always include fromJson and toJson methods
- Handle nested objects properly
- Use proper type converters for complex types
- Test serialization with real API data

### 3. Type Safety
- Use enums instead of string constants
- Implement proper generic types
- Validate data at model boundaries
- Use nullable types appropriately

### 4. Performance
- Keep models lightweight
- Avoid heavy computations in getters
- Use lazy initialization when appropriate
- Cache computed properties when needed

## Testing Models

### Model Testing

**Model Testing Example:**

The UserModel test demonstrates comprehensive model testing strategies:
- **Test Group Organization**: Uses group() to organize related UserModel tests for better structure
- **JSON Deserialization Testing**: Creates test JSON map with id, name, and email fields to verify fromJson functionality
- **Model Creation**: Tests UserModel.fromJson() method with sample data to ensure proper object construction
- **Property Validation**: Uses expect() assertions to verify correct field mapping from JSON to model properties
- **Extension Testing**: Tests custom validation extension (isValidEmail) to ensure business logic validation works correctly
- **Test Data**: Uses realistic test data that matches expected API response structure

This testing approach ensures model reliability, JSON serialization correctness, and validation logic functionality.

**Testing Strategies:**
- **JSON Serialization**: Test fromJson and toJson methods
- **Validation Logic**: Verify custom validation methods
- **Edge Cases**: Test with null and invalid data
- **Equality**: Verify Freezed equality implementation

## Related Documentation

- [API Architecture](api-architecture.md)
- [Data Sources](data-sources.md)
- [Repositories](repositories.md)
- [State Management](../architecture/state-management.md)
