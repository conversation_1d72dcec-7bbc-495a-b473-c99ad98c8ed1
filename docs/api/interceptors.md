# API Interceptors

HTTP interceptors in the NSP app handle authentication, retries, and debugging automatically. This guide shows you how they work and how to use them effectively.

## What Interceptors Do For You

| Interceptor | Automatic Behavior | Developer Benefit |
|-------------|-------------------|-------------------|
| **Auth** | Adds JWT tokens to all requests | Never manually add auth headers |
| **Token Refresh** | Refreshes expired tokens and retries requests | Seamless user experience |
| **Retry** | Retries failed GET requests | Better reliability |
| **Alice Debug** | Logs all HTTP traffic | Easy API debugging |

## Quick Reference

**Automatic API Enhancement**: All HTTP requests through the Dio client automatically receive authentication headers, unique request IDs, retry logic for failed requests, token refresh handling, and debug logging in development mode. Manual header configuration is unnecessary as the interceptor system handles these concerns transparently.

## How It Works

### Request Flow
```
Your API Call
    ↓
1. Alice Debug (logs request)
    ↓
2. Retry Interceptor (will retry if fails)
    ↓
3. Auth Interceptor (adds token + headers)
    ↓
Server Response
    ↓
4. Token Refresh (if 401/403, refresh & retry)
    ↓
Your Success/Error Handler
```

## Interceptor Chain

Interceptors are executed in the order they are added to the Dio client:

**Interceptor Chain Configuration:**

The _initInterceptors method establishes the complete interceptor chain for HTTP request processing:
- **Alice Debug Interceptor**: Conditionally added only in debug mode using kDebugMode check for development HTTP traffic inspection
- **Retry Interceptor**: Configured with Dio instance, appPrint logging, and custom _retryOnlyGetRequestsEvaluator for intelligent retry logic
- **Authentication Wrapper**: Uses InterceptorsWrapper with onRequest callback that:
  - Retrieves authentication token via AuthTokenProvider with secure storage injection
  - Adds X-Current-Role header set to Constants.TRAINEE for user type identification
  - Generates unique X-Request-Id using UUID v1 for request tracking
  - Conditionally adds Authorization header with Bearer token when available
- **Error Handling**: onError callback delegates to RefreshTokenInterceptor for automatic token refresh on authentication failures
- **Execution Order**: Interceptors execute in sequence (Alice → Retry → Authentication) for proper request processing

This configuration ensures comprehensive request handling with debugging, retry logic, authentication, and error recovery.

## Authentication Interceptor

### What It Does
Automatically adds authentication and tracking headers to **every** API request.

### Headers Added Automatically

| Header | Value | Why It's Added |
|--------|-------|----------------|
| `Authorization` | `Bearer <jwt_token>` | Authenticates the user |
| `X-Current-Role` | `TRAINEE` | Identifies user type for backend |
| `X-Request-Id` | `uuid-v1` | Tracks requests for support/debugging |

### Real-World Impact

**Manual vs Automatic Header Management**: Without interceptors, developers must manually retrieve tokens from AuthTokenProvider, construct Options objects with Authorization Bearer tokens, add X-Current-Role headers, and generate unique X-Request-Id values for every API call. With interceptors enabled, a simple dio.get() call automatically includes all required headers.

### When Headers Are Added

- ✅ **Always**: `X-Current-Role` and `X-Request-Id`
- ✅ **If logged in**: `Authorization` header with JWT token
- ✅ **If not logged in**: No auth header (for public endpoints)

### Debugging Auth Issues

**Authentication Debugging**: Use appPrint() to log the current token from AuthTokenProvider.getToken() and verify token presence in storage. Inspect request headers through the Alice debugging interface to confirm Authorization headers are properly attached to outgoing requests.

## Refresh Token Interceptor

### What It Does
When your JWT token expires, this interceptor automatically refreshes it and retries your failed request. **You never see the error.**

### User Experience

**Seamless Token Refresh Experience:**

**User Flow**: When user.loadTrainings() is called and the JWT token expires during the request, the RefreshTokenInterceptor automatically detects the 401 error, refreshes the token, retries the original request, and returns successful data. The user experiences user.seesTrainings() without any interruption or error handling required in the application code.

### When It Activates

| Scenario | Action |
|----------|--------|
| **401/403 error** | Automatically refresh token and retry |
| **Network error** | Pass through (let retry interceptor handle) |
| **Refresh token expired** | Log user out |
| **No refresh token** | Pass error to app |

### Smart Behavior

**Smart Refresh Logic**: The interceptor prevents infinite loops by checking if the request path equals '/auth/refresh-token' and logging out the user instead of attempting another refresh. It also queues multiple failed requests during token expiration, refreshes the token once, then retries all queued requests with the new token.

### What You Don't Need To Do

**Avoid Manual Token Handling**: Don't wrap API calls in try-catch blocks to manually handle 401 errors by calling refreshToken() and retrying requests. Instead, simply make the API call with dio.get() and let the RefreshTokenInterceptor automatically handle token expiration and retry logic.

### Flow Diagram

```mermaid
graph TD
    A[API Request] --> B{Auth Error?}
    B -->|No| C[Continue Request]
    B -->|Yes| D{Refresh Token Request?}
    D -->|Yes| E[Invalidate Tokens]
    D -->|No| F{Valid Tokens?}
    F -->|No| G[Pass Error]
    F -->|Yes| H[Queue Request]
    H --> I[Trigger Refresh]
    I --> J{Refresh Success?}
    J -->|Yes| K[Retry Queued Requests]
    J -->|No| L[Handle Refresh Error]
```

## Retry Interceptor

### Purpose
Automatically retries failed GET requests based on network conditions and response codes.

### Configuration

**Retry Interceptor Configuration**: The RetryInterceptor is configured with a Dio instance from dependency injection, uses appPrint for logging retry attempts, and employs a custom _retryOnlyGetRequestsEvaluator to ensure only safe GET requests are automatically retried.

### Custom Retry Evaluator

**Custom Retry Evaluator Implementation:**

The _retryOnlyGetRequestsEvaluator provides intelligent retry logic for safe HTTP operations:
- **Method Filtering**: Checks error.requestOptions.method against Constants.GET to ensure only idempotent operations are retried
- **Safety First**: Returns false immediately for non-GET requests (POST, PUT, DELETE) to prevent duplicate operations
- **Default Logic Integration**: Uses DefaultRetryEvaluator with defaultRetryableStatuses for standard retry conditions on GET requests
- **Attempt Tracking**: Accepts attempt parameter for retry count management and exponential backoff
- **Error Analysis**: Evaluates DioException to determine if retry is appropriate based on error type and status code

This approach ensures safe automatic retries while preventing potentially harmful duplicate operations on state-changing requests.

### Retry Conditions

- **Method restriction**: Only GET requests are retried
- **Status codes**: Retries on network errors and 5xx server errors
- **Attempt limit**: Configurable maximum retry attempts
- **Exponential backoff**: Increasing delays between retries

### Benefits

- **Improved reliability**: Handles temporary network issues
- **User experience**: Reduces failed requests due to network glitches
- **Safe retries**: Only retries idempotent GET operations

## Alice Debug Interceptor

### Purpose
Provides detailed HTTP traffic logging and inspection during development.

### Configuration

**Debug-Only Alice Integration**: The Alice debugging interceptor is conditionally added only when kDebugMode is true, using dependency injection to retrieve the Alice instance and attach its Dio interceptor for HTTP traffic inspection.

### Features

- **Request/Response logging**: Complete HTTP traffic details
- **Network inspector**: Visual interface for debugging
- **Performance metrics**: Request timing and size information
- **Error details**: Detailed error information

### Usage

- **Notification access**: Tap Alice notification to open inspector
- **Shake gesture**: Shake device to open Alice interface
- **Programmatic access**: Call `alice.showInspector()` in code

## Configuration

### Dio Client Setup

**Dio Client Registration:**

The RegisterModule provides dependency injection configuration for the HTTP client:
- **@module Annotation**: Marks this as a dependency injection module for automatic registration
- **Abstract Class**: Uses abstract class pattern for module definition without implementation details
- **@singleton Annotation**: Ensures single Dio instance throughout the application lifecycle for connection pooling and consistency
- **BaseOptions Configuration**: Creates Dio instance with BaseOptions containing baseUrl from EnvironmentConfigs for environment-specific API endpoints
- **Environment Integration**: Uses EnvironmentConfigs.baseUrl to automatically configure API base URL based on current environment (dev, staging, prod)

This setup provides centralized HTTP client configuration with proper dependency injection and environment management.

### Base Configuration

**Base Dio Configuration**: The Dio client is configured with BaseOptions containing the environment-specific baseUrl from EnvironmentConfigs, 30-second timeouts for both connection and data reception, and default headers for JSON content type and accept headers.

### Environment-Specific Setup

**Environment-Specific Interceptor Setup**: In development mode, both Alice debugging interceptor and detailed LogInterceptor with full request/response body logging are added. In production, only minimal LogInterceptor without body logging is used to reduce overhead and protect sensitive data.

## Usage Examples

### Manual Token Addition

**Manual Token Override**: For special cases requiring custom authentication tokens, create an Options object with specific Authorization Bearer headers and custom headers, then pass it to the Dio request method to override the default interceptor behavior.

### Bypassing Interceptors

**Bypassing Interceptors**: Create a separate Dio instance with only BaseOptions and the environment-specific baseUrl to make requests without any interceptors for public endpoints that don't require authentication or special handling.

### Custom Error Handling

**Custom Error Handling Pattern**: Wrap API calls in try-catch blocks to handle DioException, check for specific status codes like 401 for authentication errors, and rethrow exceptions after custom handling. Note that RefreshTokenInterceptor should automatically handle 401 errors in most cases.

## Best Practices

### Token Management

1. **Use AuthTokenProvider**: Always use the centralized token provider
2. **Handle token expiration**: Let the refresh interceptor handle expired tokens
3. **Secure storage**: Store tokens in Flutter Secure Storage
4. **Clear on logout**: Invalidate tokens when user logs out

### Error Handling

1. **Let interceptors work**: Don't manually handle 401/403 errors
2. **Provide fallbacks**: Handle cases where refresh fails
3. **User feedback**: Show appropriate messages for different error types
4. **Logging**: Use appropriate logging levels for different environments

### Performance

1. **Minimize interceptors**: Only add necessary interceptors
2. **Efficient token retrieval**: Cache tokens when possible
3. **Retry limits**: Set reasonable retry limits to avoid infinite loops
4. **Request deduplication**: Avoid duplicate requests during token refresh

### Security

1. **Token protection**: Never log tokens in production
2. **HTTPS only**: Ensure all requests use HTTPS
3. **Request validation**: Validate request data before sending
4. **Error information**: Limit error details in production

## Troubleshooting

### Common Issues

1. **Infinite refresh loops**: Check refresh token interceptor logic
2. **Missing tokens**: Verify AuthTokenProvider is working correctly
3. **Retry failures**: Check retry evaluator conditions
4. **Alice not showing**: Ensure debug mode is enabled

### Debugging

**Custom Debug Interceptor**: Add an InterceptorsWrapper to the Dio client with onRequest, onResponse, and onError callbacks that print request methods and paths, headers, response status codes, and error messages for debugging purposes. Always call handler.next() to continue the request chain.

## Related Documentation

- [API Architecture](api-architecture.md) - Overall API communication setup
- [Data Sources](data-sources.md) - Data source implementations
- [Authentication](../features/auth.md) - Authentication flow
- [Error Handling](../core/error-handling.md) - Error management strategies

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
