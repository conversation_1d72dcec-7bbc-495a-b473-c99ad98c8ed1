# Data Sources

This document covers the data source implementations in the NSP Flutter application. Data sources are responsible for direct communication with external APIs and handle the low-level details of HTTP requests and responses.

## Table of Contents

- [Overview](#overview)
- [Data Source Architecture](#data-source-architecture)
- [Implementation Patterns](#implementation-patterns)
- [Common Data Sources](#common-data-sources)
- [Error Handling](#error-handling)
- [Request Parameters](#request-parameters)
- [Response Processing](#response-processing)
- [Best Practices](#best-practices)

## Overview

Data sources in the NSP app follow the Clean Architecture pattern and serve as the boundary between the application and external APIs. They encapsulate all HTTP communication logic and provide a clean interface for repositories to consume.

### Key Responsibilities

- **HTTP Communication**: Direct interaction with REST APIs using Dio
- **Request Formatting**: Prepare request parameters and headers
- **Response Parsing**: Convert API responses to domain models
- **Error Handling**: Handle HTTP errors and network issues
- **Authentication**: Include authentication headers when required

### Architecture Position

```
Presentation Layer (BLoCs/Cubits)
        ↓
Domain Layer (Repositories)
        ↓
Data Layer (Data Sources) ← You are here
        ↓
External APIs
```

## Data Source Architecture

### Base Structure

All data sources follow a consistent structure:

**Standard Data Source Structure:**

The TrainingsDataSource demonstrates the consistent architecture pattern used across all data sources:
- **Injectable Annotation**: Marked with @injectable for automatic dependency injection registration
- **Constructor Pattern**: Uses named parameter with required Dio instance, assigned to private final field _dio
- **Method Implementation**: getTrainingsList method accepts TrainingsRequestParams and returns Future<TrainingsModel>
- **HTTP Request**: Uses _dio.get with API endpoint and query parameters from requestParams.getParams()
- **Response Validation**: Checks for successful status codes (200 or 201) before processing
- **Model Conversion**: Converts response.data to TrainingsModel using fromJson factory method
- **Error Handling**: Throws DioException with original request options and response for consistent error processing

This structure ensures all data sources follow the same patterns for maintainability and consistency.

### Key Components

1. **Injectable Annotation**: Registered with dependency injection
2. **Dio Instance**: HTTP client for making requests
3. **Method Implementation**: Specific API endpoint handling
4. **Response Validation**: Status code checking
5. **Model Conversion**: JSON to Dart object transformation
6. **Error Handling**: Consistent error throwing

## Implementation Patterns

### 1. Simple GET Request

**Simple GET Request Pattern:**

The MyLearningsDataSource illustrates the basic GET request implementation:
- **Class Structure**: Follows standard injectable pattern with Dio dependency injection
- **Simple GET Method**: getMyLearnings performs straightforward GET request to ApiConstants.currentCoursesAll endpoint
- **No Parameters**: Method requires no input parameters, making a direct API call
- **Standard Response Handling**: Validates status codes (200/201) and converts response data to MyLearningsModel
- **Consistent Error Pattern**: Uses DioException with request options and response for uniform error handling
- **Return Type**: Returns Future<MyLearningsModel> for async operation handling

This pattern is ideal for simple API endpoints that don't require query parameters or complex request configuration.

### 2. GET Request with Headers

**GET Request with Custom Headers Pattern:**

The SectorDataSource demonstrates how to include custom headers in API requests:
- **Header Configuration**: Creates header map with Constants.acceptLanguageHeader set to uppercase locale value
- **Options Usage**: Uses Dio Options to merge custom headers with existing _dio.options.headers using spread operator
- **Locale Parameter**: Accepts locale string parameter for internationalization support
- **Header Merging**: Combines default Dio headers with custom Accept-Language header for proper API communication
- **Response Processing**: Uses sectorsFromJson helper function instead of model's fromJson method for list processing
- **Return Type**: Returns Future<List<SectorModel>> for handling multiple sector objects

This pattern is essential for APIs that require specific headers like language preferences, authentication tokens, or content type specifications.

### 3. POST Request with Body

**POST Request with Body Pattern:**

The AuthDataSource shows how to send POST requests with JSON data:
- **Debug Logging**: Uses appPrint with jsonEncode to log request data for debugging purposes
- **POST Method**: Uses _dio.post with ApiConstants.signInPath endpoint and data parameter containing authModel.toJson()
- **Request Body**: Converts AuthModel to JSON using toJson() method for proper serialization
- **Response Validation**: Checks both status code and data type (Map) to ensure valid response structure
- **Token Extraction**: Extracts accessToken and refreshToken from response data using Constants keys
- **Record Return Type**: Returns (String, String) record type for multiple values (access token, refresh token)
- **Type Safety**: Uses string interpolation to ensure token values are strings

This pattern is crucial for authentication endpoints and any API calls that require sending data in the request body.

### 4. Request with Query Parameters

**Request with Query Parameters Pattern:**

The SearchDataSource demonstrates how to include query parameters in GET requests:
- **Query Parameters Map**: Creates inline map with Constants.term (search query), Constants.page (pagination), and Constants.size (results limit)
- **Parameter Passing**: Uses queryParameters parameter in _dio.get to append URL query string
- **Search Functionality**: Accepts query string parameter and sets default pagination values (page: 0, size: 6)
- **URL Construction**: Dio automatically constructs proper URL with query parameters appended to ApiConstants.searchPath
- **Direct Model Conversion**: Uses SearchModel.fromJson directly on response.data for straightforward deserialization
- **Standard Error Handling**: Maintains consistent error throwing pattern with DioException

This pattern is ideal for search endpoints, filtering, pagination, and any API that requires URL query parameters.

### 5. File Download with Progress

**File Download with Progress Pattern:**

The downloadSlide method demonstrates advanced file download functionality with progress tracking:
- **Token Retrieval**: Uses GetIt dependency injection to access AuthTokenProvider and retrieve authentication token
- **Resource Extraction**: Extracts slideKey and fileName from Resource object with null safety using ?? operator
- **URL Construction**: Builds complete download URL by concatenating base URL, API path, and slide key
- **Custom Headers**: Creates headers map with Authorization (Bearer token), X-Current-Role (TRAINEE), and X-Request-Id (UUID)
- **Download Task**: Creates DownloadTask object with URL, headers, filename, and progress update configuration
- **Progress Callback**: Uses FileDownloader with onProgress callback that forwards progress updates to caller
- **Background Download**: Leverages background_downloader package for robust file downloading with progress tracking

This pattern is essential for downloading large files like presentations, videos, or documents with user progress feedback.

## Common Data Sources

### 1. Authentication Data Source

**AuthDataSource Class Structure:**
- Annotated with @injectable for dependency injection
- Contains signInWithToken method that accepts AuthModel parameter and returns Future<(String, String)> tuple
- Includes refreshToken method that takes RefreshTokenModel parameter and returns Future<(String, String)> tuple
- Provides signOut method that returns Future<void> for clearing authentication tokens
- Handles OAuth token-based authentication flow and token refresh logic
- Manages user session lifecycle

### 2. Catalog Data Sources

**TrainingsDataSource Class:**
- Annotated with @injectable for dependency injection
- Contains getTrainingsList method that accepts TrainingsRequestParams and returns Future<TrainingsModel>
- Handles training catalog retrieval with filtering capabilities

**LearningTracksDataSource Class:**
- Annotated with @injectable for dependency injection
- Provides getLearningTracks method that takes LearningTracksRequestParams and returns Future<LearningTracksModel>
- Manages learning tracks catalog data retrieval

**LocationDataSource Class:**
- Annotated with @injectable for dependency injection
- Includes getLocations method that accepts String locale parameter and returns Future<List<LocationModel>>
- Retrieves available locations and cities for filtering

**TrainingProvidersDataSource Class:**
- Annotated with @injectable for dependency injection
- Contains getTrainingProviders method that takes String locale parameter and returns Future<List<TrainingProviderModel>>
- Manages training providers and organizations data retrieval

### 3. User Data Sources

**UserDataSource Class:**
- Annotated with @injectable for dependency injection
- Contains getUserData method that returns Future<UserModel> for retrieving user profile information
- Includes updateUserProfile method that accepts UserModel parameter and returns Future<void>
- Provides uploadProfilePicture method that takes File imageFile parameter and returns Future<String>
- Manages user profile data operations and image uploads

**MyLearningsDataSource Class:**
- Annotated with @injectable for dependency injection
- Contains getMyLearnings method that returns Future<MyLearningsModel>
- Retrieves user's enrolled courses and learning progress data

### 4. Content Data Sources

**TrainingConsumptionDataSource Class:**
- Annotated with @injectable for dependency injection
- Contains getTrainingConsumptionDetails method that accepts String trainingId and returns Future<TrainingConsumptionModel>
- Includes markLessonAsCompleted method that takes String trainingId and String lessonId parameters, returns Future<void>
- Provides getVideoUrl method that accepts String videoKey parameter and returns Future<String>
- Contains downloadFile method that takes String fileKey and String fileName parameters, returns Future<void>
- Manages training content consumption, progress tracking, video streaming, and file downloads

## Error Handling

### Standard Error Pattern

All data sources use consistent error handling:

**Standard Error Handling Pattern:**
- Create async method apiMethod that returns Future<ModelType>
- Make HTTP request using _dio.get(endpoint)
- Check response.statusCode against Constants.statusCode200 and Constants.statusCode201
- Extract data from response.data when status codes indicate success
- Use ModelType.fromJson(data) to convert response data to model objects
- Throw DioException with requestOptions and response for error handling by interceptors
- Ensure consistent error propagation throughout the application

### Error Types Handled

1. **Network Errors**: Connection timeouts, no internet
2. **HTTP Errors**: 4xx and 5xx status codes
3. **Parsing Errors**: Invalid JSON or model conversion failures
4. **Authentication Errors**: 401/403 handled by refresh token interceptor

### Custom Error Handling

**Complex Error Handling Pattern:**
- Create async method complexApiMethod that returns Future<ModelType>
- Wrap HTTP request in try-catch block for comprehensive error handling
- Make request using _dio.get(endpoint) within try block
- Check response.statusCode against Constants.statusCode200 for success validation
- Extract and validate response data structure using type checking (data is! Map<String, dynamic>)
- Throw FormatException with descriptive message for invalid response format
- Use ModelType.fromJson(data) for successful data conversion
- Throw DioException with requestOptions and response for HTTP errors
- Catch FormatException specifically and wrap in DioException with custom error message
- Include RequestOptions(path: endpoint) and descriptive error text for parsing failures

## Request Parameters

### Parameter Objects

Complex requests use parameter objects for type safety:

**TrainingsRequestParams Class Structure:**
- Define final fields: int page, int size, String? searchTerm, List<String>? sectors, List<String>? providers, String? sortBy
- Create const constructor with named parameters and default values (page = 0, size = 10)
- Use nullable types for optional parameters (searchTerm, sectors, providers, sortBy)
- Implement getParams() method that returns Map<String, dynamic>
- Initialize params map with required page and size values
- Add conditional logic for optional parameters:
  - Check searchTerm is not null and not empty before adding 'term' key
  - Validate sectors list is not null and not empty, then join with comma separator for 'sectors' key
  - Validate providers list is not null and not empty, then join with comma separator for 'providers' key
  - Check sortBy is not null before adding 'sort' key
- Return complete params map for API request query parameters

### Usage in Data Source

**Parameter Object Usage Pattern:**
- Create async method getTrainingsList that accepts TrainingsRequestParams requestParams and returns Future<TrainingsModel>
- Use _dio.get() with ApiConstants.trainingCataloguePath as the endpoint
- Pass requestParams.getParams() as queryParameters to convert parameter object to map
- Include comment indicating additional response processing logic follows
- Demonstrates type-safe parameter passing and clean API integration

## Response Processing

### List Processing

**List Processing Pattern:**

The getLocations method demonstrates how to process API responses that return arrays of data:
- **Simple GET Request**: Makes straightforward GET request to ApiConstants.cities endpoint without parameters
- **Type Casting**: Casts response.data to List<dynamic> for safe list processing
- **List Transformation**: Uses map() to convert each JSON object to LocationModel using fromJson factory method
- **List Conversion**: Calls toList() to convert the mapped iterable to a concrete List
- **In-Place Sorting**: Uses cascade operator (..) with sort() to alphabetically order locations by cityName
- **Comparison Function**: Provides (a, b) => a.cityName.compareTo(b.cityName) for string comparison sorting
- **Return Type**: Returns Future<List<LocationModel>> for handling multiple location objects

This pattern is essential for processing API endpoints that return arrays of objects requiring transformation and sorting.

### Complex Response Processing

**Complex Data Processing Pattern:**
- Create async method getComplexData that returns Future<ProcessedModel>
- Make HTTP request using _dio.get(endpoint)
- Validate response.statusCode against Constants.statusCode200
- Extract data from response.data for processing
- Check for required fields like data['content'] and throw FormatException if missing
- Cast data['content'] to List for array processing
- Use map() to transform each item with ItemModel.fromJson(item)
- Apply where() filter to include only items where item.isValid is true
- Convert mapped and filtered results to List with toList()
- Create ProcessedModel instance with:
  - items: processed list of valid items
  - totalCount: data['totalElements'] with null fallback to 0
  - hasMore: data['hasNext'] with null fallback to false
- Throw DioException with requestOptions and response for non-success status codes

## Best Practices

### 1. Consistent Structure

**Good Practice - Consistent Data Source Structure:**
- Use the @injectable annotation for dependency injection
- Create a class named FeatureDataSource with a const constructor
- Accept a required Dio parameter using named parameters
- Store Dio instance as a private final field (_dio)
- Define async methods that return Future<Model> types
- Follow consistent naming conventions

**Bad Practice - Inconsistent Structure:**
- Missing @injectable annotation
- Non-final, non-private Dio field
- Constructor without named parameters
- Inconsistent field declarations

### 2. Error Handling

**Good Practice - Consistent Error Handling:**
- Check response status codes against Constants.statusCode200 and Constants.statusCode201
- Only proceed with data parsing when status codes indicate success
- Use Model.fromJson() method to convert response data to model objects
- Implement proper conditional logic for status code validation

- Throw DioException with requestOptions and response for proper error handling

**Bad Practice - Inconsistent Error Handling:**
- Using simple status code comparison (response.statusCode != 200)
- Throwing generic Exception with basic message instead of DioException
- Missing proper error context and request information

### 3. Type Safety

**Good Practice - Type-Safe Parameter Objects:**
- Create getData method that accepts RequestParams params parameter
- Use params.getParams() to convert parameter object to query parameters map
- Ensure type safety through strongly-typed parameter classes
- Make HTTP request with _dio.get(endpoint, queryParameters: params.getParams())

**Bad Practice - Raw Parameters:**
- Accept Map<String, dynamic> params directly without type safety
- Missing parameter validation and type checking
- Direct usage of raw map in _dio.get(endpoint, queryParameters: params)

### 4. Header Management

**Good Practice - Merge with Existing Headers:**
- Create header map with Constants.acceptLanguageHeader key and locale.toUpperCase() value
- Use spread operator to merge _dio.options.headers with new header map
- Make request with Options(headers: {..._dio.options.headers, ...header})
- Preserves existing headers while adding new ones

**Bad Practice - Override All Headers:**
- Create Options with only Constants.acceptLanguageHeader and locale value
- Overwrites all existing headers instead of merging
- Loses important headers like Authorization, X-Request-Id, etc.

### 5. Response Validation

**Response Validation Best Practices:**

- **Status Code Check**: Validate response.statusCode against Constants.statusCode200 before processing data
- **Data Type Validation**: Check if response.data is Map<String, dynamic> using type check (data is! Map<String, dynamic>)
- **Error Handling**: Throw FormatException('Invalid response format') for invalid data types before calling Model.fromJson(data)

**Bad Practice - Assume Response Structure:**
- Directly calling Model.fromJson(response.data) without validation
- Can throw exceptions if data is null or has wrong type
- Missing proper error handling and data validation

### 6. Logging

**Good Practice - Debug Logging:**
- Create a getData() method that returns Future<Model>
- Use appPrint() function to log endpoint information before making requests
- Log response status codes after receiving responses
- Use debug-safe logging that won't expose sensitive data in production
- Follow consistent logging patterns throughout data sources

**Bad Practice - Production Logging:**
- Using print() statements that expose sensitive response data in production builds
- Logging complete API responses that may contain user data or tokens

## Related Documentation

- [API Architecture](api-architecture.md) - Overall API communication setup
- [Repositories](repositories.md) - Repository pattern usage
- [Models](models.md) - Data models and serialization
- [Interceptors](interceptors.md) - Request/response interceptors
- [Error Handling](../core/error-handling.md) - Error management strategies

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
