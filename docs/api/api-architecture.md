# API Architecture

## Table of Contents
- [Overview](#overview)
- [Architecture Components](#architecture-components)
- [HTTP Client Configuration](#http-client-configuration)
- [Interceptor System](#interceptor-system)
- [Data Flow](#data-flow)
- [Error Handling](#error-handling)
- [Security](#security)
- [Performance](#performance)

## Overview

The NSP mobile application implements a robust API communication layer using **Dio** HTTP client with a comprehensive interceptor system. The architecture ensures secure, reliable, and performant communication with backend services while maintaining clean separation of concerns.

### Key Features
- **Secure Authentication**: Automatic token management and refresh
- **Error Resilience**: Intelligent retry logic and error handling
- **Performance Optimization**: Request caching and connection pooling
- **Development Tools**: Comprehensive logging and debugging support
- **Clean Architecture**: Clear separation between data access and business logic

## Architecture Components

### API Layer Structure

```mermaid
graph TB
    subgraph "🌐 API Communication Layer"
        Client[Dio HTTP Client<br/>• Base Configuration<br/>• Timeout Settings<br/>• Environment Aware]

        subgraph "🔧 Interceptor Chain"
            Auth[Auth Interceptor<br/>• Token Injection<br/>• Role Headers<br/>• Request IDs]
            Refresh[Refresh Token<br/>• Auto Renewal<br/>• Session Management<br/>• Error Recovery]
            Retry[Retry Logic<br/>• Network Failures<br/>• Exponential Backoff<br/>• Smart Evaluation]
            Debug[Debug Tools<br/>• Alice Inspector<br/>• Request Logging<br/>• Response Monitoring]
        end

        subgraph "📊 Data Layer"
            DataSources[Data Sources<br/>• API Endpoints<br/>• Request Building<br/>• Response Parsing]
            Repositories[Repositories<br/>• Data Coordination<br/>• Caching Logic<br/>• Error Translation]
            Models[Data Models<br/>• JSON Serialization<br/>• Type Safety<br/>• Validation]
        end
    end

    subgraph "🏛️ Domain Layer"
        UseCases[Use Cases<br/>• Business Logic<br/>• Data Orchestration<br/>• Validation Rules]
        Entities[Domain Entities<br/>• Business Objects<br/>• Core Models<br/>• Business Rules]
    end

    Client --> Auth
    Auth --> Refresh
    Refresh --> Retry
    Retry --> Debug
    Debug --> DataSources

    DataSources --> Repositories
    Repositories --> Models
    Repositories --> UseCases
    UseCases --> Entities

    style Client fill:#e3f2fd
    style Auth fill:#f3e5f5
    style Refresh fill:#e8f5e8
    style Retry fill:#fff3e0
    style Debug fill:#fce4ec
    style DataSources fill:#e0f2f1
    style Repositories fill:#f1f8e9
    style Models fill:#fff8e1
```

### Request Flow Architecture

```mermaid
sequenceDiagram
    participant App as Application
    participant Repo as Repository
    participant DS as Data Source
    participant Dio as Dio Client
    participant Auth as Auth Interceptor
    participant Retry as Retry Interceptor
    participant API as Backend API

    App->>Repo: Request Data
    Repo->>DS: Call Data Source
    DS->>Dio: HTTP Request
    Dio->>Auth: Add Auth Headers
    Auth->>Retry: Process Request
    Retry->>API: Send Request

    alt Success Response
        API-->>Retry: 200 OK + Data
        Retry-->>Auth: Forward Response
        Auth-->>Dio: Return Response
        Dio-->>DS: Raw Response
        DS->>DS: Parse & Validate
        DS-->>Repo: Data Model
        Repo->>Repo: Transform to Entity
        Repo-->>App: Domain Entity
    else Auth Error
        API-->>Retry: 401/403 Error
        Retry->>Auth: Handle Auth Error
        Auth->>Auth: Refresh Token
        Auth->>API: Retry with New Token
        API-->>Auth: Success Response
        Auth-->>Dio: Return Response
    else Network Error
        API-->>Retry: Network Error
        Retry->>Retry: Evaluate Retry
        Retry->>API: Retry Request
        API-->>Retry: Success/Failure
    end
```

## HTTP Client Configuration

### Dio Client Setup

The Dio HTTP client serves as the foundation of the API communication layer, configured as a singleton with environment-specific settings and comprehensive debugging capabilities.

**Core Configuration**:
- **Singleton Pattern**: Single Dio instance shared throughout the application lifecycle
- **Environment Awareness**: Automatic base URL selection based on build environment (dev/staging/prod)
- **Timeout Management**: 30-second timeouts optimized for mobile network conditions
- **Debug Integration**: Alice HTTP inspector for development request/response monitoring
- **Dependency Injection**: Automatic registration through Injectable module system

**Network Optimization**:
- **Connection Pooling**: Efficient HTTP connection reuse for improved performance
- **Timeout Configuration**: Balanced timeouts for connection establishment and data transfer
- **Base URL Management**: Environment-specific endpoint configuration for seamless deployment
- **Header Standardization**: Consistent header management across all API requests

### Environment Configuration

Environment-specific configurations are managed through compile-time constants for security and flexibility.

**Environment Configuration Management:**

The EnvironmentConfigs class provides compile-time environment variable access for secure configuration management. This approach ensures sensitive configuration data is embedded during build time rather than stored in the app bundle.

**Configuration Properties:**
- **Base URL**: API endpoint URL that varies by deployment environment (development, staging, production)
- **Environment Identifier**: Current environment name for conditional feature flags and logging
- **Qiwa Client ID**: OAuth client identifier for Qiwa authentication integration
- **Compile-Time Security**: All values are set during build process using --dart-define flags for enhanced security

**Environment Variables:**
- **BASE_URL**: API endpoint URL (changes per environment)
- **ENVIRONMENT**: Current environment identifier (dev, staging, prod)
- **QIWA_CLIENT_ID**: OAuth client ID for Qiwa integration
- **Compile-time**: Values set during build process for security

## Interceptor System

### Interceptor Setup

**Interceptor Chain Configuration:**

The interceptor initialization method sets up a comprehensive chain of HTTP interceptors to handle authentication, debugging, retries, and error management. This layered approach ensures robust API communication.

**Interceptor Stack:**
- **Debug Interceptor**: Alice HTTP inspector enabled only in debug mode for request/response monitoring
- **Retry Interceptor**: Automatic retry logic for failed GET requests with custom evaluation criteria
- **Authentication Interceptor**: Automatic injection of auth tokens, user roles, and request IDs
- **Error Handling**: Integrated refresh token interceptor for seamless token renewal

**Request Enhancement:**
- **UUID Generation**: Unique request IDs for tracking and debugging purposes
- **Role Headers**: Automatic TRAINEE role assignment for API authorization
- **Token Management**: Secure token retrieval and automatic header injection
- **Error Recovery**: Automatic token refresh and request retry on authentication failures

### 1. Authentication Interceptor

**Purpose**: Automatically injects authentication and tracking headers into all outgoing requests.

**Header Management**:
- **Authorization Token**: Retrieves current auth token from secure storage and adds Bearer token header
- **User Role**: Automatically sets X-Current-Role header to TRAINEE for proper API authorization
- **Request Tracking**: Generates unique UUID for each request using X-Request-Id header for debugging
- **Conditional Authentication**: Only adds authorization header when valid token is available

**Security Features**:
- **Secure Token Retrieval**: Uses AuthTokenProvider with secure storage for token access
- **Automatic Header Injection**: Transparent authentication without manual header management
- **Role-Based Access**: Consistent role assignment for API endpoint authorization
- **Request Traceability**: Unique request IDs for debugging and monitoring purposes

### 2. Refresh Token Interceptor

**Refresh Token Interceptor Logic:**

The RefreshTokenInterceptor handles automatic token renewal when authentication errors occur, providing seamless user experience without requiring manual re-authentication.

**Error Handling Flow:**
- **Error Detection**: Monitors for 401 (Unauthorized) and 403 (Forbidden) HTTP status codes
- **Token Refresh**: Automatically attempts to refresh expired authentication tokens
- **Request Retry**: Re-executes the original failed request with the new token
- **Fallback Handling**: Initiates logout flow if token refresh fails

**Key Features:**
- **Transparent Operation**: Users don't experience authentication interruptions
- **Automatic Recovery**: Seamlessly handles token expiration scenarios
- **Error Propagation**: Properly forwards non-authentication errors
- **Logout Integration**: Triggers app-wide logout when refresh fails completely

### 3. Retry Interceptor

**Purpose**: Implements intelligent retry logic for failed network requests to improve reliability.

**Retry Strategy**:
- **GET Request Focus**: Only retries GET requests to avoid unintended side effects
- **Smart Evaluation**: Custom evaluator determines which errors warrant retry attempts
- **Exponential Backoff**: Increasing delays between retry attempts to avoid overwhelming servers
- **Maximum Attempts**: Configurable retry limits to prevent infinite retry loops

**Error Evaluation**:
- **Network Timeouts**: Retries connection and receive timeout errors
- **Connection Errors**: Handles network connectivity issues with automatic retry
- **Server Errors**: Retries 5xx server errors that may be temporary
- **Client Error Exclusion**: Avoids retrying 4xx client errors that won't resolve with retry

**Performance Benefits**:
- **Improved Reliability**: Handles transient network issues automatically
- **User Experience**: Reduces failed requests due to temporary network problems
- **Resource Efficiency**: Smart evaluation prevents unnecessary retry attempts
- **Configurable Behavior**: Customizable retry parameters for different use cases

## Data Flow

### Request Processing Flow

```mermaid
graph LR
    subgraph "📱 Application Layer"
        UI[User Interface]
        BLoC[BLoC State Management]
    end

    subgraph "🏛️ Domain Layer"
        UseCase[Use Cases]
        RepoInterface[Repository Interface]
    end

    subgraph "💾 Data Layer"
        Repository[Repository Implementation]
        DataSource[Data Source]
        Model[Data Models]
    end

    subgraph "🌐 Network Layer"
        Dio[Dio Client]
        Interceptors[Interceptor Chain]
        API[Backend API]
    end

    UI -->|User Action| BLoC
    BLoC -->|Execute| UseCase
    UseCase -->|Call| RepoInterface
    RepoInterface -->|Implement| Repository
    Repository -->|Request| DataSource
    DataSource -->|HTTP Call| Dio
    Dio -->|Process| Interceptors
    Interceptors -->|Send| API

    API -->|Response| Interceptors
    Interceptors -->|Return| Dio
    Dio -->|Raw Data| DataSource
    DataSource -->|Parse| Model
    Model -->|Transform| Repository
    Repository -->|Entity| UseCase
    UseCase -->|Result| BLoC
    BLoC -->|State| UI
```

### Error Handling Flow

```mermaid
graph TD
    Request[API Request] -->|Send| Server{Server Response}

    Server -->|200-299| Success[Success Response]
    Server -->|401/403| AuthError[Auth Error]
    Server -->|4xx| ClientError[Client Error]
    Server -->|5xx| ServerError[Server Error]
    Server -->|Network| NetworkError[Network Error]

    Success -->|Parse| DataModel[Data Model]
    DataModel -->|Transform| Entity[Domain Entity]

    AuthError -->|Refresh Token| TokenRefresh{Token Refresh}
    TokenRefresh -->|Success| RetryRequest[Retry Original Request]
    TokenRefresh -->|Failure| Logout[Force Logout]
    RetryRequest -->|Send| Server

    ClientError -->|Log & Report| ErrorState[Error State]

    ServerError -->|Evaluate| RetryLogic{Should Retry?}
    RetryLogic -->|Yes| DelayRetry[Delay & Retry]
    RetryLogic -->|No| ErrorState
    DelayRetry -->|Send| Server

    NetworkError -->|Evaluate| NetworkRetry{Network Retry?}
    NetworkRetry -->|Yes| DelayRetry
    NetworkRetry -->|No| ErrorState

    ErrorState -->|Display| UserFeedback[User Error Message]
```

## Error Handling

### Error Classification

**Authentication Errors (401/403)**:
- **Automatic Token Refresh**: Attempts to refresh expired tokens transparently
- **Session Recovery**: Retries original request with new authentication token
- **Logout Fallback**: Forces user logout if token refresh fails completely
- **User Notification**: Minimal disruption to user experience during token refresh

**Network Errors**:
- **Connection Timeouts**: Automatic retry with exponential backoff
- **DNS Resolution**: Retry logic for temporary DNS issues
- **Network Unavailable**: Graceful degradation with offline mode support
- **Bandwidth Issues**: Adaptive timeout adjustments for slow connections

**Server Errors (5xx)**:
- **Temporary Failures**: Automatic retry for likely temporary server issues
- **Service Unavailable**: Intelligent backoff to avoid overwhelming recovering servers
- **Gateway Errors**: Retry logic for proxy and gateway timeout issues
- **Rate Limiting**: Respect server rate limits with appropriate delays

**Client Errors (4xx)**:
- **Validation Errors**: Immediate user feedback without retry attempts
- **Not Found**: Clear error messages for missing resources
- **Bad Request**: Detailed error information for debugging
- **Forbidden**: Appropriate access denied messaging

## Security

### Authentication Security

**Token Management**:
- **Secure Storage**: Authentication tokens stored using Flutter Secure Storage
- **Automatic Refresh**: Transparent token renewal without user intervention
- **Token Validation**: Server-side validation of token authenticity and expiration
- **Secure Transmission**: All tokens transmitted over HTTPS with proper headers

**Request Security**:
- **HTTPS Enforcement**: All API communication uses encrypted HTTPS connections
- **Certificate Pinning**: Optional certificate pinning for enhanced security
- **Request Signing**: Unique request IDs for tracking and preventing replay attacks
- **Header Validation**: Consistent security headers across all API requests

### Data Protection

**Transmission Security**:
- **TLS Encryption**: End-to-end encryption for all data transmission
- **Data Validation**: Server-side validation of all incoming data
- **Sanitization**: Input sanitization to prevent injection attacks
- **Compression**: Secure compression algorithms for bandwidth optimization

**Privacy Protection**:
- **Data Minimization**: Only necessary data transmitted in API requests
- **PII Handling**: Special handling for personally identifiable information
- **Audit Logging**: Comprehensive logging for security audit trails
- **Access Control**: Role-based access control for API endpoints

## Performance

### Optimization Strategies

**Request Optimization**:
- **Connection Pooling**: Reuse HTTP connections for improved performance
- **Request Batching**: Combine multiple related requests when possible
- **Compression**: Automatic request/response compression for bandwidth efficiency
- **Caching Headers**: Proper cache control headers for client-side caching

**Response Optimization**:
- **Pagination**: Efficient pagination for large data sets
- **Field Selection**: Request only necessary data fields to reduce payload size
- **Response Caching**: Intelligent caching of frequently accessed data
- **Lazy Loading**: Load data on-demand to improve initial response times

### Monitoring and Analytics

**Performance Metrics**:
- **Request Timing**: Track request duration and identify slow endpoints
- **Error Rates**: Monitor error frequencies and patterns
- **Network Quality**: Adapt behavior based on network conditions
- **Cache Hit Rates**: Optimize caching strategies based on usage patterns

**Debug and Development**:
- **Alice Integration**: Comprehensive HTTP traffic inspection during development
- **Request Logging**: Detailed logging of all API interactions
- **Error Tracking**: Integration with crash reporting and error tracking services
- **Performance Profiling**: Tools for identifying and resolving performance bottlenecks

## Related Documentation

- [Architecture Overview](../architecture/overview.md)
- [Data Sources](data-sources.md)
- [Repositories](repositories.md)
- [Models and Serialization](models.md)
- [Interceptors Deep Dive](interceptors.md)
- [Error Handling Guide](../core/error-handling.md)
- [Security Best Practices](../development/coding-standards.md)
