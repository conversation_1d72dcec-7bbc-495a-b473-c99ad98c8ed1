# Repositories

This document covers the repository pattern implementation in the NSP Flutter application. Repositories provide a clean abstraction layer between the domain logic and data sources, implementing business rules and caching strategies.

## Table of Contents

- [Overview](#overview)
- [Repository Pattern](#repository-pattern)
- [Implementation Types](#implementation-types)
- [Caching Strategies](#caching-strategies)
- [Business Logic](#business-logic)
- [Error Handling](#error-handling)
- [Testing Repositories](#testing-repositories)
- [Best Practices](#best-practices)

## Overview

Repositories in the NSP app serve as the bridge between the domain layer and data layer. They encapsulate data access logic, implement caching strategies, and provide a clean interface for business logic consumption.

### Key Responsibilities

- **Data Abstraction**: Hide data source implementation details
- **Business Rules**: Implement domain-specific data processing
- **Caching**: Manage data caching and invalidation
- **Error Translation**: Convert data layer errors to domain errors
- **Data Transformation**: Transform data models for domain consumption

### Architecture Position

```
Presentation Layer (BLoCs/Cubits)
        ↓
Domain Layer (Repositories) ← You are here
        ↓
Data Layer (Data Sources)
        ↓
External APIs
```

## Repository Pattern

### Basic Repository Structure

**Basic Repository Implementation:**

The TrainingsRepository demonstrates the standard repository pattern with dependency injection:
- **@injectable Annotation**: Marks class for automatic dependency injection registration with GetIt
- **Constructor Injection**: Uses named parameter with required TrainingsDataSource dependency, assigned to private final field
- **Dependency Encapsulation**: _dataSource field provides controlled access to data layer functionality
- **Simple Pass-Through**: getTrainings method directly delegates to _dataSource.getTrainingsList with parameter forwarding
- **Async Operation**: Returns Future<TrainingsModel> for asynchronous data fetching
- **Clean Architecture**: Maintains separation between domain and data layers while providing business interface

This pattern provides a clean abstraction over data sources while enabling dependency injection and testability.

### Repository with Business Logic

**Authentication Repository with Business Logic**

The AuthRepository demonstrates a repository implementation that combines data access with essential business logic for authentication management:

**Dependency Injection Setup**: Uses @injectable annotation with constructor dependency injection for AuthDataSource and AuthTokenProvider, ensuring loose coupling and testability through the dependency injection container.

**Private Field Management**: Stores injected dependencies as private final fields (_dataSource, _authTokenProvider) following encapsulation principles and preventing external modification of critical authentication components.

**Sign-In Process**: The signIn method orchestrates the complete authentication flow by calling the data source with AuthModel credentials, receiving a tuple containing access and refresh tokens, then securely storing both tokens through the AuthTokenProvider for session management.

**Token Refresh Logic**: Implements automatic token refresh functionality by accepting a RefreshTokenModel, requesting new tokens from the data source, and updating the stored tokens to maintain seamless user sessions without requiring re-authentication.

**Sign-Out Coordination**: The signOut method performs complete session cleanup by notifying the data source of the logout action and invalidating stored tokens through the AuthTokenProvider, ensuring proper security and session termination.

**Business Logic Integration**: Beyond simple data access, the repository coordinates between multiple services (data source and token provider) to implement complete authentication workflows, demonstrating the repository pattern's role in orchestrating business operations.

### Repository Interface Pattern

**Repository Interface Implementation:**

The repository interface pattern demonstrates clean architecture principles with abstract contracts and concrete implementations:

**Abstract Interface Definition**: ITrainingsRepository defines the contract with three key methods - getTrainings accepting TrainingsRequestParams and returning Future<TrainingsModel>, getTrainingDetails accepting trainingId string and returning Future<TrainingDetailsModel>, and enrollInTraining accepting trainingId for enrollment operations.

**Concrete Implementation**: TrainingsRepository implements ITrainingsRepository with @injectable annotation for dependency injection, constructor accepting required TrainingsDataSource dependency stored in private _dataSource field.

**Method Implementation**: Each @override method delegates to the corresponding data source method - getTrainings forwards parameters to _dataSource.getTrainingsList, getTrainingDetails passes trainingId to _dataSource.getTrainingDetails, and enrollInTraining calls data source enrollment then adds business logic like _trackEnrollment for analytics.

**Business Logic Integration**: The enrollInTraining method demonstrates how repositories can add domain-specific logic (analytics tracking) beyond simple data access, while maintaining clean separation between data access and business rules.

**Type Safety**: All methods maintain strong typing with specific parameter types and Future return types, ensuring compile-time safety and clear API contracts for consuming layers.

## Implementation Types

### 1. Simple Pass-Through Repository

For basic CRUD operations without additional logic:

**Simple Pass-Through Repository Pattern:**

The QuizRepository exemplifies a straightforward repository implementation for basic operations:
- **Minimal Structure**: @injectable class with single QuizDatasource dependency injection
- **Direct Delegation**: submitAnswer method directly forwards parameters (trainingId, lessonId, AnswerModel) to data source
- **No Business Logic**: Pure pass-through without additional validation, transformation, or caching
- **Type Safety**: Maintains strong typing with Future<QuizResult> return type and specific parameter types
- **Clean Interface**: Provides domain-level interface while delegating implementation to data layer
- **Testability**: Enables easy mocking of data source for unit testing

This pattern is ideal for simple CRUD operations that don't require additional business logic or data manipulation.

### 2. Caching Repository

For data that benefits from caching:

**Caching Repository Implementation:**

The TrainingProvidersRepository demonstrates in-memory caching for performance optimization:
- **@singleton Annotation**: Ensures single instance throughout app lifecycle for consistent cache state
- **Cache Field**: _cachedTrainingProviders nullable list stores fetched data in memory
- **Cache Check Logic**: getTrainingProviders first checks if cached data exists before making API call
- **Cache Population**: Fetches data from _dataSource.getTrainingProviders and stores result in cache
- **Cache Return**: Returns cached data immediately on subsequent calls for improved performance
- **Cache Management**: clearCache method provides manual cache invalidation when needed
- **Locale Parameter**: Accepts locale parameter but doesn't cache per locale (simple implementation)

This pattern reduces API calls and improves app responsiveness for frequently accessed, relatively static data.

### 3. Complex Business Logic Repository

For repositories with significant business rules:

**Complex Business Logic Repository Implementation:**

The UserRepository demonstrates advanced repository patterns with multiple dependencies and comprehensive business logic:

**Multi-Dependency Injection**: @injectable class constructor accepts three dependencies - UserDataSource for data access, AuthTokenProvider for authentication management, and AnalyticsService for tracking, all stored as private final fields.

**Authentication Integration**: getUserProfile method first validates authentication by checking token existence through _tokenProvider.getToken(), throwing UnauthorizedException if no valid token exists, ensuring secure data access.

**Analytics Integration**: Successful profile access triggers analytics tracking with 'profile_accessed' event including user_id and timestamp, while errors are logged with 'profile_fetch_failed' event for monitoring and debugging.

**Data Validation**: updateProfile method implements comprehensive validation through _validateUserData helper, checking for empty names and invalid email formats using regex pattern, throwing ValidationException for invalid data.

**Business Logic Orchestration**: Profile update process combines data validation, data source update call, and analytics tracking with 'profile_updated' event including user_id and fields_updated array for complete operation monitoring.

**Helper Methods**: Private methods _validateUserData, _isValidEmail, and _getUpdatedFields encapsulate specific business rules, maintaining clean separation of concerns and reusable validation logic.

**Error Handling**: Try-catch blocks ensure proper error logging while allowing exceptions to propagate to calling layers, maintaining error transparency with added monitoring capabilities.

## Caching Strategies

### 1. Simple In-Memory Caching

**Locale-Aware Caching Repository:**

The SectorRepository implements sophisticated caching with locale awareness and data filtering:
- **@singleton Pattern**: Single instance ensures consistent cache state across the application
- **Dual Cache Fields**: _cachedSectors stores data and _cachedLocale tracks which locale the cache represents
- **Locale-Specific Caching**: Cache validation checks both data existence and locale match for accurate cache hits
- **Data Filtering**: Business logic removes sectors with empty titles using removeWhere() before caching
- **Cache Invalidation**: clearCache method resets both cached data and locale for complete cache clearing
- **Locale Tracking**: Stores locale alongside data to prevent serving wrong language data from cache
- **Performance Optimization**: Avoids unnecessary API calls and data processing for same locale requests

This pattern provides intelligent caching that respects internationalization requirements while maintaining data quality.

### 2. Conditional Caching

**Conditional Caching Repository Implementation:**

The SectorRepository demonstrates intelligent caching that respects filtering and search parameters:

**Cache State Management**: @singleton class maintains _cachedActiveSectors for data storage and _cachedActiveLocale for locale tracking, ensuring cache validity across different language contexts.

**Cache Validation Logic**: getActiveSectors method implements sophisticated cache validation checking multiple conditions - no search term, no shown filter, empty or null sort parameters, existing cached data, and matching locale before using cached results.

**Selective Caching Strategy**: Cache is only populated when no filters are applied (term, shown, sort are null/empty), preventing cached filtered results from being served for different filter combinations, ensuring data accuracy.

**Parameter Handling**: Method accepts comprehensive parameters including required locale, optional page/size for pagination, sort array for ordering, term string for search, and shown boolean for visibility filtering.

**Data Source Integration**: When cache miss occurs, method calls _dataSource.getActiveSectors with all parameters, converts response using ActiveSectorResponse.fromJson, and conditionally caches based on filter absence.

**Locale Awareness**: Cache validation includes locale matching to prevent serving wrong language data, with _cachedActiveLocale tracking which locale the cached data represents for accurate cache hits.

### 3. Time-Based Cache Invalidation

**Time-Based Cache Invalidation Implementation:**

The CachedRepository demonstrates advanced caching with automatic expiration and cleanup:

**Generic Cache Storage**: Uses Map<String, CacheEntry> for flexible key-based caching with _cacheTimeout constant set to 5 minutes for automatic expiration policy.

**Generic Cache Method**: getCachedData<T> accepts string key and Future<T> Function() fetchFunction, providing type-safe caching for any data type with automatic fetch-on-miss functionality.

**Expiration Logic**: Checks cached entry existence and expiration status using cachedEntry.isExpired before returning cached data, ensuring stale data is never served to consumers.

**Cache Population**: On cache miss or expiration, executes fetchFunction to retrieve fresh data, creates new CacheEntry with current timestamp plus timeout duration, and stores in cache for future requests.

**Automatic Cleanup**: clearExpiredCache method removes all expired entries using removeWhere with entry.isExpired predicate, maintaining cache efficiency and memory usage.

**CacheEntry Structure**: Helper class encapsulates cached data with expiryTime timestamp, providing isExpired getter that compares current time with expiry time for accurate expiration detection.

**Type Safety**: Generic implementation ensures type safety while maintaining flexibility for different data types, with proper casting and return type preservation throughout the caching flow.

## Business Logic

### 1. Data Transformation

**Data Transformation Repository Implementation:**

The TrainingRepository demonstrates comprehensive data transformation and enhancement patterns:

**Popular Trainings Retrieval**: getPopularTrainings method fetches data using TrainingsRequestParams with sortBy set to 'popularity' and size limited to 10, then applies filtering and transformation pipeline.

**Data Filtering and Enhancement**: Applies where clause to filter only active trainings, then maps each training through _enhanceTrainingData method for business logic enhancement, finally converting to list for consumption.

**Training Data Enhancement**: _enhanceTrainingData method uses copyWith pattern to add computed fields - displayDuration from _formatDuration helper, difficultyLevel from _calculateDifficulty logic, and isRecommended from _isRecommendedForUser analysis.

**Duration Formatting Logic**: _formatDuration converts integer minutes to human-readable format using integer division for hours calculation, modulo for remaining minutes, and conditional formatting for hours display.

**Difficulty Calculation**: _calculateDifficulty implements business rules based on prerequisites count - empty list returns DifficultyLevel.beginner, more than 3 prerequisites returns DifficultyLevel.advanced, otherwise DifficultyLevel.intermediate.

**Recommendation Logic**: _isRecommendedForUser applies business criteria checking training.rating above 4.0 and training.enrollmentCount above 100 to determine recommendation status for enhanced user experience.

**Transformation Pipeline**: Demonstrates functional programming approach with method chaining (where -> map -> toList) for clean, readable data processing that separates filtering, transformation, and collection operations.

### 2. Data Aggregation

**Data Aggregation Repository Implementation:**

The LearningProgressRepository demonstrates complex data aggregation and business metrics calculation:

**Progress Summary Generation**: getUserProgressSummary method fetches MyLearningsModel from data source and constructs LearningProgressSummary with computed metrics including totalCourses, completedCourses, inProgressCourses, totalHoursLearned, averageProgress, and achievements.

**Course Counting Logic**: _countCompletedCourses aggregates completed courses by filtering trainings and learningTracks arrays where status equals Constants.COMPLETED, then summing the lengths for total completed count.

**Hours Calculation**: _calculateTotalHours computes learning time by filtering completed trainings and tracks, using fold operation to sum duration values converted from minutes to hours (division by 60), then combining trainingHours and trackHours.

**Achievement System**: _getAchievements implements gamification logic by checking completed course count against thresholds - 5 or more courses earns Achievement.learningEnthusiast, 10 or more earns Achievement.dedicatedLearner.

**Functional Programming Patterns**: Uses where clauses for filtering, fold operations for aggregation, and list operations for data processing, demonstrating clean functional approaches to complex data manipulation.

**Business Metrics**: Transforms raw learning data into meaningful business metrics for user engagement, progress tracking, and achievement recognition, providing comprehensive learning analytics from simple data sources.

## Error Handling

### 1. Error Translation

**Error Translation Repository Implementation:**

The UserRepository demonstrates comprehensive error handling with domain-specific exception translation:

**Exception Handling Strategy**: getUserProfile method wraps _dataSource.getUserData call in try-catch blocks to intercept and translate data layer exceptions into meaningful domain exceptions.

**HTTP Status Code Translation**: DioException catch block examines response status codes - 404 becomes UserNotFoundException with 'User profile not found' message, 403 becomes UnauthorizedException with 'Access denied to user profile' message.

**Generic Error Handling**: Catches other DioException cases and wraps them in UserServiceException with original error message, plus general catch block for unexpected errors with toString() conversion.

**Custom Domain Exceptions**: Defines three domain-specific exception classes - UserNotFoundException for missing user data, UnauthorizedException for access control violations, and UserServiceException for general service failures.

**Exception Structure**: Each custom exception extends Exception with final String message field and constructor accepting message parameter, providing consistent error information structure for consuming layers.

**Error Abstraction**: Translates low-level HTTP and network errors into high-level domain concepts, preventing data layer implementation details from leaking to business logic and presentation layers.

### 2. Retry Logic

**Retry Logic Repository Implementation:**

The TrainingRepository demonstrates robust retry mechanisms for handling transient failures:

**Retry Wrapper Method**: getTrainingDetails wraps _dataSource.getTrainingDetails call in _retryOperation with maxRetries set to 3 and delay of 1 second for automatic failure recovery.

**Generic Retry Operation**: _retryOperation<T> accepts Future<T> Function() operation with configurable maxRetries and delay parameters, providing reusable retry logic for any async operation.

**Attempt Loop Logic**: While loop tracks attempts counter, executes operation in try-catch block, increments attempts on failure, and rethrows exception when maxRetries exceeded or error is non-retryable.

**Exponential Backoff**: Implements delay multiplication by attempts count (delay * attempts) for exponential backoff strategy, reducing server load and improving success probability on subsequent attempts.

**Retryable Error Detection**: _isRetryableError method checks DioException types for connectionTimeout, receiveTimeout, connectionError, and server errors (status code >= 500) to determine if retry is appropriate.

**Error Classification**: Only retries on transient network errors and server failures, avoiding retries for client errors (4xx status codes) or permanent failures that won't resolve with additional attempts.

**Failure Handling**: Throws 'Max retries exceeded' exception when all retry attempts are exhausted, providing clear indication of persistent failure after retry attempts.

## Testing Repositories

### 1. Unit Testing with Mocks

**Unit Testing Repository Implementation:**

The TrainingsRepository test suite demonstrates comprehensive testing patterns with mock dependencies:

**Mock Generation**: @GenerateMocks annotation generates MockTrainingsDataSource for TrainingsDataSource interface, enabling isolated testing without real data source dependencies.

**Test Setup**: setUp method initializes MockTrainingsDataSource instance and creates TrainingsRepository with mock dependency injection for consistent test environment across all test cases.

**Success Case Testing**: First test arranges TrainingsRequestParams and expected TrainingsModel, configures mock to return expected data using when().thenAnswer(), executes repository method, and verifies result equality and method call count.

**Failure Case Testing**: Second test configures mock to throw DioException using when().thenThrow(), then verifies repository propagates exception correctly using expect() with throwsA matcher and isA<DioException>() type checking.

**Test Structure**: Follows Arrange-Act-Assert pattern with clear separation of test setup, method execution, and result verification for maintainable and readable test cases.

**Verification Patterns**: Uses verify() to confirm mock method calls with specific parameters and call counts, ensuring repository correctly delegates to data source with proper parameter passing.

### 2. Testing Caching Logic

**Caching Logic Testing Implementation:**

The repository caching tests demonstrate verification of cache behavior and invalidation:

**Cache Hit Testing**: First test arranges locale and expected SectorModel list, configures mock data source response, executes getSectors twice, then verifies both results match expected data and data source called only once, confirming cache effectiveness.

**Cache Miss Verification**: Test validates that first call populates cache and second call returns cached data without additional data source calls, using verify() with called(1) to ensure caching prevents redundant API requests.

**Cache Invalidation Testing**: Second test verifies cache clearing functionality by calling getSectors, executing clearCache method, calling getSectors again, then confirming data source called twice using verify() with called(2).

**Cache Lifecycle Testing**: Demonstrates complete cache lifecycle from initial population through cache hit to cache invalidation and repopulation, ensuring cache management works correctly across different scenarios.

**Mock Verification Patterns**: Uses verify() with specific call count expectations to validate caching behavior - called(1) for cache hits and called(2) for cache invalidation scenarios, providing precise cache behavior verification.

## Best Practices

### 1. Single Responsibility

**Single Responsibility Best Practices:**

**Good Practice**: TrainingsRepository demonstrates focused domain responsibility with three related methods - getTrainings for listing, getTrainingDetails for individual items, and enrollInTraining for enrollment actions, all within the training domain scope.

**Bad Practice**: DataRepository violates single responsibility by handling multiple unrelated domains - trainings, users, and sectors - creating tight coupling and making the class difficult to maintain and test.

### 2. Dependency Injection

**Dependency Injection Best Practices:**

**Good Practice**: TrainingsRepository uses @injectable annotation with constructor dependency injection, accepting required TrainingsDataSource parameter and storing in private _dataSource field for loose coupling and testability.

**Bad Practice**: Direct instantiation creates tight coupling by directly creating TrainingsDataSource with Dio dependency, making testing difficult and violating inversion of control principles.

### 3. Error Handling

**Error Handling Best Practices:**

**Good Practice**: getUser method wraps data source call in try-catch block, catches DioException specifically, and translates to domain-specific UserServiceException with meaningful message, preventing data layer details from leaking.

**Bad Practice**: Direct return of _dataSource.getUserData() allows DioException to bubble up to domain layer, exposing implementation details and violating clean architecture boundaries.

### 4. Caching Strategy

**Caching Strategy Best Practices:**

**Good Practice**: invalidateUserCache method provides explicit cache management by setting _cachedUser and _cacheTimestamp to null, giving clear control over cache lifecycle and invalidation timing.

**Bad Practice**: Declaring _cachedData without management strategy leads to stale data issues, memory leaks, and unpredictable cache behavior due to lack of invalidation or validation logic.

## Related Documentation

- [Data Sources](data-sources.md) - Data source implementations
- [Models](models.md) - Data models and serialization
- [API Architecture](api-architecture.md) - Overall API communication setup
- [State Management](../architecture/state-management.md) - BLoC patterns
- [Testing Guide](../development/testing-guide.md) - Testing strategies

---

**Last Updated**: December 2024
**Maintained by**: NSP Development Team
