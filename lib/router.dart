import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/auth/presentation/pages/get_started_page.dart';
import 'package:national_skills_platform/features/auth/presentation/pages/login_page.dart';
import 'package:national_skills_platform/features/auth/presentation/widgets/qiwa_web_view.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/catalog_page.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/presentation/pages/learning_track_details_page.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/pages/training_details_page.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';
import 'package:national_skills_platform/features/home/<USER>/pages/home_page.dart';
import 'package:national_skills_platform/features/home/<USER>/pages/sectors_page.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/pages/my_learnings_page.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/view_all/view_all_my_learnings.dart';
import 'package:national_skills_platform/features/profile_page/presentation/pages/profile_page.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/personal_info_page.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/image_crop_view.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/data/params/qualification_test_params.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/pages/qualification_test_page.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/presentation/pages/quiz_page.dart';
import 'package:national_skills_platform/features/search/presentation/pages/search_page.dart';
import 'package:national_skills_platform/features/search/presentation/pages/view_all_page.dart';
import 'package:national_skills_platform/features/shared/pages/app_bottom_nav_bar.dart';
import 'package:national_skills_platform/features/shared/pages/error_page.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/shared/transitions/slide_in_transition.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/live_session_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/article_lesson.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/file_lesson.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/live_session_page.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/slide_lesson.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/video_lesson.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/training_consumption_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/root_page.dart';

// no idea how to cover this file with tests, thus ignoring the file
// coverage:ignore-file
final GlobalKey<NavigatorState> _homePageNavigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> _catalogNavigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> _myLearningsNavigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> _profileNavigatorKey = GlobalKey<NavigatorState>();

enum Routes {
  login,
  qiwaWebView,
  profile,
  rootPage,
  getStarted,
  catalogPage,
  searchPage,
  trainingDetailsPage,
  learningTrackDetailsPage,
  homePage,
  myLearningPage,
  viewAllPage,
  sectorsPage,
  personalInfoPage,
  trainingConsumptionPage,
  articlePage,
  videoLessonPage,
  fileLessonPage,
  quizPage,
  qualificationTestPage,
  slideLessonPage,
  imageCropperPage,
  liveSessionPage,
  viewAllMyLearnings,
}

GoRouter get router => GetIt.instance<GoRouter>();

void setupRouter() => GetIt.instance.registerSingleton<GoRouter>(
      GoRouter(
        navigatorKey: kDebugMode ? GetIt.instance<Alice>().getNavigatorKey() : null,
        initialLocation: Routes.rootPage.path,
        routes: [
          GoRoute(
            path: Routes.rootPage.path, //note, path of rootPage is '/' but name is 'rootPage'
            name: Routes.rootPage.name,
            builder: (context, state) => const RootPage(),
            routes: [
              GoRoute(
                path: Routes.getStarted.path,
                name: Routes.getStarted.name,
                builder: (context, state) => const GetStartedPage(),
              ),
              GoRoute(
                path: Routes.login.path,
                name: Routes.login.name,
                pageBuilder: (context, state) =>
                    SlideBottomToTopTransition(child: const LoginPage()),
              ),
              GoRoute(
                path: Routes.qiwaWebView.path,
                name: Routes.qiwaWebView.name,
                builder: (context, state) => QiwaWebView(logoutToken: state.extra as String?),
              ),
              GoRoute(
                path: Routes.trainingDetailsPage.path,
                name: Routes.trainingDetailsPage.name,
                builder: (context, state) =>
                    TrainingDetailsPage(trainingID: (state.extra as String?) ?? ''),
                routes: [
                  GoRoute(
                    path: Routes.trainingConsumptionPage.path,
                    name: Routes.trainingConsumptionPage.name,
                    builder: (context, state) {
                      final trainingDetailsModel = state.extra as TrainingDetailsModel?;
                      if (trainingDetailsModel == null) {
                        return ErrorPage(
                          errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                        );
                      }

                      return TrainingConsumptionPage(trainingDetailsModel);
                    },
                    routes: [
                      GoRoute(
                        path: Routes.articlePage.path,
                        name: Routes.articlePage.name,
                        pageBuilder: (context, state) {
                          final lessonParams = state.extra as LessonParams?;

                          if (lessonParams == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: ArticleLesson(lessonParams: lessonParams),
                            forwardType: lessonParams.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.liveSessionPage.path,
                        name: Routes.liveSessionPage.name,
                        pageBuilder: (context, state) {
                          final params = state.extra as LiveSessionParams?;
                          if (params == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: LiveSessionPage(params: params),
                            forwardType: params.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.videoLessonPage.path,
                        name: Routes.videoLessonPage.name,
                        pageBuilder: (context, state) {
                          final lessonParams = state.extra as LessonParams?;

                          if (lessonParams == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: VideoLesson(lessonParams: lessonParams),
                            forwardType: lessonParams.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.fileLessonPage.path,
                        name: Routes.fileLessonPage.name,
                        pageBuilder: (context, state) {
                          final lessonParams = state.extra as LessonParams?;

                          if (lessonParams == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: FileLesson(lessonParams: lessonParams),
                            forwardType: lessonParams.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.slideLessonPage.path,
                        name: Routes.slideLessonPage.name,
                        pageBuilder: (context, state) {
                          final lessonParams = state.extra as LessonParams?;

                          if (lessonParams == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: SlideLesson(lessonParams: lessonParams),
                            forwardType: lessonParams.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.quizPage.path,
                        name: Routes.quizPage.name,
                        pageBuilder: (context, state) {
                          final params = (state.extra as List?) ?? [];
                          final lessonParams = params.first as LessonParams?;
                          final quizResults = params.last as QuizResult?;

                          if (lessonParams == null) {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          return MultiTransition(
                            child: QuizPage(lessonParams: lessonParams, quizResult: quizResults),
                            forwardType: lessonParams.lessonNavigationType,
                          );
                        },
                      ),
                      GoRoute(
                        path: Routes.qualificationTestPage.path,
                        name: Routes.qualificationTestPage.name,
                        pageBuilder: (context, state) {
                          final params = state.extra as QualificationTestParams?;
                          final qualificationTestModel = params?.qualificationTestModel;
                          final trainingId = params?.trainingId;

                          if (params == null ||
                              qualificationTestModel == null ||
                              trainingId == '') {
                            return SlideBottomToTopTransition(
                              child: ErrorPage(
                                errorMessage: LocaleKeys.somethingWentWrongPage_undefinedError.tr(),
                              ),
                            );
                          }

                          if (qualificationTestModel.type == QualificationTestType.POST) {
                            return MultiTransition(
                              child: BlocProvider.value(
                                value: params.trainingConsumptionBloc,
                                child: QualificationTestPage(
                                  qualificationTestModel,
                                  params.trainingId,
                                ),
                              ),
                              forwardType: params.navigationType,
                            );
                          }

                          return MultiTransition(
                            forwardType: params.navigationType,
                            child: BlocProvider.value(
                              value: params.trainingConsumptionBloc,
                              child:
                                  QualificationTestPage(qualificationTestModel, params.trainingId),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
              GoRoute(
                path: Routes.learningTrackDetailsPage.path,
                name: Routes.learningTrackDetailsPage.name,
                builder: (context, state) =>
                    LearningTrackDetailsPage(learningTrackID: (state.extra as String?) ?? ''),
              ),
              GoRoute(
                path: Routes.personalInfoPage.path,
                name: Routes.personalInfoPage.name,
                builder: (context, state) => const PersonalInfoPage(),
              ),
              GoRoute(
                path: Routes.imageCropperPage.path,
                name: Routes.imageCropperPage.name,
                pageBuilder: (context, state) =>
                    SlideBottomToTopTransition(child: const ImageCropView()),
              ),
              StatefulShellRoute.indexedStack(
                builder: (context, state, navigationShell) => AppBottomNavBar(navigationShell),
                branches: [
                  StatefulShellBranch(
                    navigatorKey: _homePageNavigatorKey,
                    routes: [
                      GoRoute(
                        path: Routes.homePage.path,
                        name: Routes.homePage.name,
                        builder: (context, state) => HomePage(
                          authState: (state.extra as AuthStateEnum?) ?? AuthStateEnum.notLoggedIn,
                        ),
                        routes: [
                          GoRoute(
                            path: Routes.sectorsPage.path,
                            name: Routes.sectorsPage.name,
                            builder: (context, state) => SectorsPage(
                              sectors: (state.extra as List<SectorFeatureModel>?) ?? [],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  StatefulShellBranch(
                    navigatorKey: _catalogNavigatorKey,
                    routes: [
                      GoRoute(
                        path: Routes.catalogPage.path,
                        name: Routes.catalogPage.name,
                        builder: (context, state) {
                          final tab = state.uri.queryParameters[RouterQueryConstants.tab];

                          return CatalogPage(
                            key: ValueKey(tab),
                            initialTab: tab == RouterQueryConstants.learningTrackTab ? 1 : 0,
                          );
                        },
                        routes: [
                          GoRoute(
                            path: Routes.searchPage.path,
                            name: Routes.searchPage.name,
                            builder: (context, state) => SearchPage(
                              courseType: (state.extra as CourseType?) ?? CourseType.training,
                            ),
                            routes: [
                              GoRoute(
                                path: Routes.viewAllPage.path,
                                name: Routes.viewAllPage.name,
                                builder: (context, state) {
                                  final params = (state.extra as List?) ?? [];
                                  final courseType = params.first;
                                  final searchResultsState = params.last;

                                  return ViewAllPage(
                                    courseType: courseType,
                                    searchResultsState: searchResultsState,
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                  StatefulShellBranch(
                    navigatorKey: _myLearningsNavigatorKey,
                    routes: [
                      GoRoute(
                        path: Routes.myLearningPage.path,
                        name: Routes.myLearningPage.name,
                        builder: (context, state) => const MyLearningsPage(),
                        routes: [
                          GoRoute(
                            path: Routes.viewAllMyLearnings.path,
                            name: Routes.viewAllMyLearnings.name,
                            builder: (context, state) {
                              final params = (state.extra as List?) ?? [];
                              final learningType = params.first as LearningType;
                              List<ApplicantTraining>? myTrainings;
                              List<LearningTrackView>? myLearningTracks;

                              if (params.last is List<ApplicantTraining>) {
                                myTrainings = params.last as List<ApplicantTraining>;
                              } else if (params.last is List<LearningTrackView>) {
                                myLearningTracks = params.last as List<LearningTrackView>;
                              }

                              return ViewAllMyLearnings(
                                myTrainings: myTrainings,
                                myLearningTracks: myLearningTracks,
                                learningType: learningType,
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                  StatefulShellBranch(
                    navigatorKey: _profileNavigatorKey,
                    routes: [
                      GoRoute(
                        path: Routes.profile.path,
                        name: Routes.profile.name,
                        builder: (context, state) => const ProfilePage(),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );

extension RoutesExtension on Routes {
  String get path {
    switch (this) {
      case Routes.rootPage:
        return '/';
      default:
        return toString().split('.').last;
    }
  }
}

extension Navigator on GoRouter {
  void popUntil(bool Function(RouteMatch) predicate) {
    while (canPop() && !predicate(routerDelegate.currentConfiguration.last)) {
      pop();
    }
  }

  void popAllRoutes() {
    while (canPop()) {
      pop();
    }
  }
}
