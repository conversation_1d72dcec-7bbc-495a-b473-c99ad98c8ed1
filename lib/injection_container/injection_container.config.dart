// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:math' as _i407;

import 'package:dio/dio.dart' as _i361;
import 'package:flutter_alice/alice.dart' as _i934;
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as _i447;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../core/bloc_observer.dart' as _i702;
import '../core/shared/token_provider/token_provider.dart' as _i231;
import '../features/auth/data/data_source/data_source/auth_data_source.dart' as _i1047;
import '../features/auth/domain/repositories/auth_repository.dart' as _i869;
import '../features/auth/presentation/bloc/auth_bloc.dart' as _i59;
import '../features/catalog/data/data_sources/learning_tracks_data_source.dart' as _i526;
import '../features/catalog/data/data_sources/location_datasource.dart' as _i46;
import '../features/catalog/data/data_sources/training_providers_datasource.dart' as _i803;
import '../features/catalog/data/data_sources/trainings_data_source.dart' as _i350;
import '../features/catalog/domain/repositories/learning_tracks_repository.dart' as _i379;
import '../features/catalog/domain/repositories/location_repository.dart' as _i649;
import '../features/catalog/domain/repositories/training_providers_repository.dart' as _i312;
import '../features/catalog/domain/repositories/trainings_repositories.dart' as _i548;
import '../features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart' as _i954;
import '../features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart'
    as _i179;
import '../features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart' as _i322;
import '../features/course_details/bloc/course_details_bloc.dart' as _i438;
import '../features/course_details/learning_track_details/data/data_sources/learning_track_details_datasource.dart'
    as _i402;
import '../features/course_details/learning_track_details/domain/repositories/learning_track_details_repository.dart'
    as _i724;
import '../features/course_details/training_details/data/data_sources/training_details_datasource.dart'
    as _i105;
import '../features/course_details/training_details/domain/repositories/training_details_repository.dart'
    as _i141;
import '../features/course_details/usecases/update_study_streams.dart' as _i24;
import '../features/home/<USER>/data_sources/sectors_datasource.dart' as _i1024;
import '../features/home/<USER>/data_sources/sectors_features_datasource.dart' as _i418;
import '../features/home/<USER>/repositories/sectors_features_repository.dart' as _i282;
import '../features/home/<USER>/repositories/sectors_repository.dart' as _i344;
import '../features/home/<USER>/home_page_bloc/home_page_bloc.dart' as _i147;
import '../features/my_learnings/data/data_sources/my_learning_datasource.dart' as _i422;
import '../features/my_learnings/domain/repositories/my_learnings_repository.dart' as _i11;
import '../features/my_learnings/presentation/bloc/my_learnings_bloc.dart' as _i181;
import '../features/profile_page/data/datasource/user_datasource.dart' as _i952;
import '../features/profile_page/domain/repository/user_repository.dart' as _i18;
import '../features/profile_page/presentation/bloc/user_bloc.dart' as _i869;
import '../features/qualification_test/data/data_sources/qualification_test_datasource.dart'
    as _i984;
import '../features/qualification_test/domain/repositories/qualification_test_repository.dart'
    as _i80;
import '../features/qualification_test/presentation/bloc/qualification_test_bloc.dart' as _i607;
import '../features/quiz/data/data_sources/quiz_datasource.dart' as _i700;
import '../features/quiz/domain/repositories/quiz_repository.dart' as _i741;
import '../features/quiz/presentation/bloc/quiz_bloc.dart' as _i942;
import '../features/search/data/data_sources/search_data_source.dart' as _i6;
import '../features/search/domain/repositories/search_repository.dart' as _i575;
import '../features/search/presentation/bloc/search_bloc.dart' as _i348;
import '../features/training_consumption/data/data_sources/training_consumption_datasource.dart'
    as _i674;
import '../features/training_consumption/domain/repositories/training_consumption_repository.dart'
    as _i936;
import '../features/training_consumption/presentation/bloc/training_consumption_bloc.dart' as _i169;
import 'register_module.dart' as _i291;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i702.AppBlocObserver>(() => _i702.AppBlocObserver());
    gh.factory<_i407.Random>(() => registerModule.random());
    gh.factory<_i952.FormDataHelper>(() => _i952.FormDataHelper());
    gh.factory<_i24.UpdateStudyStreams>(() => _i24.UpdateStudyStreams());
    gh.singleton<_i361.Dio>(() => registerModule.dio());
    gh.singleton<_i447.DefaultCacheManager>(() => registerModule.cacheManager());
    gh.lazySingleton<_i934.Alice>(() => registerModule.alice);
    gh.lazySingleton<_i558.FlutterSecureStorage>(() => registerModule.flutterSecureStorage);
    gh.factory<_i952.UserDataSource>(() => _i952.UserDataSource(
          dio: gh<_i361.Dio>(),
          formDataHelper: gh<_i952.FormDataHelper>(),
        ));
    gh.factory<_i46.LocationDataSource>(() => _i46.LocationDataSource(gh<_i361.Dio>()));
    gh.factory<_i803.TrainingProvidersDataSource>(
        () => _i803.TrainingProvidersDataSource(gh<_i361.Dio>()));
    gh.factory<_i649.LocationRepository>(
        () => _i649.LocationRepository(locationDataSource: gh<_i46.LocationDataSource>()));
    gh.factory<_i105.TrainingDetailsDataSource>(
        () => _i105.TrainingDetailsDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i402.LearningTrackDetailsDataSource>(
        () => _i402.LearningTrackDetailsDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i1024.SectorDataSource>(() => _i1024.SectorDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i418.SectorsFeaturesDataSource>(
        () => _i418.SectorsFeaturesDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i1047.AuthDataSource>(() => _i1047.AuthDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i350.TrainingsDataSource>(() => _i350.TrainingsDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i526.LearningTracksDataSource>(
        () => _i526.LearningTracksDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i422.MyLearningsDataSource>(
        () => _i422.MyLearningsDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i700.QuizDatasource>(() => _i700.QuizDatasource(dio: gh<_i361.Dio>()));
    gh.factory<_i6.SearchDataSource>(() => _i6.SearchDataSource(dio: gh<_i361.Dio>()));
    gh.factory<_i674.TrainingConsumptionDatasource>(
        () => _i674.TrainingConsumptionDatasource(dio: gh<_i361.Dio>()));
    gh.factory<_i984.QualificationTestDatasource>(
        () => _i984.QualificationTestDatasource(dio: gh<_i361.Dio>()));
    gh.factory<_i282.SectorsFeaturesRepository>(
        () => _i282.SectorsFeaturesRepository(dataSource: gh<_i418.SectorsFeaturesDataSource>()));
    gh.factory<_i741.QuizRepository>(
        () => _i741.QuizRepository(dataSource: gh<_i700.QuizDatasource>()));
    gh.factory<_i18.UserRepository>(
        () => _i18.UserRepository(dataSource: gh<_i952.UserDataSource>()));
    gh.factory<_i141.TrainingDetailsRepository>(
        () => _i141.TrainingDetailsRepository(dataSource: gh<_i105.TrainingDetailsDataSource>()));
    gh.lazySingleton<_i231.AuthTokenProvider>(
        () => _i231.AuthTokenProvider(secureStorage: gh<_i558.FlutterSecureStorage>()));
    gh.factory<_i548.TrainingsRepository>(
        () => _i548.TrainingsRepository(dataSource: gh<_i350.TrainingsDataSource>()));
    gh.singleton<_i312.TrainingProvidersRepository>(() =>
        _i312.TrainingProvidersRepository(dataSource: gh<_i803.TrainingProvidersDataSource>()));
    gh.factory<_i80.QualificationTestRepository>(() =>
        _i80.QualificationTestRepository(dataSource: gh<_i984.QualificationTestDatasource>()));
    gh.factory<_i954.GetSortedTrainingProvidersUseCase>(
        () => _i954.GetSortedTrainingProvidersUseCase(gh<_i312.TrainingProvidersRepository>()));
    gh.factory<_i607.QualificationTestBloc>(() => _i607.QualificationTestBloc(
          qualificationTestRepository: gh<_i80.QualificationTestRepository>(),
          random: gh<_i407.Random>(),
        ));
    gh.factory<_i942.QuizBloc>(() => _i942.QuizBloc(
          quizRepository: gh<_i741.QuizRepository>(),
          random: gh<_i407.Random>(),
        ));
    gh.singleton<_i344.SectorRepository>(
        () => _i344.SectorRepository(dataSource: gh<_i1024.SectorDataSource>()));
    gh.factory<_i575.SearchRepository>(
        () => _i575.SearchRepository(searchDataSource: gh<_i6.SearchDataSource>()));
    gh.factory<_i724.LearningTrackDetailsRepository>(() => _i724.LearningTrackDetailsRepository(
        dataSource: gh<_i402.LearningTrackDetailsDataSource>()));
    gh.factory<_i869.AuthRepository>(() => _i869.AuthRepository(
          dataSource: gh<_i1047.AuthDataSource>(),
          authTokenProvider: gh<_i231.AuthTokenProvider>(),
        ));
    gh.lazySingleton<_i869.UserBloc>(
        () => _i869.UserBloc(userRepository: gh<_i18.UserRepository>()));
    gh.singleton<_i322.TrainingsBloc>(
        () => _i322.TrainingsBloc(trainingsRepository: gh<_i548.TrainingsRepository>()));
    gh.singleton<_i147.HomePageBloc>(
        () => _i147.HomePageBloc(sectorsFeaturesRepository: gh<_i282.SectorsFeaturesRepository>()));
    gh.factory<_i936.TrainingConsumptionRepository>(() =>
        _i936.TrainingConsumptionRepository(dataSource: gh<_i674.TrainingConsumptionDatasource>()));
    gh.factory<_i379.LearningTracksRepository>(
        () => _i379.LearningTracksRepository(dataSource: gh<_i526.LearningTracksDataSource>()));
    gh.factory<_i11.MyLearningsRepository>(
        () => _i11.MyLearningsRepository(dataSource: gh<_i422.MyLearningsDataSource>()));
    gh.factory<_i169.TrainingConsumptionBloc>(() => _i169.TrainingConsumptionBloc(
        trainingConsumptionRepository: gh<_i936.TrainingConsumptionRepository>()));
    gh.factory<_i348.SearchBloc>(
        () => _i348.SearchBloc(searchRepository: gh<_i575.SearchRepository>()));
    gh.lazySingleton<_i179.LearningTracksBloc>(() =>
        _i179.LearningTracksBloc(learningTracksRepository: gh<_i379.LearningTracksRepository>()));
    gh.singleton<_i59.AuthBloc>(() => _i59.AuthBloc(
          authRepository: gh<_i869.AuthRepository>(),
          userBloc: gh<_i869.UserBloc>(),
          authTokenProvider: gh<_i231.AuthTokenProvider>(),
        ));
    gh.singleton<_i181.MyLearningsBloc>(() => _i181.MyLearningsBloc(
          myLearningsRepository: gh<_i11.MyLearningsRepository>(),
          trainingDetailsRepository: gh<_i141.TrainingDetailsRepository>(),
          trainingConsumptionRepository: gh<_i936.TrainingConsumptionRepository>(),
          trainingsBloc: gh<_i322.TrainingsBloc>(),
        ));
    gh.factory<_i438.CourseDetailsBloc>(() => _i438.CourseDetailsBloc(
          trainingDetailsRepository: gh<_i141.TrainingDetailsRepository>(),
          learningTrackDetailsRepository: gh<_i724.LearningTrackDetailsRepository>(),
          myLearningsBloc: gh<_i181.MyLearningsBloc>(),
          updateAndSortStudyStreams: gh<_i24.UpdateStudyStreams>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i291.RegisterModule {}
