import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/environment/environment_configs.dart';

@module
abstract class RegisterModule {
  @lazySingleton
  Alice get alice => Alice();

  @lazySingleton
  FlutterSecureStorage get flutterSecureStorage => const FlutterSecureStorage(
        iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock),
      );

  @singleton
  //linter assuming that EnvironmentConfigs.baseUrl is empty but it's not
  //ignore: avoid_redundant_argument_values
  Dio dio() => Dio(BaseOptions(baseUrl: EnvironmentConfigs.baseUrl));

  @singleton
  DefaultCacheManager cacheManager() => DefaultCacheManager();

  @factory
  Random random() => Random();
}
