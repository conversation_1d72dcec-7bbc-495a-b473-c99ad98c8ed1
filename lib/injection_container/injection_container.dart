import 'dart:async';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/bloc_observer.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/shared/token_provider/refresh_token_interceptor.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/injection_container/injection_container.config.dart';
import 'package:national_skills_platform/router.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:uuid/uuid.dart';

final _injector = GetIt.instance;

@InjectableInit(initializerName: 'init', preferRelativeImports: true, asExtension: true)
void configureDependencies() => _injector.init();

Future<void> init() async {
  configureDependencies();
  await _initLibraries();
  await _injector.allReady();
  await lockScreenOrientation();
  setupRouter();
}

Future<void> lockScreenOrientation() async {
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

Future<void> _initLibraries() async {
  await Hive.initFlutter();
  await Hive.openBox(HiveKeys.hiveNspStorage);

  _overrideBlocObserver();

  _errorReporting();
  _initInterceptors();
}

void _overrideBlocObserver() {
  if (kDebugMode) Bloc.observer = AppBlocObserver();
}

void _errorReporting() {
  if (kReleaseMode) {
    FlutterError.onError = (details) async {
      await Sentry.captureException(details.exception, stackTrace: details.stack);
      appPrint(details);
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      Sentry.captureException(error, stackTrace: stack);
      appPrint(error);
      return true;
    };
  }
}

void _initInterceptors() {
  const uuid = Uuid();

  _injector<Dio>().interceptors.addAll([
    if (kDebugMode) _injector<Alice>().getDioInterceptor(),

    RetryInterceptor(
      dio: _injector(),
      logPrint: appPrint,
      retryEvaluator: _retryOnlyGetRequestsEvaluator,
    ),

    InterceptorsWrapper(
      onRequest: (options, handler) async {
        final authToken = await AuthTokenProvider(secureStorage: _injector()).getToken();
        //todo: below current role is hardcoded until user roles feature is implemented
        options.headers[Constants.xCurrentRole] = Constants.TRAINEE;
        options.headers[Constants.xRequestId] = uuid.v1();

        if (authToken != null) {
          options.headers[Constants.Authorization] = '${Constants.Bearer} $authToken';
        }
        return handler.next(options);
      },
      onError: (err, handler) =>
          RefreshTokenInterceptor(_injector(), _injector(), _injector()).onError(err, handler),
    ),
    //
  ]);
}

Future<bool> _retryOnlyGetRequestsEvaluator(DioException error, int attempt) async {
  if (error.requestOptions.method != Constants.GET) {
    return false;
  }

  return DefaultRetryEvaluator(defaultRetryableStatuses).evaluate(error, attempt);
}
