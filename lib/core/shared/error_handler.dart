import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

typedef OnSuccess<T> = Future<void> Function(T result);
typedef OnError<T> = void Function(T error);
typedef OnDioException = void Function(DioException dioException);

extension FutureExt<T> on Future<T> {
  Future<void> errorHandler({required OnSuccess<T> onSuccess, OnError<String>? onError}) async {
    try {
      await onSuccess.call(await this);
    } on DioException catch (error, stackTrace) {
      appPrint(error);
      appPrint(stackTrace);
      final response = error.response;

      final responseId = response?.headers[Constants.xRequestId]?.first ??
          response?.requestOptions.headers[Constants.xRequestId] ??
          '';
      final errorMsg = LocaleKeys.error_body.tr(args: [responseId ?? '']);

      onError?.call(errorMsg);
    } catch (error, stackTrace) {
      appPrint(error);
      appPrint(stackTrace);

      onError?.call(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr());
    }
  }
}
