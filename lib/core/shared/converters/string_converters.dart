import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

String languageCodeToText(String languageCode) {
  return languageCode.toLowerCase() == Constants.localeEN
      ? LocaleKeys.filterModal_english.tr()
      : LocaleKeys.filterModal_arabic.tr();
}

String levelEnumToText(String level) {
  return '${Constants.levelConst}$level'.tr();
}

String fromJsonToDuration(Object duration) {
  return duration.toString();
}

TrainingType stringToTrainingType(String? type) {
  switch (type) {
    case Constants.INSTRUCTOR_LED:
      return TrainingType.InstructorLed;
    case Constants.SELF_PACED:
      return TrainingType.SelfPaced;
    default:
      return TrainingType.none;
  }
}

DtoStatus stringToDtoStatus(String? dtoStatus) {
  switch (dtoStatus) {
    case Constants.ACTIVE:
      return DtoStatus.Active;
    case Constants.DRAFT:
    default:
      return DtoStatus.Draft;
  }
}

LessonType? stringToLessonType(String? lessonType) {
  switch (lessonType) {
    case Constants.VIDEO:
      return LessonType.Video;
    case Constants.ARTICLE:
      return LessonType.Article;
    case Constants.QUIZ:
      return LessonType.Quiz;
    case Constants.SLIDE:
      return LessonType.Slide;
    case Constants.FILE:
      return LessonType.File;
  }

  return null;
}
