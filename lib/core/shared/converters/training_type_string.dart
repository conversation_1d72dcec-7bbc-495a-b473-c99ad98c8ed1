import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

String trainingTypeString(TrainingDetailsModel? training) {
  final type = training?.type;
  if (type == TrainingType.none) return LocaleKeys.training.tr();
  if (type == TrainingType.SelfPaced) return LocaleKeys.self_paced.tr();

  if (type == TrainingType.InstructorLed) {
    if (training?.hasFutureOnline == true) return LocaleKeys.trainingDetails_online.tr();
    if (training?.hasFutureInPerson == true) return LocaleKeys.in_person.tr();
    return LocaleKeys.online_in_person.tr();
  }
  return LocaleKeys.training.tr();
}

String applicantTrainingTypeString(LearningType? learningType) {
  switch (learningType) {
    case LearningType.SelfPaced:
      return LocaleKeys.self_paced.tr();
    case LearningType.OnlineStudy:
      return LocaleKeys.trainingDetails_online.tr();
    case LearningType.InPersonStudy:
      return LocaleKeys.in_person.tr();
    case LearningType.LearningTrack:
      return LocaleKeys.training.tr();
    default:
      return LocaleKeys.learningTracks_title.tr();
  }
}

String trainingContentTypeString(TrainingContentModel? training) {
  final type = training?.trainingType;
  if (type == TrainingType.none) return LocaleKeys.training.tr();
  if (type == TrainingType.SelfPaced) return LocaleKeys.self_paced.tr();

  if (type == TrainingType.InstructorLed) {
    if (training?.hasFutureOnline == true) return LocaleKeys.trainingDetails_online.tr();
    if (training?.hasFutureInPerson == true) return LocaleKeys.in_person.tr();
    return LocaleKeys.online_in_person.tr();
  }
  return LocaleKeys.training.tr();
}
