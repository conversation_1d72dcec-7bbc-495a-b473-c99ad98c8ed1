import 'package:easy_localization/easy_localization.dart';

String? stringDateTimeFormat(String? inputDate) {
  if (inputDate == null) return null;

  return DateFormat('dd.MM.yyyy').format(DateTime.parse(inputDate));
}

String? dateTimeFormat(DateTime? inputDate) {
  if (inputDate == null) return null;

  return DateFormat('dd.MM.yyyy').format(inputDate);
}

DateTime truncateToSeconds(DateTime dateTime) {
  return DateTime(
    dateTime.year,
    dateTime.month,
    dateTime.day,
    dateTime.hour,
    dateTime.minute,
    dateTime.second,
  );
}

/// Return format example: "Nov 15 - 20, 2023"
String formatDateRange(DateTime? startDate, DateTime? endDate) {
  if (startDate == null || endDate == null) return '';

  final monthDayFormat = DateFormat('MMM d');
  final yearFormat = DateFormat('y');

  final String start = monthDayFormat.format(startDate);
  final String end = monthDayFormat.format(endDate);
  final String year = yearFormat.format(endDate);

  return '$start - $end, $year';
}

/// Return format example: "Apr 15, 1:34 P.M."
String formatDateTime(DateTime? dateTime) {
  if (dateTime == null) return '';
  return DateFormat('MMM d, hh:mm a').format(dateTime);
}

/// Return format example: "04:00 PM - 05:00 PM"
String formatTimeRange(DateTime? startTime, DateTime? endTime) {
  if (startTime == null || endTime == null) return '';

  final formatter = DateFormat('hh:mm a');
  return '${formatter.format(startTime)} - ${formatter.format(endTime)}';
}

/// Return format example: "Nov 15, 2023"
String formatTime(DateTime? dateTime) {
  if (dateTime == null) return '';

  final formatter = DateFormat('MMM d, y');
  return formatter.format(dateTime);
}
