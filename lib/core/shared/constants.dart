///Do NOT try to merge similar keys/variables into one, because they may be used in different places, and changing one could break other
class Constants {
  static const localeAR = 'ar';
  static const localeEN = 'en';
  static const generalErrorMsg = 'Error occur while loading the page';
  static const flutterTest = 'FLUTTER_TEST';
  static const contentType = 'Content-Type';
  static const multiPartFormData = 'multipart/form-data';
  static const levelConst = 'levels.';
  static const applicationTypeJson = 'application/json';
  static const Authorization = 'Authorization';
  static const xCurrentRole = 'X-Current-Role';
  static const xRequestId = 'X-Request-Id';
  static const xResponseId = 'X-Response-Id';
  static const TRAINEE = 'TRAINEE';
  static const Bearer = 'Bearer';
  static const GET = 'GET';
  static const accessToken = 'accessToken';
  static const refreshToken = 'refreshToken';
  static const token = 'token';
  static const authCode = 'authCode';
  static const codeVerifier = 'codeVerifier';
  static const codeChallenge = 'codeChallenge';
  static const role = 'role';
  static const isAdmin = 'isAdmin';
  static const acceptLanguageHeader = 'Accept-Language';
  static const profileType = 'profileType';
  static const preSignedUrl = 'preSignedUrl';
  static const httpPrefix = 'http://';
  static const httpsPrefix = 'https://';

  static const mobile = 'mobile';
  static const body = 'body';
  static const demo = 'demo';
  static const term = 'term';
  static const page = 'page';
  static const p = 'p';
  static const size = 'size';
  static const file = 'file';
  static const jpg = '.jpg';
  static const jpeg = '.jpeg';
  static const png = '.png';
  static const mb = 'MB';
  static const ENROLLED = 'ENROLLED';
  static const NOMINATED = 'NOMINATED';
  static const COMPLETED = 'COMPLETED';
  static const DISABLED = 'DISABLED';
  static const ACTIVE = 'ACTIVE';
  static const DRAFT = 'DRAFT';
  static const PUBLISHED = 'PUBLISHED';
  static const SELF_PACED = 'SELF_PACED';
  static const OFFLINE = 'OFFLINE';
  static const INSTRUCTOR_LED = 'INSTRUCTOR_LED';
  static const VIDEO = 'VIDEO';
  static const ARTICLE = 'ARTICLE';
  static const QUIZ = 'QUIZ';
  static const SLIDE = 'SLIDE';
  static const FILE = 'FILE';
  static const TRUEFALSE = 'TRUEFALSE';
  static const RADIO = 'RADIO';
  static const ONLINE = 'ONLINE';
  static const IN_PERSON = 'IN_PERSON';

  static const daySunday = 'Sunday';
  static const dayMonday = 'Monday';
  static const dayTuesday = 'Tuesday';
  static const dayWednesday = 'Wednesday';
  static const dayThursday = 'Thursday';
  static const dayFriday = 'Friday';
  static const daySaturday = 'Saturday';

  static const List<String> orderedDaysOfWeek = [
    daySunday,
    dayMonday,
    dayTuesday,
    dayWednesday,
    dayThursday,
    dayFriday,
    daySaturday,
  ];

  static const List<String> weekdaysList = [
    dayMonday,
    dayTuesday,
    dayWednesday,
    dayThursday,
    dayFriday,
  ];

  static const List<String> weekendsList = [
    dayFriday,
    daySaturday,
  ];

  static const statusCode200 = 200;
  static const statusCode201 = 201;
  static const statusCode401 = 401;
  static const statusCode403 = 403;
  static const statusCode404 = 404;
}

class ApiConstants {
  static const fallbackURl = 'https://0.0.0.0/';
  static const signInPath = '/sso/request-token';
  static const refreshTokenPath = '/auth/refresh-token';
  static const logoutPath = '/auth/logout';
  static const trainingCataloguePath = '/training-catalogue?';
  static const learningTracksCataloguePath = '/learning-track-catalog?';
  static const trainingDetailsPath = '/trainings';
  static const learningTracksPath = '/learning-tracks';
  static const userLearningsPath = '/user-learnings';
  static const searchPath = '/course-catalogue/suggestions';
  static const userPath = '/user';
  static const userAvatarPath = '/users/avatar';
  static const coursesInProgressPath = '/applicants/current/courses/ENROLLED';
  static const currentCoursesAll = '/applicants/current/courses/all';
  static const sectorsTreePath = '/dictionary/sectors/tree';
  static const applicantsTrainings = '/applicants/current/trainings/';
  static const applicantsLearningTrack = '/applicants/current/learning-tracks/';
  static const videoLessonPath = '/trainings/files/playlists/signer?videoKey=';
  static const trainingsFilesPath = '/trainings/files?key=';
  static const usersFilesPath = '/users/files?key=';
  static const quizAnswerSubmitPath = '/quizzes/';
  static const qualificationTestAnswerSubmitPath = '/tests/';
  static const retakeQualificationTestPath = '/tests?testType=';
  static const streamsPath = '/streams/';
  static const organizations = '/public/organizations';
  static const cities = '/training-catalogue/cities';
  static const sectorsFeaturesPath = '/trainings/sectors/features';
}

class AssetsPath {
  static const logoSvg = 'assets/vector_images/logo_svg.svg';
  static const imageWithLines = 'assets/vector_images/image_with_lines.svg';
  static const imageWithLinesNew = 'assets/images/image_with_lines.png';
  static const imageWithLinesMini = 'assets/vector_images/image_with_lines_mini.svg';
  static const imageWithLinesMiniNew = 'assets/images/image_with_lines_mini.png';
  static const translationPath = 'assets/translations';
  static const nspBackground = 'assets/images/nsp_background.png';
  static const logoBlackText = 'assets/images/logo_black_text.png';
  static const logoWhiteText = 'assets/images/logo_white_text.png';
  static const emptyAvatarImage = 'assets/images/empty_avatar.png';
  static const fileImage = 'assets/images/file.jpg';
  static const qiwaLogo = 'assets/icons/qiwa_logo.png';
  static const passwordVisibilityIcon = 'assets/icons/password_visibility.png';
  static const globeIcon = 'assets/icons/globe.png';
  static const profileIcon = 'assets/icons/profile_icon.png';
  static const searchIcon = 'assets/icons/search_icon.png';
  static const bookIcon = 'assets/icons/book_icon.png';
  static const filterIcon = 'assets/icons/filter_icon.png';
  static const filterIconGreen = 'assets/icons/filter_icon_green.png';
  static const sortIcon = 'assets/icons/sort_icon.png';
  static const notificationIcon = 'assets/icons/notification_icon.png';
  static const checkIcon = 'assets/icons/check_icon.png';
  static const radioIconActive = 'assets/icons/radio_icon_active.png';
  static const radioIconInactive = 'assets/icons/radio_icon_inactive.png';
  static const shareIcon = 'assets/icons/share_icon.png';
  static const backIcon = 'assets/icons/back_icon.png';
  static const mockupsIcon = 'assets/icons/mockups.png';
  static const componentsGroupsIcon = 'assets/icons/components_groups.png';
  static const cameraIcon = 'assets/icons/camera_icon.png';
  static const arrowIcon = 'assets/icons/arrow.png';
  static const uploadAvatarIcon = 'assets/icons/upload_icon.png';
  static const peopleIcon = 'assets/icons/people_icon.png';
  static const logoutIcon = 'assets/icons/logout.png';
  static const lampIcon = 'assets/icons/lamp.png';
  static const warningIcon = 'assets/icons/warning_icon.png';
  static const errorIcon = 'assets/icons/error_icon.png';
  static const successIcon = 'assets/icons/success_icon.png';
  static const booksIcon = 'assets/icons/books_icon.png';
  static const downloadIcon = 'assets/icons/download_icon.png';
  static const codeIcon = 'assets/icons/code_icon.png';
  static const closeFullScreenIcon = 'assets/icons/close_full_screen.png';
  static const inPersonStudyBadge = 'assets/icons/in_person_study_badge.png';
  static const learningTrackBadge = 'assets/icons/lt_badge.png';
  static const selfStudyBadge = 'assets/icons/self_study_badge.png';
  static const onlineStudyBadge = 'assets/icons/online_study_badge.png';
  static const inPersonIcon = 'assets/icons/in_person_icon.png';
  static const onlineIcon = 'assets/icons/online_icon.png';
  static const selfPacedIcon = 'assets/icons/self_paced_icon.png';
  static const learningTrackIcon = 'assets/icons/learning_track_icon.png';
  static const locationIcon = 'assets/icons/location_icon.png';
  static const quizLessonIcon = 'assets/icons/quiz_lesson.png';
  static const articleLessonIcon = 'assets/icons/article_lesson.png';
  static const fileLessonIcon = 'assets/icons/file_lesson.png';
  static const slidesLessonIcon = 'assets/icons/slides_lesson.png';
  static const videoLessonIcon = 'assets/icons/video_lesson.png';

  // Course Details Icons
  static const seatingCapacityIcon = 'assets/icons/course_details/seatingCapacity.png';
  static const startEndDateIcon = 'assets/icons/course_details/start_end_date.png';
  static const estimatedTimeIcon = 'assets/icons/course_details/trainingDetails_estimatedTime.png';
  static const languageIcon =
      'assets/icons/course_details/learningTracks_builder_languageLabel.png';
  static const levelIcon = 'assets/icons/course_details/learningTracks_builder_levelLabel.png';

  static const bottomNavBar_home_active = 'assets/icons/bottom_nav_bar_icons/home_active.png';
  static const bottomNavBar_home_inactive = 'assets/icons/bottom_nav_bar_icons/home_inactive.png';
  static const bottomNavBar_catalog_active =
      'assets/icons/bottom_nav_bar_icons/catalog_icon_active.png';
  static const bottomNavBar_catalog_inactive =
      'assets/icons/bottom_nav_bar_icons/catalog_icon_inactive.png';
  static const bottomNavBar_myLearning_active =
      'assets/icons/bottom_nav_bar_icons/my_learnings_active.png';
  static const bottomNavBar_myLearning_inactive =
      'assets/icons/bottom_nav_bar_icons/my_learnings_inactive.png';
  static const bottomNavBar_profile_active = 'assets/icons/bottom_nav_bar_icons/profile_active.png';
  static const bottomNavBar_profile_inactive =
      'assets/icons/bottom_nav_bar_icons/profile_inactive.png';
}

class RouterQueryConstants {
  static const learningTrackTab = 'learningTrackTab';
  static const tab = 'tab';
}

class HiveKeys {
  static const hiveNspStorage = 'nsp_storage';
  static const currentLocale = 'hiveLocale';
  static const visitedBefore = 'visitedBefore';
}

class SecureStorageKeys {
  static const token = 'token';
  static const refreshToken = 'refreshToken';
}
