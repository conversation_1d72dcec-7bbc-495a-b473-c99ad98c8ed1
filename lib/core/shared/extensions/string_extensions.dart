import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

extension StringProcessing on String {
  String removeHtmlElements() {
    final exp = RegExp('<[^>]*>', multiLine: true);

    return replaceAll(exp, '').replaceAll('&nbsp;', ' ').trim();
  }

  String languageCodeToName() {
    if (toLowerCase() == Constants.localeEN) return LocaleKeys.languages_english_en.tr();
    if (toLowerCase() == Constants.localeAR) return LocaleKeys.languages_arabic_ar.tr();
    return 'Unknown language';
  }
}
