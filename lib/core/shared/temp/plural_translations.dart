import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

//ToDo: temp solution until the issue is resolved: https://github.com/aissat/easy_localization/issues/573
String mapNumberToLessons(int number, String currentLanguage) {
  const lessonsCountArabic = <int, String>{
    0: '0 درس',
    1: 'درس واحد',
    2: 'درسان',
    3: '3 دروس',
    4: '4 دروس',
    5: '5 دروس',
    6: '6 دروس',
    7: '7 دروس',
    8: '8 دروس',
    9: '9 دروس',
    10: '10 دروس',
  };

  if (currentLanguage == Constants.localeAR) {
    if (lessonsCountArabic.containsKey(number)) {
      return lessonsCountArabic[number]!;
    } else {
      return '{} درس'.replaceFirst('{}', number.toString());
    }
  } else if (currentLanguage == Constants.localeEN) {
    return LocaleKeys.trainingDetails_lessonsCount.plural(number);
  } else {
    return number.toString();
  }
}

String mapNumberToSections(int number, String currentLanguage) {
  const sectionsCountArabic = <int, String>{
    0: '0 قسم',
    1: 'قسم واحد',
    2: 'قسمان',
    3: '3 أقسام',
    4: '4 أقسام',
    5: '5 أقسام',
    6: '6 أقسام',
    7: '7 أقسام',
    8: '8 أقسام',
    9: '9 أقسام',
    10: '10 أقسام',
  };
  if (currentLanguage == Constants.localeAR) {
    if (sectionsCountArabic.containsKey(number)) {
      return sectionsCountArabic[number]!;
    } else {
      return '{} أقسام'.replaceFirst('{}', number.toString());
    }
  } else if (currentLanguage == Constants.localeEN) {
    return LocaleKeys.trainingDetails_sectionsCount.plural(number);
  } else {
    return number.toString();
  }
}

String mapNumberToTrainingsInProgress(int number, String currentLanguage) {
  const trainingsInProgressArabic = <int, String>{
    0: '0 دورة قيد التدريب',
    1: 'دورة واحدة قيد التدريب',
    2: 'دورتان قيد التدريب',
    3: '3 دورات قيد التدريب',
    4: '4 دورات قيد التدريب',
    5: '5 دورات قيد التدريب',
    6: '6 دورات قيد التدريب',
    7: '7 دورات قيد التدريب',
    8: '8 دورات قيد التدريب',
    9: '9 دورات قيد التدريب',
    10: '10 دورات قيد التدريب',
  };

  if (currentLanguage == Constants.localeAR) {
    if (trainingsInProgressArabic.containsKey(number)) {
      return trainingsInProgressArabic[number]!;
    } else {
      return '{} دورة قيد التدريب'.replaceFirst('{}', number.toString());
    }
  } else if (currentLanguage == Constants.localeEN) {
    return LocaleKeys.trainingsInProgress.plural(number);
  } else {
    return number.toString();
  }
}
