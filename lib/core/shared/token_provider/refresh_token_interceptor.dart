import 'dart:async';

import 'package:dio/dio.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';

class RefreshTokenInterceptor extends Interceptor {
  RefreshTokenInterceptor(AuthTokenProvider authTokenProvider, AuthBloc authBloc, Dio dio)
      : _authTokenProvider = authTokenProvider,
        _dio = dio,
        _authBloc = authBloc;
  final AuthTokenProvider _authTokenProvider;
  final AuthBloc _authBloc;
  final Dio _dio;

  final _requestsNeedRetry = <({RequestOptions options, ErrorInterceptorHandler handler})>[];

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    final isAuthError = err.response?.statusCode == Constants.statusCode401 ||
        err.response?.statusCode == Constants.statusCode403;

    if (!isAuthError) {
      handler.next(err);
      return;
    }

    final isRefreshTokenRequest = err.requestOptions.path == ApiConstants.refreshTokenPath;

    ///This check below prevents recursive calls of refresh token when refreshToken request itself fails
    if (isRefreshTokenRequest) {
      handler.next(err);
      await _authTokenProvider.invalidateToken();
      return;
    }

    final accessToken = await _authTokenProvider.getToken();
    final refreshToken = await _authTokenProvider.getRefreshToken();
    final hasValidTokens = accessToken != null && refreshToken != null;

    if (!hasValidTokens) {
      handler.next(err);

      ///if tokens are null then there's no point in try to refresh tokens.
      return;
    }

    _requestsNeedRetry.add((options: err.requestOptions, handler: handler));

    _authBloc.add(RefreshToken(RefreshTokenModel(refreshToken: refreshToken)));

    _authBloc.stream.listen((state) {
      if (state is RefreshSuccess) {
        /// After refreshing the token, reattempt the failed requests
        for (final requestNeedRetry in _requestsNeedRetry) {
          _dio.fetch(requestNeedRetry.options).then(requestNeedRetry.handler.resolve);
        }
        _requestsNeedRetry.clear();
      }
    });
  }
}
