import 'dart:async';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:rxdart/rxdart.dart';

@lazySingleton
class AuthTokenProvider {
  final BehaviorSubject<bool> _isAuthenticatedSubject = BehaviorSubject<bool>();
  final FlutterSecureStorage _secureStorage;
  String? _cachedRefreshToken;
  String? _cachedToken;

  AuthTokenProvider({required FlutterSecureStorage secureStorage})
      : _secureStorage = secureStorage {
    _init();
  }

  Future<void> _init() async {
    _cachedToken = await _secureStorage.read(key: SecureStorageKeys.token);
    _isAuthenticatedSubject.add(_cachedToken != null);
  }

  Stream<bool> get authStateStream => _isAuthenticatedSubject.stream;

  Future<String?> getToken() async {
    return _cachedToken ??= await _secureStorage.read(key: SecureStorageKeys.token);
  }

  Future<String?> getRefreshToken() async {
    return _cachedRefreshToken ??= await _secureStorage.read(key: SecureStorageKeys.refreshToken);
  }

  Future<void> invalidateToken() async {
    _cachedToken = null;
    _cachedRefreshToken = null;
    await _secureStorage.deleteAll();

    ///update listeners
    _isAuthenticatedSubject.add(_cachedToken != null);
  }

  Future<void> updateTokens(String token, String refreshToken) async {
    _cachedToken = token;
    _cachedRefreshToken = refreshToken;

    await _secureStorage.write(key: SecureStorageKeys.token, value: token);
    await _secureStorage.write(key: SecureStorageKeys.refreshToken, value: refreshToken);

    ///update listeners
    _isAuthenticatedSubject.add(_cachedToken != null);
  }
}
