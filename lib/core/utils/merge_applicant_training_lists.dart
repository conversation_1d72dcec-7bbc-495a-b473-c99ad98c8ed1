import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

List<ApplicantTraining> mergeApplicantTrainingLists(MyLearningsModel coursesInProgress) {
  return [
    ...coursesInProgress.applicantSelfPacedTrainingList.map(
      (training) => training.copyWith(learningType: LearningType.SelfPaced),
    ),
    ...coursesInProgress.applicantOnlineStudyTrainingList.map(
      (training) => training.copyWith(learningType: LearningType.OnlineStudy),
    ),
    ...coursesInProgress.applicantInPersonStudyTrainingList.map(
      (training) => training.copyWith(learningType: LearningType.InPersonStudy),
    ),
  ];
}
