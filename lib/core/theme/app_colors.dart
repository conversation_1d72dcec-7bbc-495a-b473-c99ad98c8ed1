import 'package:flutter/material.dart';

///Colors given in Figma UI Kit
class AppColors {
  static const greenAccentSecondary = Color(0xFF80A9AC);
  static const greenAccentPrimary = Color(0xFF0060FF);
  static const greenAccentTertiary = Color(0xFF204D88);
  static const greenAccentDark = Color(0xFF173255);
  static const greenAccentExtraDark = Color(0xFF0C2643);

  static const accentLight = Color(0xFFE2E7E8);
  static const accentExtraLight = Color(0xFFF3F7F7);

  static const orangeAccentSecondary = Color(0xFFF1AA61);
  static const orangeAccentPrimary = Color(0xFF0060FF);
  static const orangeAccentTertiary = Color(0xFFC87409);
  static const orangeAccentLight = Color(0xFFFDF4EA);

  static const greyPrimary = Color(0xFF575758);
  static const greySecondary = Color(0xFF959595);
  static const greyTertiary = Color(0xFFD9D9D9);
  static const greyLight = Color(0xFFEDEDED);
  static const greyExtraLight = Color(0xFFF2F2F2);

  static const neutralWhite = Color(0xFFFFFFFF);
  static const neutralBlack = Color(0xFF072527);

  static const statusSuccessDark = Color(0xFF1E4620);
  static const statusSuccess = Color(0xFF068463);
  static const statusSuccessLight = Color(0xFFEAF6EA);

  static const statusWarningDark = Color(0xFF5F2120);
  static const statusWarning = Color(0xFFF05667);
  static const statusWarningLight = Color(0xFFFEF4F4);

  static const uiBackgroundPrimary = Color(0xFFFAFAFA);
  static const uiBackgroundSecondary = Color(0xFFFEFEFE);

  static const additionalDarkBlue = Color(0xFF13415A);
  static const additionalBlueGreen = Color(0xFF55A9B0);
  static const additionalGreen = Color(0xFF58B37A);
  static const additionalYellow = Color(0xFFF5C365);
  static const additionalGrey = Color(0xFF768686); // #768686
}
