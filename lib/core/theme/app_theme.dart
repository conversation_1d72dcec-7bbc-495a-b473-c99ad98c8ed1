import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';

extension ThemeMode on ThemeData {
  bool isDarkMode(BuildContext context) =>
      MediaQuery.of(context).platformBrightness == Brightness.dark;
}

class AppTheme {
  static const fontFamily = 'Noto Sans';
  static const arabicFontFamily = 'Tajawal';

  ThemeData light() {
    final themeColors = ColorScheme.fromSeed(
      seedColor: const Color(0xFF0060FF),
      surface: Colors.white,
    );
    final currentLocale =
        Hive.box(HiveKeys.hiveNspStorage).get(HiveKeys.currentLocale) ?? Constants.localeEN;
    final isLocaleEnglish = currentLocale == Constants.localeEN;

    return ThemeData.light(useMaterial3: true).copyWith(
      primaryColor: AppColors.greenAccentPrimary,
      colorScheme: themeColors.copyWith(surface: themeColors.surface),
      canvasColor: themeColors.surface,
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      appBarTheme: const AppBarTheme(backgroundColor: Colors.white),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          minimumSize: WidgetStateProperty.all(Size.zero),
          padding: WidgetStateProperty.all(EdgeInsets.zero),
          splashFactory: NoSplash.splashFactory,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          foregroundColor: WidgetStateProperty.resolveWith((state) => Colors.black),
        ),
      ),
      cardTheme: const CardThemeData(color: Colors.white),
      iconButtonTheme: IconButtonThemeData(
        style: ButtonStyle(
          foregroundColor: WidgetStateProperty.resolveWith((state) => AppColors.greenAccentPrimary),
        ),
      ),
      iconTheme: const IconThemeData(color: AppColors.greenAccentPrimary),
      inputDecorationTheme: InputDecorationTheme(
        fillColor: const Color(0xFFE6E3D3),
        iconColor: Colors.grey,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        contentPadding: const EdgeInsets.fromLTRB(12, 10, 0, 10),
      ),
      textTheme: ThemeData.light().textTheme.apply(
            fontFamily: isLocaleEnglish ? fontFamily : arabicFontFamily,
          ),
      primaryTextTheme: ThemeData.light().textTheme.apply(
            fontFamily: isLocaleEnglish ? fontFamily : arabicFontFamily,
          ),
      scaffoldBackgroundColor: AppColors.neutralWhite,
      dataTableTheme: DataTableThemeData(
        decoration: BoxDecoration(
          color: const Color(0xFFF2F5EC),
          boxShadow: <BoxShadow>[
            BoxShadow(color: Colors.black.withValues(alpha: 0.1), offset: const Offset(1.0, 1.0)),
          ],
          borderRadius: BorderRadius.circular(12),
        ),
        headingRowHeight: 32,
      ),
      dialogTheme: DialogThemeData(backgroundColor: themeColors.surface),
    );
  }
}
