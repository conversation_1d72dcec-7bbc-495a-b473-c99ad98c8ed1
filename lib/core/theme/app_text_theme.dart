import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_theme.dart';

// coverage:ignore-file
extension ContextExtension on BuildContext {
  _AppTextStyles get textTheme => _AppTextStyles(this);
}

class _AppTextStyles {
  const _AppTextStyles(this.context);

  final BuildContext context;

  bool get _isLocaleArabic => context.locale == const Locale(Constants.localeAR);

  TextStyle get _defaultTextStyle => TextStyle(
        fontFamily: _isLocaleArabic ? AppTheme.arabicFontFamily : AppTheme.fontFamily,
        color: AppColors.neutralBlack,
      );

  ///TextStyles from UI KIT: https://www.figma.com/file/FzNX418dKMDs3JWEzLqFb7/Takamol-NSP-Design?type=design&node-id=154-36838&mode=design&t=nvATwzl9Uw6tX2XO-0
  TextStyle get h1 {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 33, fontWeight: FontWeight.w700);
    }
    return _defaultTextStyle.copyWith(fontSize: 32, fontWeight: FontWeight.w700);
  }

  TextStyle get h2 {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 29);
    }
    return _defaultTextStyle.copyWith(fontSize: 28);
  }

  TextStyle get h3 {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 21);
    }

    return _defaultTextStyle.copyWith(fontSize: 20);
  }

  TextStyle get h4 {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 19);
    }
    return _defaultTextStyle.copyWith(fontSize: 18);
  }

  TextStyle get textLarge {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 17);
    }
    return _defaultTextStyle.copyWith(fontSize: 16);
  }

  TextStyle get textMedium {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 16);
    }
    return _defaultTextStyle.copyWith(fontSize: 15);
  }

  TextStyle get textSmall {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 15);
    }
    return _defaultTextStyle.copyWith(fontSize: 14);
  }

  TextStyle get textXSmall {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 13);
    }
    return _defaultTextStyle.copyWith(fontSize: 12);
  }

  TextStyle get paragraphLarge {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 17, height: 28 / 17);
    }
    return _defaultTextStyle.copyWith(fontSize: 16, height: 27 / 16);
  }

  TextStyle get bottomNavigation {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(fontSize: 11, height: 13 / 11);
    }
    return _defaultTextStyle.copyWith(fontSize: 10, height: 12 / 10);
  }

  TextStyle get linkButton {
    if (_isLocaleArabic) {
      return _defaultTextStyle.copyWith(
        fontSize: 15,
        decoration: TextDecoration.underline,
        fontWeight: FontWeight.w500,
      );
    }
    return _defaultTextStyle.copyWith(
      fontSize: 14,
      decoration: TextDecoration.underline,
      fontWeight: FontWeight.w500,
    );
  }
}

extension TextStyleExt on TextStyle {
  ///Text FontWeight
  TextStyle get w400 => copyWith(fontWeight: FontWeight.w400);

  TextStyle get w500 => copyWith(fontWeight: FontWeight.w500);

  TextStyle get w600 => copyWith(fontWeight: FontWeight.w600);

  TextStyle get w700 => copyWith(fontWeight: FontWeight.w700);

  ///Font weight by name according
  TextStyle get bold => w700;

  TextStyle get semiBold => w600;

  TextStyle get medium => w500;

  TextStyle get regular => w400; //flutter textStyle by default will use "regular"/w400

  ///Text Decoration
  TextStyle get underline => copyWith(decoration: TextDecoration.underline);

  TextStyle get noDecoration => copyWith(decoration: TextDecoration.none);

  ///Text Colors
  TextStyle get orangeAccentPrimary => copyWith(color: AppColors.orangeAccentPrimary);
  TextStyle get orangeAccentDark => copyWith(color: AppColors.greenAccentDark);

  TextStyle get statusWarning => copyWith(color: AppColors.statusWarning);

  TextStyle get statusSuccess => copyWith(color: AppColors.statusSuccess);

  TextStyle get greyPrimary => copyWith(color: AppColors.greyPrimary);

  TextStyle get greySecondary => copyWith(color: AppColors.greySecondary);

  TextStyle get greyAdditional => copyWith(color: AppColors.additionalGrey);

  TextStyle get accentGreenPrimary => copyWith(color: AppColors.greenAccentPrimary);

  TextStyle get accentExtraDark => copyWith(color: AppColors.greenAccentExtraDark);

  TextStyle get accentGreenSecondary => copyWith(color: AppColors.greenAccentSecondary);

  TextStyle get white => copyWith(color: AppColors.neutralWhite);

  TextStyle get accentLight => copyWith(color: AppColors.accentLight);

  TextStyle get accentGreenDark => copyWith(color: AppColors.greenAccentDark);
}
