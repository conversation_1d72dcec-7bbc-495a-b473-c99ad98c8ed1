import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hive/hive.dart';
import 'package:national_skills_platform/core/environment/environment_configs.dart';
import 'package:national_skills_platform/core/shared/app_print.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/environment_banner.dart';
import 'package:national_skills_platform/generated/codegen_loader.g.dart';
import 'package:national_skills_platform/injection_container/injection_container.dart';
import 'package:national_skills_platform/router.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

Future<void> main() async {
  await runZonedGuarded(
    () async {
      FlutterNativeSplash.preserve(widgetsBinding: WidgetsFlutterBinding.ensureInitialized());
      await EasyLocalization.ensureInitialized();
      await init();

      await SentryFlutter.init(
        (options) {
          options
            ..dsn = kReleaseMode
                ? 'https://<EMAIL>/4506955499503616'
                : ''
            ..environment = EnvironmentConfigs.environment
            // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
            // We recommend adjusting this value in production.
            ..tracesSampleRate = 1.0;
        },
        appRunner: () => runApp(
          EasyLocalization(
            supportedLocales: const [Locale(Constants.localeAR), Locale(Constants.localeEN)],
            path: AssetsPath.translationPath,
            fallbackLocale: const Locale(Constants.localeAR),
            assetLoader: const CodegenLoader(),
            child: const App(),
          ),
        ),
      );
    },
    (exception, stackTrace) async {
      appPrint(stackTrace);
      appPrint(exception);
      await Sentry.captureException(exception, stackTrace: stackTrace);
    },
  );
}

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  @override
  void initState() {
    super.initState();

    ///Removes Splash Screen
    FlutterNativeSplash.remove();

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Hive.box(
        HiveKeys.hiveNspStorage,
      ).put(HiveKeys.currentLocale, context.locale.languageCode);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = AppTheme();

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark,
      child: OverlaySupport.global(
        child: EnvironmentBanner(
          environmentName: EnvironmentConfigs.environment,
          child: MaterialApp.router(
            routerConfig: router,
            theme: theme.light(),
            locale: context.locale,
            localizationsDelegates: context.localizationDelegates,
            supportedLocales: context.supportedLocales,
          ),
        ),
      ),
    );
  }
}
