// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class LocaleKeys {
  static const cancelled = 'cancelled';
  static const meeting_id = 'meeting_id';
  static const meeting_password = 'meeting_password';
  static const join_meeting = 'join_meeting';
  static const meeting_cancelled = 'meeting_cancelled';
  static const meeting_moved = 'meeting_moved';
  static const moved_date = 'moved_date';
  static const at_time_gmt3 = 'at_time_gmt3';
  static const actions = 'actions';
  static const seats_unlimited = 'seats.unlimited';
  static const seats_total = 'seats.total';
  static const seats_available = 'seats.available';
  static const seats = 'seats';
  static const freq_daily = 'freq.daily';
  static const freq_days_mid = 'freq.days.mid';
  static const freq_days_long = 'freq.days.long';
  static const freq_days = 'freq.days';
  static const freq_months_mid = 'freq.months.mid';
  static const freq_months_long = 'freq.months.long';
  static const freq_months = 'freq.months';
  static const freq_monthly = 'freq.monthly';
  static const freq_weekend = 'freq.weekend';
  static const freq_weekdays = 'freq.weekdays';
  static const freq_days_of_week_friday = 'freq.days_of_week.friday';
  static const freq_days_of_week_saturday = 'freq.days_of_week.saturday';
  static const freq_days_of_week_sunday = 'freq.days_of_week.sunday';
  static const freq_days_of_week_monday = 'freq.days_of_week.monday';
  static const freq_days_of_week_tuesday = 'freq.days_of_week.tuesday';
  static const freq_days_of_week_wednesday = 'freq.days_of_week.wednesday';
  static const freq_days_of_week_thursday = 'freq.days_of_week.thursday';
  static const freq_days_of_week = 'freq.days_of_week';
  static const freq = 'freq';
  static const GENERAL = 'GENERAL';
  static const BASIC = 'BASIC';
  static const ADVANCED = 'ADVANCED';
  static const skills_level = 'skills_level';
  static const addArticle = 'addArticle';
  static const article = 'article';
  static const and = 'and';
  static const max6TPSelection = 'max6TPSelection';
  static const max6LocationSelection = 'max6LocationSelection';
  static const max6SectorsSelection = 'max6SectorsSelection';
  static const view = 'view';
  static const test = 'test';
  static const type = 'type';
  static const self_paced = 'self_paced';
  static const in_person = 'in_person';
  static const online_in_person = 'online_in_person';
  static const self_paced_description = 'self_paced_description';
  static const online_training_description = 'online_training_description';
  static const in_person_training_description = 'in_person_training_description';
  static const learning_track_description = 'learning_track_description';
  static const study_stream = 'study_stream';
  static const start = 'start';
  static const explore = 'explore';
  static const complete_lesson = 'complete_lesson';
  static const instructorLed = 'instructorLed';
  static const selfPaced = 'selfPaced';
  static const yesConfirm = 'yesConfirm';
  static const areYouSureToLogout = 'areYouSureToLogout';
  static const filterBy = 'filterBy';
  static const manageList = 'manageList';
  static const rYouSureToEnrollToStream = 'rYouSureToEnrollToStream';
  static const previousStreamWillBeDisabled = 'previousStreamWillBeDisabled';
  static const yesEnroll = 'yesEnroll';
  static const upcomingStudyStreams = 'upcomingStudyStreams';
  static const seatingCapacity = 'seatingCapacity';
  static const start_end_date = 'start_end_date';
  static const choosePhotoFromTheLibrary = 'choosePhotoFromTheLibrary';
  static const removeCurrentPhoto = 'removeCurrentPhoto';
  static const sureToDeleteThePhoto = 'sureToDeleteThePhoto';
  static const cropPost = 'cropPost';
  static const cropImage = 'cropImage';
  static const maximumFileSize2mb = 'maximumFileSize2mb';
  static const unsupportedFileFormat = 'unsupportedFileFormat';
  static const inProgress = 'inProgress';
  static const viewTrainingDetails = 'viewTrainingDetails';
  static const favorites = 'favorites';
  static const addContent = 'addContent';
  static const addContentSuggestion = 'addContentSuggestion';
  static const addFile = 'addFile';
  static const skills_portal = 'skills_portal';
  static const home_card_body = 'home_card_body';
  static const scrollDown = 'scrollDown';
  static const recentInProgress = 'recentInProgress';
  static const popularTrainings = 'popularTrainings';
  static const enrollSuccessBody = 'enrollSuccessBody';
  static const sectors = 'sectors';
  static const noAvailableTrainings = 'noAvailableTrainings';
  static const signInLater = 'signInLater';
  static const training_self_paced = 'training_self_paced';
  static const training_instructor_led = 'training_instructor_led';
  static const welcome_user = 'welcome_user';
  static const trainingsInProgress = 'trainingsInProgress';
  static const addHomework = 'addHomework';
  static const addNewSection = 'addNewSection';
  static const totalResults = 'totalResults';
  static const totalResults1 = 'totalResults1';
  static const totalResults2 = 'totalResults2';
  static const totalResults2to11 = 'totalResults2to11';
  static const itemsSelected1 = 'itemsSelected1';
  static const itemsSelected2 = 'itemsSelected2';
  static const itemsSelected3to10 = 'itemsSelected3to10';
  static const itemsSelected11 = 'itemsSelected11';
  static const personal_info = 'personal_info';
  static const account_settings = 'account_settings';
  static const addQuiz = 'addQuiz';
  static const by_sign_in_i_accept = 'by_sign_in_i_accept';
  static const terms_of_use = 'terms_of_use';
  static const welcome_to_nsp = 'welcome_to_nsp';
  static const unlock_new_opportunities = 'unlock_new_opportunities';
  static const get_started = 'get_started';
  static const the_privacy_policy = 'the_privacy_policy';
  static const addSlides = 'addSlides';
  static const addVideo = 'addVideo';
  static const allTrainings = 'allTrainings';
  static const archive = 'archive';
  static const archiveTraining = 'archiveTraining';
  static const deleteTraining = 'deleteTraining';
  static const deleteTrainingPopupText = 'deleteTrainingPopupText';
  static const deleteTrainingPopupTitle = 'deleteTrainingPopupTitle';
  static const cancel = 'cancel';
  static const changesWontBeSaved = 'changesWontBeSaved';
  static const create = 'create';
  static const createNewTraining = 'createNewTraining';
  static const createTraining = 'createTraining';
  static const createTrainingSuggestion = 'createTrainingSuggestion';
  static const delete = 'delete';
  static const yesDelete = 'yesDelete';
  static const deleteContentSection = 'deleteContentSection';
  static const deleteSection = 'deleteSection';
  static const modalDeleteSectionTitle = 'modalDeleteSectionTitle';
  static const description = 'description';
  static const draft = 'draft';
  static const duplicateSection = 'duplicateSection';
  static const duplicateLesson = 'duplicateLesson';
  static const enterText = 'enterText';
  static const enterTextArea = 'enterTextArea';
  static const editSectionTitle = 'editSectionTitle';
  static const sectionSettings = 'sectionSettings';
  static const editTraining = 'editTraining';
  static const emptySectionSuggestion = 'emptySectionSuggestion';
  static const emptyLessonSectionSuggestion = 'emptyLessonSectionSuggestion';
  static const enterTrainingTitle = 'enterTrainingTitle';
  static const enterTrainingTitleInArabic = 'enterTrainingTitleInArabic';
  static const enterTrainingTitleInEnglish = 'enterTrainingTitleInEnglish';
  static const exitTrainingBuilder = 'exitTrainingBuilder';
  static const exitWithoutSaving = 'exitWithoutSaving';
  static const fieldIsEmpty = 'fieldIsEmpty';
  static const fieldIsRequired = 'fieldIsRequired';
  static const expectationFieldIsEmpty = 'expectationFieldIsEmpty';
  static const sectionFieldIsEmpty = 'sectionFieldIsEmpty';
  static const filters = 'filters';
  static const hideSection = 'hideSection';
  static const inputContainLatinAndArabicCharsWithSpace =
      'inputContainLatinAndArabicCharsWithSpace';
  static const logOut = 'logOut';
  static const nonLoggedInPageBody = 'nonLoggedInPageBody';
  static const nonLoggedInPageTitle = 'nonLoggedInPageTitle';
  static const maxCharactersAmount = 'maxCharactersAmount';
  static const newAccount = 'newAccount';
  static const newest = 'newest';
  static const noSectionYet = 'noSectionYet';
  static const noTrainings = 'noTrainings';
  static const password = 'password';
  static const previewButton = 'previewButton';
  static const published = 'published';
  static const or = 'or';
  static const email_mobile_or_id = 'email_mobile_or_id';
  static const enter_email_mobile_or_id = 'enter_email_mobile_or_id';
  static const word_new = 'word_new';
  static const pendingApproval = 'pendingApproval';
  static const rejected = 'rejected';
  static const approved = 'approved';
  static const publishWarning = 'publishWarning';
  static const qiwaSignIn = 'qiwaSignIn';
  static const search = 'search';
  static const search_results = 'search_results';
  static const expand_search = 'expand_search';
  static const search_for_training = 'search_for_training';
  static const search_for_ltrack = 'search_for_ltrack';
  static const view_all = 'view_all';
  static const searchResultCount = 'searchResultCount';
  static const section = 'section';
  static const settingsButton = 'settingsButton';
  static const signInIntoNationalSkillsPlatform = 'signInIntoNationalSkillsPlatform';
  static const signUp = 'signUp';
  static const signUpHere = 'signUpHere';
  static const success = 'success';
  static const testsButton = 'testsButton';
  static const text = 'text';
  static const thereIsNoContentYet = 'thereIsNoContentYet';
  static const trainingDescription = 'trainingDescription';
  static const testDescription = 'testDescription';
  static const trainingStructure = 'trainingStructure';
  static const trainingTitle = 'trainingTitle';
  static const trainingOverview = 'trainingOverview';
  static const trainingTitleInArabic = 'trainingTitleInArabic';
  static const trainingTitleInEnglish = 'trainingTitleInEnglish';
  static const createNewSection = 'createNewSection';
  static const edit = 'edit';
  static const location = 'location';
  static const editButton = 'editButton';
  static const editSectionName = 'editSectionName';
  static const save = 'save';
  static const yesSave = 'yesSave';
  static const showMore = 'showMore';
  static const showLess = 'showLess';
  static const sectionName = 'sectionName';
  static const sectionTitle = 'sectionTitle';
  static const title = 'title';
  static const articleFieldIsEmpty = 'articleFieldIsEmpty';
  static const shareKnowledge = 'shareKnowledge';
  static const articleTextIsEmpty = 'articleTextIsEmpty';
  static const enterArticleTitle = 'enterArticleTitle';
  static const trainingIsArchived = 'trainingIsArchived';
  static const trainingIsDeleted = 'trainingIsDeleted';
  static const dragAndDropFile = 'dragAndDropFile';
  static const fileExtensions = 'fileExtensions';
  static const selectFile = 'selectFile';
  static const file = 'file';
  static const fileTitlePlaceholder = 'fileTitlePlaceholder';
  static const wrongFormat = 'wrongFormat';
  static const maxFileSize = 'maxFileSize';
  static const fileTitleIsEmpty = 'fileTitleIsEmpty';
  static const video = 'video';
  static const addMoreItems = 'addMoreItems';
  static const max160Characters = 'max160Characters';
  static const characters = 'characters';
  static const whatCanBeLearnedInTheTraining = 'whatCanBeLearnedInTheTraining';
  static const atLeast2Items = 'atLeast2Items';
  static const descriptionInputPlaceholder = 'descriptionInputPlaceholder';
  static const progressValidation = 'progressValidation';
  static const slide = 'slide';
  static const slidesText = 'slidesText';
  static const slidesSubText = 'slidesSubText';
  static const enterValidResource = 'enterValidResource';
  static const noEmptyResource = 'noEmptyResource';
  static const youWontBeAbleToRestoreIt = 'youWontBeAbleToRestoreIt';
  static const duration = 'duration';
  static const durationValue = 'durationValue';
  static const timeLimit = 'timeLimit';
  static const noLimit = 'noLimit';
  static const deleteLessonConfirmation = 'deleteLessonConfirmation';
  static const requirements = 'requirements';
  static const results = 'results';
  static const mostPopular = 'mostPopular';
  static const recentlyAdded = 'recentlyAdded';
  static const clearAll = 'clearAll';
  static const home = 'home';
  static const trainings = 'trainings';
  static const done = 'done';
  static const reviewsCount = 'reviewsCount';
  static const descriptionImage_title = 'descriptionImage.title';
  static const descriptionImage_guidelines = 'descriptionImage.guidelines';
  static const descriptionImage_text = 'descriptionImage.text';
  static const descriptionImage = 'descriptionImage';
  static const descriptionVideo_title = 'descriptionVideo.title';
  static const descriptionVideo_text = 'descriptionVideo.text';
  static const descriptionVideo_guidelines = 'descriptionVideo.guidelines';
  static const descriptionVideo = 'descriptionVideo';
  static const selectLanguage = 'selectLanguage';
  static const selectLevel = 'selectLevel';
  static const level = 'level';
  static const fieldValidation = 'fieldValidation';
  static const emailValidation = 'emailValidation';
  static const back = 'back';
  static const training = 'training';
  static const selectTraining = 'selectTraining';
  static const selectTrainingFromTheList = 'selectTrainingFromTheList';
  static const addToTrack = 'addToTrack';
  static const languageMismatching = 'languageMismatching';
  static const logout_fail_title = 'logout_fail_title';
  static const error = 'error';
  static const error_body = 'error_body';
  static const language = 'language';
  static const languages_EN = 'languages.EN';
  static const languages_AR = 'languages.AR';
  static const languages_arabic_en = 'languages.arabic_en';
  static const languages_arabic_ar = 'languages.arabic_ar';
  static const languages_english_en = 'languages.english_en';
  static const languages_english_ar = 'languages.english_ar';
  static const languages = 'languages';
  static const filterModal_english = 'filterModal.english';
  static const filterModal_arabic = 'filterModal.arabic';
  static const filterModal_HighestRated = 'filterModal.HighestRated';
  static const filterModal_Sort = 'filterModal.Sort';
  static const filterModal_Filter = 'filterModal.Filter';
  static const filterModal_Show = 'filterModal.Show';
  static const filterModal_Reset = 'filterModal.Reset';
  static const filterModal = 'filterModal';
  static const levels_BEGINNER = 'levels.BEGINNER';
  static const levels_INTERMEDIATE = 'levels.INTERMEDIATE';
  static const levels_ADVANCED = 'levels.ADVANCED';
  static const levels_ALL = 'levels.ALL';
  static const levels = 'levels';
  static const durations_DAYS = 'durations.DAYS';
  static const durations_WEEKS = 'durations.WEEKS';
  static const durations_MONTHS = 'durations.MONTHS';
  static const durations_ALL = 'durations.ALL';
  static const durations = 'durations';
  static const lessons_slide_description = 'lessons.slide.description';
  static const lessons_slide_title = 'lessons.slide.title';
  static const lessons_slide_inputPlaceholder = 'lessons.slide.inputPlaceholder';
  static const lessons_slide_emptyTitleValidationMessage =
      'lessons.slide.emptyTitleValidationMessage';
  static const lessons_slide = 'lessons.slide';
  static const lessons_video_description = 'lessons.video.description';
  static const lessons_video_title = 'lessons.video.title';
  static const lessons_video_inputPlaceholder = 'lessons.video.inputPlaceholder';
  static const lessons_video_emptyTitleValidationMessage =
      'lessons.video.emptyTitleValidationMessage';
  static const lessons_video = 'lessons.video';
  static const lessons = 'lessons';
  static const header_platformTitle = 'header.platformTitle';
  static const header_profileSettings = 'header.profileSettings';
  static const header_findTraining = 'header.findTraining';
  static const header_languagePlaceholder = 'header.languagePlaceholder';
  static const header_logOut = 'header.logOut';
  static const header_students = 'header.students';
  static const header_trainings = 'header.trainings';
  static const header_signIn = 'header.signIn';
  static const header_myTrainings = 'header.myTrainings';
  static const header_noTrainingsFound = 'header.noTrainingsFound';
  static const header_trainingProvidersList = 'header.trainingProvidersList';
  static const header_traineeProfile = 'header.traineeProfile';
  static const header_employerProfile = 'header.employerProfile';
  static const header_trainingProviderProfile = 'header.trainingProviderProfile';
  static const header_ministryEmployeeProfile = 'header.ministryEmployeeProfile';
  static const header_traineeProfileActive = 'header.traineeProfileActive';
  static const header_employerProfileActive = 'header.employerProfileActive';
  static const header_trainingProviderProfileActive = 'header.trainingProviderProfileActive';
  static const header_ministryEmployeeProfileActive = 'header.ministryEmployeeProfileActive';
  static const header_becomeEmployer = 'header.becomeEmployer';
  static const header_becomeTrainingProvider = 'header.becomeTrainingProvider';
  static const header_trainingProviders = 'header.trainingProviders';
  static const header_employers = 'header.employers';
  static const header_applicationRequests = 'header.applicationRequests';
  static const header_employer = 'header.employer';
  static const header_trainee = 'header.trainee';
  static const header_ministryEmployee = 'header.ministryEmployee';
  static const header_trainingProvider = 'header.trainingProvider';
  static const header_learningTracks = 'header.learningTracks';
  static const header_learningTrack = 'header.learningTrack';
  static const header_employerPrograms = 'header.employerPrograms';
  static const header_management = 'header.management';
  static const header_dashboard = 'header.dashboard';
  static const header_employees = 'header.employees';
  static const header_employerLearningTracks = 'header.employerLearningTracks';
  static const header = 'header';
  static const landingPage_gainSkills = 'landingPage.gainSkills';
  static const landingPage_exploreTrainings = 'landingPage.exploreTrainings';
  static const landingPage_discoverLearning = 'landingPage.discoverLearning';
  static const landingPage_withoutBoundaries = 'landingPage.withoutBoundaries';
  static const landingPage_activeUsers = 'landingPage.activeUsers';
  static const landingPage_TrainingProviders = 'landingPage.TrainingProviders';
  static const landingPage_trainingPrograms = 'landingPage.trainingPrograms';
  static const landingPage_weAreNationalSkillsPlatform = 'landingPage.weAreNationalSkillsPlatform';
  static const landingPage_youFutureAtYourFingertips = 'landingPage.youFutureAtYourFingertips';
  static const landingPage_exploreRecentlyAddedTrainings =
      'landingPage.exploreRecentlyAddedTrainings';
  static const landingPage_exploreAllTrainingPrograms = 'landingPage.exploreAllTrainingPrograms';
  static const landingPage_skillsPassport = 'landingPage.skillsPassport';
  static const landingPage_customizedLearningPathways = 'landingPage.customizedLearningPathways';
  static const landingPage_learnAnytimeAnywhere = 'landingPage.learnAnytimeAnywhere';
  static const landingPage_skillsPassportText = 'landingPage.skillsPassportText';
  static const landingPage_customizedLearningPathwaysText =
      'landingPage.customizedLearningPathwaysText';
  static const landingPage_learnAnytimeAnywhereText = 'landingPage.learnAnytimeAnywhereText';
  static const landingPage = 'landingPage';
  static const completedLessonsCount = 'completedLessonsCount';
  static const trainingDetails_promoVideo = 'trainingDetails.promoVideo';
  static const trainingDetails_trainingDescription = 'trainingDetails.trainingDescription';
  static const trainingDetails_lTrackEnrollmentModalTitle =
      'trainingDetails.lTrackEnrollmentModalTitle';
  static const trainingDetails_trainingEnrollmentModalTitle =
      'trainingDetails.trainingEnrollmentModalTitle';
  static const trainingDetails_whatYouWillLearn = 'trainingDetails.whatYouWillLearn';
  static const trainingDetails_skillsYouWillGain = 'trainingDetails.skillsYouWillGain';
  static const trainingDetails_requirements = 'trainingDetails.requirements';
  static const trainingDetails_trainingStructure = 'trainingDetails.trainingStructure';
  static const trainingDetails_apply = 'trainingDetails.apply';
  static const trainingDetails_studentsHaveAlreadyEnrolled =
      'trainingDetails.studentsHaveAlreadyEnrolled';
  static const trainingDetails_students = 'trainingDetails.students';
  static const trainingDetails_beenAlreadyEnrolled = 'trainingDetails.beenAlreadyEnrolled';
  static const trainingDetails_createdBy = 'trainingDetails.createdBy';
  static const trainingDetails_sectionIndex = 'trainingDetails.sectionIndex';
  static const trainingDetails_lessonsCount = 'trainingDetails.lessonsCount';
  static const trainingDetails_averageRate = 'trainingDetails.averageRate';
  static const trainingDetails_estimatedTime = 'trainingDetails.estimatedTime';
  static const trainingDetails_estimatedTimeValue = 'trainingDetails.estimatedTimeValue';
  static const trainingDetails_trainingLanguage = 'trainingDetails.trainingLanguage';
  static const trainingDetails_online = 'trainingDetails.online';
  static const trainingDetails_trainingLevel = 'trainingDetails.trainingLevel';
  static const trainingDetails_skillsTitle = 'trainingDetails.skillsTitle';
  static const trainingDetails_skillsSubtitle = 'trainingDetails.skillsSubtitle';
  static const trainingDetails_noSkillsFound = 'trainingDetails.noSkillsFound';
  static const trainingDetails_findSkill = 'trainingDetails.findSkill';
  static const trainingDetails_sectionsCount = 'trainingDetails.sectionsCount';
  static const trainingDetails_newTopic = 'trainingDetails.newTopic';
  static const trainingDetails_skillsValidation = 'trainingDetails.skillsValidation';
  static const trainingDetails_goToTraining = 'trainingDetails.goToTraining';
  static const trainingDetails_registrationSuccessfullyFinished =
      'trainingDetails.registrationSuccessfullyFinished';
  static const trainingDetails_trainingList = 'trainingDetails.trainingList';
  static const trainingDetails_design = 'trainingDetails.design';
  static const trainingDetails_enroll = 'trainingDetails.enroll';
  static const trainingDetails_self_based = 'trainingDetails.self_based';
  static const trainingDetails = 'trainingDetails';
  static const becomeTrainingProvider_becomeTrainingProviderTitle =
      'becomeTrainingProvider.becomeTrainingProviderTitle';
  static const becomeTrainingProvider_trainingProviderApplication =
      'becomeTrainingProvider.trainingProviderApplication';
  static const becomeTrainingProvider_trainingProviderDetails =
      'becomeTrainingProvider.trainingProviderDetails';
  static const becomeTrainingProvider_filesNeeded = 'becomeTrainingProvider.filesNeeded';
  static const becomeTrainingProvider_commercialRegistration =
      'becomeTrainingProvider.commercialRegistration';
  static const becomeTrainingProvider_accreditationCertificate =
      'becomeTrainingProvider.accreditationCertificate';
  static const becomeTrainingProvider_gosi = 'becomeTrainingProvider.gosi';
  static const becomeTrainingProvider_vat = 'becomeTrainingProvider.vat';
  static const becomeTrainingProvider_organizationName = 'becomeTrainingProvider.organizationName';
  static const becomeTrainingProvider_commercialRegistrationEndDate =
      'becomeTrainingProvider.commercialRegistrationEndDate';
  static const becomeTrainingProvider_commercialRegistrationEndDatePlaceholder =
      'becomeTrainingProvider.commercialRegistrationEndDatePlaceholder';
  static const becomeTrainingProvider_commercialRegistrationPlaceholder =
      'becomeTrainingProvider.commercialRegistrationPlaceholder';
  static const becomeTrainingProvider_gosiPlaceholder = 'becomeTrainingProvider.gosiPlaceholder';
  static const becomeTrainingProvider_vatPlaceholder = 'becomeTrainingProvider.vatPlaceholder';
  static const becomeTrainingProvider_organizationNamePlaceholder =
      'becomeTrainingProvider.organizationNamePlaceholder';
  static const becomeTrainingProvider_becomeTrainingProviderFileExtensions =
      'becomeTrainingProvider.becomeTrainingProviderFileExtensions';
  static const becomeTrainingProvider_discardApplication =
      'becomeTrainingProvider.discardApplication';
  static const becomeTrainingProvider_submit = 'becomeTrainingProvider.submit';
  static const becomeTrainingProvider_discardTheApplication =
      'becomeTrainingProvider.discardTheApplication';
  static const becomeTrainingProvider_yesDiscard = 'becomeTrainingProvider.yesDiscard';
  static const becomeTrainingProvider_privacyPolicy = 'becomeTrainingProvider.privacyPolicy';
  static const becomeTrainingProvider_application = 'becomeTrainingProvider.application';
  static const becomeTrainingProvider_reuseFilesFromApplication =
      'becomeTrainingProvider.reuseFilesFromApplication';
  static const becomeTrainingProvider_downloadDocumentsToReviewIt =
      'becomeTrainingProvider.downloadDocumentsToReviewIt';
  static const becomeTrainingProvider_documnetsWereRejected =
      'becomeTrainingProvider.documnetsWereRejected';
  static const becomeTrainingProvider_documnetsWereApproved =
      'becomeTrainingProvider.documnetsWereApproved';
  static const becomeTrainingProvider_toTrainingProviderProfile =
      'becomeTrainingProvider.toTrainingProviderProfile';
  static const becomeTrainingProvider_dontShowAgain = 'becomeTrainingProvider.dontShowAgain';
  static const becomeTrainingProvider_continueRegistrationAsTrainingProvider =
      'becomeTrainingProvider.continueRegistrationAsTrainingProvider';
  static const becomeTrainingProvider_completeDocumentsUploadingToBecomeTrainingProvider =
      'becomeTrainingProvider.completeDocumentsUploadingToBecomeTrainingProvider';
  static const becomeTrainingProvider_continue = 'becomeTrainingProvider.continue';
  static const becomeTrainingProvider_skip = 'becomeTrainingProvider.skip';
  static const becomeTrainingProvider_pending = 'becomeTrainingProvider.pending';
  static const becomeTrainingProvider_rejected = 'becomeTrainingProvider.rejected';
  static const becomeTrainingProvider_completed = 'becomeTrainingProvider.completed';
  static const becomeTrainingProvider_resubmitted = 'becomeTrainingProvider.resubmitted';
  static const becomeTrainingProvider_errorMessage = 'becomeTrainingProvider.errorMessage';
  static const becomeTrainingProvider_generalBackendError =
      'becomeTrainingProvider.generalBackendError';
  static const becomeTrainingProvider_maxFileLimit = 'becomeTrainingProvider.maxFileLimit';
  static const becomeTrainingProvider_submissionDate = 'becomeTrainingProvider.submissionDate';
  static const becomeTrainingProvider_approvalDate = 'becomeTrainingProvider.approvalDate';
  static const becomeTrainingProvider_rejectionDate = 'becomeTrainingProvider.rejectionDate';
  static const becomeTrainingProvider_id = 'becomeTrainingProvider.id';
  static const becomeTrainingProvider_applicationSuccessfullySubmitted =
      'becomeTrainingProvider.applicationSuccessfullySubmitted';
  static const becomeTrainingProvider = 'becomeTrainingProvider';
  static const trainingsList_noTrainingsFound = 'trainingsList.noTrainingsFound';
  static const trainingsList_noTrainingsFoundDescription =
      'trainingsList.noTrainingsFoundDescription';
  static const trainingsList_skillsValidation = 'trainingsList.skillsValidation';
  static const trainingsList = 'trainingsList';
  static const trainingProgress_sectionsProgress = 'trainingProgress.sectionsProgress';
  static const trainingProgress_progressPercentage = 'trainingProgress.progressPercentage';
  static const trainingProgress = 'trainingProgress';
  static const footer_allRightsReserved = 'footer.allRightsReserved';
  static const footer_overview = 'footer.overview';
  static const footer_aboutTheInitiatives = 'footer.aboutTheInitiatives';
  static const footer_howToUse = 'footer.howToUse';
  static const footer_registration = 'footer.registration';
  static const footer_TermsOfUse = 'footer.TermsOfUse';
  static const footer_contactUs = 'footer.contactUs';
  static const footer_registerAsTrainee = 'footer.registerAsTrainee';
  static const footer_faqs = 'footer.faqs';
  static const footer = 'footer';
  static const trainingView_pagesCount = 'trainingView.pagesCount';
  static const trainingView_next = 'trainingView.next';
  static const trainingView_previous = 'trainingView.previous';
  static const trainingView_lessonsProgress = 'trainingView.lessonsProgress';
  static const trainingView_trainingContent = 'trainingView.trainingContent';
  static const trainingView_mandatoryTest = 'trainingView.mandatoryTest';
  static const trainingView_numberOfQuestionsInTest = 'trainingView.numberOfQuestionsInTest';
  static const trainingView_questionOutOf = 'trainingView.questionOutOf';
  static const trainingView_minScoreOfTest = 'trainingView.minScoreOfTest';
  static const trainingView_startTestLabel = 'trainingView.startTestLabel';
  static const trainingView_preQualificationTest = 'trainingView.preQualificationTest';
  static const trainingView_postQualificationTest = 'trainingView.postQualificationTest';
  static const trainingView_postEvaluationTest = 'trainingView.postEvaluationTest';
  static const trainingView = 'trainingView';
  static const conductTest_True = 'conductTest.True';
  static const conductTest_False = 'conductTest.False';
  static const conductTest = 'conductTest';
  static const userLearnings_continue = 'userLearnings.continue';
  static const userLearnings_overview = 'userLearnings.overview';
  static const userLearnings_decline = 'userLearnings.decline';
  static const userLearnings_apply = 'userLearnings.apply';
  static const userLearnings_rateTraining = 'userLearnings.rateTraining';
  static const userLearnings_in_progress = 'userLearnings.in_progress';
  static const userLearnings_viewCertificate = 'userLearnings.viewCertificate';
  static const userLearnings_assignedOn = 'userLearnings.assignedOn';
  static const userLearnings_completedOn = 'userLearnings.completedOn';
  static const userLearnings_yourRating = 'userLearnings.yourRating';
  static const userLearnings_trainingsList = 'userLearnings.trainingsList';
  static const userLearnings_current = 'userLearnings.current';
  static const userLearnings_nominated = 'userLearnings.nominated';
  static const userLearnings_completed = 'userLearnings.completed';
  static const userLearnings_beginOnlineLearningExperience =
      'userLearnings.beginOnlineLearningExperience';
  static const userLearnings_emptyCurrentListText = 'userLearnings.emptyCurrentListText';
  static const userLearnings_emptyNominatedListHeader = 'userLearnings.emptyNominatedListHeader';
  static const userLearnings_emptyNominatedListText = 'userLearnings.emptyNominatedListText';
  static const userLearnings_emptyCompletedListText = 'userLearnings.emptyCompletedListText';
  static const userLearnings_exploreTrainings = 'userLearnings.exploreTrainings';
  static const userLearnings_haveToCompletePretrainingTest =
      'userLearnings.haveToCompletePretrainingTest';
  static const userLearnings = 'userLearnings';
  static const bottom_nav_bar_home = 'bottom_nav_bar.home';
  static const bottom_nav_bar_catalog = 'bottom_nav_bar.catalog';
  static const bottom_nav_bar_myLearnings = 'bottom_nav_bar.myLearnings';
  static const bottom_nav_bar_profile = 'bottom_nav_bar.profile';
  static const bottom_nav_bar = 'bottom_nav_bar';
  static const trainingBuilder_publish = 'trainingBuilder.publish';
  static const trainingBuilder_saveAsDraft = 'trainingBuilder.saveAsDraft';
  static const trainingBuilder_validationErrorMessage = 'trainingBuilder.validationErrorMessage';
  static const trainingBuilder_trainingIsPublished = 'trainingBuilder.trainingIsPublished';
  static const trainingBuilder_allChangesAreSaved = 'trainingBuilder.allChangesAreSaved';
  static const trainingBuilder_requiredFieldsErrorMessage =
      'trainingBuilder.requiredFieldsErrorMessage';
  static const trainingBuilder_emptySectionsMessage = 'trainingBuilder.emptySectionsMessage';
  static const trainingBuilder_tests_label = 'trainingBuilder.tests.label';
  static const trainingBuilder_tests_preTrainingQualificationLabel =
      'trainingBuilder.tests.preTrainingQualificationLabel';
  static const trainingBuilder_tests_postTrainingEvaluationLabel =
      'trainingBuilder.tests.postTrainingEvaluationLabel';
  static const trainingBuilder_tests_settings = 'trainingBuilder.tests.settings';
  static const trainingBuilder_tests_minScore = 'trainingBuilder.tests.minScore';
  static const trainingBuilder_tests_minScorePlaceholder =
      'trainingBuilder.tests.minScorePlaceholder';
  static const trainingBuilder_tests_minScoreTooltip = 'trainingBuilder.tests.minScoreTooltip';
  static const trainingBuilder_tests_title = 'trainingBuilder.tests.title';
  static const trainingBuilder_tests_titlePlaceholder = 'trainingBuilder.tests.titlePlaceholder';
  static const trainingBuilder_tests_description = 'trainingBuilder.tests.description';
  static const trainingBuilder_tests_descriptionPlaceholder =
      'trainingBuilder.tests.descriptionPlaceholder';
  static const trainingBuilder_tests_cancel = 'trainingBuilder.tests.cancel';
  static const trainingBuilder_tests_save = 'trainingBuilder.tests.save';
  static const trainingBuilder_tests_mandatory = 'trainingBuilder.tests.mandatory';
  static const trainingBuilder_tests_mandatoryTooltip = 'trainingBuilder.tests.mandatoryTooltip';
  static const trainingBuilder_tests_preHidden = 'trainingBuilder.tests.preHidden';
  static const trainingBuilder_tests_postHidden = 'trainingBuilder.tests.postHidden';
  static const trainingBuilder_tests_randomize = 'trainingBuilder.tests.randomize';
  static const trainingBuilder_tests_randomizeTooltip = 'trainingBuilder.tests.randomizeTooltip';
  static const trainingBuilder_tests_deletePre = 'trainingBuilder.tests.deletePre';
  static const trainingBuilder_tests_deletePost = 'trainingBuilder.tests.deletePost';
  static const trainingBuilder_tests_cancelDelete = 'trainingBuilder.tests.cancelDelete';
  static const trainingBuilder_tests_confirmDelete = 'trainingBuilder.tests.confirmDelete';
  static const trainingBuilder_tests_deleteDescriptionPre =
      'trainingBuilder.tests.deleteDescriptionPre';
  static const trainingBuilder_tests_deleteDescriptionPost =
      'trainingBuilder.tests.deleteDescriptionPost';
  static const trainingBuilder_tests_deleteDescription = 'trainingBuilder.tests.deleteDescription';
  static const trainingBuilder_tests_questions = 'trainingBuilder.tests.questions';
  static const trainingBuilder_tests_newQuestion = 'trainingBuilder.tests.newQuestion';
  static const trainingBuilder_tests_questionCount_one = 'trainingBuilder.tests.questionCount_one';
  static const trainingBuilder_tests_questionCount_other =
      'trainingBuilder.tests.questionCount_other';
  static const trainingBuilder_tests_hiddenLabel = 'trainingBuilder.tests.hiddenLabel';
  static const trainingBuilder_tests_draftLabel = 'trainingBuilder.tests.draftLabel';
  static const trainingBuilder_tests_editTest = 'trainingBuilder.tests.editTest';
  static const trainingBuilder_tests_duplicateTest = 'trainingBuilder.tests.duplicateTest';
  static const trainingBuilder_tests_deleteTest = 'trainingBuilder.tests.deleteTest';
  static const trainingBuilder_tests_deleteQuestion = 'trainingBuilder.tests.deleteQuestion';
  static const trainingBuilder_tests_duplicateQuestion = 'trainingBuilder.tests.duplicateQuestion';
  static const trainingBuilder_tests_question = 'trainingBuilder.tests.question';
  static const trainingBuilder_tests_answers = 'trainingBuilder.tests.answers';
  static const trainingBuilder_tests_addAnswer = 'trainingBuilder.tests.addAnswer';
  static const trainingBuilder_tests_questionType = 'trainingBuilder.tests.questionType';
  static const trainingBuilder_tests_correctAnswer = 'trainingBuilder.tests.correctAnswer';
  static const trainingBuilder_tests_incorrectAnswer = 'trainingBuilder.tests.incorrectAnswer';
  static const trainingBuilder_tests_questionDescription_choose_singular_answer =
      'trainingBuilder.tests.questionDescription.choose_singular_answer';
  static const trainingBuilder_tests_questionDescription_RADIO =
      'trainingBuilder.tests.questionDescription.RADIO';
  static const trainingBuilder_tests_questionDescription_TRUEFALSE =
      'trainingBuilder.tests.questionDescription.TRUEFALSE';
  static const trainingBuilder_tests_questionDescription =
      'trainingBuilder.tests.questionDescription';
  static const trainingBuilder_tests_questionTypes_RADIO =
      'trainingBuilder.tests.questionTypes.RADIO';
  static const trainingBuilder_tests_questionTypes_TRUEFALSE =
      'trainingBuilder.tests.questionTypes.TRUEFALSE';
  static const trainingBuilder_tests_questionTypes = 'trainingBuilder.tests.questionTypes';
  static const trainingBuilder_tests_minOneQuestion = 'trainingBuilder.tests.minOneQuestion';
  static const trainingBuilder_tests_minTwoAnswers = 'trainingBuilder.tests.minTwoAnswers';
  static const trainingBuilder_tests_hasCorrectAnswer = 'trainingBuilder.tests.hasCorrectAnswer';
  static const trainingBuilder_tests_confirmExit = 'trainingBuilder.tests.confirmExit';
  static const trainingBuilder_tests = 'trainingBuilder.tests';
  static const trainingBuilder = 'trainingBuilder';
  static const trainingProviderDetailedPage_generalInformation =
      'trainingProviderDetailedPage.generalInformation';
  static const trainingProviderDetailedPage = 'trainingProviderDetailedPage';
  static const trainingProviders_label = 'trainingProviders.label';
  static const trainingProviders_trainings = 'trainingProviders.trainings';
  static const trainingProviders_employers = 'trainingProviders.employers';
  static const trainingProviders_trainees = 'trainingProviders.trainees';
  static const trainingProviders_searchPlaceholder = 'trainingProviders.searchPlaceholder';
  static const trainingProviders_pendingStatus = 'trainingProviders.pendingStatus';
  static const trainingProviders_rejectedStatus = 'trainingProviders.rejectedStatus';
  static const trainingProviders_approvedStatus = 'trainingProviders.approvedStatus';
  static const trainingProviders_tableTitle = 'trainingProviders.tableTitle';
  static const trainingProviders_rejectLabel = 'trainingProviders.rejectLabel';
  static const trainingProviders_approveLabel = 'trainingProviders.approveLabel';
  static const trainingProviders_noData = 'trainingProviders.noData';
  static const trainingProviders_noDataDescription = 'trainingProviders.noDataDescription';
  static const trainingProviders_trainingProviderName = 'trainingProviders.trainingProviderName';
  static const trainingProviders_status = 'trainingProviders.status';
  static const trainingProviders_organizationalId = 'trainingProviders.organizationalId';
  static const trainingProviders_requestDate = 'trainingProviders.requestDate';
  static const trainingProviders_viewDetails = 'trainingProviders.viewDetails';
  static const trainingProviders_id = 'trainingProviders.id';
  static const trainingProviders = 'trainingProviders';
  static const trainingProviderDetailed_label = 'trainingProviderDetailed.label';
  static const trainingProviderDetailed_generalInformation =
      'trainingProviderDetailed.generalInformation';
  static const trainingProviderDetailed_name = 'trainingProviderDetailed.name';
  static const trainingProviderDetailed_email = 'trainingProviderDetailed.email';
  static const trainingProviderDetailed_mobileNumber = 'trainingProviderDetailed.mobileNumber';
  static const trainingProviderDetailed_organizationId = 'trainingProviderDetailed.organizationId';
  static const trainingProviderDetailed_accreditationRequest =
      'trainingProviderDetailed.accreditationRequest';
  static const trainingProviderDetailed_youCanDownloadDocuments =
      'trainingProviderDetailed.youCanDownloadDocuments';
  static const trainingProviderDetailed_organizationName =
      'trainingProviderDetailed.organizationName';
  static const trainingProviderDetailed_gosi = 'trainingProviderDetailed.gosi';
  static const trainingProviderDetailed_commercialRegistration =
      'trainingProviderDetailed.commercialRegistration';
  static const trainingProviderDetailed_vat = 'trainingProviderDetailed.vat';
  static const trainingProviderDetailed_rejected = 'trainingProviderDetailed.rejected';
  static const trainingProviderDetailed_rejectedReason = 'trainingProviderDetailed.rejectedReason';
  static const trainingProviderDetailed_trainingProvidersList =
      'trainingProviderDetailed.trainingProvidersList';
  static const trainingProviderDetailed_trainingProviderDetails =
      'trainingProviderDetailed.trainingProviderDetails';
  static const trainingProviderDetailed_id = 'trainingProviderDetailed.id';
  static const trainingProviderDetailed = 'trainingProviderDetailed';
  static const rejectTrainingProvider_rejectionReason = 'rejectTrainingProvider.rejectionReason';
  static const rejectTrainingProvider_rejectionReasonPlaceholder =
      'rejectTrainingProvider.rejectionReasonPlaceholder';
  static const rejectTrainingProvider_reject = 'rejectTrainingProvider.reject';
  static const rejectTrainingProvider_rejectionReasonRequired =
      'rejectTrainingProvider.rejectionReasonRequired';
  static const rejectTrainingProvider = 'rejectTrainingProvider';
  static const modalTitle = 'modalTitle';
  static const newPasswordLabel = 'newPasswordLabel';
  static const passwordConfirmLabel = 'passwordConfirmLabel';
  static const enter_password = 'enter_password';
  static const passwordFieldRequired = 'passwordFieldRequired';
  static const passwordMustMatch = 'passwordMustMatch';
  static const passwordValidationMessage = 'passwordValidationMessage';
  static const profileAndSettings_title = 'profileAndSettings.title';
  static const profileAndSettings_generalInfo = 'profileAndSettings.generalInfo';
  static const profileAndSettings_trainingProvider = 'profileAndSettings.trainingProvider';
  static const profileAndSettings_profileInfo = 'profileAndSettings.profileInfo';
  static const profileAndSettings_edit = 'profileAndSettings.edit';
  static const profileAndSettings_changePassword = 'profileAndSettings.changePassword';
  static const profileAndSettings_changePasswordDescription =
      'profileAndSettings.changePasswordDescription';
  static const profileAndSettings_setPassword = 'profileAndSettings.setPassword';
  static const profileAndSettings_enterCode = 'profileAndSettings.enterCode';
  static const profileAndSettings_codeModalDescription = 'profileAndSettings.codeModalDescription';
  static const profileAndSettings_submit = 'profileAndSettings.submit';
  static const profileAndSettings_resend = 'profileAndSettings.resend';
  static const profileAndSettings_notRevivedCode = 'profileAndSettings.notRevivedCode';
  static const profileAndSettings_toastTitle = 'profileAndSettings.toastTitle';
  static const profileAndSettings_toastMessage = 'profileAndSettings.toastMessage';
  static const profileAndSettings_incorrectVerificationCode =
      'profileAndSettings.incorrectVerificationCode';
  static const profileAndSettings_resendCodeMessage = 'profileAndSettings.resendCodeMessage';
  static const profileAndSettings_name = 'profileAndSettings.name';
  static const profileAndSettings_email = 'profileAndSettings.email';
  static const profileAndSettings_phoneNumber = 'profileAndSettings.phoneNumber';
  static const profileAndSettings_nationalId = 'profileAndSettings.nationalId';
  static const profileAndSettings_birthDate = 'profileAndSettings.birthDate';
  static const profileAndSettings_tooltipText = 'profileAndSettings.tooltipText';
  static const profileAndSettings_timeRemaining = 'profileAndSettings.timeRemaining';
  static const profileAndSettings = 'profileAndSettings';
  static const textField_usedCharacters = 'textField.usedCharacters';
  static const textField = 'textField';
  static const questions_singleChoice_label = 'questions.singleChoice.label';
  static const questions_singleChoice = 'questions.singleChoice';
  static const questions_errors_selectOneAnswer = 'questions.errors.selectOneAnswer';
  static const questions_errors = 'questions.errors';
  static const questions_confirm = 'questions.confirm';
  static const questions_goBack = 'questions.goBack';
  static const questions_results_preCompleted = 'questions.results.preCompleted';
  static const questions_results_postCompleted = 'questions.results.postCompleted';
  static const questions_results_quizCompleted = 'questions.results.quizCompleted';
  static const questions_results_yourScore = 'questions.results.yourScore';
  static const questions_results_score = 'questions.results.score';
  static const questions_results_retakeTest = 'questions.results.retakeTest';
  static const questions_results = 'questions.results';
  static const questions = 'questions';
  static const hint = 'hint';
  static const employerApplication_employerApplicationTitle =
      'employerApplication.employerApplicationTitle';
  static const employerApplication_applicationRequestTitle =
      'employerApplication.applicationRequestTitle';
  static const employerApplication_workingEmail = 'employerApplication.workingEmail';
  static const employerApplication_workingEmailPlaceholder =
      'employerApplication.workingEmailPlaceholder';
  static const employerApplication_positionInCompany = 'employerApplication.positionInCompany';
  static const employerApplication_positionInCompanyPlaceholder =
      'employerApplication.positionInCompanyPlaceholder';
  static const employerApplication_moi = 'employerApplication.moi';
  static const employerApplication_moiPlaceholder = 'employerApplication.moiPlaceholder';
  static const employerApplication_referenceInfoTitle = 'employerApplication.referenceInfoTitle';
  static const employerApplication_referenceInfoTooltip =
      'employerApplication.referenceInfoTooltip';
  static const employerApplication_referenceName = 'employerApplication.referenceName';
  static const employerApplication_referenceNamePlaceholder =
      'employerApplication.referenceNamePlaceholder';
  static const employerApplication_referenceEmail = 'employerApplication.referenceEmail';
  static const employerApplication_referenceEmailPlaceholder =
      'employerApplication.referenceEmailPlaceholder';
  static const employerApplication_referencePhone = 'employerApplication.referencePhone';
  static const employerApplication_optionalComment = 'employerApplication.optionalComment';
  static const employerApplication_optionalCommentPlaceholder =
      'employerApplication.optionalCommentPlaceholder';
  static const employerApplication_discardApplication = 'employerApplication.discardApplication';
  static const employerApplication_submit = 'employerApplication.submit';
  static const employerApplication_filesNeeded = 'employerApplication.filesNeeded';
  static const employerApplication_commercialRegistration =
      'employerApplication.commercialRegistration';
  static const employerApplication_gosi = 'employerApplication.gosi';
  static const employerApplication_vatOptional = 'employerApplication.vatOptional';
  static const employerApplication_continue = 'employerApplication.continue';
  static const employerApplication_skip = 'employerApplication.skip';
  static const employerApplication_continueRegistrationLabel =
      'employerApplication.continueRegistrationLabel';
  static const employerApplication_continueRegistrationText =
      'employerApplication.continueRegistrationText';
  static const employerApplication_discardButton = 'employerApplication.discardButton';
  static const employerApplication_discardRegistrationLabel =
      'employerApplication.discardRegistrationLabel';
  static const employerApplication_discardRegistrationText =
      'employerApplication.discardRegistrationText';
  static const employerApplication_requestId = 'employerApplication.requestId';
  static const employerApplication_submitDate = 'employerApplication.submitDate';
  static const employerApplication_rejectedReason = 'employerApplication.rejectedReason';
  static const employerApplication_formRestriction = 'employerApplication.formRestriction';
  static const employerApplication_applicationSuccessfullySubmitted =
      'employerApplication.applicationSuccessfullySubmitted';
  static const employerApplication = 'employerApplication';
  static const employerApplicationsList_title = 'employerApplicationsList.title';
  static const employerApplicationsList_subTitle = 'employerApplicationsList.subTitle';
  static const employerApplicationsList_totalCount_one = 'employerApplicationsList.totalCount_one';
  static const employerApplicationsList_totalCount_other =
      'employerApplicationsList.totalCount_other';
  static const employerApplicationsList_requestId = 'employerApplicationsList.requestId';
  static const employerApplicationsList_username = 'employerApplicationsList.username';
  static const employerApplicationsList_status = 'employerApplicationsList.status';
  static const employerApplicationsList_organizationName =
      'employerApplicationsList.organizationName';
  static const employerApplicationsList_commercialRegistration =
      'employerApplicationsList.commercialRegistration';
  static const employerApplicationsList_date = 'employerApplicationsList.date';
  static const employerApplicationsList_search = 'employerApplicationsList.search';
  static const employerApplicationsList_applicationStatus_pending =
      'employerApplicationsList.applicationStatus.pending';
  static const employerApplicationsList_applicationStatus_approved =
      'employerApplicationsList.applicationStatus.approved';
  static const employerApplicationsList_applicationStatus_rejected =
      'employerApplicationsList.applicationStatus.rejected';
  static const employerApplicationsList_applicationStatus =
      'employerApplicationsList.applicationStatus';
  static const employerApplicationsList_searchPopover = 'employerApplicationsList.searchPopover';
  static const employerApplicationsList_clearAll = 'employerApplicationsList.clearAll';
  static const employerApplicationsList_apply = 'employerApplicationsList.apply';
  static const employerApplicationsList_filterApplied = 'employerApplicationsList.filterApplied';
  static const employerApplicationsList_filters = 'employerApplicationsList.filters';
  static const employerApplicationsList_dateTooltip = 'employerApplicationsList.dateTooltip';
  static const employerApplicationsList = 'employerApplicationsList';
  static const employerApplicationDetails_label = 'employerApplicationDetails.label';
  static const employerApplicationDetails_requests = 'employerApplicationDetails.requests';
  static const employerApplicationDetails_userName = 'employerApplicationDetails.userName';
  static const employerApplicationDetails_nationalId = 'employerApplicationDetails.nationalId';
  static const employerApplicationDetails_birthDate = 'employerApplicationDetails.birthDate';
  static const employerApplicationDetails_email = 'employerApplicationDetails.email';
  static const employerApplicationDetails_mobileNumber = 'employerApplicationDetails.mobileNumber';
  static const employerApplicationDetails_rejectApplicationTitle =
      'employerApplicationDetails.rejectApplicationTitle';
  static const employerApplicationDetails_rejectApplicationSubTitle =
      'employerApplicationDetails.rejectApplicationSubTitle';
  static const employerApplicationDetails = 'employerApplicationDetails';
  static const employerProfile_title = 'employerProfile.title';
  static const employerProfile_userInfo = 'employerProfile.userInfo';
  static const employerProfile_applicationRequestHistory =
      'employerProfile.applicationRequestHistory';
  static const employerProfile_associatedEmployers = 'employerProfile.associatedEmployers';
  static const employerProfile = 'employerProfile';
  static const quiz = 'quiz';
  static const quizQuestionType_RADIO = 'quizQuestionType.RADIO';
  static const quizQuestionType_TRUEFALSE = 'quizQuestionType.TRUEFALSE';
  static const quizQuestionType_RANK = 'quizQuestionType.RANK';
  static const quizQuestionType_CHECKBOX = 'quizQuestionType.CHECKBOX';
  static const quizQuestionType = 'quizQuestionType';
  static const privacyPolicy_privacyPolicyTitle = 'privacyPolicy.privacyPolicyTitle';
  static const privacyPolicy_date = 'privacyPolicy.date';
  static const privacyPolicy_generalInfo = 'privacyPolicy.generalInfo';
  static const privacyPolicy_generalInfoTextFirst = 'privacyPolicy.generalInfoTextFirst';
  static const privacyPolicy_generalInfoSubtitle = 'privacyPolicy.generalInfoSubtitle';
  static const privacyPolicy_generalInfoTextSecond = 'privacyPolicy.generalInfoTextSecond';
  static const privacyPolicy_infoToUsTitle = 'privacyPolicy.infoToUsTitle';
  static const privacyPolicy_infoToUsText = 'privacyPolicy.infoToUsText';
  static const privacyPolicy_infoToCollectTitle = 'privacyPolicy.infoToCollectTitle';
  static const privacyPolicy_infoToCollectText = 'privacyPolicy.infoToCollectText';
  static const privacyPolicy_infoWeReceiveTitle = 'privacyPolicy.infoWeReceiveTitle';
  static const privacyPolicy_infoWeReceiveText = 'privacyPolicy.infoWeReceiveText';
  static const privacyPolicy_privacyPolicyDetails = 'privacyPolicy.privacyPolicyDetails';
  static const privacyPolicy_cookiesTitle = 'privacyPolicy.cookiesTitle';
  static const privacyPolicy_cookiesText = 'privacyPolicy.cookiesText';
  static const privacyPolicy_cookiesThatWeUsedTitle = 'privacyPolicy.cookiesThatWeUsedTitle';
  static const privacyPolicy_cookiesThatWeUsedText = 'privacyPolicy.cookiesThatWeUsedText';
  static const privacyPolicy_waysOfInfoUsageTitle = 'privacyPolicy.waysOfInfoUsageTitle';
  static const privacyPolicy_waysOfInfoUsageText = 'privacyPolicy.waysOfInfoUsageText';
  static const privacyPolicy_infoFromResourcesTitle = 'privacyPolicy.infoFromResourcesTitle';
  static const privacyPolicy_infoFromResourcesText = 'privacyPolicy.infoFromResourcesText';
  static const privacyPolicy_disclosureInfoTitle = 'privacyPolicy.disclosureInfoTitle';
  static const privacyPolicy_disclosureInfoText = 'privacyPolicy.disclosureInfoText';
  static const privacyPolicy_consentOfDataTitle = 'privacyPolicy.consentOfDataTitle';
  static const privacyPolicy_consentOfDataText = 'privacyPolicy.consentOfDataText';
  static const privacyPolicy_yourRightsTitle = 'privacyPolicy.yourRightsTitle';
  static const privacyPolicy_yourRightsText = 'privacyPolicy.yourRightsText';
  static const privacyPolicy_changePrivacyPolicyTitle = 'privacyPolicy.changePrivacyPolicyTitle';
  static const privacyPolicy_changePrivacyPolicyText = 'privacyPolicy.changePrivacyPolicyText';
  static const privacyPolicy = 'privacyPolicy';
  static const learningTracks_sidebarTitle = 'learningTracks.sidebarTitle';
  static const learningTracks_title = 'learningTracks.title';
  static const learningTracks_createTitle = 'learningTracks.createTitle';
  static const learningTracks_allTracks = 'learningTracks.allTracks';
  static const learningTracks_tracksCount_zero = 'learningTracks.tracksCount_zero';
  static const learningTracks_tracksCount_one = 'learningTracks.tracksCount_one';
  static const learningTracks_tracksCount_other = 'learningTracks.tracksCount_other';
  static const learningTracks_trainingsCount = 'learningTracks.trainingsCount';
  static const learningTracks_noTracksYet = 'learningTracks.noTracksYet';
  static const learningTracks_createTrackSuggestion = 'learningTracks.createTrackSuggestion';
  static const learningTracks_editTrack = 'learningTracks.editTrack';
  static const learningTracks_duplicateTrack = 'learningTracks.duplicateTrack';
  static const learningTracks_deleteTrack = 'learningTracks.deleteTrack';
  static const learningTracks_areYouSureDelete = 'learningTracks.areYouSureDelete';
  static const learningTracks_builder_modalTitle = 'learningTracks.builder.modalTitle';
  static const learningTracks_builder_titlePlaceholder = 'learningTracks.builder.titlePlaceholder';
  static const learningTracks_builder_titleLabel = 'learningTracks.builder.titleLabel';
  static const learningTracks_builder_sectorLabel = 'learningTracks.builder.sectorLabel';
  static const learningTracks_builder_sectorPlaceholder =
      'learningTracks.builder.sectorPlaceholder';
  static const learningTracks_builder_domainLabel = 'learningTracks.builder.domainLabel';
  static const learningTracks_builder_domainPlaceholder =
      'learningTracks.builder.domainPlaceholder';
  static const learningTracks_builder_languageLabel = 'learningTracks.builder.languageLabel';
  static const learningTracks_builder_languagePlaceholder =
      'learningTracks.builder.languagePlaceholder';
  static const learningTracks_builder_submit = 'learningTracks.builder.submit';
  static const learningTracks_builder_description = 'learningTracks.builder.description';
  static const learningTracks_builder_structure = 'learningTracks.builder.structure';
  static const learningTracks_builder_syllabus = 'learningTracks.builder.syllabus';
  static const learningTracks_builder_descriptionSubtitle =
      'learningTracks.builder.descriptionSubtitle';
  static const learningTracks_builder_structureSubtitle =
      'learningTracks.builder.structureSubtitle';
  static const learningTracks_builder_levelLabel = 'learningTracks.builder.levelLabel';
  static const learningTracks_builder_levelPlaceholder = 'learningTracks.builder.levelPlaceholder';
  static const learningTracks_builder_descriptionLabel = 'learningTracks.builder.descriptionLabel';
  static const learningTracks_builder_descriptionPlaceholder =
      'learningTracks.builder.descriptionPlaceholder';
  static const learningTracks_builder_nominationOnly = 'learningTracks.builder.nominationOnly';
  static const learningTracks_builder_confirmLeave = 'learningTracks.builder.confirmLeave';
  static const learningTracks_builder_imageTitle = 'learningTracks.builder.imageTitle';
  static const learningTracks_builder_imageText = 'learningTracks.builder.imageText';
  static const learningTracks_builder_min2trainings = 'learningTracks.builder.min2trainings';
  static const learningTracks_builder_max10trainings = 'learningTracks.builder.max10trainings';
  static const learningTracks_builder_submittedForReview =
      'learningTracks.builder.submittedForReview';
  static const learningTracks_builder = 'learningTracks.builder';
  static const learningTracks = 'learningTracks';
  static const traineeProfile_title = 'traineeProfile.title';
  static const traineeProfile_orgTab = 'traineeProfile.orgTab';
  static const traineeProfile_orgNameColumnHeader = 'traineeProfile.orgNameColumnHeader';
  static const traineeProfile_pendingMessage = 'traineeProfile.pendingMessage';
  static const traineeProfile_sendRequest = 'traineeProfile.sendRequest';
  static const traineeProfile_tableHeader = 'traineeProfile.tableHeader';
  static const traineeProfile_tableHeaderDescription = 'traineeProfile.tableHeaderDescription';
  static const traineeProfile_searchPlaceholder = 'traineeProfile.searchPlaceholder';
  static const traineeProfile_resultCount_one = 'traineeProfile.resultCount_one';
  static const traineeProfile_resultCount_other = 'traineeProfile.resultCount_other';
  static const traineeProfile_employerName = 'traineeProfile.employerName';
  static const traineeProfile_workEmail = 'traineeProfile.workEmail';
  static const traineeProfile_orgWith2Rejects = 'traineeProfile.orgWith2Rejects';
  static const traineeProfile_sendRequestModal_cancel = 'traineeProfile.sendRequestModal.cancel';
  static const traineeProfile_sendRequestModal_yesBecome =
      'traineeProfile.sendRequestModal.yesBecome';
  static const traineeProfile_sendRequestModal_areYouSureQuestion =
      'traineeProfile.sendRequestModal.areYouSureQuestion';
  static const traineeProfile_sendRequestModal_successToastTitle =
      'traineeProfile.sendRequestModal.successToastTitle';
  static const traineeProfile_sendRequestModal_successToastMsg =
      'traineeProfile.sendRequestModal.successToastMsg';
  static const traineeProfile_sendRequestModal_errorToastTitle =
      'traineeProfile.sendRequestModal.errorToastTitle';
  static const traineeProfile_sendRequestModal_errorToastMsg =
      'traineeProfile.sendRequestModal.errorToastMsg';
  static const traineeProfile_sendRequestModal = 'traineeProfile.sendRequestModal';
  static const traineeProfile_rejectionError_title = 'traineeProfile.rejectionError.title';
  static const traineeProfile_rejectionError_oneAttemptLeft =
      'traineeProfile.rejectionError.oneAttemptLeft';
  static const traineeProfile_rejectionError = 'traineeProfile.rejectionError';
  static const traineeProfile = 'traineeProfile';
  static const notFoundPage_error404 = 'notFoundPage.error404';
  static const notFoundPage_pageNotFound = 'notFoundPage.pageNotFound';
  static const notFoundPage_errorMessage = 'notFoundPage.errorMessage';
  static const notFoundPage_homePage = 'notFoundPage.homePage';
  static const notFoundPage = 'notFoundPage';
  static const somethingWentWrongPage_undefinedError = 'somethingWentWrongPage.undefinedError';
  static const somethingWentWrongPage_somethingWentWrong =
      'somethingWentWrongPage.somethingWentWrong';
  static const somethingWentWrongPage_errorMessage = 'somethingWentWrongPage.errorMessage';
  static const somethingWentWrongPage_refreshPage = 'somethingWentWrongPage.refreshPage';
  static const somethingWentWrongPage = 'somethingWentWrongPage';
  static const selectYourFirstRole_title = 'selectYourFirstRole.title';
  static const selectYourFirstRole_traineeDescription = 'selectYourFirstRole.traineeDescription';
  static const selectYourFirstRole_employerDescription = 'selectYourFirstRole.employerDescription';
  static const selectYourFirstRole_trainingProviderDescription =
      'selectYourFirstRole.trainingProviderDescription';
  static const selectYourFirstRole_traineeTitle = 'selectYourFirstRole.traineeTitle';
  static const selectYourFirstRole_employerTitle = 'selectYourFirstRole.employerTitle';
  static const selectYourFirstRole_trainingProviderTitle =
      'selectYourFirstRole.trainingProviderTitle';
  static const selectYourFirstRole_continue = 'selectYourFirstRole.continue';
  static const selectYourFirstRole = 'selectYourFirstRole';
  static const employeePage_title = 'employeePage.title';
  static const employeePage_associatedTrainee = 'employeePage.associatedTrainee';
  static const employeePage_requests = 'employeePage.requests';
  static const employeePage_nationalId = 'employeePage.nationalId';
  static const employeePage_mobileNumber = 'employeePage.mobileNumber';
  static const employeePage_birthDate = 'employeePage.birthDate';
  static const employeePage_tableTitle = 'employeePage.tableTitle';
  static const employeePage_tableResults = 'employeePage.tableResults';
  static const employeePage_selectedCount = 'employeePage.selectedCount';
  static const employeePage_rejectApplicationsTitle = 'employeePage.rejectApplicationsTitle';
  static const employeePage_approveApplicationsTitle = 'employeePage.approveApplicationsTitle';
  static const employeePage_approveButton = 'employeePage.approveButton';
  static const employeePage_rejectButton = 'employeePage.rejectButton';
  static const employeePage_rejectLabel = 'employeePage.rejectLabel';
  static const employeePage_approveLabel = 'employeePage.approveLabel';
  static const employeePage = 'employeePage';
  static const submit = 'submit';
  static const next_lesson = 'next_lesson';
  static const qualificationTestStartPage_postTestTitle =
      'qualificationTestStartPage.postTestTitle';
  static const qualificationTestStartPage_preTestTitle = 'qualificationTestStartPage.preTestTitle';
  static const qualificationTestStartPage_testIsMandatory =
      'qualificationTestStartPage.testIsMandatory';
  static const qualificationTestStartPage_testIsNotMandatory =
      'qualificationTestStartPage.testIsNotMandatory';
  static const qualificationTestStartPage_start = 'qualificationTestStartPage.start';
  static const qualificationTestStartPage_postTestHint = 'qualificationTestStartPage.postTestHint';
  static const qualificationTestStartPage_preTestHint = 'qualificationTestStartPage.preTestHint';
  static const qualificationTestStartPage_postTestDescription =
      'qualificationTestStartPage.postTestDescription';
  static const qualificationTestStartPage_preTestDescription =
      'qualificationTestStartPage.preTestDescription';
  static const qualificationTestStartPage = 'qualificationTestStartPage';
}
