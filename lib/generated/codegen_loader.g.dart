// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader {
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String, dynamic> _en = {
    "cancelled": "Cancelled",
    "meeting_id": "Meeting ID: {}",
    "meeting_password": "Password: {}",
    "join_meeting": "Join Meeting",
    "meeting_cancelled": "Meeting Cancelled",
    "meeting_moved": "Meeting Moved",
    "moved_date": "Moved to {}",
    "at_time_gmt3": "at {} GMT+3",
    "actions": "Actions",
    "seats": {
      "unlimited": "No limit seats available",
      "total": "{} total participants",
      "available": "{} out of {} seats available"
    },
    "freq": {
      "daily": "every day",
      "days": {
        "one": "Every 1 day",
        "two": "Every 2 days",
        "mid": "Every 3-10 days",
        "long": "Every {} days"
      },
      "months": {
        "one": "Every 1 month",
        "two": "Every 2 months",
        "mid": "Every 3-10 months",
        "long": "Every {} months"
      },
      "monthly": "every month",
      "weekend": "weekend",
      "weekdays": "weekdays",
      "days_of_week": {
        "friday": "Friday",
        "saturday": "Saturday",
        "sunday": "Sunday",
        "monday": "Monday",
        "tuesday": "Tuesday",
        "wednesday": "Wednesday",
        "thursday": "Thursday"
      }
    },
    "GENERAL": "General Skills",
    "BASIC": "Basic Skills",
    "ADVANCED": "Advanced Skills",
    "skills_level": "Skills Level",
    "addArticle": "Add Article",
    "article": "Article",
    "and": "and",
    "max6TPSelection": "Maximum 6 training providers",
    "max6LocationSelection": "Maximum 6 cities",
    "max6SectorsSelection": "Maximum 6 sectors",
    "view": "View",
    "test": "Test",
    "type": "Type",
    "self_paced": "Self-paced",
    "in_person": "In-person",
    "online_in_person": "Online/In-person",
    "self_paced_description":
        "Self-paced training is designed for individuals seeking flexibility in their learning journey.",
    "online_training_description":
        "The online study stream creates a virtual space for real-time, focused study sessions via online platforms.",
    "in_person_training_description":
        "The in-person study stream brings participants together in shared spaces for structured sessions.",
    "learning_track_description":
        "These programs are designed to take learners through a series of interconnected trainings, building essential skills and knowledge.",
    "study_stream": "Study Stream",
    "start": "Start",
    "explore": "Explore",
    "complete_lesson": "Complete",
    "instructorLed": "Instructor-Led",
    "selfPaced": "Self Paced",
    "yesConfirm": "Yes, Confirm",
    "areYouSureToLogout": "Are you sure you want to log out of the skills portal?",
    "filterBy": "Filter By:",
    "manageList": "Manage List",
    "rYouSureToEnrollToStream": "Are You Sure You Want to Enroll in the Study Stream?",
    "previousStreamWillBeDisabled":
        "The previous study stream will be disabled and you will not be able to continue it",
    "yesEnroll": "Yes, Enroll",
    "upcomingStudyStreams": "Upcoming Study Streams",
    "seatingCapacity": "Seating Capacity",
    "start_end_date": "Start/End Date",
    "choosePhotoFromTheLibrary": "Choose Photo from the Library",
    "removeCurrentPhoto": "Remove Current Photo",
    "sureToDeleteThePhoto": "Are you sure you want to delete the photo?",
    "cropPost": "Crop & Post ",
    "cropImage": "Crop Image",
    "maximumFileSize2mb": "Maximum File Size: 2MB",
    "unsupportedFileFormat": "Unsupported file format",
    "inProgress": "In Progress ({})",
    "viewTrainingDetails": "View Training Details",
    "favorites": "Favorites",
    "addContent": "Add Content",
    "addContentSuggestion":
        "<1>Click <2>\"Add Content\"</2> button to start filling in training section</1>",
    "addFile": "Add File",
    "skills_portal": "Skills Portal",
    "home_card_body":
        "The Skills portal represents a cornerstone in the journey of comprehensive development. It opens up broad horizons for acquiring vital skills and fostering capabilities. The portal reinforces a national vision based on stimulating innovation and personal excellence through a diverse range of initiatives and programs.",
    "scrollDown": "Scroll down",
    "recentInProgress": "Recent In Progress ",
    "popularTrainings": "Popular Trainings",
    "enrollSuccessBody":
        "Welcome to the start of a new learning journey. Now you can see this training in My Learning section",
    "sectors": "Sectors",
    "noAvailableTrainings": "There are no available trainings yet. Sign in to start exploring.",
    "signInLater": "Sign in Later",
    "training_self_paced": "Training (Self-paced)",
    "training_instructor_led": "Training (Instructor-led)",
    "welcome_user": "Welcome, {}",
    "trainingsInProgress": {
      "zero": "0 training in progress",
      "one": "1 training in progress",
      "other": "{} trainings in progress"
    },
    "addHomework": "Add Homework",
    "addNewSection": "Add New Section",
    "totalResults": "total results",
    "totalResults1": "total results",
    "totalResults2": "total results",
    "totalResults2to11": "total results",
    "itemsSelected1": "item selected",
    "itemsSelected2": "items selected",
    "itemsSelected3to10": "items selected",
    "itemsSelected11": "items selected",
    "personal_info": "Personal Info",
    "account_settings": "Account Settings",
    "addQuiz": "Add Quiz",
    "by_sign_in_i_accept": "By Sign In I accept the",
    "terms_of_use": "terms of use",
    "welcome_to_nsp": "Welcome to\nThe Skills Portal",
    "unlock_new_opportunities": "Unlock New Opportunities",
    "get_started": "Get Started",
    "the_privacy_policy": "the privacy policy",
    "addSlides": "Add Slides",
    "addVideo": "Add Video",
    "allTrainings": "All Trainings",
    "archive": "Archive",
    "archiveTraining": "Archive Training",
    "deleteTraining": "Delete Training",
    "deleteTrainingPopupText": "After deleting, no one will be able to access the training",
    "deleteTrainingPopupTitle": "Are You Sure You Want to Delete the Training?",
    "cancel": "Cancel",
    "changesWontBeSaved": "Changes will not be saved",
    "create": "Create",
    "createNewTraining": "Create New Training",
    "createTraining": "Create Training",
    "createTrainingSuggestion":
        "<1>Click <2>\"Create Training\"</2> button to create new training</1>",
    "delete": "Delete",
    "yesDelete": "Yes, Delete",
    "deleteContentSection": "Are You Sure You Want to Delete the Content from Section?",
    "deleteSection": "Delete Section",
    "modalDeleteSectionTitle": "Are You Sure You Want to Delete the Training Section?",
    "description": "Description",
    "draft": "Draft",
    "duplicateSection": "Duplicate Section",
    "duplicateLesson": "Duplicate Lesson",
    "enterText": "Enter Text",
    "enterTextArea": "Enter Text",
    "editSectionTitle": "Edit Section Title",
    "sectionSettings": "Section settings",
    "editTraining": "Edit Training",
    "emptySectionSuggestion":
        "<1>Click <2>\"Add New Section\"</2> button to add new section to training structure</1>",
    "emptyLessonSectionSuggestion":
        "Select trainings from the dropdown to add them to the learning track",
    "enterTrainingTitle": "Enter training title",
    "enterTrainingTitleInArabic": "Enter Training Title in Arabic",
    "enterTrainingTitleInEnglish": "Enter Training Title in English",
    "exitTrainingBuilder": "Are You Sure You Want to Exit Training Builder?",
    "exitWithoutSaving": "Yes, Exit",
    "fieldIsEmpty": "The training title should not be empty",
    "fieldIsRequired": "This field is required",
    "expectationFieldIsEmpty": "The field should not be empty",
    "sectionFieldIsEmpty": "The section title should not be empty",
    "filters": "Filters",
    "hideSection": "Hide Section",
    "inputContainLatinAndArabicCharsWithSpace": "Numbers and special characters are not allowed",
    "logOut": "Log Out",
    "nonLoggedInPageBody":
        "You don't have a user profile yet. Sign in now to access exclusive features and personalized content.",
    "nonLoggedInPageTitle": "Start Your Experience!",
    "maxCharactersAmount": "Maximum length is {{amount}} chars",
    "newAccount": "Don’t have an account yet?",
    "newest": "Newest",
    "noSectionYet": "There is no section yet",
    "noTrainings": "There are no trainings yet",
    "password": "Password",
    "previewButton": "Preview",
    "published": "Published",
    "or": "or",
    "email_mobile_or_id": "Email, Mobile Number or ID",
    "enter_email_mobile_or_id": "Enter Email, Mobile Number or ID",
    "word_new": "New",
    "pendingApproval": "Pending Approval",
    "rejected": "Rejected",
    "approved": "Approved",
    "publishWarning": "To publish please save",
    "qiwaSignIn": "Sign in with Qiwa SSO",
    "search": "Search",
    "search_results": "Search Results",
    "expand_search": "Please try to expand the search criteria.",
    "search_for_training": "Search for Training",
    "search_for_ltrack": "Search for Learning Track",
    "view_all": "View All",
    "searchResultCount": {"other": "{} total results for {}"},
    "section": "Section:",
    "settingsButton": "Settings",
    "signInIntoNationalSkillsPlatform": "Sign in into National Skills Platform",
    "signUp": "Sign Up",
    "signUpHere": "Sign up here",
    "success": "Success",
    "testsButton": "Tests",
    "text": "Text",
    "thereIsNoContentYet": "There is no content yet",
    "trainingDescription": "Training Description",
    "testDescription": "Test Description",
    "trainingStructure": "Training Structure",
    "trainingTitle": "Training Title",
    "trainingOverview": "Training Overview",
    "trainingTitleInArabic": "Training Title in Arabic",
    "trainingTitleInEnglish": "Training Title in English",
    "createNewSection": "Create New Section",
    "edit": "Edit",
    "location": "Location",
    "editButton": "Add Content",
    "editSectionName": "Edit Section Name",
    "save": "Save",
    "yesSave": "Yes, Save",
    "showMore": "Show More",
    "showLess": "Show Less",
    "sectionName": "Section Name",
    "sectionTitle": "Section Title",
    "title": "Title",
    "articleFieldIsEmpty": "The article title should not be empty",
    "shareKnowledge": "Your knowledge is a valuable resource – just share it!",
    "articleTextIsEmpty": "The article text should not be empty",
    "enterArticleTitle": "Enter article title",
    "trainingIsArchived": "The training is archived",
    "trainingIsDeleted": "The training is deleted",
    "dragAndDropFile": "Drag and drop file here",
    "fileExtensions":
        "PDF, PPT, PPTX, DOC, DOCX, XLS, XLSX, TXT, HTM, HTML, JPG, JPEG, GIF, PNG, TIF, TIFF, MP3, M4A, WAW",
    "selectFile": "Select File",
    "file": "File",
    "fileTitlePlaceholder": "Enter File title",
    "wrongFormat": "Please enter valid format",
    "maxFileSize": "File max size is {}",
    "fileTitleIsEmpty": "The file title should not be empty",
    "video": "Video",
    "addMoreItems": "Add More Items",
    "max160Characters": "Max 160 characters",
    "characters": "characters",
    "whatCanBeLearnedInTheTraining": "What Can Be Learned in the Training?",
    "atLeast2Items": "At least 2 items",
    "descriptionInputPlaceholder": "Enter Text",
    "progressValidation": "Please wait until file is uploaded",
    "slide": "Slides",
    "slidesText": "Add Slides to your Training.",
    "slidesSubText": "Important guidelines: 10 MB; PDF format.",
    "enterValidResource": "Please enter valid resource",
    "noEmptyResource": "The lesson resources should not be empty",
    "youWontBeAbleToRestoreIt": "You will not be able to restore it",
    "duration": "Duration",
    "durationValue": "{{from}}h - {{to}}h",
    "timeLimit": "Time limit:",
    "noLimit": "no limit",
    "deleteLessonConfirmation": "Are you sure you want to delete the content from section?",
    "requirements": "Requirements",
    "results": "{} Results",
    "mostPopular": "Most Popular",
    "recentlyAdded": "Recently Added",
    "clearAll": "Clear All",
    "home": "Home",
    "trainings": "Trainings",
    "done": "Done",
    "reviewsCount": "{} Reviews",
    "descriptionImage": {
      "title": "Training Image",
      "guidelines": "Important guidelines: JPG, JPEG, GIF, PNG, TIF or TIFF; no text on the image.",
      "text": "Add an image to your training."
    },
    "descriptionVideo": {
      "title": "Training video",
      "text": "Add a video to your training",
      "guidelines":
          "Important guidelines: 100 MB; MP4, MOV and AVI formats; no longer than 15 minutes"
    },
    "selectLanguage": "Select language",
    "selectLevel": "Select level",
    "level": "Level",
    "fieldValidation": "The field cannot be empty",
    "emailValidation": "Wrong mail format",
    "back": "Back",
    "training": "Training",
    "selectTraining": "Select Training",
    "selectTrainingFromTheList": "Select training from the list first",
    "addToTrack": "Add to Track",
    "languageMismatching":
        "You have added trainings that do not match the language selected in the learning track description. To continue, please either change the language of the learning track or delete unsuitable trainings",
    "logout_fail_title": "Logout Fails",
    "error": "Error",
    "error_body":
        "Please try again later. If the error persists, please contact support. The error number is {}. Kindly include this number in your support request.",
    "language": "Language",
    "languages": {
      "EN": "الإنجليزية",
      "AR": "العربية",
      "arabic_en": "Arabic",
      "arabic_ar": "العربية",
      "english_en": "English",
      "english_ar": "الإنجليزية"
    },
    "filterModal": {
      "english": "English",
      "arabic": "Arabic",
      "HighestRated": "Highest Rated",
      "Sort": "Sort",
      "Filter": "Filter",
      "Show": "Show",
      "Reset": "Reset"
    },
    "levels": {
      "BEGINNER": "Beginner",
      "INTERMEDIATE": "Intermediate",
      "ADVANCED": "Advanced",
      "ALL": "All Levels"
    },
    "durations": {"DAYS": "Days", "WEEKS": "Weeks", "MONTHS": "Months", "ALL": "All"},
    "lessons": {
      "slide": {
        "description": "Add Slides to your training. Important guidelines: 10 MB; PDF format.",
        "title": "Slides",
        "inputPlaceholder": "Enter slides title",
        "emptyTitleValidationMessage": "Slides title should not be empty"
      },
      "video": {
        "description":
            "Add a Video to your training. Important guidelines: 100 MB; MP4, MOV and AVI formats; no longer than 15 minutes.",
        "title": "Video",
        "inputPlaceholder": "Enter video title",
        "emptyTitleValidationMessage": "Video title should not be empty"
      }
    },
    "header": {
      "platformTitle": "National Skills Platform",
      "profileSettings": "General User Settings",
      "findTraining": "Find training",
      "languagePlaceholder": "العربية",
      "logOut": "Log Out",
      "students": "Students",
      "trainings": "Trainings",
      "signIn": "Sign In",
      "myTrainings": "My Trainings",
      "noTrainingsFound": "No trainings found",
      "trainingProvidersList": "Training Providers",
      "traineeProfile": "Switch to Trainee Profile",
      "employerProfile": "Switch to Employer",
      "trainingProviderProfile": "Switch to Training Provider",
      "ministryEmployeeProfile": "Switch to Ministry Employee",
      "traineeProfileActive": "Trainee Profile",
      "employerProfileActive": "Employer Profile",
      "trainingProviderProfileActive": "Training Provider Profile",
      "ministryEmployeeProfileActive": "Ministry Employee Profile",
      "becomeEmployer": "Become Employer",
      "becomeTrainingProvider": "Become Training Provider",
      "trainingProviders": "Training Providers",
      "employers": "Employers",
      "applicationRequests": "Application Requests",
      "employer": "Employer",
      "trainee": "Trainee",
      "ministryEmployee": "Ministry Employee",
      "trainingProvider": "Training Provider",
      "learningTracks": "Learning Tracks",
      "learningTrack": "Learning Track",
      "employerPrograms": "Trainings",
      "management": "Management",
      "dashboard": "Dashboard",
      "employees": "Employees",
      "employerLearningTracks": "Learning Tracks"
    },
    "landingPage": {
      "gainSkills":
          "Gain skills and certificates with our globally and nationally recognized diverse courses",
      "exploreTrainings": "Explore Trainings",
      "discoverLearning": "Discover skills",
      "withoutBoundaries": "Without Boundaries",
      "activeUsers": "Active \n Users",
      "TrainingProviders": "Training \n Providers",
      "trainingPrograms": "Training \n Programs",
      "weAreNationalSkillsPlatform": "National skills platform",
      "youFutureAtYourFingertips":
          "Your future at your fingertips: explore the world of skills through our unique platform, we are committed to providing you with the tools and support you to achieve your development goals.",
      "exploreRecentlyAddedTrainings": "Explore Recently Added Trainings",
      "exploreAllTrainingPrograms": "Explore All Training Programs",
      "skillsPassport": "Skills Passport",
      "customizedLearningPathways": "Customized learning pathways",
      "learnAnytimeAnywhere": "Learn anytime, anywhere",
      "skillsPassportText":
          "Showcasing trainees skills, gaps and recommendations, that highlight the expertise and interests to employers.",
      "customizedLearningPathwaysText":
          "Customized learning pathways provide a structured and goal-oriented approach, enhancing comprehension and skill development efficiently through upskilling, reskilling and cross skilling.",
      "learnAnytimeAnywhereText":
          "Flexibility as it not only accommodates busy schedules but also ensures that learning can adopt to various environments, prompting lifelong learning."
    },
    "completedLessonsCount": "{}/{} Lessons",
    "trainingDetails": {
      "promoVideo": "Promo Video",
      "trainingDescription": "Description",
      "lTrackEnrollmentModalTitle": "You’ve Just Enrolled in the Learning Track!",
      "trainingEnrollmentModalTitle": "You’ve Just Enrolled in the Training!",
      "whatYouWillLearn": "What You’ll Learn",
      "skillsYouWillGain": "Skills You’ll Gain",
      "requirements": "Requirements",
      "trainingStructure": "Training Structure",
      "apply": "Apply",
      "studentsHaveAlreadyEnrolled": "{} students have already enrolled",
      "students": "students",
      "beenAlreadyEnrolled": "have already enrolled",
      "createdBy": "Created by",
      "sectionIndex": "Section {}",
      "lessonsCount": {"zero": "0 Lesson", "one": "1 Lesson", "other": "{} Lessons"},
      "averageRate": "Average Rate",
      "estimatedTime": "Estimated Time",
      "estimatedTimeValue": "{} Hours",
      "trainingLanguage": "Training Language",
      "online": "Online",
      "trainingLevel": "Training Level",
      "skillsTitle": "What Skills Can Be Gained?",
      "skillsSubtitle": "Description. At least 2 items",
      "noSkillsFound": "No skills found",
      "findSkill": "Find skill",
      "sectionsCount": {"zero": "0 Section", "one": "1 Section", "other": "{} Sections"},
      "newTopic": "Add new topic {}",
      "skillsValidation": "At least {} skill items should be added to this block",
      "goToTraining": "Go to Training",
      "registrationSuccessfullyFinished": "Great choice, {}!",
      "trainingList": "Trainings List",
      "design": "Design",
      "enroll": "Enroll",
      "self_based": "Self-Based"
    },
    "becomeTrainingProvider": {
      "becomeTrainingProviderTitle": "Become Training Provider",
      "trainingProviderApplication": "Training Provider Application",
      "trainingProviderDetails": "Training Provider Details",
      "filesNeeded": "Files needed:",
      "commercialRegistration": "Commercial registration",
      "accreditationCertificate": "Accreditation Certificate",
      "gosi": "GOSI",
      "vat": "VAT",
      "organizationName": "Organization Name",
      "commercialRegistrationEndDate": "Commercial Registration End Date",
      "commercialRegistrationEndDatePlaceholder": "DD/MM/YYYY",
      "commercialRegistrationPlaceholder": "Enter Commercial registration",
      "gosiPlaceholder": "Enter GOSI",
      "vatPlaceholder": "Enter VAT",
      "organizationNamePlaceholder": "Enter Organization Name",
      "becomeTrainingProviderFileExtensions": "PDF (MAX. 10MB)",
      "discardApplication": "Discard Application",
      "submit": "Submit",
      "discardTheApplication": "Are you sure you want to discard the application?",
      "yesDiscard": "Yes, Discard",
      "privacyPolicy": "Privacy Policy",
      "application": "Application",
      "reuseFilesFromApplication": "You can reuse files from this application for a new one.",
      "downloadDocumentsToReviewIt": "You can download documents to review it.",
      "documnetsWereRejected": "Rejection reason",
      "documnetsWereApproved": "Documents Were Approved",
      "toTrainingProviderProfile":
          "Congratulations! Now you can switch to Training provider Profile",
      "dontShowAgain": "Don't show again",
      "continueRegistrationAsTrainingProvider": "Continue Registration as Training Provider",
      "completeDocumentsUploadingToBecomeTrainingProvider":
          "Complete documents uploading to become Training Provider",
      "continue": "Continue",
      "skip": "Skip",
      "pending": "Status: Pending",
      "rejected": "Status: Rejected",
      "completed": "Status: Completed",
      "resubmitted": "Resubmitted",
      "errorMessage": "The date format is incorrect",
      "generalBackendError": "System error. Please try again later.",
      "maxFileLimit": "Maximum number of files is 5 files",
      "submissionDate": "Submission Date",
      "approvalDate": "Approval Date",
      "rejectionDate": "Rejection Date",
      "id": "ID",
      "applicationSuccessfullySubmitted":
          "The request has been successfully submitted. Currently, you can explore the system as a Trainee. Upon approval, you will get access to the tools. We promptly notify you upon approval from the Ministry"
    },
    "trainingsList": {
      "noTrainingsFound": "No Results Found",
      "noTrainingsFoundDescription": "No results were found that match the specified filters",
      "skillsValidation": "At least 2 items"
    },
    "trainingProgress": {"sectionsProgress": "{} of {} Sections", "progressPercentage": "{}% "},
    "footer": {
      "allRightsReserved": "© All Rights Reserved For National Skills Platform 2023",
      "overview": "Overview",
      "aboutTheInitiatives": "About the Initiatives",
      "howToUse": "How to Use",
      "registration": "Registration",
      "TermsOfUse": "Terms of Use",
      "contactUs": "Contact us",
      "registerAsTrainee": "Register as a Trainee",
      "faqs": "FAQs"
    },
    "trainingView": {
      "pagesCount": "Page {} of {}",
      "next": "Next",
      "previous": "Previous",
      "lessonsProgress": "{{completed}} of {{total}} Lessons",
      "trainingContent": "Training Content",
      "mandatoryTest": "Test is mandatory",
      "numberOfQuestionsInTest": "Questions:",
      "questionOutOf": "Question {} of {}",
      "minScoreOfTest": "Minimum score:",
      "startTestLabel": "Start Test",
      "preQualificationTest": "Pre-training Qualification Test",
      "postQualificationTest": "Post-training Evaluation Test",
      "postEvaluationTest": "Post-training Evaluation Test"
    },
    "conductTest": {"True": "True", "False": "False"},
    "userLearnings": {
      "continue": "Continue",
      "overview": "Overview",
      "decline": "Decline",
      "apply": "Apply",
      "rateTraining": "Rate Training",
      "in_progress": "In Progress",
      "viewCertificate": "View Certificate",
      "assignedOn": "Assigned on {}",
      "completedOn": "Completed on {}",
      "yourRating": "Your Rating",
      "trainingsList": "Trainings List",
      "current": "Current",
      "nominated": "Nominated",
      "completed": "Completed",
      "beginOnlineLearningExperience": "Begin Your Online Learning Experience",
      "emptyCurrentListText": "There are no current trainings yet. Explore our trainings!",
      "emptyNominatedListHeader": "There Are No Nominated Trainings Yet",
      "emptyNominatedListText": "They will appear after your Employer nominates trainings for you",
      "emptyCompletedListText": "There are no completed trainings yet. Explore our trainings!",
      "exploreTrainings": "Explore Trainings",
      "haveToCompletePretrainingTest": "You have to complete Pre-training Qualification Test"
    },
    "bottom_nav_bar": {
      "home": "Home",
      "catalog": "Catalog",
      "myLearnings": "My Learning",
      "profile": "Profile"
    },
    "trainingBuilder": {
      "publish": "Publish",
      "saveAsDraft": "Save as Draft",
      "validationErrorMessage": "Not all required fields are filled in",
      "trainingIsPublished": "Training is published",
      "allChangesAreSaved": "All Changes are Saved",
      "requiredFieldsErrorMessage": "All required fields should be filled in",
      "emptySectionsMessage": "You must add content in the lessons to publish",
      "tests": {
        "label": "Tests",
        "preTrainingQualificationLabel": "Pre-training Qualification Test",
        "postTrainingEvaluationLabel": "Post-training Evaluation Test",
        "settings": "Settings",
        "minScore": "Minimum score to pass (percentage)",
        "minScorePlaceholder": "Enter Minimum Score",
        "minScoreTooltip":
            "The \"Minimum score to pass\" denotes the threshold percentage a test taker must achieve to successfully pass the test. This criterion ensures that candidates attain a specified level of proficiency or knowledge deemed necessary for completion. Please input a numeric value within the range of 1 to 100 without any spaces or special characters. This value represents the minimum percentage a test taker must attain to pass the test successfully.",
        "title": "Test Title",
        "titlePlaceholder": "Enter Test Title",
        "description": "Test Description",
        "descriptionPlaceholder": "Enter Test Description",
        "cancel": "Cancel",
        "save": "Save Test",
        "mandatory": "Mandatory",
        "mandatoryTooltip":
            "Mandatory Pre-training Qualification test implies that without successfully passing this test, individuals may not be permitted to enroll or participate in the training.",
        "preHidden": "Hide Pre-Qualification Training Test",
        "postHidden": "Hide Post-training Evaluation Test",
        "randomize": "Randomize questions and answers",
        "randomizeTooltip":
            "The system shuffles and reorders both the questions and their respective answer choices. It ensures that the sequence of questions and options presented to each test taker varies, preventing a consistent or predictable order in subsequent attempts of the same test.",
        "deletePre": "Delete Pre-training Qualification Test",
        "deletePost": "Delete Post-training Evaluation Test",
        "cancelDelete": "Cancel",
        "confirmDelete": "Yes, Delete",
        "deleteDescriptionPre":
            "Are You Sure You Want to Delete the Pre-training Qualification Test?",
        "deleteDescriptionPost":
            "Are You Sure You Want to Delete the Post-training Evaluation Test?",
        "deleteDescription": "You will not be able to restore it",
        "questions": "Questions",
        "newQuestion": "New Question",
        "questionCount_one": "{{count}} question",
        "questionCount_other": "{{count}} questions",
        "hiddenLabel": "Hidden",
        "draftLabel": "Draft",
        "editTest": "Edit Test",
        "duplicateTest": "Duplicate Test",
        "deleteTest": "Delete Test",
        "deleteQuestion": "Delete Question",
        "duplicateQuestion": "Duplicate Question",
        "question": "Question",
        "answers": "Answers",
        "addAnswer": "Add Answer",
        "questionType": "Question Type",
        "correctAnswer": "Correct answer",
        "incorrectAnswer": "Incorrect answer",
        "questionDescription": {
          "choose_singular_answer": "Choose singular correct answer",
          "RADIO": "Choose singular correct answer by clicking at radio button",
          "TRUEFALSE": "Choose singular correct answer by clicking True or False"
        },
        "questionTypes": {"RADIO": "Radio (Singular Choice)", "TRUEFALSE": "True/False question"},
        "minOneQuestion": "At least one question must be added",
        "minTwoAnswers": "At least two answers should be provided",
        "hasCorrectAnswer": "Select correct answer",
        "confirmExit": "Are You Sure You Want to Exit to a Different Section?"
      }
    },
    "trainingProviderDetailedPage": {"generalInformation": "General Information"},
    "trainingProviders": {
      "label": "Training Providers",
      "trainings": "Trainings",
      "employers": "Employers",
      "trainees": "Trainees",
      "searchPlaceholder": "Search by Name or Email",
      "pendingStatus": "Pending",
      "rejectedStatus": "Rejected",
      "approvedStatus": "Approved",
      "tableTitle": "List of Training Providers",
      "rejectLabel": "Reject",
      "approveLabel": "Approve",
      "noData": "No Data",
      "noDataDescription": "There is no requests yet",
      "trainingProviderName": "Training Provider Name",
      "status": "Status",
      "organizationalId": "Organizational ID",
      "requestDate": "Request Date",
      "viewDetails": "View Details",
      "id": "ID"
    },
    "trainingProviderDetailed": {
      "label": "Training Provider Details",
      "generalInformation": "General Information",
      "name": "Name",
      "email": "Email",
      "mobileNumber": "Mobile Number",
      "organizationId": "Organization ID",
      "accreditationRequest": "Accreditation Request",
      "youCanDownloadDocuments": "You can download documents to review",
      "organizationName": "Organization Name",
      "gosi": "GOSI",
      "commercialRegistration": "Commercial Registration",
      "vat": "VAT",
      "rejected": "Rejected",
      "rejectedReason": "Rejected Reason",
      "trainingProvidersList": "Training Providers",
      "trainingProviderDetails": "Training Provider Details",
      "id": "ID"
    },
    "rejectTrainingProvider": {
      "rejectionReason": "Rejection Reason",
      "rejectionReasonPlaceholder": "Enter Text",
      "reject": "Reject",
      "rejectionReasonRequired": "Rejection Reason is required"
    },
    "modalTitle": "Set password",
    "newPasswordLabel": "New Password",
    "passwordConfirmLabel": "Confirm New Password",
    "enter_password": "Enter Password",
    "passwordFieldRequired": "The field cannot be empty",
    "passwordMustMatch": "Passwords must match",
    "passwordValidationMessage": "Password must answer the rules",
    "profileAndSettings": {
      "title": "User Profile & Settings",
      "generalInfo": "General Info",
      "trainingProvider": "Training Provider Applications",
      "profileInfo": "Profile Info",
      "edit": "Edit",
      "changePassword": "Change Password",
      "changePasswordDescription": "You can set password to Sign in using additional credentials.",
      "setPassword": "Set password",
      "enterCode": "Enter Code",
      "codeModalDescription": "Your code has been sent via SMS to your mobile number.",
      "submit": "Submit",
      "resend": "Resend Code",
      "notRevivedCode": "Don’t receive the code?",
      "toastTitle": "Password has been successfully Set.",
      "toastMessage": "Use new password to sign in into National Skills Platform",
      "incorrectVerificationCode": "Entered code is incorrect",
      "resendCodeMessage":
          "You have entered an invalid code 4 times. Please, use the link below to resend the code",
      "name": "Name",
      "email": "Email",
      "phoneNumber": "Phone Number",
      "nationalId": "National ID",
      "birthDate": "Date of Birth",
      "tooltipText":
          "Password must meet the following rules: <1>at least 1 uppercase character (A-Z)</1> <1>at least 1 lowercase character (a-z)</1> <1>at least 1 digit (0-9)</1> <1>at least 1 special character (punctuation), space is a special character too</1> <1>at least 10 characters</1> <1>at most 128 characters</1> <1>no more than 2 identical characters in a row (e.g., 111 not allowed)</1>",
      "timeRemaining": "Time remaining: {}"
    },
    "textField": {"usedCharacters": "{{used}}/{{total}} characters"},
    "questions": {
      "singleChoice": {"label": "Choose singular correct answer"},
      "errors": {
        "selectOneAnswer": "Please choose the correct answer to move on to the next question"
      },
      "confirm": "Confirm",
      "goBack": "Back",
      "results": {
        "preCompleted": "You completed Pre-training Qualification Test",
        "postCompleted": "You completed Post-training Evaluation Test",
        "quizCompleted": "You completed Quiz",
        "yourScore": "Your Score",
        "score": "{}% out of 100%",
        "retakeTest": "Retake Test"
      }
    },
    "hint": "Hint",
    "employerApplication": {
      "employerApplicationTitle": "Employer Application",
      "applicationRequestTitle": "Employer Application Request",
      "workingEmail": "Working Email",
      "workingEmailPlaceholder": "Enter Working Email",
      "positionInCompany": "Position in the Company",
      "positionInCompanyPlaceholder": "Enter Position in the Company",
      "moi": "MOI",
      "moiPlaceholder": "Enter MOI",
      "referenceInfoTitle": "Reference Information",
      "referenceInfoTooltip":
          "Reference information is an individual who can confirm that the applicant genuinely represents the interests of the organisation.",
      "referenceName": "Reference Full Name",
      "referenceNamePlaceholder": "Enter Reference Full Name",
      "referenceEmail": "Reference Working Email",
      "referenceEmailPlaceholder": "Enter Reference Working Email",
      "referencePhone": "Reference Mobile Number",
      "optionalComment": "Optional Comment",
      "optionalCommentPlaceholder": "Enter Optional Comment",
      "discardApplication": "Discard Application",
      "submit": "Submit",
      "filesNeeded": "Files needed:",
      "commercialRegistration": "Commercial registration",
      "gosi": "GOSI",
      "vatOptional": "VAT (if exists)",
      "continue": "Continue",
      "skip": "Skip",
      "continueRegistrationLabel": "Continue Registration as Employer",
      "continueRegistrationText": "Complete documents uploading to become Employer",
      "discardButton": "Yes, Discard",
      "discardRegistrationLabel":
          "Are you sure you want to discard the employer application request?",
      "discardRegistrationText": "You will be able to get back later but files will be deleted",
      "requestId": "Request ID: {}",
      "submitDate": "Submit Date:",
      "rejectedReason": "Rejected Reason",
      "formRestriction":
          "Employers are already associated with the Organization {}. This form cannot be submitted. For further information, kindly reach out to your Organization.",
      "applicationSuccessfullySubmitted":
          "The request has been successfully submitted. Currently, you can explore the system as a Trainee. Upon approval, you will get access to the tools. We promptly notify you upon approval from the Ministry"
    },
    "employerApplicationsList": {
      "title": "Application Requests",
      "subTitle": "List of Employer Application Requests",
      "totalCount_one": "{{count}} result",
      "totalCount_other": "{{count}} results",
      "requestId": "Request ID",
      "username": "User Name",
      "status": "Status",
      "organizationName": "Organization Name",
      "commercialRegistration": "Commercial Registration",
      "date": "Date",
      "search": "Search",
      "applicationStatus": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"},
      "searchPopover":
          "Search by Request ID, User Name, Email, Organization Name or Commercial Registration",
      "clearAll": "Clear All",
      "apply": "Apply",
      "filterApplied": "Filter ({{count}})",
      "filters": "Filters",
      "dateTooltip":
          "The date depends on the status. There are 3 types of data: request date, approval date and rejection date."
    },
    "employerApplicationDetails": {
      "label": "Application Request Details",
      "requests": "Application Requests",
      "userName": "User Name",
      "nationalId": "National ID",
      "birthDate": "Date of Birth",
      "email": "Email",
      "mobileNumber": "Mobile Number",
      "rejectApplicationTitle": "Reject Application Request",
      "rejectApplicationSubTitle":
          "Describe the reason for rejecting the application. Please note that the reason will be visible to the applicant."
    },
    "employerProfile": {
      "title": "Employer Profile",
      "userInfo": "User Info",
      "applicationRequestHistory": "Application Request History",
      "associatedEmployers": "Associated Employers"
    },
    "quiz": "Quiz",
    "quizQuestionType": {
      "RADIO": "Radio (Singular Choice)",
      "TRUEFALSE": "True or False",
      "RANK": "Rank Order",
      "CHECKBOX": "Checkboxes (Multiple Choice)"
    },
    "privacyPolicy": {
      "privacyPolicyTitle": "Privacy Policy",
      "date": "Last updated on December 11, 2023",
      "generalInfo": "General Information",
      "generalInfoTextFirst":
          "When you use the National Skills Platform , you trust us with your information, so the intent of the privacy policy is to help you and protect data related to you by complying with all applicable data protection and privacy laws.",
      "generalInfoSubtitle": "Please read this privacy policy carefully before using this site",
      "generalInfoTextSecond":
          "National Skills Platform is a website (\"our site\") Managed by the Ministry of Human Resource sand Social Development (\"we\"). We are committed to protecting and respecting your privacy.This policy (Terms of Use for Ministry of human resources and social development) and any other documents referred in this policy, sets out the basis on which any personal information we collect from you, or that you provide to us, will be processed by us. Please read the following carefully to understand our views and practices regarding your personal information and how we will treat it. By visiting our site you are accepting and consenting to the practices described in this policy. Information we may collect from you We may collect and process the following information about you:",
      "infoToUsTitle": "Information you provide to us",
      "infoToUsText":
          "You may give us information about you by entering or filling out the forms specified on our site or in other ways. This includes information you provide when you register to use our site, participate in discussion forums or submit your user postings on our site, or when you email us or send us a report about a problem you are having with our site. The information you provide to us may include personal information such as your name, address, email address, phone number, financial and credit card information, personal description, and photograph. It may also include comments or other information posted by you on our site that are designed for public communication or communication among other users and which can be viewed and downloaded by other parties who visit our site .",
      "infoToCollectTitle": "Information we collect about you",
      "infoToCollectText":
          "<2>With regard to each of your visits to our site we may automatically collect the following information: </2> <3> <1>Technical information, including the Internet Protocol (IP) address used to connect your computer to the Internet, your login information, browser type and version, time zone setting, browser add-on types and versions, and Internet operating system and platform;</1><1>Information about your visit ,including the full Uniform Resource Locators (URL)clickstream ,through our site (including date and time); Information you viewed or searched for ;response times on search pages; download errors; The length of visits to certain pages, information about interaction with search pages (such as scrolls, clicks, and mouseovers), methods used to browse away from the page ,and any phone number used to call the customer service number .</1> </3>",
      "infoWeReceiveTitle": "Information we receive from other sources",
      "infoWeReceiveText":
          "We may receive information about you if you use any of the other websites we operate or take advantage of other services we provide .We also work closely with third parties (including, for example, business partners, technical, payment and delivery subcontractors, advertising networks ,analytics providers, search information providers and credit reference agencies) from whom we may obtain Information about you from them.",
      "privacyPolicyDetails": "Privacy Policy Details",
      "cookiesTitle": "Cookies",
      "cookiesText":
          "Our site uses cookies to distinguish you from other users of our site . This helps us to provide you with a good experience when you browse our site and also allows us to improve our site. By continuing to browse our website, you agree to our use of cookies. A cookie is a small file consisting of letters and numbers that we store in your browser or the hard drive of your computer if you agree. Cookies contain information that is transferred to your computer's hard drive .",
      "cookiesThatWeUsedTitle": "We use the following cookies:",
      "cookiesThatWeUsedText":
          "<2>Cookies that should be used when strictly necessary: These are the cookies that are required to operate our website. They include, for example, cookies that enable you to log in to secure areas of our website and benefit from online payment services. </2> <3> <1>Cookies that are used for analytical/performance purposes. They allow us to recognise and count the number of visitors and see how visitors move around our site while using it. This helps us to improve the way our site works, for example, by ensuring that users find what they are looking for easily .</1><1>Cookies that are used for functional purposes .These cookies are used to recognise you when you return to our site. This enables us to personalise the content we provide to you, to greet you by name and to remember your transaction preferences (for example, your chosen language or region).</1><1>Cookies that are used for targeting purposes : These files are used to record the events of your visit to our website, the pages you have browsed and the links you have followed. We will use this information to make our site and the advertisements displayed on it more relevant and in your interests. We may also share this information with third parties for this purpose.</1> </3> <2> Please note that third parties (including, for example ,advertising networks and providers of external services such as web traffic analysis services) may also use cookies, over which we have no control. The cookies used by these parties are likely to be of the type used for analytical, performance or targeting purposes as described above. </2>  <2>Of your own cookies by activating the settings on your browser that allow you to reject or select all or some cookies. However, if you use your browser settings to prevent the use of all cookies(including essential cookies), you may not be able to access all or some parts of our site. Except for essential cookies, all cookies expire after one hour.</2>",
      "waysOfInfoUsageTitle":
          "Uses made of the information We use information held about you in the following ways:",
      "waysOfInfoUsageText":
          "<2>Information you give to us or we collect about you. The information that you provide to us, or that we collect about you, will be used as follows : </2> <3> <1>To enable us, and our partners and third-party suppliers, to provide, manage, and improve the level of services.</1> <1>To enable us, and our partners and third-party suppliers, to raise the level of customer service performance ,whether on an individual level as a whole, or by individualising their experiencing evaluating the possibilities of accessing and using our website and the impact of that on establishments in the Kingdom of Saudi Arabia.</1> <1>For research and development purposes.</1> <1>Monitor and detect violations of the terms and conditions of our website, as well as other possible cases of misuse of our website.</1> <1>Dissemination of information collected about accessibility and use, and its impact on the performance of facilities.</1> <1>Providing you with the latest developments about services, products or other events provided by us, our partners, suppliers or third-party operators that may be of interest to you ,and which may be sent to you via emails about maintenance or updates that occur on the site.</1> <1>Archive this information and/or use it for the possibility of future communication with you, and for the possibility of third parties verifying your identity.</1> <1>Maintaining and raising the level of performance and security of our website, programs, systems and networks.</1> <1>For the purposes described elsewhere in this Privacy Policy (including, for example, exchanging information with third parties).</1> <1>In any manner otherwise described at any stage of information collection or based on your consent.</1> <1>To authenticate your identity when registering for some of the services provided on our website.</1> <1>To process refund procedures, in cases where this applies.</1> <1>To administer our site and carry out internal operations, including troubleshooting, data analysis, testing, research, statistical and comprehensive survey purposes.</1> <1> To improve our website to ensure that content is presented in the most effective way for you and for your computer.</1> <1>To allow you to participate in interactive features of our Service, if you so desire. As part of our efforts to maintain the security and integrity of our site.</1> <1>As part of our endeavor to ensure the safety and security of our website.</1> <1>To measure or understand the effectiveness of the advertisements we provide to you and others, and to provide advertisements that are relevant to you.</1> <1>To provide suggestions and recommendations to you and other users of our site about services that may interest you or concern them.</1></3>",
      "infoFromResourcesTitle": "Information we receive from other sources",
      "infoFromResourcesText":
          "We may combine this information with information you give to us and information we collect about you. We may us this information and the combined information for the purposes set out above (depending on the types of information we receive).",
      "disclosureInfoTitle": "Disclosure of your information",
      "disclosureInfoText":
          "<2>We may exchange, process and share any information and data that you provide to us with your full knowledge and/or that we collect about you with our partners, third-party suppliers, analytics providers, search engine service providers and advertisers, as follows : </2> <3><1>If we are required to disclose or exchange your personal information and data in the context of fulfilling any legal obligation, or in order to implement or apply (the terms and conditions of Takamul Business Services Products) and implement other agreements; Or to protect the rights, property, or maintain the safety of Takamul Business Services Company, the safety of our customers, or other parties.</1> <1>With service providers in various activities or contractors who perform certain functions on our behalf or on behalf of our partners, including processing information and data that you provide on our website, carrying out operations through a third party, and other operations carried out through our website, or that are related to the operation of our website. or any parts thereof, providing or managing the Services, or in connection with any other aspects of the Services we provide.</1> <1>For research and development purposes .However, we will exchange your personal information and data for this purpose in a manner that does not conflict with the provisions of applicable laws and regulations and within the limits of the personal information required to achieve the purposes stated at the time of its collection.</1> <1>Share information and data with third parties in order to provide services that may be of interest to you.</1> <1>To provide networking opportunities between you and other users who may have similar interests or goals. In such cases, we may use all information and data collected about you to identify parties that may be interested in communicating with you.</1> <1>To respond to subpoenas, court orders or other legal process. To investigate, prevent or take action regarding illegal activities, suspected fraudulent activities, security or technical issues, or to implement our terms and conditions (Takamul Business Services Products Terms and Conditions) or privacy policy, and whatever is required Otherwise, in accordance with the provisions of applicable laws and regulations, to protect our rights and property, or to preserve the safety and interests of others.</1> <1>With our affiliates, or with their successors in the event of a merger, acquisition or reorganization, for use in accordance with the Privacy Policy.</1> <1>As otherwise described to you at the point of collection or based on your consent;</1> <1>For integration with third-party services. For example, the ability to host video and other content on YouTube and other sites that are not under our control.</1></3>",
      "consentOfDataTitle": "Consent to processing data outside home country",
      "consentOfDataText":
          "<1>By using our website, or providing information to us, this constitutes your consent to the collection, use, disclosure and preservation of information and data in the Kingdom of Saudi Arabia and other countries and regions, for the purposes stipulated in the privacy policy in accordance with the terms and conditions (terms and conditions for Takamul Business Services products). You hereby consent to the collection, use ,disclosure and retention by us of your personal information and data as described in the Privacy Policy, including, without limitation, the sharing of your personal information between us and third parties, and with our affiliates and subsidiaries described in the Privacy Policy. Privacy. For the avoidance of doubt, any consent related to the right to exchange information referred to in this paragraph includes your consent to exchange your personal information with any jurisdiction that may provide a different level of privacy protection than the level available in your country. </1> We will take all reasonable steps to ensure that your information and data are treated securely and in accordance with the Privacy Policy. All information you provide to us is stored on our secure server computers. If we provide you (or if you choose) a password to enable you to access certain parts of our site, you will be responsible for maintaining the confidentiality of this password. We advise you not to share your password with anyone. Unfortunately, the process of transmitting information over the Internet is not completely secure. Although we will do our best to protect your personal information, we cannot guarantee the security of your information transmitted to our site. Therefore, any such transfer will be at your own risk. For our part, once we receive your information, we will implement strict procedures and security features in an attempt to prevent unauthorized access to that information. If you do not agree to these terms, we regret that you are not able to access, browse, or register to subscribe to our site. If you choose not to provide us with certain information that is required to provide you with the various services offered on our website, you will not be able to create a private user account, and therefore we will not be able to provide you with those services .",
      "yourRightsTitle": "Your rights",
      "yourRightsText":
          "You have the right to ask us not to use your personal and sensitive information and data for the purposes for which it was intended. \n Our site may, from time to time, contain links to and from the websites of our partner networks, advertisers and affiliates. If you follow a link to any of these websites, please note that these websites have their own privacy policies and that we do not accept any responsibility or liability for these policies. Please check these policies before you submit any personal information to these websites.",
      "changePrivacyPolicyTitle": "Changes to our privacy policy",
      "changePrivacyPolicyText":
          "<2>Any changes we may make to our privacy policy in the future will be posted on this page and, where appropriate, notified to you by e-mail. Please check back frequently to see any updates or changes to our privacy policy.</2> be should which ,policy privacy the regarding requests and comments ,questions all welcome We address following the to directed: <1><EMAIL></1>"
    },
    "learningTracks": {
      "sidebarTitle": "Learning Tracks",
      "title": "Learning Tracks",
      "createTitle": "Create Track",
      "allTracks": "All Learning Tracks",
      "tracksCount_zero": "0 Tracks",
      "tracksCount_one": "1 Track",
      "tracksCount_other": "{{count}} Tracks",
      "trainingsCount": {"zero": "0 Trainings", "one": "1 Training", "other": "{} Trainings"},
      "noTracksYet": "There are no Learning Tracks yet",
      "createTrackSuggestion":
          "<1>Click <2>\"Create Learning Track\"</2> button to create new learning track</1>",
      "editTrack": "Edit Track",
      "duplicateTrack": "Duplicate Track",
      "deleteTrack": "Delete Track",
      "areYouSureDelete": "Are You Sure You Want to Delete the Learning Track?",
      "builder": {
        "modalTitle": "Create Learning Track",
        "titlePlaceholder": "Enter Learning Track Title",
        "titleLabel": "Learning Track Title",
        "sectorLabel": "Sector",
        "sectorPlaceholder": "Select Sector",
        "domainLabel": "Domain",
        "domainPlaceholder": "Select Domain",
        "languageLabel": "Language",
        "languagePlaceholder": "Select Language",
        "submit": "Create Learning Track",
        "description": "Description",
        "structure": "Structure",
        "syllabus": "Syllabus",
        "descriptionSubtitle": "Learning Track Description",
        "structureSubtitle": "List of Trainings",
        "levelLabel": "Level",
        "levelPlaceholder": "Select Level",
        "descriptionLabel": "Learning Track Description",
        "descriptionPlaceholder": "Enter text",
        "nominationOnly": "For Nomination Only",
        "confirmLeave": "Are You Sure You Want to Exit Learning Track Builder?",
        "imageTitle": "Learning Track Image",
        "imageText": "Add an image to your learning track.",
        "min2trainings": "Minimum 2 trainings should be added",
        "max10trainings": "Maximum 10 trainings can be added",
        "submittedForReview": "The learning track is submitted for review"
      }
    },
    "traineeProfile": {
      "title": "Trainee Profile",
      "orgTab": "Organization",
      "orgNameColumnHeader": "Organization Name",
      "pendingMessage":
          "The organization's employer will review your request and you will be notified as soon as it is approved or rejected.",
      "sendRequest": "Send Request",
      "tableHeader": "List of Organizations",
      "tableHeaderDescription":
          "Select one of the organisations to become a part of an organisation.",
      "searchPlaceholder": "Search by Organization Name",
      "resultCount_one": "{{count}} result",
      "resultCount_other": "{{count}} results",
      "employerName": "Employer Name",
      "workEmail": "Work Email",
      "orgWith2Rejects":
          "You have already received 2 rejects and can no longer send a request to the organization.",
      "sendRequestModal": {
        "cancel": "Cancel",
        "yesBecome": "Yes, become",
        "areYouSureQuestion": "Are you sure you want to become part of \"{{orgName}}\"",
        "successToastTitle": "Your request has been successfully sent",
        "successToastMsg": "You will be notified as soon as it is approved or denied",
        "errorToastTitle": "You request has not been submitted",
        "errorToastMsg": "Please try again later"
      },
      "rejectionError": {
        "title": "\"{{orgName}}\" has rejected your request to join the organization",
        "oneAttemptLeft":
            "Contact your organization to clarify all the details or choose another organization from the list to join. You have 1 attempt left to join the organization."
      }
    },
    "notFoundPage": {
      "error404": "Error 404",
      "pageNotFound": "Whoops! Page not found.",
      "errorMessage":
          "Sorry, we can’t find the page you are looking for. Below are some useful links instead:",
      "homePage": "Home Page"
    },
    "somethingWentWrongPage": {
      "undefinedError": "Error Is Not Defined",
      "somethingWentWrong": "Whoops! Something went wrong.",
      "errorMessage": "We are working on fixing the problem. Refresh the page or try again later.",
      "refreshPage": "Refresh Page"
    },
    "selectYourFirstRole": {
      "title": "Select Role to Sign Up into National Skills Platform",
      "traineeDescription": "Engage in various activities to enhance your learning experience.",
      "employerDescription": "Optimize onboarding and skills development for your team.",
      "trainingProviderDescription":
          "Create a full learning experience with useful content and track your trainings.",
      "traineeTitle": "Sign In as Trainee",
      "employerTitle": "Sign In as Employer",
      "trainingProviderTitle": "Sign In as Training Provider",
      "continue": "Continue"
    },
    "employeePage": {
      "title": "Employees",
      "associatedTrainee": "Associated Trainee",
      "requests": "Requests",
      "nationalId": "National ID",
      "mobileNumber": "Mobile Number",
      "birthDate": "Date of Birth",
      "tableTitle": "List of Employee Requests",
      "tableResults": "{{count}} results",
      "selectedCount": "Selected ({{count}}):",
      "rejectApplicationsTitle": "Are you sure you want to reject {{count}} selected employee(s)?",
      "approveApplicationsTitle":
          "Are you sure you want to approve {{count}} selected employee(s)?",
      "approveButton": "Yes, Approve",
      "rejectButton": "Yes, Reject",
      "rejectLabel": "Reject",
      "approveLabel": "Approve"
    },
    "submit": "Submit",
    "next_lesson": "Next Lesson",
    "qualificationTestStartPage": {
      "postTestTitle": "Post-training Evaluation Test",
      "preTestTitle": "Pre-training Qualification Test",
      "testIsMandatory": "Test is mandatory",
      "testIsNotMandatory": "Test is not mandatory",
      "start": "Start",
      "postTestHint":
          "Please note, that once the Post-training Evaluation Test begins, it cannot be stopped mid-process. Ensure you're ready to proceed before initiating the test.",
      "preTestHint":
          "Please note, that once the Pre-training Qualification Test begins, it cannot be stopped mid-process. Ensure you're ready to proceed before initiating the test.",
      "postTestDescription":
          "The Post-training Evaluation Test (PTET) serves as a crucial evaluation tool designed to assess the readiness and aptitude of individuals after they undergo specialised training programs.",
      "preTestDescription":
          "The Pre-training Qualification Test (PTQT) serves as a crucial evaluation tool designed to assess the readiness and aptitude of individuals before they undergo specialised training programs."
    }
  };
  static const Map<String, dynamic> _ar = {
    "cancelled": "تم الغاء الاجتماع",
    "meeting_id": "رقم الاجتماع: {}",
    "meeting_password": "كلمة المرور: {}",
    "join_meeting": "انضم إلى الاجتماع",
    "meeting_cancelled": "تم الغاء الاجتماع",
    "meeting_moved": "تم تأجيل الاجتماع",
    "moved_date": "{} تم تأجيل الاجتماع إلى",
    "at_time_gmt3": "الوقت: {time} GMT+3",
    "actions": "إجراءات",
    "next_lesson": "الدرس التالي",
    "location": "الموقع",
    "addArticle": "إضافة مقال",
    "article": "مقال",
    "max6TPSelection": "الحد الأقصى 6 اختيارات",
    "max6LocationSelection": "الحد الأقصى 6 مدن",
    "max6SectorsSelection": "الحد الأقصى 6 قطاعات",
    "addContent": "إضافة محتوى",
    "password": "كلمة المرور",
    "filterBy": "التصفية:",
    "yesConfirm": "نعم",
    "GENERAL": "المهارات العامة",
    "BASIC": "المهارات الأساسية",
    "ADVANCED": "المهارات المتقدمة",
    "skills_level": "مستوى المهارات",
    "self_paced": "ذاتي",
    "in_person": "حضوري",
    "online_in_person": "عن بعد/حضوري",
    "self_paced_description":
        "يتميز التدريب الذاتي بالمرونة التي تتيح للأفراد اختيار الوقت والمكان المناسبين لتعلمهم",
    "online_training_description":
        "توفر الدورات عن بعد جلسات افتراضية تسمح للمستخدم حضور الدورة من أي مكان.",
    "in_person_training_description":
        "توفر الدورات الحضورية فرصة للمشاركين للمشاركة في جلسات دراسية مكثفة.",
    "learning_track_description":
        "تم تصميم المسارات التدريبية بحيث تأخذ المشاركين عبر سلسلة من الدورات لبناء المهارات والتعلم.",
    "study_stream": "المسار التدريبي",
    "seats": {
      "unlimited": "لا توجد مقاعد متاحة",
      "total": "إجمالي عدد المشاركين {}",
      "available": "مقعد متاح {} من {}"
    },
    "freq": {
      "daily": "يومياً",
      "days": {"one": "كل يوم", "two": "كل يومين", "mid": "كل 3-10 أيام", "long": "كل {} أيام"},
      "months": {"one": "كل شهر", "two": "كل شهرين", "mid": "كل 3-10 أشهر", "long": "كل {} أشهر"},
      "monthly": "شهرياً",
      "weekend": "عطلة نهاية الأسبوع",
      "weekdays": "أيام الأسبوع",
      "days_of_week": {
        "friday": "الجمعة",
        "saturday": "السبت",
        "sunday": "الأحد",
        "monday": "الإثنين",
        "tuesday": "الثلاثاء",
        "wednesday": "الأربعاء",
        "thursday": "الخميس"
      }
    },
    "start": "البداية",
    "explore": "ابحث",
    "type": "النوع",
    "complete_lesson": "إكمال التدريب",
    "instructorLed": "عن بعد",
    "selfPaced": "إفتراضي",
    "test": "اختبار",
    "enrollSuccessBody":
        "مرحبا بك في بداية رحلة التعلم، تستطيع الوصول للدورة التدريبية من صفحة دوراتي",
    "areYouSureToLogout": "هل أنت متأكد من رغبتك بتسجيل الخروج من البوابة؟",
    "inProgress": "قيد التدريب ({})",
    "totalResults": "نتيجة",
    "totalResults1": "نتيجة",
    "totalResults2": "نتائج",
    "favorites": "المفضلة",
    "view": "عرض",
    "manageList": "القائمة",
    "choosePhotoFromTheLibrary": "اختر صورة من",
    "removeCurrentPhoto": "حذف صورة البروفايل",
    "sureToDeleteThePhoto": "هل أنت متأكد من حذف الصورة؟",
    "cropPost": "تعديل الصورة",
    "cropImage": "قص الصورة",
    "maximumFileSize2mb": "يجب ألا يتخطى حجم الملف: 2MB",
    "unsupportedFileFormat": "نوع الملف غير مدعوم",
    "viewTrainingDetails": "عرض تفاصيل الدورة",
    "totalResults2to11": "نتائج",
    "itemsSelected1": "عنصر مختار",
    "itemsSelected2": "عنصرين مختارين",
    "rYouSureToEnrollToStream": "هل أنت متأكد من التسجيل للمسار التدريبي؟",
    "previousStreamWillBeDisabled": "سيتم إيقاف المسار التدريبي السابق ولن تتمكن من إستكماله",
    "yesEnroll": "نعم، انضم",
    "upcomingStudyStreams": "المسارات التدريبية القادمة",
    "seatingCapacity": " السعة الاستيعابية",
    "start_end_date": "تواريخ البداية والنهاية",
    "itemsSelected3to10": "عناصر مختاره",
    "itemsSelected11": "عنصر مختار",
    "personal_info": "المعلومات الشخصية",
    "account_settings": "ابدأ رحلة التعلم",
    "nonLoggedInPageBody":
        " ليس لديك ملف شخصي بعد. قم بتسجيل الدخول الآن للخصول على مزايا حصرية ومحتوى مخصص لك",
    "nonLoggedInPageTitle": "ابدأ تجربتك!",
    "or": "أو",
    "noAvailableTrainings": "لا توجد دورات متاحة. قم بتسجيل الدخول لبدء الاستكشاف",
    "signInLater": "تسجيل الدخول لاحقاً",
    "email_mobile_or_id": "البريد الإلكتروني أو رقم الجوال أو الهوية الوطنية",
    "enter_email_mobile_or_id": "ادخل البريد الإلكتروني أو رقم الجوال أو الهوية الوطنية",
    "by_sign_in_i_accept": "من خلال تسجيل الدخول، أوافق على",
    "welcome_to_nsp": "مرحبا بك في\nبوابة المهارات",
    "unlock_new_opportunities": "اكتشف فرص جديدة",
    "get_started": "ابدأ",
    "terms_of_use": "شروط الاستخدام",
    "the_privacy_policy": "شروط الاستخدام",
    "and": "و",
    "skills_portal": "بوابة المهارات",
    "home_card_body":
        "تمثل بوابة المهارات محطة رئيسية في رحلة التنمية الشاملة، حيث تفتح أفقًا واسعًا لاكتساب المهارات الحيوية وتنمية القدرات. حيث تُعزز البوابة رؤية وطنية قائمة على تحفيز الابتكار والتفوق الشخصي، من خلال مجموعة متنوعة من المبادرات والبرامج.",
    "scrollDown": "اسحب للأسفل",
    "recentInProgress": "قيد التدريب",
    "popularTrainings": "الأكثر تسجيلاً",
    "sectors": "القطاعات",
    "training_self_paced": "التدريب (إفتراضي)",
    "training_instructor_led": "التدريب (عن بعد)",
    "welcome_user": "مرحبا {}",
    "trainingsInProgress": {
      "zero": "0 دورة قيد التدريب",
      "one": "دورة واحدة قيد التدريب",
      "two": "دورتان قيد التدريب",
      "few": "{} دورات قيد التدريب",
      "other": "{} دورة قيد التدريب"
    },
    "addContentSuggestion":
        "<1>لتتمكن من تعبئة الجزء الخاص بالتدريب<2>\"إضافة محتوى\"</2> اضغط على</1>",
    "addFile": "إضافة ملف",
    "addHomework": "Add homework",
    "addNewSection": "إضافة قسم جديد",
    "addQuiz": "إضافة اختبار قصير",
    "addSlides": "إضافة شرائح عرض",
    "addVideo": "إضافة فيديو",
    "allTrainings": "جميع المواد التدريبية",
    "archive": "أرشفة",
    "archiveTraining": "أرشفة التدريب",
    "deleteTraining": "حذف التدريب",
    "deleteTrainingPopupText": "بعد الحذف، لا تستطيع الدخول على التدريب",
    "deleteTrainingPopupTitle": "هل أنت متأكد من حذف التدريب؟",
    "cancel": "إلغاء",
    "changesWontBeSaved": "لم يتم حفظ التغيرات",
    "create": "إنشاء",
    "createNewTraining": "إنشاء تدريب جديد",
    "createTraining": "إنشاء مادة تدريبية",
    "createTrainingSuggestion":
        "</1>اضغط على<2>\"اضافة دورة تدريبية\"</2>لتتمكن من إنشاء تدريب جديد</1>",
    "delete": " مسح",
    "yesDelete": "نعم, حذف",
    "deleteContentSection": "هل أنت متأكد من حذف محتوى القسم؟",
    "deleteSection": "حذف القسم",
    "modalDeleteSectionTitle": "هل أنت متأكد من حذف القسم؟",
    "description": "الوصف",
    "draft": "مسودة",
    "duplicateSection": "نسخ القسم",
    "duplicateLesson": "تكرار الدرس",
    "editSectionTitle": "تعديل عنوان القسم",
    "sectionSettings": "إعدادات القسم",
    "editTraining": "تعديل التدريب",
    "emptySectionSuggestion": "</1>اضغط على<2>\"إضافة قسم جديد\"</2>لتتمكن من إضافة قسم </1>",
    "emptyLessonSectionSuggestion":
        "Select trainings from the dropdown to add them to the learning track",
    "enterText": "ادخل عنوان القسم",
    "enterTextArea": "ادخل نص المقال",
    "enterTrainingTitle": "أدخل عنوان التدريب",
    "enterTrainingTitleInArabic": "Enter training title in Arabic",
    "enterTrainingTitleInEnglish": "Enter training title in English",
    "exitTrainingBuilder": "هل انت متأكد من إغلاق صفحة التدريب؟",
    "exitWithoutSaving": "نعم ,خروج",
    "fieldIsEmpty": "يجب ألا يكون عنوان التدريب فارغا",
    "fieldIsRequired": "لا يجب ان يكون الحقل فارغاً",
    "expectationFieldIsEmpty": "يجب ألا يكون الحقل فارغاً",
    "sectionFieldIsEmpty": "يجب ألا يكون عنوان القسم فارغا",
    "filters": "التصفية",
    "hideSection": "إخفاء القسم",
    "inputContainLatinAndArabicCharsWithSpace": "الأرقام والأحرف الخاصة غير مسموح بها",
    "languagePlaceholder": "English",
    "maxCharactersAmount": "الحد الأقصى هو  {{amount}}حرف",
    "characters": "حرف",
    "newAccount": "ليس لديك حساب؟",
    "newest": "الأحدث",
    "noSectionYet": "لم تقم بإضافة قسم حتى الآن",
    "noTrainings": "لم تقم بإضافة دورات تدريبية حتى الآن",
    "enter_password": "ادخل كلمة المرور",
    "previewButton": "عرض",
    "published": "تم النشر",
    "word_new": "جديد",
    "pendingApproval": "قيد الانتظار",
    "rejected": "تم الرفض",
    "approved": "تم القبول",
    "qiwaSignIn": "تسجيل الدخول باستخدام قوى",
    "search": "البحث",
    "search_results": "نتائج البحث",
    "expand_search": "يرجى تغيير تصفية البحث والمحاولة مرة أخرى",
    "search_for_training": "البحث عن دورة تدريبية",
    "search_for_ltrack": "البحث عن مسار تدريبي",
    "searchResultCount": {
      "two": "2 \"{}\" نتيجتان",
      "few": "{} \"{}\" نتائج",
      "other": "\"{}\" نتائج {}"
    },
    "view_all": "عرض الكل",
    "section": "قسم:",
    "settingsButton": "الإعدادات",
    "signInIntoNationalSkillsPlatform": "تسجيل الدخول في منصة المهارات الوطنية",
    "signUp": "تسجيل الدخول",
    "signUpHere": "إنشاء حساب",
    "success": "Success",
    "text": "نص",
    "testsButton": "الإختبارات",
    "thereIsNoContentYet": "لم تقم بإضافة محتوى حتى الآن",
    "trainingDescription": "وصف التدريب",
    "trainingStructure": "تصميم المادة التدريبية",
    "trainingIsArchived": "تم أرشفة التدريب",
    "trainingIsDeleted": "تم حذف التدريب",
    "trainingTitle": "عنوان التدريب",
    "trainingOverview": "ملخص التدريب",
    "trainingTitleInArabic": "Training title in Arabic",
    "trainingTitleInEnglish": "Training title in English",
    "trainings": "الدورات التدريبية",
    "createNewSection": "إنشاء قسم جديد",
    "edit": "تعديل",
    "editButton": "إضافة محتوى",
    "editSectionName": "تعديل عنوان القسم",
    "publishWarning": "الرجاء حفظ المحتوى قبل النشر",
    "save": "حفظ",
    "yesSave": "نعم, حفظ",
    "showMore": "إظهار المزيد",
    "showLess": "إخفاء المزيد",
    "sectionName": "عنوان القسم",
    "sectionTitle": "عنوان القسم",
    "title": "عنوان",
    "articleFieldIsEmpty": "يجب ألا يكون عنوان المقال فارغا",
    "shareKnowledge": "علمك ثروة - ساهم بنشره",
    "articleTextIsEmpty": "يجب ألا يكون نص المقال فارغا",
    "enterArticleTitle": "ادخل عنوان المقال",
    "dragAndDropFile": "أرفق الملف",
    "fileExtensions":
        "PDF, PPT, PPTX, DOC, DOCX, XLS, XLSX, TXT, HTM, HTML, JPG, JPEG, GIF, PNG, TIF, TIFF, MP3, M4A, WAW",
    "selectFile": "أرفق ملف",
    "file": "ملف",
    "fileTitlePlaceholder": "ادخل عنوان الملف",
    "wrongFormat": "صيغة الملف غير صحيحة.",
    "maxFileSize": "الحد الاعلى لحجم الملف {}",
    "fileTitleIsEmpty": "يجب ألا يكون الملف فارغاً",
    "video": "فيديو",
    "addMoreItems": "إضافة عناصر أكثر",
    "max160Characters": "ادخل النص/الحد الأقصى 160 حرف ",
    "whatCanBeLearnedInTheTraining": "ما يمكن أن تتعلمه في التدريب؟",
    "atLeast2Items": "اكتب عنصرين على الاقل",
    "descriptionInputPlaceholder": "ادخل النص",
    "progressValidation": "جار رفع الملف، الرجاء الانتظار",
    "slide": "شرائح",
    "slidesText": "أضف شرائح عرض للمادة التدريبية",
    "slidesSubText": "نوع وحجم الملف: 10 MB; PDF",
    "enterValidResource": "Please enter valid resource",
    "noEmptyResource": "يجب ألا يكون محتوى الدرس فارغاً",
    "youWontBeAbleToRestoreIt": "لا تستطيع الاسترجاع بعد الحذف",
    "duration": "المدة",
    "durationValue": "{{from}}h - {{to}}h",
    "timeLimit": "الوقت المحدد:",
    "noLimit": "غير محدد",
    "deleteLessonConfirmation": "هل أنت متأكد من حذف المحتوى من القسم؟",
    "requirements": "المتطلبات",
    "fieldValidation": "يجب ان لا يكون الحقل فارغاً",
    "emailValidation": "تنسيق بريد خاطئ",
    "language": "اللغة",
    "results": "النتائج {}",
    "mostPopular": "الأكثر تسجيلًا",
    "recentlyAdded": "مضاف حديثًا",
    "clearAll": "اعادة ضبط التصنيف",
    "done": "تطبيق",
    "home": "الصفحة الرئيسية",
    "back": "رجوع",
    "training": "تدريب",
    "selectTraining": "Select Training",
    "selectTrainingFromTheList": "Select training from the list first",
    "addToTrack": "Add to Track",
    "languageMismatching":
        "You have added trainings that do not match the language selected in the learning track description. To continue, please either change the language of the learning track or delete unsuitable trainings",
    "error": "يوجد خطأ",
    "error_body":
        "يرجى اعاد المحاولة لاحقا. اذا استمر الخطأ، يرجى التواصل مع الدعم الفني نأمل ارفاق رقم الخطأ عند طلب الدعم، رقم الخطأ {}",
    "languages": {
      "EN": "English",
      "AR": "Arabic",
      "arabic_en": "Arabic",
      "arabic_ar": "العربية",
      "english_en": "English",
      "english_ar": "الإنجليزية"
    },
    "filterModal": {
      "english": "الإنجليزية",
      "arabic": "العربية",
      "HighestRated": "أعلى التقييم",
      "Sort": "ترتيب",
      "Filter": "تصفية",
      "Show": "عرض",
      "Reset": "حذف الكل"
    },
    "levels": {
      "BEGINNER": "مبتدئ",
      "INTERMEDIATE": "متوسط",
      "ADVANCED": "متقدم",
      "ALL": "جميع المستويات"
    },
    "durations": {"DAYS": "أيام", "WEEKS": "أسابيع", "MONTHS": "أشهر", "ALL": "الجميع"},
    "descriptionImage": {
      "title": "صورة التدريب",
      "text": "أضف صورة للمادة التدريبية",
      "guidelines": "نوع الملف: JPG, JPEG, GIF, PNG, TIF or TIFF"
    },
    "descriptionVideo": {
      "title": "صورة الفيديو",
      "text": "أضف فيديو للمادة التدريبية",
      "guidelines": "إرشادات الفيديو:\n\n100 MB; MP4, MOV and AVI formats; لاتزيد مدته عن 15دقيقة"
    },
    "selectLanguage": "اختر لغة",
    "selectLevel": "اختر مستوى",
    "level": "مستوى الدورة",
    "lessons": {
      "slide": {
        "description": "أضف شرائح عرض للمادة التدريبية نوع وحجم الملف PDF، MB10",
        "title": "شرائح",
        "inputPlaceholder": "أدخل عنوان الشرائح",
        "emptyTitleValidationMessage": "يجب ان لا يكون عنوان الشرائح فارغاً"
      },
      "video": {
        "description":
            "اضافة فيديو للمحتوى التدريبي - إرشادات الفيديو :MB100, MP4, MOV, and AVI Format-لاتزيد مدته عن 15دقيقة",
        "title": "فيديو",
        "inputPlaceholder": "ادخل عنوان الفيديو",
        "emptyTitleValidationMessage": "لا يجب ان يكون عنوان الفيديو فارغاً"
      }
    },
    "header": {
      "platformTitle": "منصة المهارات الوطنية",
      "profileSettings": "إعدادات المستخدم",
      "findTraining": "ابحث بالاسم",
      "languagePlaceholder": "English",
      "logOut": "تسجيل الخروج",
      "students": "الطلبات",
      "trainings": "قائمة الدورات التدريبية",
      "signIn": "تسجيل الدخول",
      "myTrainings": "دوراتي التدريبية",
      "noTrainingsFound": "لا يوجد تدريب",
      "trainingBuilder": "إنشاء مادة تدريبية",
      "trainingProvidersList": "مقدم الخدمة",
      "traineeProfile": "إنتقال إلى حساب المتدرب",
      "employerProfile": "إنتقال إلى حساب الموظف",
      "trainingProviderProfile": "إنتقال إلى حساب مقدم خدمة التدريب",
      "ministryEmployeeProfile": "إنتقال إلى حساب ممثل الوزارة",
      "traineeProfileActive": "حساب المتدرب",
      "employerProfileActive": "حساب الموظف",
      "trainingProviderProfileActive": "حساب مقدم خدمة التدريب",
      "ministryEmployeeProfileActive": "حساب ممثل الوزارة",
      "becomeEmployer": "التسجيل كممثل لجهة",
      "becomeTrainingProvider": "التجسيل كمزود خدمة تدريب",
      "trainingProviders": "مقدمي التدريب",
      "employers": "جهات العمل",
      "applicationRequests": "طلبات جهات العمل",
      "employer": "ممثل جهة العمل",
      "trainee": "متدرب",
      "ministryEmployee": "ممثل الوزارة",
      "trainingProvider": "مزود خدمة التدريب",
      "learningTracks": "مسارات التدريب",
      "learningTrack": "مسار التدريب",
      "employerPrograms": "قائمة الدورات التدريبية",
      "management": "لوحة التحكم",
      "dashboard": "لوحة البيانات",
      "employees": "الموظفين"
    },
    "landingPage": {
      "gainSkills":
          "اكتسب المهارات والشهادات من خلال البرامج التدريبية المتنوعة المعترف بها عالميا ومحليا",
      "exploreTrainings": "تصفح برامج التدريب",
      "discoverLearning": "مهاراتك بلا حدود",
      "withoutBoundaries": "",
      "activeUsers": "المتدربين النشطين",
      "TrainingProviders": "مزودي خدمات التدريب",
      "trainingPrograms": "برامج التدريب",
      "weAreNationalSkillsPlatform": "منصة المهارات الوطنية",
      "youFutureAtYourFingertips":
          "مستقبلك في متناول يدك: استكشف عالم المهارات من خلال المنصة ، نحن ملتزمون بدعمك وتزويدك بكافة الأدوات لتحقيق أهدافك التطويرية.",
      "exploreRecentlyAddedTrainings": "أحدث الدورات التدريبية",
      "exploreAllTrainingPrograms": "اكتشف جميع الدورات التدريبية",
      "skillsPassport": "هوية المهارات",
      "customizedLearningPathways": "مسارات تدريبية متخصصة",
      "learnAnytimeAnywhere": "تعلم في اي وقت,واي مكان",
      "skillsPassportText":
          "عرض مهارات المتدربين والثغرات والتوصيات التي تسلط الضوء على الخبرات والاهتمامات لأصحاب العمل.",
      "customizedLearningPathwaysText":
          "توفر المسارات التدريبية المتخصصة نهجا منظما وموجها نحو الهدف، مما يعزز تنمية المهارات بكفاءة من خلال تحسين وإعادة بناء وتبادل المهارات",
      "learnAnytimeAnywhereText":
          "توفير مرونة الاستخدام للمستفيدين بأي وقت ومكان يتناسب مع مختلف جداول الاعمال, مما يدفع لتبني مفهوم التعلم مدى الحياة"
    },
    "reviewsCount": "{} تقييم",
    "becomeTrainingProvider": {
      "becomeTrainingProviderTitle": "التسجيل كمزود خدمة تدريب",
      "trainingProviderApplication": "بيانات مقدم خدمة التدريب",
      "trainingProviderDetails": "معلومات مزود خدمة التدريب",
      "filesNeeded": "الملفات المطلوبة:",
      "commercialRegistration": "السجل التجاري",
      "accreditationCertificate": "شهادة الاعتماد",
      "gosi": "رقم التأمينات الاجتماعية",
      "vat": "رقم السجل الضريبي",
      "organizationName": "اسم المؤسسة",
      "commercialRegistrationEndDate": "تاريخ انتهاء السجل التجاري",
      "commercialRegistrationEndDatePlaceholder": "DD/MM/YYYY",
      "commercialRegistrationPlaceholder": "ادخل رقم السجل التجاري",
      "gosiPlaceholder": "ادخل رقم التأمينات الاجتماعية",
      "vatPlaceholder": "ادخل الرقم الضريبي",
      "organizationNamePlaceholder": "ادخل اسم المؤسسة",
      "becomeTrainingProviderFileExtensions": "PDF (حد أقصى. 10MB)",
      "discardApplication": "إلغاء",
      "submit": "تسجيل",
      "discardTheApplication": "هل أنت متأكد من إلغاء الطلب؟",
      "yesDiscard": "نعم,الغ",
      "application": "طلب",
      "reuseFilesFromApplication": "You can reuse files from this application for a new one.",
      "downloadDocumentsToReviewIt": "بإمكانك تحميل الملفات لمراجعتها",
      "documnetsWereRejected": "سبب الرفض",
      "documnetsWereApproved": "تم قبول الطلب",
      "toTrainingProviderProfile": "تهانينا! يمكنك الآن الانتقال إلى ملف مزود خدمة التدريب",
      "dontShowAgain": "عدم الإظهار مرة أخرى",
      "continueRegistrationAsTrainingProvider": "أكمل تسجيلك كمقدم تدريب",
      "completeDocumentsUploadingToBecomeTrainingProvider": "أكمل التسجيل لتصبح مقدم تدريب",
      "continue": "أكمل التسجيل",
      "skip": "تخطي",
      "pending": "حالة الطلب: قيد الانتظار",
      "rejected": "حالة الطلب: تم الرفض",
      "completed": "حالة الطلب: تمت الموافقة",
      "resubmitted": "أعيد تقديمه",
      "errorMessage": "صيغة التاريخ غير صحيحة",
      "generalBackendError": "حدث خطأ في النظام. الرجاء المحاولة مرة آخرى",
      "maxFileLimit": "الحد الأعلى لرفع الملفات هو 5 ملفات",
      "submissionDate": "تاريخ الطلب",
      "approvalDate": "تاريخ الموافقة",
      "rejectionDate": "تاريخ الرفض",
      "id": "رمز",
      "applicationSuccessfullySubmitted":
          "تم تقديم الطلب بنجاح. حاليًا يمكنك استكشاف النظام كمتدرب. عند الموافقة، سيتم تزويدك بالصلاحيات اللازمة. سنقوم بإعلامك فوراً عند الحصول على الموافقة من الوزارة."
    },
    "completedLessonsCount": "{}/{} درس",
    "trainingDetails": {
      "promoVideo": "فيديو ترويجي",
      "trainingDescription": "وصف الإختبار",
      "lTrackEnrollmentModalTitle": "لقد قمت بالتسجيل في المسار التدريبي",
      "trainingEnrollmentModalTitle": "لقد قمت بالتسجيل في الدورة التدريبية",
      "whatYouWillLearn": "ماذا ستتعلم",
      "skillsYouWillGain": "المهارات المكتسبة من التدريب",
      "requirements": "المتطلبات",
      "trainingStructure": "المادة التدريبية",
      "apply": "تسجيل",
      "studentsHaveAlreadyEnrolled": " تم تسجيل {} متدرب",
      "students": " متدرب ",
      "beenAlreadyEnrolled": " تم تسجيل ",
      "createdBy": "انشاء بواسطة",
      "sectionIndex": "قسم {}",
      "lessonsCount": {
        "zero": "0 درس",
        "one": "درس واحد",
        "two": "درسان",
        "few": "{} دروس",
        "other": "{} درس"
      },
      "averageRate": "متوسط التقييم",
      "estimatedTimeValue": "{} ساعة",
      "estimatedTime": "الوقت التقريبي",
      "trainingLanguage": "لغة التدريب",
      "trainingLevel": "مستوى التدريب",
      "online": "أونلاين",
      "skillsTitle": "المهارات المكتسبه من التدريب؟",
      "skillsSubtitle": "أضف مهارتين على الأقل",
      "noSkillsFound": "لا توجد مهارة",
      "findSkill": "البحث عن مهارة",
      "sectionsCount": {
        "zero": "0 قسم",
        "one": "قسم واحد",
        "two": "قسمان",
        "few": "{} أقسام",
        "other": "{} أقسام"
      },
      "newTopic": "اضف موضوع جديد {}",
      "skillsValidation": "يجب اضافة {} عنصر على الاقل",
      "goToTraining": "انتقل إلى المادة التدريبية",
      "registrationSuccessfullyFinished": "تم التسجيل",
      "trainingList": "قائمة الدورات التدريبية",
      "design": "التجزئة",
      "enroll": "تسجيل",
      "self_based": "ذاتي"
    },
    "trainingsList": {
      "noTrainingsFound": "لم يتم العثور على نتائج",
      "noTrainingsFoundDescription": "لم يتم العثور على نتائج"
    },
    "trainingProgress": {"sectionsProgress": "{} من {} أقسام", "progressPercentage": "%{}"},
    "footer": {
      "privacyPolicy": "سياسة الخصوصية",
      "allRightsReserved": "جميع الحقوق محفوظة لمبادرة مسرعة المهارات وقسائم التدريب",
      "overview": "نظرة عامة",
      "aboutTheInitiatives": "عن المبادرات",
      "howToUse": "كيفية الاستخدام ",
      "registration": "التسجيل بالبوابة",
      "Terms_of_Use": "شروط الاستخدام",
      "contactUs": "تواصل معنا ",
      "registerAsTrainee": "تسجيل متدرب جديد",
      "faqs": "الاسئلة الشائعة"
    },
    "trainingView": {
      "pagesCount": "صفحة {} من {}",
      "next": "التالي",
      "previous": "السابق",
      "lessonsProgress": "{} / {} دروس",
      "trainingContent": "محتوى التدريب",
      "mandatoryTest": "الاختبار إجباري",
      "numberOfQuestionsInTest": "عدد الأسئلة:",
      "minScoreOfTest": "الحد الأدنى للنجاح:",
      "startTestLabel": "بدء الاختبار",
      "questionOutOf": "السؤال {} من {}",
      "preQualificationTest": "اختبار ماقبل التدريب",
      "postQualificationTest": "اختبار اجتياز التدريب",
      "postEvaluationTest": "اختبار اجتياز التدريب"
    },
    "conductTest": {"True": "صح", "False": "خطأ"},
    "userLearnings": {
      "continue": "أكمل التدريب",
      "overview": "نظرة عامة",
      "decline": "Decline",
      "apply": "Apply",
      "in_progress": "قيد التدريب",
      "rateTraining": "Rate Training",
      "viewCertificate": "عرض الشهادة",
      "assignedOn": "Assigned on {}",
      "completedOn": "تم الاكتمال في {}",
      "yourRating": "Your Rating",
      "trainingsList": "قائمة الدورات التدريبية",
      "current": "الحالية",
      "nominated": "Nominated",
      "completed": "مكتملة",
      "beginOnlineLearningExperience": "إبدأ رحلة التعلم",
      "emptyCurrentListText": "لم تقم بالتسجيل في دورة تدريبية",
      "emptyNominatedListHeader": "There Are No Nominated Trainings Yet",
      "emptyNominatedListText": "They will appear after your Employer nominates trainings for you",
      "emptyCompletedListText": "لم تقم بإكمال دورة تدريبية حتى الآن",
      "exploreTrainings": "استكشف الدورات التدريبية",
      "haveToCompletePretrainingTest": "يجب عليك إكمال الاختبار لبدء التدريب"
    },
    "bottom_nav_bar": {
      "home": "الرئيسية",
      "catalog": "دليل الدورات",
      "myLearnings": "دوراتي التدريبية",
      "profile": "حسابي"
    },
    "trainingBuilder": {
      "saveAsDraft": "الحفظ كمسودة",
      "publish": "نشر",
      "validationErrorMessage": "يجب تعبئة جميع الحقول",
      "trainingIsPublished": "تم نشر التدريب",
      "allChangesAreSaved": "تم حفظ التغييرات",
      "requiredFieldsErrorMessage": "يجب تعبئة جميع الحقول المطلوبة",
      "emptySectionsMessage": "يجب إضافة محتوى للدروس لتتمكن من النشر",
      "tests": {
        "label": "الاختبارات",
        "preTrainingQualificationLabel": "اختبار ماقبل التدريب",
        "postTrainingEvaluationLabel": "اختبار اجتياز التدريب",
        "settings": "إعدادات الاختبار",
        "minScore": "الحد الأدنى لدرجة النجاح (بالنسبة المئوية)",
        "minScorePlaceholder": "ادخل الحد الأدنى",
        "minScoreTooltip":
            "الحد الأدنى للنجاح يشير إلى أقل نسبة مئوية مطلوبه من المتدرب للنجاح في الاختبار. هذا المعيار يضمن كفائة المتدرب لإكمال التدريب. يرجى إدخال رقم من 1 إلى 100 بدون استخدام المسافات أو أحرف خاصة. هذه القيمة تمثل النتيجة الأدنى التي يجب على المتدرب تحقيقها للنجاح في الاختبار.",
        "title": "عنوان الاختبار",
        "titlePlaceholder": "ادخل عنوان الاختبار",
        "description": "وصف الاختبار",
        "descriptionPlaceholder": "ادخل وصف الإختبار",
        "cancel": "إلغاء",
        "save": "حفظ الاختبار",
        "mandatory": "إجباري",
        "mandatoryTooltip":
            "اختبار ما قبل التدريب الإجباري يعني أن المتدرب لن يستطيع الوصول لمحتوى التدريب قبل إكمال الاختبار بنجاح",
        "preHidden": "إخفاء اختبار ماقبل التدريب",
        "postHidden": "إخفاء اختبار اجتياز التدريب",
        "randomize": "ترتيب عشوائي للأسئلة والإجابات",
        "randomizeTooltip":
            "يقوم النظام بترتيب جميع الأسئلة والأجوبة بشكل عشوائي. هذه الميزة تضمن أن تختلف الأسئلة والاختيارات لكل متدرب يؤدي الاختبار مما يمنع وجود تسلسل ثابت في المحاولات اللاحقة لنفس الاختبار",
        "delete": "حذف اختبار ماقبل التدريب",
        "cancelDelete": "إلغاء",
        "confirmDelete": "نعم حذف",
        "deleteDescriptionPre": "هل أنت متأكد من حذف اختبار ماقبل التدريب؟",
        "deleteDescriptionPost": "هل أنت متأكد من حذف اختبار اجتياز التدريب؟",
        "deleteDescription": "لن تستطيع العودة لإكمال بناء هذا الاختبار لاحقاً",
        "questions": "الأسئلة",
        "newQuestion": "سؤال جديد",
        "questionCount_one": "{{count}} سؤال",
        "questionCount_two": "{{count}} سؤالين",
        "questionCount_few": "{{count}} أسئلة",
        "questionCount_many": "{{count}} سؤال",
        "hiddenLabel": "إخفاء",
        "draftLabel": "مسودة",
        "editTest": "تعديل الاختبار",
        "duplicateTest": "نسخ الاختبار",
        "deleteTest": "حذف الاختبار",
        "deleteQuestion": "حذف السؤال",
        "duplicateQuestion": "تكرار السؤال",
        "question": "سؤال",
        "answers": "أجوبة",
        "addAnswer": "أضف إجابة",
        "questionType": "نوع السؤال",
        "correctAnswer": "الإجابة الصحيحة",
        "incorrectAnswer": "إجابة خاطئة",
        "questionTypes": {"RADIO": "إختيارات (إجابة واحدة)", "TRUEFALSE": "صح و خطأ"},
        "questionDescription": {
          "choose_singular_answer": "اختر الاجابة الصحيحة",
          "RADIO": "اختر الاجابة الصحيحة",
          "TRUEFALSE": "اختر الاجابة الصحيحة"
        },
        "deletePre": "حذف اختبار ماقبل التدريب",
        "deletePost": "حذف اختبار اجتياز التدريب",
        "minOneQuestion": "يجب إضافة سؤال واحد على الاقل",
        "minTwoAnswers": "يجب إضافة إجابتين على الاقل",
        "hasCorrectAnswer": "اختر الإجابة الصحيحة",
        "confirmExit": "هل أنت متأكد أنك تريد الخروج ؟"
      }
    },
    "trainingProviderDetailedPage": {"generalInformation": "General Information"},
    "trainingProviders": {
      "label": "مقدمي التدريب",
      "trainings": "Trainings",
      "employers": "Employers",
      "trainees": "Trainees",
      "searchPlaceholder": "ابحث بالاسم أو البريد الالكتروني",
      "pendingStatus": "قيد الانتظار",
      "rejectedStatus": "تم الرفض",
      "approvedStatus": "تمت الموافقة",
      "noData": "لا توجد طلبات حالية",
      "noDataDescription": "لا توجد طلبات حالية",
      "rejectLabel": "رفض",
      "approveLabel": "قبول",
      "tableTitle": "قائمة مقدمي التدريب",
      "trainingProviderName": "اسم مقدم الخدمة",
      "status": "الحالة",
      "organizationalId": "رمز المؤسسة",
      "requestDate": "تاريخ الطلب",
      "viewDetails": "عرض التفاصيل",
      "id": "رمز"
    },
    "trainingProviderDetailed": {
      "label": "بيانات مقدم الخدمة",
      "generalInformation": "معلومات عامة",
      "name": "اسم",
      "email": "البريد الالكتروني",
      "mobileNumber": "رقم الجوال",
      "organizationId": "رمز المؤسسة",
      "accreditationRequest": "طلب الاعتماد",
      "youCanDownloadDocuments": "يمكنك تحميل الوثائق لمراجعتها",
      "organizationName": "اسم الموسسة",
      "gosi": "التأمينات الإجتماعية",
      "commercialRegistration": "السجل التجاري",
      "vat": "ضريبة القيمة المضافة",
      "rejected": "مرفوض",
      "rejectedReason": "سبب الرفض",
      "trainingProvidersList": "مقدم الخدمة",
      "trainingProviderDetails": "بيانات مقدم الخدمة",
      "id": "رمز"
    },
    "rejectTrainingProvider": {
      "rejectionReason": "سبب الرفض",
      "rejectionReasonPlaceholder": "ادخل سبب الرفض",
      "reject": "رفض",
      "rejectionReasonRequired": "يجب إدخال سبب الرفض"
    },
    "modalTitle": "Set password",
    "newPasswordLabel": "كلمة المرور الجديدة",
    "passwordConfirmLabel": "تأكيد كلمة المرور",
    "passwordFieldRequired": " يجب ألا يكون الحقل فارغاً",
    "passwordMustMatch": " يجب أن تتطابق كلمات المرور",
    "passwordValidationMessage": "يجب اتباع قواعد كلمة المرور",
    "profileAndSettings": {
      "title": "الملف الشخصي",
      "generalInfo": "معلومات عامة",
      "trainingProvider": "طلبات مقدم الخدمة",
      "profileInfo": "البيانات الشخصية",
      "edit": "تعديل",
      "changePassword": "تغيير كلمة المرور",
      "changePasswordDescription":
          "يمكنك تعيين كلمة مرور لتسجيل الدخول باستخدام بيانات اعتماد إضافية",
      "setPassword": "تعيين كلمة المرور",
      "enterCode": "ادخل رمز التحقق",
      "codeModalDescription": "تم إرسال رمز التحقق عبر الرسائل النصية إلى رقم هاتفك",
      "submit": "تعيين",
      "resend": "أعد الإرسال",
      "notRevivedCode": "لم تستلم رمز التحقق؟",
      "toastTitle": "تم تعيين كلمة المرور بنجاح.",
      "toastMessage": "استخدم كلمة المرور الجديدة لتسجيل الدخول إلى منصة المهارات الوطنية.",
      "incorrectVerificationCode": "الرمز المدخل غير صحيح",
      "resendCodeMessage":
          "ادخلت رمز تحقق خاطئ عدة مرات. الرجاء استخدام الرابط المرفق لإرسال رمز تحقق جديد",
      "name": "اسم",
      "email": "البريد الالكتروني",
      "phoneNumber": "رقم الجوال",
      "nationalId": "الهوية الوطنية",
      "birthDate": "تاريخ الميلاد",
      "tooltipText":
          "يجب أن تحتوي كلمة المرور على الشروط التالية:\n\n<1>حرف كبير على الأقل (A-Z)</1>\n<1>حرف صغير على الأقل (a-z)</1>\n<1>رقم واحد على الأقل (0-9)</1>\n<1>حرف خاص واحد على الأقل (علامات ترقيم)، المسافة تعتبر حرف خاص أيضًا</1>\n<1>لا تقل عن 10 أحرف</1>\n<1>لا تزيد عن 128 حرف</1>\n<1>يمنع وجود التطابق (مثال: 111)</1>",
      "timeRemaining": "الوقت المتبقي {}"
    },
    "textField": {"usedCharacters": "{{used}}/{{total}} حرف"},
    "questions": {
      "singleChoice": {"label": "اختر الاجابة الصحيحة"},
      "errors": {"selectOneAnswer": "الرجاء اختيار الجواب قبل الانتقال للسؤال التالي"},
      "confirm": "تأكيد",
      "goBack": "رجوع",
      "results": {
        "preCompleted": "أكملت اختبار ماقبل التدريب",
        "postCompleted": "أكملت اختبار اجتياز التدريب",
        "quizCompleted": "أكملت الأختبار القصير",
        "yourScore": "نتيجتك",
        "score": "{}% من %100",
        "retakeTest": "إعادة الاختبار"
      }
    },
    "hint": "ملاحظة",
    "employerApplication": {
      "employerApplicationTitle": "تسجيل جهة العمل",
      "applicationRequestTitle": "طلب تسجيل جهة العمل",
      "workingEmail": "بريد العمل الإلكتروني",
      "workingEmailPlaceholder": "ادخل بريد العمل الإلكتروني",
      "positionInCompany": "منصب العمل",
      "positionInCompanyPlaceholder": "ادخل منصب العمل في جهة العمل",
      "moi": "رقم وزارة الداخلية",
      "moiPlaceholder": "ادخل رقم MOI ",
      "referenceInfoTitle": "بيانات المرجع",
      "referenceInfoTooltip":
          "تعتبر معلومات المرجع شخصًا يمكنه تأكيد أن المتقدم يمثل بصدق مصالح المنظمة.",
      "referenceName": "اسم المرجع",
      "referenceNamePlaceholder": "ادخل الاسم الكامل للمرجع",
      "referenceEmail": "بريد العمل الإلكتروني للمرجع",
      "referenceEmailPlaceholder": "بريد العمل الإلكتروني للمرجع",
      "referencePhone": "رقم جوال المرجع",
      "optionalComment": "إضافة ملاحظة",
      "optionalCommentPlaceholder": "ادخل ملاحظاتك",
      "discardApplication": "إلغاء",
      "submit": "تسجيل",
      "vatOptional": "الرقم الضريبي (إن وجد)",
      "applicationSuccessfullySubmitted":
          "تم تقديم الطلب بنجاح. حاليًا يمكنك استكشاف النظام كمتدرب. عند الموافقة، سيتم تزويدك بالصلاحيات اللازمة. سنقوم بإعلامك فوراً عند الحصول على الموافقة من الوزارة."
    },
    "privacyPolicy": {
      "privacyPolicyTitle": "سياسة  الخصوصية ",
      "date": "آخر تحديث في 11 ديسمبر 2023",
      "generalInfo": "معلومات عامة",
      "generalInfoTextFirst":
          "عند استخدام خدمات منصة المهارات الوطنية، فإنك تأتمننا على معلوماتك لذا فإن المقصد من سياسة الخصوصية هو مساعدتك وحماية البيانات المتعلقة بك من خلال الامتثال لجميع قوانين حماية البيانات والخصوصية المعمول بها",
      "generalInfoSubtitle": "الرجاء قراءة سياسة الخصوصية بعناية قبل استخدام الموقع",
      "generalInfoTextSecond":
          "منصة المهارات الوطنية هو موقع إلكتروني (\"موقعنا\") والذي تقوم بإدارته وزارة الموارد البشرية والتنمية الاجتماعية (\"نحن\"). نحن ملتزمون بحماية واحترام خصوصيتكم. هذه السياسة (سياسة الخصوصية الخاصة بـوزارة الموارد البشرية والتنمية الاجتماعية)، وغيرها من الوثائق المشار إليها في هذه السياسة تحدد الأساس الذي نقوم بناء عليه بجمع أية معلومات شخصية منكم، أو توفرونها لنا، والتي ستتم معالجتها من قبلنا. فيرجى قراءة ما يلي بعناية لتفهم وجهات نظرنا والممارسات المتعلقة بمعلوماتكم الشخصية وكيفية التعامل معها. ومن خلال زيارة موقعنا، فإنكم تتقبلون وتوافقون على الممارسات الوارد وصفها في هذه السياسة.",
      "infoToUsTitle": "المعلومات التي تقدمونها لنا",
      "infoToUsText":
          "قد تقدمون لنا معلومات خاصة بكم من خلال الدخول أو تعبئة النماذج المحددة في موقعنا أو غير ذلك من الطرق. وهذا يشمل المعلومات التي تقدمونها عند التسجيل لاستخدام موقعنا، أو المشاركة في منتديات النقاش أو تقديم منشورات المستخدم الخاصة بكم على موقعنا، أو عندما توجهون لنا رسالة بالبريد الإلكتروني أو ترسلون لنا تقريرًا عن مشكلة تواجهونها في التعامل مع موقعنا. فالمعلومات التي تقدمونها لنا قد تتضمن معلومات شخصية مثل اسمكم وعنوانكم وعنوان البريد الإلكتروني ورقم الهاتف، والمعلومات المالية وبطاقات الائتمان، ووصف الشخصية وصورة فوتوغرافية. كما قد تتضمن تعليقات أو غيرها من المعلومات المنشورة من قبلكم في موقعنا التي تكون مصممة للتواصل العام أو التواصل فيما بين مستخدمين آخرين والتي يمكن الاطلاع عليها وتحميلها من قبل أطراف أخرى ممن يزورون موقعنا.",
      "infoToCollectTitle": "المعلومات التي نجمعها عنكم",
      "infoToCollectText":
          "بخصوص كل زيارة لموقعنا، قد نقوم تلقائياً بجمع المعلومات التالية: <2> <3> <1>المعلومات التقنية، وتشمل عنوان بروتوكول الانترنت (IP) المستخدم لربط جهاز الكمبيوتر بالإنترنت، ومعلومات تسجيل الدخول، ونوع وإصدار المتصفح، وتحديد المنطقة الزمنية، وأنواع وإصدارات الوظائف الإضافية للمتصفح، ونظام ومنصة تشغيل الإنترنت؛</1> <1>معلومات عن زيارتكم، تشمل العناوين الكاملة لمعرفات الموارد الموحدة (URL)، ومعلومات تاريخ ووقت الزيارة؛ المعلومات التي تم عرضها أو البحث عنها؛ وأوقات الاستجابة على صفحات البحث؛ وأخطاء التنزيل؛ ومدى مدة الزيارات لصفحات محددة، ومعلومات التفاعل مع صفحات البحث (مثل التمرير والنقر وحركات الماوس)، والطرق المستخدمة للتصفح بعيداً عن الصفحة، وأي رقم هاتف يُستخدم للاتصال برقم خدمة العملاء.</1> </3> </2>",
      "infoWeReceiveTitle": "المعلومات التي نتلقاها من مصادر أخرى",
      "infoWeReceiveText":
          "قد نتلقى معلومات عنكم إذا كنتم تستخدمون أيًا من المواقع الأخرى التي نقوم بتشغيلها أو الاستفادة من الخدمات الأخرى التي نقدمها. كما أننا نعمل بشكل وثيق مع أطراف ثالثة (بما في ذلك، على سبيل المثال، الشركاء التجاريين، والمقاولين من الباطن في مجال تقديم الخدمات الفنية والدفع والتسليم وشبكات الإعلان، ومقدمي الدراسات التحليلية، ومقدمي معلومات البحث، والوكالات المرجعية الائتمانية) التي بإمكاننا الحصول منها على المعلومات التي تتعلق بكم.",
      "privacyPolicyDetails": "تفاصيل سياسة الخصوصية",
      "cookiesTitle": "ملفات تعريف الارتباط (Cookies)",
      "cookiesText":
          "يستخدم موقعنا ملفات تعريف الارتباط (Cookies) لتمييزكم عن المستخدمين الآخرين لموقعنا. وهذا يساعدنا على تزويدكم بتجربة جيدة عند قيامكم بتصفح موقعنا ويسمح لنا في الوقت ذاته بتحسين موقعنا. ومن خلال الاستمرار في تصفح موقعنا، فإنكم توافقون على قيامنا باستخدام ملفات تعريف الارتباط (Cookies) في التعامل معكم. ملف تعريف الارتباط هو عبارة عن ملف صغير يتألف من حروف وأرقام نقوم بتخزينها في المتصفح الخاص بكم أو على القرص الصلب لجهاز الحاسوب/الكمبيوتر الخاص بكم إذا كنتم توافقون على ذلك. تحتوي ملفات تعريف الارتباط (Cookies) على المعلومات التي يتم نقلها إلى القرص الصلب لجهاز الحاسوب/الكمبيوتر الخاص بكم. ",
      "cookiesThatWeUsedTitle": "نحن نستخدم ملفات تعريف الارتباط (Cookies) التالية:",
      "cookiesThatWeUsedText":
          "<2><1>ملفات تعريف الارتباط (Cookies) التي يجب استخدامها في حالات الضرورة القصوى:</1></2> هذه هي الملفات المطلوبة لتشغيل موقعنا. وهي تشمل، على سبيل المثال، ملفات تعريف الارتباط (Cookies) التي تمكّنكم من تسجيل الدخول إلى المناطق الآمنة من موقعنا، والاستفادة من خدمات الدفع عبر الانترنت. <2><1>ملفات تعريف الارتباط (Cookies) التي تستخدم للأغراض التحليلية / لأغراض الأداء:</1></2> فهي تسمح لنا بالتعرف وحساب عدد الزوّار والوقوف على الكيفية التي يتحرك بها الزوّار داخل موقعنا أثناء استخدامه. وهذا يساعدنا على تحسين الطريقة التي يعمل بها موقعنا، وذلك على سبيل المثال، من خلال ضمان أن المستخدمين يعثرون على ما يبحثون عنه بسهولة ويسر. <2><1>ملفات تعريف الارتباط (Cookies) التي تستخدم للأغراض الوظيفية:</1></2> تستخدم هذه الملفات للتعرف عليكم عند العودة إلى موقعنا. وهذا يمكننا من تخصيص المحتوى الذي نقدمه لكم، ومن تقديم التحية لكم بالاسم وتذكّر نواحي التعامل التي تفضلونها (على سبيل المثال، اللغة أو الإقليم المختار). <2><1>ملفات تعريف الارتباط (Cookies) التي تستخدم لأغراض الاستهداف:</1></2> تستخدم هذه الملفات في تسجيل وقائع زيارتكم لموقعنا، والصفحات التي قمتم بتصفحها والروابط التي قمتم باتباعها. وسوف نستخدم هذه المعلومات لجعل موقعنا والإعلانات المعروضة عليه أكثر ملاءمة وخدمة لمصالحكم. كما يجوز لنا إطلاع أطراف ثالثة على هذه المعلومات لهذا الغرض. يرجى ملاحظة أن الأطراف الثالثة (بما في ذلك، على سبيل المثال، شبكات الإعلان ومقدمي الخدمات الخارجية مثل خدمات تحليل حركة المرور على الشبكة) يمكنها أيضا استخدام ملفات تعريف الارتباط (Cookies) ، والتي لا يكون لدينا أي سيطرة عليها. ومن المرجح أن تكون ملفات تعريف الارتباط (Cookies) التي تستخدمها هذه الأطراف هي من النوع الذي يستخدم للأغراض التحليلية أو لأغراض الأداء أو لأغراض الاستهداف كما هو موضح أعلاه. يمكنكم منع استخدام ملفات تعريف الارتباط (Cookies) الخاصة بكم من خلال تفعيل الإعدادات على المتصفح الخاص بكم الذي يسمح لكم برفض أو تحديد كامل أو بعض ملفات تعريف الارتباط. ولكن يُراعى أنه في حال استخدامكم إعدادات المتصفح لمنع استخدام جميع ملفات تعريف الارتباط (بما في ذلك الملفات الأساسية منها) فقد لا يكون بإمكانكم الوصول إلى جميع أو بعض أجزاء موقعنا. باستثناء ملفات تعريف الارتباط الأساسية، فإن جميع ملفات تعريف الارتباط ينتهي مفعولها بعد انقضاء الساعة.</2>",
      "waysOfInfoUsageTitle":
          "الاستخدامات التطبيقية للمعلومات، نحن نستخدم المعلومات التي نحتفظ بها عنكم بالطرق التالية:",
      "waysOfInfoUsageText":
          "المعلومات التي تقدمونها لنا أو التي نجمعها عنكم، ستستخدم على النحو التالي: <2> <3> <1>لتمكيننا، وتمكين شركائنا والموردين من طرف ثالث من تقديم وإدارة، وتحسين مستوى الخدمات.</1> <1>لتمكيننا، وتمكين شركائنا والموردين من طرف ثالث من رفع مستوى أداء خدمة العملاء، سواء على المستوى الفردي بشكل إجمالي، أو من خلال إضفاء الصفة الفردية على تجربتهم وتقييم إمكانيات الدخول واستخدام موقعنا وأثر ذلك على المنشآت في المملكة العربية السعودية.</1> <1>لأغراض البحث والتطوير.</1> <1>مراقبة وكشف مخالفات الشروط والأحكام الخاصة بموقعنا، وكذلك حالات سوء الاستخدام المحتملة الأخرى لموقعنا.</1> <1>نشر المعلومات، التي تُجمع حول إمكانية الوصول والاستخدام، وأثر ذلك على أداء المنشآت.</1> <1>تزويدكم بآخر المستجدات حول الخدمات والمنتجات أو غيرها من الأحداث التي نقدمها نحن أو شركاؤنا أو الموردون أو المشغلين من طرف ثالث التي قد تكون ذات فائدة لكم، والتي يمكن أن ترسل لكم عن طريق رسائل البريد الإلكتروني حول الصيانة أو التحديثات التي تستجد على الموقع.</1> <1>أرشفة هذه المعلومات و/أو استخدامها لإمكانية التواصل المستقبلي معكم، ولإمكانية تحقق الأطراف الثالثة من هويتكم.</1> <1>صيانة ورفع مستوى أداء وأمن موقعنا وبرامجنا والنظم والشبكات الخاصة بنا.</1> <1>للأغراض الوارد وصفها في موضع آخر من سياسة الخصوصية (بما في ذلك، على سبيل المثال، تبادل المعلومات مع أطراف ثالثة).</1> <1>بأية طريقة جرى وصفها بخلاف ذلك في أي مرحلة من مراحل جمع المعلومات أو بناء على موافقتكم.</1> <1>لمصادقة هويتكم عند التسجيل في بعض الخدمات المقدّمة في موقعنا.</1> <1>لمعالجة إجراءات إعادة المبالغ المدفوعة، في الحالات التي ينطبق عليها ذلك.</1> <1>لإدارة موقعنا وتنفيذ العمليات الداخلية، بما في ذلك اكتشاف الأعطال وتحليل البيانات، والاختبار والبحث والأغراض الإحصائية وأغراض الدراسات المسحية الشاملة.</1> <1>لتحسين مستوى موقعنا لضمان عرض المحتوى بطريقة أكثر فعالية بالنسبة لكم وبالنسبة لجهاز الحاسوب/الكمبيوتر الخاص بكم.</1> <1>للسماح لكم بالمشاركة في الميزات التفاعلية للخدمة التي نقدمها، في حال رغبتكم في ذلك. كجزء من جهودنا الرامية إلى الحفاظ على أمن وسلامة موقعنا.</1> <1>كجزء من سعينا لتحقيق سلامة وأمن موقعنا.</1> <1>لقياس أو فهم فعالية الإعلانات التي نقدمها لكم ولغيركم، وتقديم الإعلانات ذات الصلة بكم.</1> <1>لتقديم الاقتراحات والتوصيات لكم ولغيركم من مستخدمي موقعنا حول الخدمات التي قد تهمكم أو تعنيهم.</1> </3>",
      "infoFromResourcesTitle": "المعلومات التي نتلقاها من مصادر أخرى",
      "infoFromResourcesText":
          "يجوز لنا الجمع بين هذه المعلومات وبين المعلومات التي تقدمونها لنا والمعلومات التي نجمعها عنكم. كما يجوز لنا استخدام هذه المعلومات والمعلومات التي جرى جمعها للأغراض المبيّنة أعلاه (اعتمادًا على أنواع المعلومات التي نتلقاها). ",
      "disclosureInfoTitle": "الإفصاح عن المعلومات الخاصة بكم",
      "disclosureInfoText":
          "قد نقوم بتبادل ومعالجة ومشاركة أي معلومات وبيانات تقدمونها لنا بمعرفتكم التامة و/أو نقوم بجمعها عنكم مع شركائنا والموردين من طرف ثالث، والجهات المختصة بإجراء التحليلات ومقدمي خدمات محرك البحث والمعلنين، على النحو الآتي: <2> <3> <1>إذا كان يتعين علينا الإفصاح أو تبادل المعلومات والبيانات الشخصية الخاصة بكم في إطار الوفاء بأي التزام قانوني، أو من أجل تنفيذ أو تطبيق (الشروط والأحكام الخاصة بـ منتجات شركة تكامل لخدمات الأعمال) وتنفيذ الاتفاقيات الأخرى؛ أو لحماية الحقوق، أو المِلكيّة، أو المحافظة على سلامة شركة تكامل لخدمات الأعمال، وسلامة عملائنا، أو غيرهم من الأطراف.</1> <1>مع مقدمي الخدمات بمختلف الأنشطة أو المقاولين الذين يؤدون وظائف معينة بالنيابة عنّا أو عن شركائنا، بما في ذلك معالجة المعلومات والبيانات التي تقدمونها على موقعنا، وتنفيذ العمليات عن طريق طرف ثالث، وغيرها من العمليات التي تنفذ من خلال موقعنا، أو التي تتعلق بتشغيل موقعنا أو أية أجزاء منه، وتقديم أو إدارة الخدمات، أو فيما يتعلق بأية جوانب أخرى من الخدمات التي نقدمها.</1> <1>لأغراض البحث والتطوير. ومع ذلك، فإننا سنقوم بتبادل المعلومات والبيانات الشخصية الخاصة بكم لهذا الغرض بما لا يتعارض مع أحكام القوانين والأنظمة المرعيّة وفي حدود المعلومات الشخصية المطلوبة لتحقيق الأغراض المبيّنة في وقت جمعها.</1> <1>مشاركة المعلومات والبيانات مع أطراف ثالثة وذلك لتوفير خدمات قد تكون ذات فائدة لكم.</1> <1>لتوفير فرص التواصل بينكم وبين المستخدمين الآخرين الذين قد يكون لهم مصالح أو أهداف مماثلة. وفي مثل هذه الحالات، فإنه يجوز لنا أن نستخدم جميع المعلومات والبيانات التي تم جمعها عنكم لتحديد الجهات التي قد تكون مهتمة بالتواصل معكم.</1> <1>للرد على مذكرات الإحضار، أو أوامر المحكمة أو الإجراءات القانونية الأخرى. وللتحقيق، أو منع أو اتخاذ اللازم تجاه إجراءات تتعلق بأنشطة غير مشروعة، أو أنشطة الاحتيال المشتبه بها، أو القضايا الأمنية أو الفنية، أو لتنفيذ الشروط والأحكام الخاصة بنا (الشروط والأحكام الخاصة بمنتجات شركة تكامل لخدمات الأعمال) أو سياسة الخصوصية، وكل ما يكون مطلوباً بخلاف ذلك بموجب أحكام القوانين والأنظمة المرعيّة أو لحماية حقوقنا وممتلكاتنا، أو المحافظة على سلامة ومصالح الآخرين.</1> <1>مع الشركات التابعة لنا، أو مع خلفائهم في حالة الاندماج، أو الاستحواذ أو إعادة التنظيم، لاستخدامها بما يتفق مع سياسة الخصوصية.</1> <1>كما هو موضح لك بخلاف ذلك في نقطة الجمع أو بناءً على م</1> <3>",
      "consentOfDataTitle": "الموافقة على معالجة البيانات خارج البلاد",
      "consentOfDataText":
          "باستخدامكم لموقعنا، أو تقديم معلومات لنا، فإن ذلك يعد موافقة من قبلكم على جمع واستخدام والإفصاح وحفظ المعلومات والبيانات في المملكة العربية السعودية وبلدان ومناطق أخرى، للأغراض المنصوص عليها في سياسة الخصوصية وفقًا للشروط والأحكام (الشروط والاحكام الخاصة بمنتجات شركة تكامل لخدمات الأعمال). وبهذا فإنكم توافقون على جمع واستخدام والإفصاح والاحتفاظ من قبلنا بالمعلومات والبيانات الشخصية الخاصة بكم كما هو موضح في سياسة الخصوصية، بما في ذلك دون حصر، تبادل المعلومات الشخصية الخاصة بكم فيما بيننا وبين أطراف ثالثة، ومع الشركات المنتسبة، والشركات التابعة لنا الوارد وصفها في سياسة الخصوصية. وتلافيًا للشك، فإن أي موافقة تتعلق بالحق في تبادل المعلومات المشار إليها في هذه الفقرة تشمل موافقتكم على تبادل المعلومات الشخصية الخاصة بكم مع أي ولاية قضائية قد توفر مستوى مختلفًا من حماية الخصوصية عن المستوى المتوفر في بلدكم. هذا وسنتخذ جميع الخطوات اللازمة بحدود المعقول لضمان التعامل مع المعلومات والبيانات الخاصة بكم بشكل آمن ووفقًا لسياسة الخصوصية. جميع المعلومات التي تقدمونها لنا يتم تخزينها في ذاكرة حاسبات خدمة آمنة خاصة بنا. وفي حال قيامنا بتزويدكم (أو في حال اختياركم) كلمة سر لتمكينكم من الدخول على أجزاء معينة من موقعنا، فستقع على عاتقكم مسؤولية المحافظة على سرية هذه الكلمة. ونحن ننصح بعدم إطلاع أي شخص على كلمة السر الخاصة بكم ومما يؤسف له أن عملية نقل المعلومات عبر شبكة الإنترنت ليست آمنة تمامًا. وعلى الرغم من أننا سنبذل قصارى جهدنا لحماية المعلومات الشخصية الخاصة بكم، فإننا لا نستطيع ضمان أمن المعلومات الخاصة بكم التي سيتم نقلها إلى موقعنا. وعليه، يراعى أن أي عملية نقل من هذا القبيل ستكون على مسؤوليتكم الخاصة. ومن ناحيتنا، فإنه بمجرد تلقينا المعلومات الخاصة بكم، سنطبّق إجراءات صارمة وميزات الأمان في محاولة لمنع الوصول غير المصرح به إلى تلك المعلومات .فإذا كنتم لا توافقون على هذه الشروط، فنأسف لعدم تمكينكم من الدخول، أو تصفح، أو التسجيل للاشتراك في موقعنا. وإذا اخترتم عدم تزويدنا بمعلومات معينة تكون مطلوبة لتزويدكم بمختلف الخدمات المقدمة على موقعنا، فلن يكون بإمكانكم إنشاء حساب مستخدم خاص، وبالتالي لن نكون قادرين على تزويدكم بتلك الخدمات. ",
      "yourRightsTitle": "حقوقكم",
      "yourRightsText":
          "إن لكم الحق في أن تطلبوا منّا عدم استخدام المعلومات والبيانات الشخصية والحساسة الخاصة بكم للأغراض التي خُصصت من أجلها. ربما يتضمّن موقعنا من وقت لآخر، روابط للاتصال من وإلى مواقع شبكات أو معلنين أو شركات تابعة لشريك من شركائنا. فإذا كنتم ترغبون في تتبع أي رابط للدخول على أي موقع من هذه المواقع، يرجى ملاحظة أن هذه المواقع لديها سياسات الخصوصية الخاصة بها، وأننا لا نتحمل أي مسؤولية أو تبعة فيما يتعلق بتلك السياسات. وعليه، يرجى الاطلاع على تلك السياسات قبل تقديم أي معلومات شخصية لتلك المواقع. ",
      "changePrivacyPolicyTitle": "التغييرات التي تطرا على سياسة الخصوصية الخاصة بنا",
      "changePrivacyPolicyText":
          "أي تغييرات قد ندخلها على سياسة الخصوصية الخاصة بنا في المستقبل ستتم نشرها على هذه الصفحة، وعند الاقتضاء، سيتم إخطاركم بها عن طريق البريد الإلكتروني. فيرجى الرجوع إلى بريدكم الإلكتروني باستمرار للوقوف على أي مستجدات أو تغييرات قد تطرأ على سياسة الخصوصية. <2>نُرحب بكافة الأسئلة والتعليقات والطلبات بخصوص سياسة الخصوصية والتي ينبغي أن توجَّه إلى العنوان التالي: <1><EMAIL></1></2>"
    },
    "quiz": "اختبار قصير",
    "quizQuestionType": {"RADIO": "اختيارات", "TRUEFALSE": "صح و خطأ"},
    "employerApplicationsList": {
      "title": "طلبات جهات العمل",
      "subTitle": "قائمة طلبات جهات العمل",
      "totalCount_zero": "لا توجد نتائج",
      "totalCount_one": "{{count}} نتيجة",
      "totalCount_two": "{{count}} نتيجتين",
      "totalCount_few": "{{count}} نتائج",
      "totalCount_many": "{{count}} نتائج",
      "requestId": "الرمز",
      "username": "اسم المستخدم",
      "status": "حالة الطلب",
      "organizationName": "اسم الجهة",
      "commercialRegistration": "السجل التجاري",
      "date": "تاريخ الطلب",
      "search": "البحث",
      "applicationStatus": {
        "pending": "قيد الانتظار",
        "approved": "تمت الموافقة",
        "rejected": "تم الرفض"
      },
      "searchPopover":
          "تستطيع البحث باستخدام الرمز، اسم المستخدم، البريد الإلكتروني، اسم الجهة، أو السجل التجاري",
      "clearAll": "مسح الكل",
      "apply": "تطبيق",
      "filterApplied": "تصفيه ({{count}})",
      "filters": "تصفيه",
      "dateTooltip":
          "التاريخ يعتمد على حالة الطلب. هناك 3 حالات: تاريخ الطلب، تاريخ الموافقة، وتاريخ الرفض."
    },
    "learningTracks": {
      "sidebarTitle": "مسارات التدريب",
      "title": "مسارات التدريب",
      "createTitle": "إنشاء مسار تدريبي",
      "allTracks": "جميع مسارات التدريب",
      "tracksCount_zero": "لا توجد نتائج",
      "trainingsCount": {
        "zero": "لا توجد نتائج",
        "one": "1 مادة تدريبية",
        "two": "2 مادتين تدريبيتين",
        "few": "{} مسارات تدريبية",
        "many": "{} مسار تدريبي"
      },
      "trainingsCount_one": "{{count}} ",
      "trainingsCount_two": "{{count}} ",
      "trainingsCount_few": "{{count}} مواد تدريبية",
      "trainingsCount_many": "{{count}} مادة تدريبية",
      "noTracksYet": "لا توجد مسارات تدريبية بعد",
      "createTrackSuggestion":
          "<1>انقر على <2>\"إنشاء مسار تدريبي\"</2> لإنشاء مسار تدريبي جديد</1>",
      "editTrack": "تعديل المسار التدريبي",
      "duplicateTrack": "نسخ المسار التدريبي",
      "deleteTrack": "حذف المسار التدريبي",
      "areYouSureDelete": "هل أنت متأكد من حذف المسار التدريبي؟",
      "builder": {
        "modalTitle": "إنشاء مسار تدريبي",
        "titleLabel": "عنوان مسار التدريب",
        "titlePlaceholder": "ادخل عنوان التدريب",
        "sectorLabel": "القطاع",
        "sectorPlaceholder": "اختر القطاع",
        "domainLabel": "المجال",
        "domainPlaceholder": "اختر المجال",
        "languageLabel": "اللغة",
        "languagePlaceholder": "اختر لغة",
        "submit": "إنشاء مسار تدريبي",
        "description": "الوصف",
        "structure": "Structure",
        "syllabus": "محتوى الدورة",
        "descriptionSubtitle": "وصف المسار التدريبي",
        "levelLabel": "مستوى الدورة",
        "levelPlaceholder": "اختر مستوى",
        "descriptionLabel": "وصف المسار التدريبي",
        "descriptionPlaceholder": "ادخل النص",
        "nominationOnly": "متوفر للمشرحين فقط",
        "confirmLeave": "هل أنت متأكد من الخروج من تصميم المسار التدريبي؟",
        "imageTitle": "صورة المسار التدريبي",
        "imageText": "اضف صورة للمسار التدريبي",
        "submittedForReview": "تم تقديم المسار التدريبي للمراجعة",
        "structureSubtitle": "List of Trainings",
        "min2trainings": "يجب إضافة تدريبين على الأقل",
        "max10trainings": "يمكن إضافة 10 تدريبات كحد أقصى"
      }
    },
    "traineeProfile": {
      "title": "الملف الشخصي",
      "orgTab": "جهة العمل",
      "orgNameColumnHeader": "اسم جهة العمل",
      "pendingMessage": "ستقوم جهة العمل بمراجعة طلبك وسيتم إخطارك فور الموافقة عليه أو رفضه",
      "sendRequest": "طلب الانضمام",
      "tableHeader": "قائمة جهات العمل",
      "tableHeaderDescription": "اختر جهة العمل الحالية",
      "searchPlaceholder": "ابحث باسم جهة العمل",
      "resultCount_zero": "لا توجد نتائج",
      "resultCount_one": "{{count}} نتيجة",
      "resultCount_two": "{{count}} نتيجتين",
      "resultCount_few": "{{count}} نتائج",
      "resultCount_many": "{{count}} نتائج",
      "employerName": "اسم صاحب العمل",
      "workEmail": "بريد العمل الإلكتروني",
      "orgWith2Rejects": "تم رفض طلب انضمامك مرتين. لا تستطيع رفض طلب آخر إلى جهة العمل",
      "sendRequestModal": {
        "cancel": "إلغاء",
        "yesBecome": "نعم، انضم",
        "areYouSureQuestion": "Are you sure you want to become part of \"{{orgName}}\"",
        "successToastTitle": "تم ارسل طلبك بنجاح",
        "successToastMsg": "سيتم إبلاغك بالرد قريباً",
        "errorToastTitle": "فشل ارسال الطلب",
        "errorToastMsg": "يرجى المحاولة مرة أخرى لاحقاً"
      },
      "rejectionError": {
        "title": "تم رفض طلب الانظمام إلى جهة \"{{orgName}}\"",
        "oneAttemptLeft":
            "تواصل مع جهة العمل لاستيضاح التفاصيل أو اختر جهة عمل أخرى من القائمة لرفع طلب انظمام. لديك محاولة واحدة متبقية للانضمام إلى جهة عمل"
      }
    },
    "notFoundPage": {
      "error404": "خطأ 404",
      "pageNotFound": "الصفحة غير موجودة",
      "errorMessage": "عذرًا، لا يمكننا العثور على الصفحة التي تبحث عنها.",
      "homePage": "الصفحة الرئيسية"
    },
    "somethingWentWrongPage": {
      "undefinedError": "الخطأ غير معرف",
      "somethingWentWrong": "عفوًا! حدث خطأ ما",
      "errorMessage": "نعمل على حل المشكلة. قم بتحديث الصفحة أو حاول مرة أخرى لاحقًا",
      "refreshPage": "حدث الصفحة"
    },
    "selectYourFirstRole": {
      "title": "اختر دورك للتسجيل في منصة المهارات الوطنية",
      "traineeDescription": "استعد للتعلم عن طريق العديد من المواد التدريبية",
      "employerDescription": "ساعد في تطوير مهارات فريقك والعاملين لديك",
      "trainingProviderDescription": "أنشئ تجربة تعليمة شاملة بمحتوى مفيد وتتبع أداء المتدربين.",
      "traineeTitle": "التسجيل كمتدرب",
      "employerTitle": "التسجيل كممثل جهة عمل",
      "trainingProviderTitle": "التسجيل كمزود خدمة تدريب",
      "continue": "أكمل التسجيل"
    },
    "employeePage": {
      "title": "Employees",
      "associatedTrainee": "Associated Trainee",
      "requests": "Requests",
      "nationalId": "National ID",
      "mobileNumber": "Mobile Number",
      "birthDate": "Date of Birth",
      "tableTitle": "List of Employee Requests",
      "tableResults": "{{count}} results",
      "selectedCount": "Selected ({{count}}):",
      "rejectApplicationsTitle": "Are you sure you want to reject {{count}} selected employee(s)?",
      "approveApplicationsTitle":
          "Are you sure you want to approve {{count}} selected employee(s)?",
      "approveButton": "Yes, Approve",
      "rejectButton": "Yes, Reject",
      "rejectLabel": "رفض",
      "approveLabel": "قبول"
    },
    "submit": "تسجيل",
    "qualificationTestStartPage": {
      "postTestTitle": "اختبار تقييم ما بعد التدريب",
      "preTestTitle": "اختبار تأهيل ما قبل التدريب",
      "testIsMandatory": "الاختبار إلزامي",
      "testIsNotMandatory": "الاختبار غير إلزامي",
      "start": "ابدأ",
      "postTestHint":
          "يرجى ملاحظة أنه بمجرد بدء اختبار تقييم ما بعد التدريب، لا يمكن إيقافه في منتصف العملية. تأكد من أنك مستعد للمتابعة قبل بدء الاختبار.",
      "preTestHint":
          "يرجى ملاحظة أنه بمجرد بدء اختبار تأهيل ما قبل التدريب، لا يمكن إيقافه في منتصف العملية. تأكد من أنك مستعد للمتابعة قبل بدء الاختبار.",
      "postTestDescription":
          "يعد اختبار تقييم ما بعد التدريب (PTET) أداة تقييم حاسمة مصممة لتقييم استعداد وقدرة الأفراد بعد خضوعهم لبرامج تدريبية متخصصة.",
      "preTestDescription":
          "يعد اختبار تأهيل ما قبل التدريب (PTQT) أداة تقييم حاسمة مصممة لتقييم استعداد وقدرة الأفراد قبل خضوعهم لبرامج تدريبية متخصصة."
    }
  };
  static const Map<String, Map<String, dynamic>> mapLocales = {"en": _en, "ar": _ar};
}
