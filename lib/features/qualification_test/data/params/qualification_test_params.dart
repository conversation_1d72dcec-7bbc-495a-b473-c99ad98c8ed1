import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';

class QualificationTestParams {
  final QualificationTestModel? qualificationTestModel;
  final TrainingConsumptionBloc trainingConsumptionBloc;
  final String trainingId;
  final LessonNavigationType navigationType;

  const QualificationTestParams({
    required this.qualificationTestModel,
    required this.trainingConsumptionBloc,
    required this.trainingId,
    this.navigationType = LessonNavigationType.initial,
  });
}
