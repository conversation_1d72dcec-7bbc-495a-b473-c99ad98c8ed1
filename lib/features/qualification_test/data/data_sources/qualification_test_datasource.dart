import 'dart:async';

import 'package:dio/dio.dart';

import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

enum QualificationTestType { PRE, POST }

@injectable
class QualificationTestDatasource {
  const QualificationTestDatasource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<QualificationTestModel> submitAnswer(
    String trainingId,
    String testId,
    ApplicantSavedAnswer answerModel,
    QualificationTestType? testType,
  ) async {
    final response = await _dio.put(
      ApiConstants.applicantsTrainings +
          trainingId +
          ApiConstants.qualificationTestAnswerSubmitPath +
          testId,
      data: [answerModel.toJson()],
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      if (testType == QualificationTestType.PRE) {
        return PreQualificationTest.fromJson(response.data);
      } else {
        return PostQualificationTest.fromJson(response.data);
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<QualificationTestModel> retakeTest(
    String trainingId,
    QualificationTestType? testType,
  ) async {
    final response = await _dio.post(
      ApiConstants.applicantsTrainings +
          trainingId +
          ApiConstants.retakeQualificationTestPath +
          (testType?.name ?? ''),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final trainingConsumptionModel = TrainingConsumptionModel.fromJson(response.data);

      //if ! leads to null-exception, it will be handled in the bloc
      if (testType == QualificationTestType.PRE) {
        return trainingConsumptionModel.preQualificationTest!;
      } else {
        return trainingConsumptionModel.postQualificationTest!;
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
