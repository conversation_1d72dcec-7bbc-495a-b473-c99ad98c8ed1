import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/widgets/answers_of_test.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answer_result_title.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/results_page_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

class QualificationResultsPage extends StatelessWidget {
  const QualificationResultsPage(
    this.qualificationTest, {
    required this.onRetakePressed,
    super.key,
  });

  final QualificationTestModel qualificationTest;
  final VoidCallback onRetakePressed;

  @override
  Widget build(BuildContext context) {
    final questions = qualificationTest.questions;
    final applicantAnswers = qualificationTest.applicantAnswers;

    return ListView(
      children: [
        ResultsPageHeader(
          finalScore: qualificationTest.finalScore,
          minPassingScore: qualificationTest.minimumScore ?? 0,
          onRetakePressed: onRetakePressed,
        ),
        const SizedBox(height: 8),
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border.symmetric(
              horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
            ),
          ),
          child: Column(
            children: [
              ///All Questions
              for (final question in questions) ...[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AnswerResultTitle(
                      isCorrect: applicantAnswers
                              .firstWhere(
                                (answer) => answer.questionId == question.id,
                                orElse: () => const ApplicantAnswer(correct: false),
                              )
                              .correct ??
                          false,
                    ),

                    ///Question number and question
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '${questions.indexOf(question) + 1}. ${question.question}',
                        style: context.textTheme.textLarge.semiBold,
                      ),
                    ),

                    const SizedBox(height: 16),

                    ///All answers of the question
                    AnswersOfTest(
                      allAnswerOptions: question.answers,
                      userSelectedAnswerID:
                          qualificationTest.applicantSavedAnswers[question.id]?.answerId ?? '',
                      isCorrect: applicantAnswers
                              .firstWhere(
                                (answer) => answer.questionId == question.id,
                                orElse: () => const ApplicantAnswer(correct: false),
                              )
                              .correct ??
                          false,
                    ),

                    const AppDivider(padding: EdgeInsets.only(top: 16)),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
