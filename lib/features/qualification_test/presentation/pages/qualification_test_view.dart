import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/domain/converters/model_converter.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/widgets/qualification_test_header.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answer_tile.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class QualificationTestView extends StatelessWidget {
  const QualificationTestView(this.qualificationTest, {super.key});

  final QualificationTestModel qualificationTest;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<QualificationTestBloc>().state;
    final currentQuestion = state.qualificationTest?.questions[state.currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        QualificationTestHeader(qualificationTest),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border.symmetric(
                horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
              ),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  Text(
                    LocaleKeys.trainingView_questionOutOf.tr(
                      args: [
                        (state.currentQuestionIndex + 1).toString(),
                        (state.qualificationTest?.questions.length ?? 0).toString(),
                      ],
                    ),
                    style: context.textTheme.textSmall.greyPrimary,
                  ),

                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    minHeight: 4,
                    borderRadius: BorderRadius.circular(40),
                    value: (state.currentQuestionIndex + 1) /
                        (state.qualificationTest?.questions.length ?? 1),
                    color: AppColors.greenAccentPrimary,
                    backgroundColor: const Color(0xFFE6E6E7),
                  ),
                  const SizedBox(height: 16),

                  ///Question
                  Text(
                    currentQuestion?.question ?? '',
                    style: context.textTheme.textLarge.semiBold,
                  ),

                  const SizedBox(height: 16),

                  Text(
                    LocaleKeys.trainingBuilder_tests_questionDescription_choose_singular_answer
                        .tr(),
                    style: context.textTheme.textMedium.greyPrimary,
                  ),

                  ///Answers
                  for (int i = 0; i < (currentQuestion?.answers.length ?? 0); i++)
                    AnswerTile(
                      //generates A, B.. letters for options
                      answerOption: String.fromCharCode(65 + i),
                      answer: currentQuestion?.answers[i],
                      isSelected: state.selectedAnswers.containsKey(currentQuestion?.id) &&
                          state.selectedAnswers[currentQuestion?.id]?.answerId ==
                              currentQuestion?.answers[i].id,
                      onTap: () => selectAnswer(context, currentQuestion, i),
                    ),

                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void selectAnswer(BuildContext context, Question? currentQuestion, int i) {
    return context.read<QualificationTestBloc>().add(
          SelectAnswerEvent(
            convertAnswerToApplicantSavedAnswer(
              currentQuestion?.answers[i],
              currentQuestion?.id,
              currentQuestion?.question,
            ),
          ),
        );
  }
}
