import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/pages/qualification_results_page.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/pages/qualification_test_start_page.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/pages/qualification_test_view.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/widgets/qualification_test_bottom_navigation.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/router.dart';

class QualificationTestPage extends StatefulWidget {
  const QualificationTestPage(this.qualificationTest, this.trainingId, {super.key});

  final QualificationTestModel qualificationTest;
  final String trainingId;

  @override
  State<QualificationTestPage> createState() => _QualificationTestPageState();
}

class _QualificationTestPageState extends State<QualificationTestPage> {
  late QualificationTestModel qualificationTest;
  late QualificationTestBloc qualificationTestBloc;

  @override
  void initState() {
    super.initState();
    qualificationTest = widget.qualificationTest;
    qualificationTestBloc = GetIt.instance.get<QualificationTestBloc>()
      ..add(LoadQualificationTestEvent(widget.qualificationTest, widget.trainingId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => qualificationTestBloc,
      child: BlocConsumer<QualificationTestBloc, QualificationTestState>(
        bloc: qualificationTestBloc,
        listener: (context, state) {
          if (state.errorMessage.isNotEmpty) {
            showAppToast(context, message: state.errorMessage);
          }

          if (state.qualificationTest != null) {
            qualificationTest = state.qualificationTest!;
          }
        },
        builder: (context, state) {
          if (state.qualificationTest == null) return const BuildLoader();

          return AppLoadingOverlay(
            isLoading: state.showLoadingIndicator,
            child: Scaffold(
              backgroundColor: AppColors.uiBackgroundPrimary,
              appBar: AppBar(
                leading: CloseButton(color: Colors.white, onPressed: () => router.pop()),
                title: Text(
                  qualificationTest.title ?? '',
                  style: context.textTheme.textSmall.semiBold.white,
                ),
                backgroundColor: AppColors.greenAccentDark,
              ),
              body: !state.testStarted
                  ? QualificationTestStartPage(qualificationTest)
                  : state.currentQuestionIndex >= qualificationTest.questions.length
                      ? QualificationResultsPage(
                          qualificationTest,
                          onRetakePressed: () {
                            qualificationTestBloc.add(
                              RetakeQualificationTestEvent(
                                widget.trainingId,
                                qualificationTest.type,
                              ),
                            );
                          },
                        )
                      : QualificationTestView(qualificationTest),
              bottomNavigationBar: state.testStarted
                  ? QualificationTestBottomNavigation(
                      trainingConsumptionBloc: context.read<TrainingConsumptionBloc>(),
                    )
                  : null,
            ),
          );
        },
      ),
    );
  }
}
