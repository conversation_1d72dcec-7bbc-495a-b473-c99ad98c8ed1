import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class QualificationTestStartPage extends StatelessWidget {
  const QualificationTestStartPage(this.qualificationTest, {super.key});

  final QualificationTestModel qualificationTest;

  @override
  Widget build(BuildContext context) {
    final isPostTest = qualificationTest.type == QualificationTestType.POST;

    final hintText = isPostTest
        ? LocaleKeys.qualificationTestStartPage_postTestHint.tr()
        : LocaleKeys.qualificationTestStartPage_preTestHint.tr();

    final isArabic = Localizations.localeOf(context).languageCode == Constants.localeAR;
    final bottomPadding =
        (MediaQuery.paddingOf(context).bottom != 0) ? MediaQuery.paddingOf(context).bottom : 16.0;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Test type text
                        Text(
                          isPostTest
                              ? LocaleKeys.qualificationTestStartPage_postTestTitle.tr()
                              : LocaleKeys.qualificationTestStartPage_preTestTitle.tr(),
                          style: context.textTheme.textMedium.semiBold,
                        ),
                        const SizedBox(height: 8),

                        // Test mandatory status
                        Text(
                          qualificationTest.mandatory == true
                              ? LocaleKeys.qualificationTestStartPage_testIsMandatory.tr()
                              : LocaleKeys.qualificationTestStartPage_testIsNotMandatory.tr(),
                          style: context.textTheme.textMedium.copyWith(
                            color: AppColors.greyPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildSeparator(),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 16),

                        // Test information
                        _InfoItem(
                          title: LocaleKeys.trainingView_numberOfQuestionsInTest.tr(),
                          value: qualificationTest.questions.length.toString(),
                        ),
                        const SizedBox(height: 8),

                        _InfoItem(
                          title: LocaleKeys.trainingView_minScoreOfTest.tr(),
                          value: "${qualificationTest.minimumScore ?? 0}%",
                        ),
                        const SizedBox(height: 8),

                        _InfoItem(
                          title: LocaleKeys.timeLimit.tr(),
                          value: qualificationTest.timeLimit == null ||
                                  qualificationTest.timeLimit == 0
                              ? LocaleKeys.noLimit.tr()
                              : "${qualificationTest.timeLimit} min",
                        ),
                        const SizedBox(height: 24),

                        // Hint
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              hintText,
                              style: context.textTheme.textSmall,
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Start button
                        AppButton(
                          onTap: () {
                            context.read<QualificationTestBloc>().add(
                                  const StartQualificationTestEvent(),
                                );
                          },
                          buttonText: LocaleKeys.qualificationTestStartPage_start.tr(),
                          textStyle: context.textTheme.textMedium.semiBold.white,
                        ),
                        const SizedBox(height: 24),

                        // Default description
                        Text(
                          isPostTest
                              ? LocaleKeys.qualificationTestStartPage_postTestDescription.tr()
                              : LocaleKeys.qualificationTestStartPage_preTestDescription.tr(),
                          style: context.textTheme.textSmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          _buildSeparator(),
          // Bottom navigation
          _buildBottomNavigation(bottomPadding, context, isArabic),
        ],
      ),
    );
  }

  Container _buildSeparator() {
    return Container(
      width: double.infinity,
      height: 8,
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        border: Border.all(color: AppColors.accentLight),
      ),
    );
  }

  Container _buildBottomNavigation(double bottomPadding, BuildContext context, bool isArabic) {
    return Container(
      padding: EdgeInsets.only(bottom: bottomPadding, top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: IntrinsicWidth(
              child: AppButton(
                onTap: () {
                  router.pop();
                },
                buttonText: LocaleKeys.trainingView_previous.tr(),
                height: 36,
                width: 100,
                padding: EdgeInsets.zero,
                backgroundColor: Colors.white,
                borderColor: AppColors.accentLight,
                textStyle: context.textTheme.textSmall.accentGreenPrimary.semiBold,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: IntrinsicWidth(
              child: AppButton(
                onTap: () {
                  context.read<QualificationTestBloc>().add(
                        const StartQualificationTestEvent(),
                      );
                },
                height: 36,
                width: 100,
                trailing: Transform(
                  alignment: Alignment.center,
                  transform: isArabic ? Matrix4.identity() : Matrix4.rotationY(pi),
                  child: Image.asset(
                    AssetsPath.backIcon,
                    color: Colors.white,
                    width: 20,
                    height: 20,
                  ),
                ),
                padding: EdgeInsets.zero,
                buttonText: LocaleKeys.trainingView_next.tr(),
                textStyle: context.textTheme.textSmall.white.semiBold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  const _InfoItem({
    required this.title,
    required this.value,
  });

  final String title;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: context.textTheme.textMedium.semiBold,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: context.textTheme.textMedium.semiBold.copyWith(
            color: AppColors.greenAccentPrimary,
          ),
        ),
      ],
    );
  }
}
