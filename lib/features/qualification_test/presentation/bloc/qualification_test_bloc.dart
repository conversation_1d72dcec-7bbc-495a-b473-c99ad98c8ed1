import 'dart:async';
import 'dart:math';
import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/domain/repositories/qualification_test_repository.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

part 'qualification_test_event.dart';

part 'qualification_test_state.dart';

part 'qualification_test_bloc.freezed.dart';

@injectable
class QualificationTestBloc extends Bloc<QualificationTestEvent, QualificationTestState> {
  QualificationTestBloc({
    required QualificationTestRepository qualificationTestRepository,
    required Random random,
  })  : _qualificationTestRepository = qualificationTestRepository,
        _random = random,
        super(QualificationTestState(selectedAnswers: {}, trainingId: '')) {
    on<LoadQualificationTestEvent>(_loadQualificationTestEvent);
    on<RetakeQualificationTestEvent>(_retakeQualificationTestEvent);
    on<SelectAnswerEvent>(_selectAnswerEvent);
    on<SubmitAnswerEvent>(_submitAnswerEvent);
    on<NavigateToPreviousQuestionEvent>(_navigateToPreviousQuestionEvent);
    on<StartQualificationTestEvent>(_startQualificationTestEvent);
  }

  final QualificationTestRepository _qualificationTestRepository;
  final Random _random;

  // Maps to track question order and answers
  Map<String, int> _questionOrderMap = {};
  Map<String, ApplicantSavedAnswer> _questionAnswerMap = {};

  void _loadQualificationTestEvent(
    LoadQualificationTestEvent event,
    Emitter<QualificationTestState> emit,
  ) {
    final qualificationTestModel = event.qualificationTestModel;
    final randomize = qualificationTestModel.randomized ?? false;

    // Reset tracking maps
    _questionOrderMap = {};
    _questionAnswerMap = Map.from(qualificationTestModel.applicantSavedAnswers);

    // Get answered and unanswered questions
    final answeredQuestionIds = _questionAnswerMap.keys.toList();
    final answeredQuestions = qualificationTestModel.questions
        .where((question) => answeredQuestionIds.contains(question.id))
        .toList();
    final unansweredQuestions = qualificationTestModel.questions
        .where((question) => !answeredQuestionIds.contains(question.id))
        .toList();

    // Shuffle unanswered questions if randomization is enabled
    final finalQuestions = [
      ...answeredQuestions,
      ...randomize ? shuffleQuestions(unansweredQuestions, _random) : unansweredQuestions,
    ];

    // Create question order map for consistent access
    for (int i = 0; i < finalQuestions.length; i++) {
      _questionOrderMap[finalQuestions[i].id] = i;
    }

    final updatedQualificationTest = qualificationTestModel is PreQualificationTest
        ? qualificationTestModel.copyWith(questions: finalQuestions)
        : qualificationTestModel is PostQualificationTest
            ? qualificationTestModel.copyWith(questions: finalQuestions)
            : null;

    emit(
      QualificationTestState(
        qualificationTest: updatedQualificationTest,
        selectedAnswers: _questionAnswerMap,
        trainingId: event.trainingId,
        currentQuestionIndex: qualificationTestModel.applicantSavedAnswers.length,
        questionOrderMap: _questionOrderMap,
      ),
    );
  }

  void _selectAnswerEvent(SelectAnswerEvent event, Emitter<QualificationTestState> emit) {
    final answer = event.answer;
    if (answer == null) return;

    final currentQuestion = getCurrentQuestion();
    if (currentQuestion == null) return;

    emit(state.copyWith(updatingAnswerSelection: true, errorMessage: ''));

    _questionAnswerMap[currentQuestion.id] = answer;

    emit(
      state.copyWith(selectedAnswers: Map.from(_questionAnswerMap), updatingAnswerSelection: false),
    );
  }

  Question? getCurrentQuestion() {
    if (state.qualificationTest == null ||
        state.currentQuestionIndex >= state.qualificationTest!.questions.length) {
      return null;
    }
    return state.qualificationTest!.questions[state.currentQuestionIndex];
  }

  Future<void> _submitAnswerEvent(
    SubmitAnswerEvent event,
    Emitter<QualificationTestState> emit,
  ) async {
    emit(state.copyWith(showLoadingIndicator: true, errorMessage: ''));

    final currentQuestion = getCurrentQuestion();
    final testID = state.qualificationTest?.id;

    if (currentQuestion == null || testID == null) {
      return emitError(emit);
    }

    final answerID = state.selectedAnswers[currentQuestion.id]?.answerId;
    if (answerID == null) return emitError(emit);

    final answerModel = ApplicantSavedAnswer(questionId: currentQuestion.id, answerId: answerID);

    await _qualificationTestRepository
        .submitAnswer(state.trainingId, testID, answerModel, state.qualificationTest?.type)
        .errorHandler(
          onSuccess: (qualificationTest) async {
            // Update tracking maps with new data
            _questionAnswerMap = Map.from(qualificationTest.applicantSavedAnswers);

            // Keep the current questions order but update the answers
            final updatedTest = state.qualificationTest?.type == QualificationTestType.PRE
                ? (qualificationTest as PreQualificationTest).copyWith(
                    questions: state.qualificationTest!.questions,
                    applicantSavedAnswers: _questionAnswerMap,
                  )
                : (qualificationTest as PostQualificationTest).copyWith(
                    questions: state.qualificationTest!.questions,
                    applicantSavedAnswers: _questionAnswerMap,
                  );

            emit(
              state.copyWith(
                showLoadingIndicator: false,
                currentQuestionIndex: state.currentQuestionIndex + 1,
                qualificationTest: updatedTest,
                selectedAnswers: _questionAnswerMap,
                errorMessage: '',
              ),
            );
          },
          onError: (errorMsg) =>
              emit(state.copyWith(showLoadingIndicator: false, errorMessage: errorMsg)),
        );
  }

  void _navigateToPreviousQuestionEvent(
    NavigateToPreviousQuestionEvent event,
    Emitter<QualificationTestState> emit,
  ) {
    if (state.currentQuestionIndex > 0) {
      emit(state.copyWith(currentQuestionIndex: state.currentQuestionIndex - 1, errorMessage: ''));
    }
  }

  void emitError(Emitter<QualificationTestState> emit) {
    emit(
      state.copyWith(
        showLoadingIndicator: false,
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    );
  }

  void _startQualificationTestEvent(
    StartQualificationTestEvent event,
    Emitter<QualificationTestState> emit,
  ) {
    emit(state.copyWith(testStarted: true));
  }

  Future<void> _retakeQualificationTestEvent(
    RetakeQualificationTestEvent event,
    Emitter<QualificationTestState> emit,
  ) async {
    emit(state.copyWith(showLoadingIndicator: true, errorMessage: ''));

    final trainingId = state.trainingId;

    await _qualificationTestRepository
        .retakeTest(trainingId, state.qualificationTest?.type)
        .errorHandler(
          onSuccess: (QualificationTestModel qualificationTest) async {
            emit(
              state.copyWith(
                showLoadingIndicator: false,
                qualificationTest: qualificationTest,
                selectedAnswers: {},
                currentQuestionIndex: 0,
                errorMessage: '',
                testStarted: false,
              ),
            );
          },
          onError: (errorMsg) => emit(
            state.copyWith(
              showLoadingIndicator: false,
              errorMessage: errorMsg,
              testStarted: false,
            ),
          ),
        );
  }

  // Function to shuffle questions and their answers
  List<Question> shuffleQuestions(List<Question> questions, Random random) {
    // Map each question to a new question with shuffled answers
    final shuffledQuestions = questions.map((q) {
      final shuffledAnswers = List<Answer>.from(q.answers)..shuffle(random);
      return q.copyWith(answers: shuffledAnswers);
    }).toList()
      ..shuffle(random);

    return shuffledQuestions;
  }

  // Function to get shuffled questions based on the randomized flag and answered questions
  List<Question> getShuffledQuestions({
    required List<Question> allQuestions,
    required Map<String?, ApplicantSavedAnswer> answered,
    required Random random,
  }) {
    final answeredQuestionIds = answered.keys.toList();
    final answeredQuestions =
        allQuestions.where((question) => answeredQuestionIds.contains(question.id)).toList();
    final nonAnsweredQuestions =
        allQuestions.where((question) => !answeredQuestionIds.contains(question.id)).toList();

    return [...answeredQuestions, ...shuffleQuestions(nonAnsweredQuestions, random)];
  }
}
