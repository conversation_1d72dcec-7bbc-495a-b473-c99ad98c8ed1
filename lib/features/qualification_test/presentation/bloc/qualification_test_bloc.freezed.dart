// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'qualification_test_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QualificationTestState implements DiagnosticableTreeMixin {
  QualificationTestModel? get qualificationTest;
  Map<String?, ApplicantSavedAnswer> get selectedAnswers;
  String get trainingId;
  int get currentQuestionIndex;
  String get errorMessage;
  bool get showLoadingIndicator;
  bool get updatingAnswerSelection;
  Map<String, int> get questionOrderMap;
  bool get testStarted;

  /// Create a copy of QualificationTestState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QualificationTestStateCopyWith<QualificationTestState> get copyWith =>
      _$QualificationTestStateCopyWithImpl<QualificationTestState>(
          this as QualificationTestState, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'QualificationTestState'))
      ..add(DiagnosticsProperty('qualificationTest', qualificationTest))
      ..add(DiagnosticsProperty('selectedAnswers', selectedAnswers))
      ..add(DiagnosticsProperty('trainingId', trainingId))
      ..add(DiagnosticsProperty('currentQuestionIndex', currentQuestionIndex))
      ..add(DiagnosticsProperty('errorMessage', errorMessage))
      ..add(DiagnosticsProperty('showLoadingIndicator', showLoadingIndicator))
      ..add(DiagnosticsProperty('updatingAnswerSelection', updatingAnswerSelection))
      ..add(DiagnosticsProperty('questionOrderMap', questionOrderMap))
      ..add(DiagnosticsProperty('testStarted', testStarted));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QualificationTestState &&
            (identical(other.qualificationTest, qualificationTest) ||
                other.qualificationTest == qualificationTest) &&
            const DeepCollectionEquality().equals(other.selectedAnswers, selectedAnswers) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.currentQuestionIndex, currentQuestionIndex) ||
                other.currentQuestionIndex == currentQuestionIndex) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.showLoadingIndicator, showLoadingIndicator) ||
                other.showLoadingIndicator == showLoadingIndicator) &&
            (identical(other.updatingAnswerSelection, updatingAnswerSelection) ||
                other.updatingAnswerSelection == updatingAnswerSelection) &&
            const DeepCollectionEquality().equals(other.questionOrderMap, questionOrderMap) &&
            (identical(other.testStarted, testStarted) || other.testStarted == testStarted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      qualificationTest,
      const DeepCollectionEquality().hash(selectedAnswers),
      trainingId,
      currentQuestionIndex,
      errorMessage,
      showLoadingIndicator,
      updatingAnswerSelection,
      const DeepCollectionEquality().hash(questionOrderMap),
      testStarted);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'QualificationTestState(qualificationTest: $qualificationTest, selectedAnswers: $selectedAnswers, trainingId: $trainingId, currentQuestionIndex: $currentQuestionIndex, errorMessage: $errorMessage, showLoadingIndicator: $showLoadingIndicator, updatingAnswerSelection: $updatingAnswerSelection, questionOrderMap: $questionOrderMap, testStarted: $testStarted)';
  }
}

/// @nodoc
abstract mixin class $QualificationTestStateCopyWith<$Res> {
  factory $QualificationTestStateCopyWith(
          QualificationTestState value, $Res Function(QualificationTestState) _then) =
      _$QualificationTestStateCopyWithImpl;
  @useResult
  $Res call(
      {QualificationTestModel? qualificationTest,
      Map<String?, ApplicantSavedAnswer> selectedAnswers,
      String trainingId,
      int currentQuestionIndex,
      String errorMessage,
      bool showLoadingIndicator,
      bool updatingAnswerSelection,
      Map<String, int> questionOrderMap,
      bool testStarted});
}

/// @nodoc
class _$QualificationTestStateCopyWithImpl<$Res> implements $QualificationTestStateCopyWith<$Res> {
  _$QualificationTestStateCopyWithImpl(this._self, this._then);

  final QualificationTestState _self;
  final $Res Function(QualificationTestState) _then;

  /// Create a copy of QualificationTestState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? qualificationTest = freezed,
    Object? selectedAnswers = null,
    Object? trainingId = null,
    Object? currentQuestionIndex = null,
    Object? errorMessage = null,
    Object? showLoadingIndicator = null,
    Object? updatingAnswerSelection = null,
    Object? questionOrderMap = null,
    Object? testStarted = null,
  }) {
    return _then(_self.copyWith(
      qualificationTest: freezed == qualificationTest
          ? _self.qualificationTest
          : qualificationTest // ignore: cast_nullable_to_non_nullable
              as QualificationTestModel?,
      selectedAnswers: null == selectedAnswers
          ? _self.selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String?, ApplicantSavedAnswer>,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      currentQuestionIndex: null == currentQuestionIndex
          ? _self.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      showLoadingIndicator: null == showLoadingIndicator
          ? _self.showLoadingIndicator
          : showLoadingIndicator // ignore: cast_nullable_to_non_nullable
              as bool,
      updatingAnswerSelection: null == updatingAnswerSelection
          ? _self.updatingAnswerSelection
          : updatingAnswerSelection // ignore: cast_nullable_to_non_nullable
              as bool,
      questionOrderMap: null == questionOrderMap
          ? _self.questionOrderMap
          : questionOrderMap // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      testStarted: null == testStarted
          ? _self.testStarted
          : testStarted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _QualificationTestState with DiagnosticableTreeMixin implements QualificationTestState {
  _QualificationTestState(
      {this.qualificationTest,
      required final Map<String?, ApplicantSavedAnswer> selectedAnswers,
      required this.trainingId,
      this.currentQuestionIndex = 0,
      this.errorMessage = '',
      this.showLoadingIndicator = false,
      this.updatingAnswerSelection = false,
      final Map<String, int> questionOrderMap = const {},
      this.testStarted = false})
      : _selectedAnswers = selectedAnswers,
        _questionOrderMap = questionOrderMap;

  @override
  final QualificationTestModel? qualificationTest;
  final Map<String?, ApplicantSavedAnswer> _selectedAnswers;
  @override
  Map<String?, ApplicantSavedAnswer> get selectedAnswers {
    if (_selectedAnswers is EqualUnmodifiableMapView) return _selectedAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_selectedAnswers);
  }

  @override
  final String trainingId;
  @override
  @JsonKey()
  final int currentQuestionIndex;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  @JsonKey()
  final bool showLoadingIndicator;
  @override
  @JsonKey()
  final bool updatingAnswerSelection;
  final Map<String, int> _questionOrderMap;
  @override
  @JsonKey()
  Map<String, int> get questionOrderMap {
    if (_questionOrderMap is EqualUnmodifiableMapView) return _questionOrderMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_questionOrderMap);
  }

  @override
  @JsonKey()
  final bool testStarted;

  /// Create a copy of QualificationTestState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QualificationTestStateCopyWith<_QualificationTestState> get copyWith =>
      __$QualificationTestStateCopyWithImpl<_QualificationTestState>(this, _$identity);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'QualificationTestState'))
      ..add(DiagnosticsProperty('qualificationTest', qualificationTest))
      ..add(DiagnosticsProperty('selectedAnswers', selectedAnswers))
      ..add(DiagnosticsProperty('trainingId', trainingId))
      ..add(DiagnosticsProperty('currentQuestionIndex', currentQuestionIndex))
      ..add(DiagnosticsProperty('errorMessage', errorMessage))
      ..add(DiagnosticsProperty('showLoadingIndicator', showLoadingIndicator))
      ..add(DiagnosticsProperty('updatingAnswerSelection', updatingAnswerSelection))
      ..add(DiagnosticsProperty('questionOrderMap', questionOrderMap))
      ..add(DiagnosticsProperty('testStarted', testStarted));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QualificationTestState &&
            (identical(other.qualificationTest, qualificationTest) ||
                other.qualificationTest == qualificationTest) &&
            const DeepCollectionEquality().equals(other._selectedAnswers, _selectedAnswers) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.currentQuestionIndex, currentQuestionIndex) ||
                other.currentQuestionIndex == currentQuestionIndex) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.showLoadingIndicator, showLoadingIndicator) ||
                other.showLoadingIndicator == showLoadingIndicator) &&
            (identical(other.updatingAnswerSelection, updatingAnswerSelection) ||
                other.updatingAnswerSelection == updatingAnswerSelection) &&
            const DeepCollectionEquality().equals(other._questionOrderMap, _questionOrderMap) &&
            (identical(other.testStarted, testStarted) || other.testStarted == testStarted));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      qualificationTest,
      const DeepCollectionEquality().hash(_selectedAnswers),
      trainingId,
      currentQuestionIndex,
      errorMessage,
      showLoadingIndicator,
      updatingAnswerSelection,
      const DeepCollectionEquality().hash(_questionOrderMap),
      testStarted);

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'QualificationTestState(qualificationTest: $qualificationTest, selectedAnswers: $selectedAnswers, trainingId: $trainingId, currentQuestionIndex: $currentQuestionIndex, errorMessage: $errorMessage, showLoadingIndicator: $showLoadingIndicator, updatingAnswerSelection: $updatingAnswerSelection, questionOrderMap: $questionOrderMap, testStarted: $testStarted)';
  }
}

/// @nodoc
abstract mixin class _$QualificationTestStateCopyWith<$Res>
    implements $QualificationTestStateCopyWith<$Res> {
  factory _$QualificationTestStateCopyWith(
          _QualificationTestState value, $Res Function(_QualificationTestState) _then) =
      __$QualificationTestStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {QualificationTestModel? qualificationTest,
      Map<String?, ApplicantSavedAnswer> selectedAnswers,
      String trainingId,
      int currentQuestionIndex,
      String errorMessage,
      bool showLoadingIndicator,
      bool updatingAnswerSelection,
      Map<String, int> questionOrderMap,
      bool testStarted});
}

/// @nodoc
class __$QualificationTestStateCopyWithImpl<$Res>
    implements _$QualificationTestStateCopyWith<$Res> {
  __$QualificationTestStateCopyWithImpl(this._self, this._then);

  final _QualificationTestState _self;
  final $Res Function(_QualificationTestState) _then;

  /// Create a copy of QualificationTestState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? qualificationTest = freezed,
    Object? selectedAnswers = null,
    Object? trainingId = null,
    Object? currentQuestionIndex = null,
    Object? errorMessage = null,
    Object? showLoadingIndicator = null,
    Object? updatingAnswerSelection = null,
    Object? questionOrderMap = null,
    Object? testStarted = null,
  }) {
    return _then(_QualificationTestState(
      qualificationTest: freezed == qualificationTest
          ? _self.qualificationTest
          : qualificationTest // ignore: cast_nullable_to_non_nullable
              as QualificationTestModel?,
      selectedAnswers: null == selectedAnswers
          ? _self._selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String?, ApplicantSavedAnswer>,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      currentQuestionIndex: null == currentQuestionIndex
          ? _self.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      showLoadingIndicator: null == showLoadingIndicator
          ? _self.showLoadingIndicator
          : showLoadingIndicator // ignore: cast_nullable_to_non_nullable
              as bool,
      updatingAnswerSelection: null == updatingAnswerSelection
          ? _self.updatingAnswerSelection
          : updatingAnswerSelection // ignore: cast_nullable_to_non_nullable
              as bool,
      questionOrderMap: null == questionOrderMap
          ? _self._questionOrderMap
          : questionOrderMap // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
      testStarted: null == testStarted
          ? _self.testStarted
          : testStarted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
