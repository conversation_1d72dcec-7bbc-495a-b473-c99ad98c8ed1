part of 'qualification_test_bloc.dart';

@freezed
abstract class QualificationTestState with _$QualificationTestState {
  factory QualificationTestState({
    QualificationTestModel? qualificationTest,
    required Map<String?, ApplicantSavedAnswer> selectedAnswers,
    required String trainingId,
    @Default(0) int currentQuestionIndex,
    @Default('') String errorMessage,
    @Default(false) bool showLoadingIndicator,
    @Default(false) bool updatingAnswerSelection,
    @Default({}) Map<String, int> questionOrderMap,
    @Default(false) bool testStarted,
  }) = _QualificationTestState;
}
