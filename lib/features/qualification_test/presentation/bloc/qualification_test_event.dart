part of 'qualification_test_bloc.dart';

sealed class QualificationTestEvent {
  const QualificationTestEvent();
}

class LoadQualificationTestEvent extends QualificationTestEvent {
  const LoadQualificationTestEvent(this.qualificationTestModel, this.trainingId);

  final QualificationTestModel qualificationTestModel;
  final String trainingId;
}

class StartQualificationTestEvent extends QualificationTestEvent {
  const StartQualificationTestEvent();
}

class NavigateToPreviousQuestionEvent extends QualificationTestEvent {
  const NavigateToPreviousQuestionEvent();
}

class SelectAnswerEvent extends QualificationTestEvent {
  final ApplicantSavedAnswer? answer;

  const SelectAnswerEvent(this.answer);
}

class SubmitAnswerEvent extends QualificationTestEvent {
  const SubmitAnswerEvent();
}

class RetakeQualificationTestEvent extends QualificationTestEvent {
  const RetakeQualificationTestEvent(this.trainingId, this.testType);

  final String trainingId;
  final QualificationTestType? testType;
}
