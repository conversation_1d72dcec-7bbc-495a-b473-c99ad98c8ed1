import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/unified_navigation_mixin.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class QualificationTestBottomNavigation extends StatelessWidget with UnifiedNavigationMixin {
  const QualificationTestBottomNavigation({required this.trainingConsumptionBloc, super.key});

  final TrainingConsumptionBloc trainingConsumptionBloc;

  @override
  Widget build(BuildContext context) {
    final bottomPadding =
        MediaQuery.paddingOf(context).bottom != 0 ? MediaQuery.paddingOf(context).bottom : 16.0;

    return BlocBuilder<QualificationTestBloc, QualificationTestState>(
      builder: (context, state) {
        final questions = state.qualificationTest?.questions ?? [];
        final currentQuestionIndex = state.currentQuestionIndex;
        final currentQuestionID =
            (currentQuestionIndex < questions.length) ? questions[currentQuestionIndex].id : '';
        final isTestCompleted = currentQuestionIndex >= questions.length;
        final isAnswerSelected = state.selectedAnswers[currentQuestionID] != null;
        final isPreTest = (state.qualificationTest?.type == QualificationTestType.PRE);
        final qualificationTest = state.qualificationTest;

        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(top: BorderSide(color: AppColors.accentLight, width: 0.5)),
          ),
          padding: EdgeInsets.only(bottom: bottomPadding, top: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Show "Go Back" if not on first question, or
              // if test is completed and it's a post-test => show "Previous"
              if (currentQuestionIndex > 0 && currentQuestionIndex < questions.length)
                _NavButton(
                  onTap: () => context.read<QualificationTestBloc>().add(
                        const NavigateToPreviousQuestionEvent(),
                      ),
                  buttonText: LocaleKeys.questions_goBack.tr(),
                )
              else if (isTestCompleted && !isPreTest)
                _NavButton(
                  onTap: () => _handlePreviousNavigation(context),
                  buttonText: LocaleKeys.trainingView_previous.tr(),
                )
              else
                const Spacer(),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: IntrinsicWidth(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(minWidth: 100),
                    child: AppButton(
                      onTap: () {
                        if (isAnswerSelected) {
                          context.read<QualificationTestBloc>().add(const SubmitAnswerEvent());
                          return;
                        }

                        if (isTestCompleted) {
                          if (isPreTest) {
                            if (qualificationTest != null &&
                                qualificationTest is PreQualificationTest) {
                              // Update pre-qualification test state before navigating
                              trainingConsumptionBloc.add(
                                UpdatePreQualificationTestEvent(qualificationTest),
                              );

                              // Check if the test was passed before navigating to first lesson
                              final isPassed = qualificationTest.resolutionDate != null &&
                                  (qualificationTest.finalScore ?? -1) >=
                                      (qualificationTest.minimumScore ?? 0);

                              if (isPassed) {
                                navigateToFirstLesson(trainingConsumptionBloc);
                              } else {
                                router.pop();
                              }
                              return;
                            }
                          }

                          router.pop();
                        }
                      },
                      backgroundColor: (isAnswerSelected || isTestCompleted)
                          ? AppColors.greenAccentPrimary
                          : AppColors.greenAccentSecondary,
                      height: 36,
                      padding: EdgeInsets.zero,
                      buttonText: isTestCompleted
                          ? (state.qualificationTest?.type == QualificationTestType.POST
                              ? LocaleKeys.complete_lesson.tr()
                              : LocaleKeys.done.tr())
                          : LocaleKeys.questions_confirm.tr(),
                      textStyle: context.textTheme.textSmall.white.semiBold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// If user clicks "Previous" on a completed post-test (not pre-test),
  /// we attempt to go back to the last lesson, or pop if none.
  void _handlePreviousNavigation(BuildContext context) {
    final qualificationTest = context.read<QualificationTestBloc>().state.qualificationTest;
    final isPreTest = (qualificationTest?.type == QualificationTestType.PRE);
    final sections = trainingConsumptionBloc.state.trainingStructure?.sections ?? [];

    // 1) If it's a pre-test or if no sections exist, just close the page
    if (isPreTest || sections.isEmpty) {
      router.pop();
      return;
    }

    // Update post-test state before navigating back
    if (qualificationTest != null && qualificationTest is PostQualificationTest) {
      trainingConsumptionBloc.add(UpdatePostQualificationTestEvent(qualificationTest));
    }

    // 2) Attempt to find the last lesson in the last section
    final lastSection = sections.last;
    final lastLesson = lastSection.lessons.lastOrNull;

    // 3) Pop if no lesson found
    if (lastLesson == null) return router.pop();

    // 4) Navigate to last lesson
    trainingConsumptionBloc.add(
      OpenLessonEvent(
        LessonParams(
          lesson: lastLesson,
          section: lastSection,
          completedLessonsInSection: sections.fold<int>(0, (sum, s) => sum + s.lessons.length),
          isCompleted: true,
          trainingConsumptionBloc: trainingConsumptionBloc,
          lessonNavigationType: LessonNavigationType.backward,
        ),
      ),
    );
  }
}

class _NavButton extends StatelessWidget {
  const _NavButton({required this.buttonText, required this.onTap});

  final String buttonText;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: IntrinsicWidth(
        child: AppButton(
          onTap: onTap,
          buttonText: buttonText,
          height: 36,
          width: 100,
          padding: EdgeInsets.zero,
          backgroundColor: Colors.white,
          borderColor: AppColors.accentLight,
          textStyle: context.textTheme.textSmall.accentGreenPrimary.semiBold,
        ),
      ),
    );
  }
}
