import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class QualificationTestHeader extends StatelessWidget {
  const QualificationTestHeader(this.qualificationTestModel, {super.key});

  final QualificationTestModel qualificationTestModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: AppColors.accentLight, width: 0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            qualificationTestModel.title ?? '',
            style: context.textTheme.textMedium.semiBold,
            maxLines: 2,
          ),
          if (qualificationTestModel.mandatory ?? false) ...[
            const SizedBox(height: 4),
            Text(LocaleKeys.trainingView_mandatoryTest.tr(), style: context.textTheme.textSmall),
          ],
        ],
      ),
    );
  }
}
