import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class AnswersOfTest extends StatelessWidget {
  const AnswersOfTest({
    required this.isCorrect,
    required this.allAnswerOptions,
    required this.userSelectedAnswerID,
    super.key,
  });

  final bool? isCorrect;
  final String userSelectedAnswerID;
  final List<Answer> allAnswerOptions;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (int j = 0; j < allAnswerOptions.length; j++)
          Container(
            color:
                //selected answer by user == each answer is compared
                userSelectedAnswerID == allAnswerOptions[j].id
                    ? AppColors.uiBackgroundPrimary
                    : null,
            child: ListTile(
              minLeadingWidth: 0,
              dense: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              leading: Text(
                String.fromCharCode(65 + j),
                style: context.textTheme.textSmall.semiBold.greyPrimary,
              ),
              title: Text(
                allAnswerOptions[j].answer ?? '',
                style: context.textTheme.textSmall.medium,
              ),
              //if the user selected answer is the same as the correct answer OR if , then show the correct icon
              trailing: userSelectedAnswerID == allAnswerOptions[j].id
                  ? (isCorrect ?? false)
                      ? const Icon(Icons.check_rounded, color: AppColors.statusSuccess)
                      : const Icon(Icons.close_rounded, color: AppColors.statusWarning)
                  : null,
            ),
          ),
      ],
    );
  }
}
