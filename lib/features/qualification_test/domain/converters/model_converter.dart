import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

ApplicantSavedAnswer? convertAnswerToApplicantSavedAnswer(
  Answer? answer,
  String? questionId,
  String? question,
) {
  if (answer == null || questionId == null) return null;

  return ApplicantSavedAnswer(questionId: questionId, question: question, answerId: answer.id);
}

QuestionAnswerPair convertAnswerToQuestionAnswerPair(
  Answer? answer,
  String? questionId,
  String? question,
) {
  if (answer == null || questionId == null) {
    return QuestionAnswerPair(questionId: questionId, question: question, answerIds: const []);
  }

  return QuestionAnswerPair(questionId: questionId, question: question, answerIds: [answer.id]);
}
