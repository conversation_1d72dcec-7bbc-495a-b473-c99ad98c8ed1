import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

@injectable
class QualificationTestRepository {
  const QualificationTestRepository({required QualificationTestDatasource dataSource})
      : _dataSource = dataSource;

  final QualificationTestDatasource _dataSource;

  Future<QualificationTestModel> submitAnswer(
    String trainingId,
    String testId,
    ApplicantSavedAnswer applicantSavedAnswer,
    QualificationTestType? testType,
  ) =>
      _dataSource.submitAnswer(trainingId, testId, applicantSavedAnswer, testType);

  Future<QualificationTestModel> retakeTest(String trainingId, QualificationTestType? testType) =>
      _dataSource.retakeTest(trainingId, testType);
}
