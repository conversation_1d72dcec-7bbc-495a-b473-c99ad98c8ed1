import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/profile_page/data/datasource/user_datasource.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';

@injectable
class UserRepository {
  const UserRepository({required UserDataSource dataSource}) : _dataSource = dataSource;

  final UserDataSource _dataSource;

  Future<UserModel> getUserData() => _dataSource.getUserData();

  Future<String> getUserAvatar() => _dataSource.getUserAvatar();

  Future<String> uploadUserAvatar(MemoryImage image) => _dataSource.uploadUserAvatar(image);

  Future<void> deleteUserAvatar() => _dataSource.deleteUserAvatar();
}
