import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:path_provider/path_provider.dart';

@injectable
class UserDataSource {
  const UserDataSource({required Dio dio, required FormDataHelper formDataHelper})
      : _formDataHelper = formDataHelper,
        _dio = dio;

  final Dio _dio;
  final FormDataHelper _formDataHelper;

  Future<UserModel> getUserData() async {
    final response = await _dio.get(ApiConstants.userPath);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return UserModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<String> getUserAvatar() async {
    final response = await _dio.get(ApiConstants.userAvatarPath);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      return response.data;
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<String> uploadUserAvatar(MemoryImage image) async {
    const imageTitle = 'profile.jpg';
    final tempDir = await getTemporaryDirectory();
    final filePath = '${tempDir.path}/$imageTitle';

    // Write the Uint8List data to a temporary file
    final file = await File(filePath).writeAsBytes(image.bytes);
    final formData = await _formDataHelper.createFormData(file.path, imageTitle);

    final response = await _dio.post(
      '${ApiConstants.userAvatarPath}?${Constants.profileType}=${Constants.TRAINEE}',
      data: formData,
      options: Options(
        headers: {..._dio.options.headers, Constants.contentType: Constants.multiPartFormData},
      ),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      return response.data;
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<void> deleteUserAvatar() async {
    final response = await _dio.delete(ApiConstants.userAvatarPath);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      return;
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}

@injectable
class FormDataHelper {
  Future<FormData> createFormData(String filePath, String imageTitle) async {
    final multipartFile = await MultipartFile.fromFile(filePath, filename: imageTitle);
    return FormData.fromMap({Constants.file: multipartFile});
  }
}
