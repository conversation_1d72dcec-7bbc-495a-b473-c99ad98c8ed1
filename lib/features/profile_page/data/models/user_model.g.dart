// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: json['id'] as String,
      name: json['name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      roles: (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      phone: json['phone'] as String? ?? '',
      birthdate: stringDateTimeFormat(json['birthdate'] as String?),
      birthdateFromQiwa: json['birthdateFromQiwa'] as bool?,
      nameFromQiwa: json['nameFromQiwa'] as bool?,
      language: json['language'] as String?,
      firstLogin: json['firstLogin'] as bool?,
      login: json['login'] as String?,
      organizationName: json['organizationName'] as String?,
      trainingProviderName: json['trainingProviderName'] as String?,
      instructor: json['instructor'] == null
          ? null
          : Instructor.fromJson(json['instructor'] as Map<String, dynamic>),
      trainingProviderOrganizationId: json['trainingProviderOrganizationId'] as String?,
      initialModalWindow: json['initialModalWindow'] as String?,
      nationalId: (json['nationalId'] as num?)?.toInt(),
      iqamaId: json['iqama_id'] as String?,
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'birthdate': instance.birthdate,
      'birthdateFromQiwa': instance.birthdateFromQiwa,
      'nameFromQiwa': instance.nameFromQiwa,
      'roles': instance.roles,
      'language': instance.language,
      'firstLogin': instance.firstLogin,
      'login': instance.login,
      'organizationName': instance.organizationName,
      'trainingProviderName': instance.trainingProviderName,
      'instructor': instance.instructor,
      'trainingProviderOrganizationId': instance.trainingProviderOrganizationId,
      'initialModalWindow': instance.initialModalWindow,
      'nationalId': instance.nationalId,
      'iqama_id': instance.iqamaId,
      'phone': instance.phone,
    };

Instructor _$InstructorFromJson(Map<String, dynamic> json) => Instructor(
      instructorId: json['instructorId'] as String,
      organizationId: json['organizationId'] as String,
      organizationName: json['organizationName'] as String,
      permission: json['permission'] as String,
    );

Map<String, dynamic> _$InstructorToJson(Instructor instance) => <String, dynamic>{
      'instructorId': instance.instructorId,
      'organizationId': instance.organizationId,
      'organizationName': instance.organizationName,
      'permission': instance.permission,
    };
