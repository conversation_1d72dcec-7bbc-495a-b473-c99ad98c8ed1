import 'dart:typed_data';

import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel extends Equatable {
  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.roles,
    required this.phone,
    this.birthdate,
    this.birthdateFromQiwa,
    this.nameFromQiwa,
    this.language,
    this.firstLogin,
    this.login,
    this.organizationName,
    this.trainingProviderName,
    this.instructor,
    this.trainingProviderOrganizationId,
    this.initialModalWindow,
    this.nationalId,
    this.iqamaId,
    this.avatar,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  final String id;
  @JsonKey(defaultValue: '')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: '')
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(fromJson: stringDateTimeFormat)
  final String? birthdate;
  final bool? birthdateFromQiwa;
  final bool? nameFromQiwa;
  @<PERSON><PERSON><PERSON><PERSON>(defaultValue: [])
  final List<String> roles;
  final String? language;
  final bool? firstLogin;
  final String? login;
  final String? organizationName;
  final String? trainingProviderName;
  final Instructor? instructor;
  final String? trainingProviderOrganizationId;
  final String? initialModalWindow;
  final int? nationalId;
  @JsonKey(name: 'iqama_id')
  final String? iqamaId;
  @JsonKey(defaultValue: '')
  final String phone;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Uint8List? avatar;

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        birthdate,
        birthdateFromQiwa,
        nameFromQiwa,
        roles,
        language,
        firstLogin,
        login,
        organizationName,
        trainingProviderName,
        instructor,
        trainingProviderOrganizationId,
        initialModalWindow,
        nationalId,
        iqamaId,
        phone,
        avatar,
      ];
}

@JsonSerializable()
class Instructor extends Equatable {
  const Instructor({
    required this.instructorId,
    required this.organizationId,
    required this.organizationName,
    required this.permission,
  });

  factory Instructor.fromJson(Map<String, dynamic> json) => _$InstructorFromJson(json);
  final String instructorId;
  final String organizationId;
  final String organizationName;
  final String permission;

  @override
  List<Object?> get props => [instructorId, organizationId, organizationName, permission];
}
