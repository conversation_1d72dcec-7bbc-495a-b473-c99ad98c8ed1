import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/domain/repository/user_repository.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

part 'user_bloc.freezed.dart';

part 'user_event.dart';

part 'user_state.dart';

@lazySingleton
class UserBloc extends Bloc<UserEvent, UserState> {
  UserBloc({required UserRepository userRepository})
      : _userRepository = userRepository,
        super(const UserState()) {
    on<_GetUserData>(_getUserData);
    on<_PickProfileImage>(_pickProfileImage);
    on<_UpdateProfileImage>(_updateProfileImage);
    on<_DeleteProfileImage>(_deleteProfileImage);
    on<_CropInProgress>(_cropInProgress);
    on<_ResetUserData>(_resetUserData);
  }

  final UserRepository _userRepository;

  Future<void> _pickProfileImage(_PickProfileImage event, Emitter<UserState> emit) async {
    final image = await ImagePicker().pickImage(source: ImageSource.gallery);

    if (image != null) {
      emit(state.copyWith(userAvatarError: ''));

      router.pop(); //close the modal

      ///Checks and shows error if the image format is not jpg, jpeg or png
      if (!(image.path.endsWith(Constants.jpg) ||
          image.path.endsWith(Constants.jpeg) ||
          image.path.endsWith(Constants.png))) {
        return emit(state.copyWith(userAvatarError: LocaleKeys.unsupportedFileFormat.tr()));
      }

      ///Checks and shows error if the image size is greater than 2MB
      if (await image.length() > 2 * 1024 * 1024) {
        return emit(state.copyWith(userAvatarError: LocaleKeys.maximumFileSize2mb.tr()));
      }

      emit(state.copyWith(imageForCropping: image, userAvatarError: ''));
      router.pushNamed(Routes.imageCropperPage.name);
    }
  }

  Future<void> _updateProfileImage(_UpdateProfileImage event, Emitter<UserState> emit) async {
    emit(state.copyWith(isUserAvatarLoading: true, userAvatarError: ''));

    await _userRepository.uploadUserAvatar(event.croppedImage).errorHandler(
      onSuccess: (avatar) async {
        emit(state.copyWith(userAvatar: avatar, isUserAvatarLoading: false));
      },
      onError: (error) {
        emit(state.copyWith(userAvatarError: error, isUserAvatarLoading: false));
      },
    );
  }

  Future<void> _getUserData(_GetUserData event, Emitter<UserState> emit) async {
    emit(
      state.copyWith(
        isUserDataLoading: true,
        isUserAvatarLoading: true,
        userDataError: '',
        userAvatarError: '',
      ),
    );

    /// by using await I didn't want to block next requests. this way we can request all data in parallel
    final futures = [
      _userRepository.getUserAvatar().errorHandler(
        onSuccess: (avatar) async {
          emit(state.copyWith(userAvatar: avatar, isUserAvatarLoading: false));
        },
        onError: (error) {
          emit(state.copyWith(userAvatarError: error, isUserAvatarLoading: false));
        },
      ),
      _userRepository.getUserData().errorHandler(
        onSuccess: (userData) async {
          emit(state.copyWith(userData: userData, isUserDataLoading: false));
        },
        onError: (error) {
          emit(state.copyWith(userDataError: error, isUserDataLoading: false));
        },
      ),
    ];

    await Future.wait(futures);
  }

  Future<void> _resetUserData(_ResetUserData event, Emitter<UserState> emit) async =>
      emit(const UserState());

  Future<void> _deleteProfileImage(_DeleteProfileImage event, Emitter<UserState> emit) async {
    router.pop(); //close the modal

    if (state.userAvatar == null || (state.userAvatar?.isEmpty ?? true)) return;

    emit(state.copyWith(isUserAvatarLoading: true, userAvatarError: ''));

    await _userRepository.deleteUserAvatar().errorHandler(
          onSuccess: (_) async =>
              emit(state.copyWith(userAvatar: null, isUserAvatarLoading: false)),
          onError: (error) =>
              emit(state.copyWith(userAvatarError: error, isUserAvatarLoading: false)),
        );
  }

  /// this event is used to show loading overlay while cropping image
  Future<void> _cropInProgress(_CropInProgress event, Emitter<UserState> emit) async {
    emit(state.copyWith(isCropInProgress: event.isCropInProgress));
  }
}
