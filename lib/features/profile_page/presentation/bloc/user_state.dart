part of 'user_bloc.dart';

@freezed
abstract class UserState with _$UserState {
  const factory UserState({
    @Default(true) bool isUserAvatarLoading,
    @Default(true) bool isUserDataLoading,
    @Default(false) bool isCropInProgress,
    UserModel? userData,
    String? userAvatar,
    XFile? imageForCropping,
    @Default('') String userAvatarError,
    @Default('') String userDataError,
  }) = _UserDataLoaded;
}
