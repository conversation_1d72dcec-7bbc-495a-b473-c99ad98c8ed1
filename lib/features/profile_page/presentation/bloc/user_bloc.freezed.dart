// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) || (other.runtimeType == runtimeType && other is UserEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UserEvent()';
  }
}

/// @nodoc
class $UserEventCopyWith<$Res> {
  $UserEventCopyWith(UserEvent _, $Res Function(UserEvent) __);
}

/// @nodoc

class _GetUserData implements UserEvent {
  const _GetUserData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) || (other.runtimeType == runtimeType && other is _GetUserData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UserEvent.getUserData()';
  }
}

/// @nodoc

class _ResetUserData implements UserEvent {
  const _ResetUserData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) || (other.runtimeType == runtimeType && other is _ResetUserData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UserEvent.resetUserData()';
  }
}

/// @nodoc

class _PickProfileImage implements UserEvent {
  const _PickProfileImage();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PickProfileImage);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UserEvent.pickProfileImage()';
  }
}

/// @nodoc

class _DeleteProfileImage implements UserEvent {
  const _DeleteProfileImage();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _DeleteProfileImage);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UserEvent.deleteProfileImage()';
  }
}

/// @nodoc

class _CropInProgress implements UserEvent {
  const _CropInProgress({required this.isCropInProgress});

  final bool isCropInProgress;

  /// Create a copy of UserEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CropInProgressCopyWith<_CropInProgress> get copyWith =>
      __$CropInProgressCopyWithImpl<_CropInProgress>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CropInProgress &&
            (identical(other.isCropInProgress, isCropInProgress) ||
                other.isCropInProgress == isCropInProgress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isCropInProgress);

  @override
  String toString() {
    return 'UserEvent.cropInProgress(isCropInProgress: $isCropInProgress)';
  }
}

/// @nodoc
abstract mixin class _$CropInProgressCopyWith<$Res> implements $UserEventCopyWith<$Res> {
  factory _$CropInProgressCopyWith(_CropInProgress value, $Res Function(_CropInProgress) _then) =
      __$CropInProgressCopyWithImpl;
  @useResult
  $Res call({bool isCropInProgress});
}

/// @nodoc
class __$CropInProgressCopyWithImpl<$Res> implements _$CropInProgressCopyWith<$Res> {
  __$CropInProgressCopyWithImpl(this._self, this._then);

  final _CropInProgress _self;
  final $Res Function(_CropInProgress) _then;

  /// Create a copy of UserEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isCropInProgress = null,
  }) {
    return _then(_CropInProgress(
      isCropInProgress: null == isCropInProgress
          ? _self.isCropInProgress
          : isCropInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _UpdateProfileImage implements UserEvent {
  const _UpdateProfileImage({required this.croppedImage});

  final MemoryImage croppedImage;

  /// Create a copy of UserEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateProfileImageCopyWith<_UpdateProfileImage> get copyWith =>
      __$UpdateProfileImageCopyWithImpl<_UpdateProfileImage>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateProfileImage &&
            (identical(other.croppedImage, croppedImage) || other.croppedImage == croppedImage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, croppedImage);

  @override
  String toString() {
    return 'UserEvent.updateProfileImage(croppedImage: $croppedImage)';
  }
}

/// @nodoc
abstract mixin class _$UpdateProfileImageCopyWith<$Res> implements $UserEventCopyWith<$Res> {
  factory _$UpdateProfileImageCopyWith(
          _UpdateProfileImage value, $Res Function(_UpdateProfileImage) _then) =
      __$UpdateProfileImageCopyWithImpl;
  @useResult
  $Res call({MemoryImage croppedImage});
}

/// @nodoc
class __$UpdateProfileImageCopyWithImpl<$Res> implements _$UpdateProfileImageCopyWith<$Res> {
  __$UpdateProfileImageCopyWithImpl(this._self, this._then);

  final _UpdateProfileImage _self;
  final $Res Function(_UpdateProfileImage) _then;

  /// Create a copy of UserEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? croppedImage = null,
  }) {
    return _then(_UpdateProfileImage(
      croppedImage: null == croppedImage
          ? _self.croppedImage
          : croppedImage // ignore: cast_nullable_to_non_nullable
              as MemoryImage,
    ));
  }
}

/// @nodoc
mixin _$UserState {
  bool get isUserAvatarLoading;
  bool get isUserDataLoading;
  bool get isCropInProgress;
  UserModel? get userData;
  String? get userAvatar;
  XFile? get imageForCropping;
  String get userAvatarError;
  String get userDataError;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserStateCopyWith<UserState> get copyWith =>
      _$UserStateCopyWithImpl<UserState>(this as UserState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserState &&
            (identical(other.isUserAvatarLoading, isUserAvatarLoading) ||
                other.isUserAvatarLoading == isUserAvatarLoading) &&
            (identical(other.isUserDataLoading, isUserDataLoading) ||
                other.isUserDataLoading == isUserDataLoading) &&
            (identical(other.isCropInProgress, isCropInProgress) ||
                other.isCropInProgress == isCropInProgress) &&
            (identical(other.userData, userData) || other.userData == userData) &&
            (identical(other.userAvatar, userAvatar) || other.userAvatar == userAvatar) &&
            (identical(other.imageForCropping, imageForCropping) ||
                other.imageForCropping == imageForCropping) &&
            (identical(other.userAvatarError, userAvatarError) ||
                other.userAvatarError == userAvatarError) &&
            (identical(other.userDataError, userDataError) ||
                other.userDataError == userDataError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isUserAvatarLoading, isUserDataLoading,
      isCropInProgress, userData, userAvatar, imageForCropping, userAvatarError, userDataError);

  @override
  String toString() {
    return 'UserState(isUserAvatarLoading: $isUserAvatarLoading, isUserDataLoading: $isUserDataLoading, isCropInProgress: $isCropInProgress, userData: $userData, userAvatar: $userAvatar, imageForCropping: $imageForCropping, userAvatarError: $userAvatarError, userDataError: $userDataError)';
  }
}

/// @nodoc
abstract mixin class $UserStateCopyWith<$Res> {
  factory $UserStateCopyWith(UserState value, $Res Function(UserState) _then) =
      _$UserStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isUserAvatarLoading,
      bool isUserDataLoading,
      bool isCropInProgress,
      UserModel? userData,
      String? userAvatar,
      XFile? imageForCropping,
      String userAvatarError,
      String userDataError});
}

/// @nodoc
class _$UserStateCopyWithImpl<$Res> implements $UserStateCopyWith<$Res> {
  _$UserStateCopyWithImpl(this._self, this._then);

  final UserState _self;
  final $Res Function(UserState) _then;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isUserAvatarLoading = null,
    Object? isUserDataLoading = null,
    Object? isCropInProgress = null,
    Object? userData = freezed,
    Object? userAvatar = freezed,
    Object? imageForCropping = freezed,
    Object? userAvatarError = null,
    Object? userDataError = null,
  }) {
    return _then(_self.copyWith(
      isUserAvatarLoading: null == isUserAvatarLoading
          ? _self.isUserAvatarLoading
          : isUserAvatarLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUserDataLoading: null == isUserDataLoading
          ? _self.isUserDataLoading
          : isUserDataLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCropInProgress: null == isCropInProgress
          ? _self.isCropInProgress
          : isCropInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      userData: freezed == userData
          ? _self.userData
          : userData // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      userAvatar: freezed == userAvatar
          ? _self.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      imageForCropping: freezed == imageForCropping
          ? _self.imageForCropping
          : imageForCropping // ignore: cast_nullable_to_non_nullable
              as XFile?,
      userAvatarError: null == userAvatarError
          ? _self.userAvatarError
          : userAvatarError // ignore: cast_nullable_to_non_nullable
              as String,
      userDataError: null == userDataError
          ? _self.userDataError
          : userDataError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _UserDataLoaded implements UserState {
  const _UserDataLoaded(
      {this.isUserAvatarLoading = true,
      this.isUserDataLoading = true,
      this.isCropInProgress = false,
      this.userData,
      this.userAvatar,
      this.imageForCropping,
      this.userAvatarError = '',
      this.userDataError = ''});

  @override
  @JsonKey()
  final bool isUserAvatarLoading;
  @override
  @JsonKey()
  final bool isUserDataLoading;
  @override
  @JsonKey()
  final bool isCropInProgress;
  @override
  final UserModel? userData;
  @override
  final String? userAvatar;
  @override
  final XFile? imageForCropping;
  @override
  @JsonKey()
  final String userAvatarError;
  @override
  @JsonKey()
  final String userDataError;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserDataLoadedCopyWith<_UserDataLoaded> get copyWith =>
      __$UserDataLoadedCopyWithImpl<_UserDataLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserDataLoaded &&
            (identical(other.isUserAvatarLoading, isUserAvatarLoading) ||
                other.isUserAvatarLoading == isUserAvatarLoading) &&
            (identical(other.isUserDataLoading, isUserDataLoading) ||
                other.isUserDataLoading == isUserDataLoading) &&
            (identical(other.isCropInProgress, isCropInProgress) ||
                other.isCropInProgress == isCropInProgress) &&
            (identical(other.userData, userData) || other.userData == userData) &&
            (identical(other.userAvatar, userAvatar) || other.userAvatar == userAvatar) &&
            (identical(other.imageForCropping, imageForCropping) ||
                other.imageForCropping == imageForCropping) &&
            (identical(other.userAvatarError, userAvatarError) ||
                other.userAvatarError == userAvatarError) &&
            (identical(other.userDataError, userDataError) ||
                other.userDataError == userDataError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isUserAvatarLoading, isUserDataLoading,
      isCropInProgress, userData, userAvatar, imageForCropping, userAvatarError, userDataError);

  @override
  String toString() {
    return 'UserState(isUserAvatarLoading: $isUserAvatarLoading, isUserDataLoading: $isUserDataLoading, isCropInProgress: $isCropInProgress, userData: $userData, userAvatar: $userAvatar, imageForCropping: $imageForCropping, userAvatarError: $userAvatarError, userDataError: $userDataError)';
  }
}

/// @nodoc
abstract mixin class _$UserDataLoadedCopyWith<$Res> implements $UserStateCopyWith<$Res> {
  factory _$UserDataLoadedCopyWith(_UserDataLoaded value, $Res Function(_UserDataLoaded) _then) =
      __$UserDataLoadedCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isUserAvatarLoading,
      bool isUserDataLoading,
      bool isCropInProgress,
      UserModel? userData,
      String? userAvatar,
      XFile? imageForCropping,
      String userAvatarError,
      String userDataError});
}

/// @nodoc
class __$UserDataLoadedCopyWithImpl<$Res> implements _$UserDataLoadedCopyWith<$Res> {
  __$UserDataLoadedCopyWithImpl(this._self, this._then);

  final _UserDataLoaded _self;
  final $Res Function(_UserDataLoaded) _then;

  /// Create a copy of UserState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isUserAvatarLoading = null,
    Object? isUserDataLoading = null,
    Object? isCropInProgress = null,
    Object? userData = freezed,
    Object? userAvatar = freezed,
    Object? imageForCropping = freezed,
    Object? userAvatarError = null,
    Object? userDataError = null,
  }) {
    return _then(_UserDataLoaded(
      isUserAvatarLoading: null == isUserAvatarLoading
          ? _self.isUserAvatarLoading
          : isUserAvatarLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isUserDataLoading: null == isUserDataLoading
          ? _self.isUserDataLoading
          : isUserDataLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCropInProgress: null == isCropInProgress
          ? _self.isCropInProgress
          : isCropInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      userData: freezed == userData
          ? _self.userData
          : userData // ignore: cast_nullable_to_non_nullable
              as UserModel?,
      userAvatar: freezed == userAvatar
          ? _self.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String?,
      imageForCropping: freezed == imageForCropping
          ? _self.imageForCropping
          : imageForCropping // ignore: cast_nullable_to_non_nullable
              as XFile?,
      userAvatarError: null == userAvatarError
          ? _self.userAvatarError
          : userAvatarError // ignore: cast_nullable_to_non_nullable
              as String,
      userDataError: null == userDataError
          ? _self.userDataError
          : userDataError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
