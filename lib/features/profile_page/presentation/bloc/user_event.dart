part of 'user_bloc.dart';

@freezed
class UserEvent with _$UserEvent {
  const factory UserEvent.getUserData() = _GetUserData;
  const factory UserEvent.resetUserData() = _ResetUserData;
  const factory UserEvent.pickProfileImage() = _PickProfileImage;
  const factory UserEvent.deleteProfileImage() = _DeleteProfileImage;
  const factory UserEvent.cropInProgress({required bool isCropInProgress}) = _CropInProgress;
  const factory UserEvent.updateProfileImage({required MemoryImage croppedImage}) =
      _UpdateProfileImage;
}
