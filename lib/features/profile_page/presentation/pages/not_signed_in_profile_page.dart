import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class NotSignedInProfilePage extends StatelessWidget {
  const NotSignedInProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const NspAppBar(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              backgroundColor: AppColors.accentExtraLight,
              radius: 30,
              child: Image.asset(
                width: 24,
                height: 24,
                AssetsPath.peopleIcon,
                color: AppColors.greenAccentPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Text(LocaleKeys.nonLoggedInPageTitle.tr(), style: context.textTheme.textLarge.semiBold),
            const SizedBox(height: 8),
            Text(
              LocaleKeys.nonLoggedInPageBody.tr(),
              style: context.textTheme.textSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AppButton(
              onTap: () => router.pushNamed(Routes.login.name),
              buttonText: LocaleKeys.header_signIn.tr(),
            ),
          ],
        ),
      ),
    );
  }
}
