import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/auth/presentation/widgets/change_app_language_widget.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/logout_button.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class ProfileAuthenticatedView extends StatefulWidget {
  const ProfileAuthenticatedView({super.key});

  @override
  State<ProfileAuthenticatedView> createState() => _ProfileAuthenticatedViewState();
}

class _ProfileAuthenticatedViewState extends State<ProfileAuthenticatedView> {
  final _refreshController = RefreshController();
  late UserBloc userBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    userBloc = context.read<UserBloc>();

    if (userBloc.state.userData == null) {
      userBloc.add(const UserEvent.getUserData());
    }
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is LogoutSuccess) {
          unawaited(router.pushNamed(Routes.qiwaWebView.name, extra: state.token));
        }
        if (state is LogoutError) {
          showAppToast(context, message: state.errorMsg);
        }
      },
      builder: (context, state) {
        return AppLoadingOverlay(
          isLoading: state is AuthLoading,
          child: Scaffold(
            appBar: const NspAppBar(),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SmartRefresher(
                controller: _refreshController,
                onRefresh: () {
                  if (!userBloc.state.isUserDataLoading || !userBloc.state.isUserAvatarLoading) {
                    userBloc.add(const UserEvent.getUserData());
                  }
                },
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ProfileHeader(refreshController: _refreshController),

                      const SizedBox(height: 24),
                      Text(
                        LocaleKeys.account_settings.tr(),
                        style: context.textTheme.textXSmall.greyAdditional.medium,
                      ),
                      const SizedBox(height: 8),

                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                        onTap: () => router.pushNamed(Routes.personalInfoPage.name),
                        title: Text(
                          LocaleKeys.personal_info.tr(),
                          style: context.textTheme.textMedium.medium,
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          color: AppColors.neutralBlack,
                          size: 16,
                        ),
                      ),
                      const AppDivider(),
                      const SizedBox(height: 16),

                      const ChangeAppLanguageWidget(),
                      const SizedBox(height: 16),

                      const LogoutButton(),
                      //
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
