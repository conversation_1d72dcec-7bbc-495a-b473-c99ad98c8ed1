import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/pages/not_signed_in_profile_page.dart';
import 'package:national_skills_platform/features/profile_page/presentation/pages/profile_authenticated_view.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: MultiBlocProvider(
        providers: [
          BlocProvider.value(value: GetIt.instance.get<UserBloc>()),
          BlocProvider.value(value: GetIt.instance.get<AuthBloc>()),
        ],
        child: StreamBuilder<bool>(
          stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
          builder: (context, snapshot) {
            if (snapshot.data == true) {
              return const ProfileAuthenticatedView();
            }

            return const NotSignedInProfilePage();
          },
        ),
      ),
    );
  }
}
