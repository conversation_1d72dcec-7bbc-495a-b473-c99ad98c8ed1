import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class DeletePhotoModal extends StatelessWidget {
  const DeletePhotoModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          const SizedBox(height: 24),

          CircleAvatar(
            backgroundColor: AppColors.orangeAccentLight,
            radius: 39,
            child: Image.asset(width: 40, height: 40, AssetsPath.warningIcon),
          ),

          const SizedBox(height: 24),

          Text(
            LocaleKeys.sureToDeleteThePhoto.tr(),
            style: context.textTheme.textLarge.semiBold,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          AppButton(
            onTap: () => GetIt.instance<UserBloc>().add(const UserEvent.deleteProfileImage()),
            buttonText: LocaleKeys.yesDelete.tr(),
            backgroundColor: AppColors.statusWarning,
            textStyle: context.textTheme.textSmall.semiBold.white,
          ),

          const SizedBox(height: 16),

          AppButton(
            onTap: () => router.pop(),
            buttonText: LocaleKeys.cancel.tr(),
            textStyle: context.textTheme.textSmall.semiBold.copyWith(
              color: AppColors.greenAccentPrimary,
            ),
            backgroundColor: AppColors.uiBackgroundPrimary,
            borderColor: AppColors.accentLight,
          ),

          const SizedBox(height: 45),

          //
        ],
      ),
    );
  }
}
