import 'dart:io';

import 'package:custom_image_crop/custom_image_crop.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_loading_overlay.dart';
import 'package:national_skills_platform/features/shared/ui_components/dummy_widget.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class ImageCropView extends StatefulWidget {
  const ImageCropView({super.key});

  @override
  State<ImageCropView> createState() => _ImageCropViewState();
}

class _ImageCropViewState extends State<ImageCropView> {
  late CustomImageCropController controller;
  late UserBloc userBloc;

  @override
  void initState() {
    super.initState();
    controller = CustomImageCropController();

    //Below is the workaround for the issue where portrait images on init fits by height and not by width
    Future.delayed(const Duration(milliseconds: 100), () {
      controller.addTransition(CropImageData(scale: 1.00001));
    });

    userBloc = GetIt.instance<UserBloc>();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTestState = Platform.environment.containsKey(Constants.flutterTest);

    return BlocBuilder<UserBloc, UserState>(
      bloc: userBloc,
      buildWhen: (prev, curr) => prev.isCropInProgress != curr.isCropInProgress,
      builder: (context, state) {
        final imageForCropping = state.imageForCropping;

        return AppLoadingOverlay(
          isLoading: state.isCropInProgress,
          child: Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: false,
              title: Text(LocaleKeys.cropImage.tr(), style: context.textTheme.textLarge.semiBold),
            ),
            body: imageForCropping != null
                ? CustomImageCrop(
                    canRotate: false,
                    cropController: controller,
                    image: FileImage(File(imageForCropping.path)),
                    backgroundColor: Colors.black,
                    drawPath: (path, {pathPaint}) => CustomPaint(
                      painter: SolidCropPathPainter(
                        path,
                        Paint()
                          ..color = Colors.black
                          ..strokeWidth = 0
                          ..style = PaintingStyle.stroke
                          ..strokeJoin = StrokeJoin.round,
                      ),
                    ),
                    maskShape: CustomCropShape.Circle,
                    ratio: Ratio(width: 100, height: 100),
                    forceInsideCropArea: true,
                    shape: CustomCropShape.Square,
                  )
                : isTestState
                    ? const DummyWidget()
                    : const SizedBox(),
            bottomNavigationBar: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.paddingOf(context).bottom == 0
                    ? 8
                    : MediaQuery.paddingOf(context).bottom,
                top: 8,
                left: 16,
                right: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: AppButton(
                      onTap: router.pop,
                      buttonText: LocaleKeys.trainingBuilder_tests_cancel.tr(),
                      backgroundColor: AppColors.uiBackgroundPrimary,
                      textStyle: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                      borderColor: AppColors.accentLight,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: AppButton(
                      onTap: () async {
                        userBloc.add(const UserEvent.cropInProgress(isCropInProgress: true));
                        final croppedImage = await controller.onCropImage();
                        userBloc.add(const UserEvent.cropInProgress(isCropInProgress: false));

                        if (croppedImage == null) return;

                        userBloc.add(UserEvent.updateProfileImage(croppedImage: croppedImage));
                        router.pop();
                      },
                      buttonText: LocaleKeys.cropPost.tr(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
