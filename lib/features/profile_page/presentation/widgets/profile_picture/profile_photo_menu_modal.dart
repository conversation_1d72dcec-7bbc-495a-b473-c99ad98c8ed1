import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/delete_photo_modal.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class ProfilePhotoMenuModal extends StatelessWidget {
  const ProfilePhotoMenuModal({super.key});

  @override
  Widget build(BuildContext context) {
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);
    final userAvatar = context.read<UserBloc>().state.userAvatar ?? '';

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: SharedDecoration.borderTopLeftRight10,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          const SizedBox(height: 6),

          Text(LocaleKeys.manageList.tr(), style: context.textTheme.textLarge.semiBold),

          const SizedBox(height: 8),

          ListTile(
            onTap: () => context.read<UserBloc>().add(const UserEvent.pickProfileImage()),
            contentPadding: EdgeInsets.zero,
            dense: true,
            title: Text(
              LocaleKeys.choosePhotoFromTheLibrary.tr(),
              style: context.textTheme.textSmall.medium,
            ),
            trailing: const SizedBox(),
          ),
          if (userAvatar.isNotEmpty) ...[
            const AppDivider(),
            ListTile(
              onTap: () {
                router.pop();
                showModalBottomSheet(
                  backgroundColor: Colors.white,
                  useRootNavigator: useRootNavigator,
                  shape: const RoundedRectangleBorder(
                    borderRadius: SharedDecoration.borderTopLeftRight10,
                  ),
                  context: context,
                  builder: (context) => const DeletePhotoModal(),
                );
              },
              contentPadding: EdgeInsets.zero,
              dense: true,
              title: Text(
                LocaleKeys.removeCurrentPhoto.tr(),
                style: context.textTheme.textSmall.statusWarning,
              ),
              trailing: const SizedBox(),
            ),
          ],
          const SizedBox(height: 34),
          //
        ],
      ),
    );
  }
}
