import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class PersonalInfoPage extends StatelessWidget {
  const PersonalInfoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const AppBackButton(),
        title: Text(LocaleKeys.personal_info.tr(), style: context.textTheme.textLarge.semiBold),
        bottom: const PreferredSize(preferredSize: Size.fromHeight(0), child: AppDivider()),
      ),
      body: BlocConsumer<UserBloc, UserState>(
        bloc: GetIt.instance.get<UserBloc>(),
        listener: (context, state) {
          if (state.userDataError.isNotEmpty) {
            showAppToast(context, message: state.userDataError);
          }
        },
        builder: (context, state) {
          if (state.userDataError.isNotEmpty) return const SizedBox.shrink();

          if (state.userData != null) {
            final UserModel userData = state.userData!;

            return SingleChildScrollView(
              child: Column(
                children: [
                  ListTile(
                    title: Text(
                      LocaleKeys.profileAndSettings_name.tr(),
                      style: context.textTheme.textMedium.medium,
                    ),
                    subtitle: Text(userData.name, style: context.textTheme.textMedium),
                  ),
                  ListTile(
                    title: Text(
                      LocaleKeys.profileAndSettings_nationalId.tr(),
                      style: context.textTheme.textMedium.medium,
                    ),
                    subtitle: Text('${userData.nationalId}', style: context.textTheme.textMedium),
                  ),
                  ListTile(
                    title: Text(
                      LocaleKeys.profileAndSettings_phoneNumber.tr(),
                      style: context.textTheme.textMedium.medium,
                    ),
                    subtitle: Text(userData.phone, style: context.textTheme.textMedium),
                  ),
                  ListTile(
                    title: Text(
                      LocaleKeys.profileAndSettings_email.tr(),
                      style: context.textTheme.textMedium.medium,
                    ),
                    subtitle: Text(userData.email, style: context.textTheme.textMedium),
                  ),
                  if (userData.birthdate != null)
                    ListTile(
                      title: Text(
                        LocaleKeys.profileAndSettings_birthDate.tr(),
                        style: context.textTheme.textMedium.medium,
                      ),
                      subtitle: Text(userData.birthdate ?? '', style: context.textTheme.textMedium),
                    ),
                ],
              ),
            );
          }

          return const BuildLoader();
        },
      ),
    );
  }
}
