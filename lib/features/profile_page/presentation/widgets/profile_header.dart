import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/profile_photo_menu_modal.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({required this.refreshController, super.key});

  final RefreshController refreshController;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UserBloc, UserState>(
      bloc: context.read<UserBloc>(),
      listener: listener,
      builder: (context, state) {
        final userAvatar = state.userAvatar ?? '';

        return Align(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              Stack(
                children: [
                  AppShimmer(
                    isLoading: state.isUserAvatarLoading,
                    child: CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.white,
                      backgroundImage: const AssetImage(AssetsPath.emptyAvatarImage),
                      child: userAvatar.isNotEmpty
                          ? AspectRatio(
                              aspectRatio: 1,
                              child: ClipOval(
                                child: CachedNetworkImage(
                                  imageUrl: userAvatar,
                                  fit: BoxFit.cover,
                                  placeholder: (_, __) => const ShimmerPlaceholder(height: 50),
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                  ),
                  if (!state.isUserAvatarLoading)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () => showProfilePhotoModal(context),
                        child: const CircleAvatar(
                          radius: 16,
                          backgroundColor: Colors.white,
                          backgroundImage: AssetImage(AssetsPath.uploadAvatarIcon),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 20),
              if (state.isUserDataLoading)
                const ShimmerPlaceholder(width: 100, height: 20, borderRadius: 8)
              else
                Text(state.userData?.name ?? '', style: context.textTheme.textLarge.semiBold),
              Text(LocaleKeys.header_trainee.tr(), style: context.textTheme.textSmall.greyPrimary),
            ],
          ),
        );
      },
    );
  }

  void showProfilePhotoModal(BuildContext context) => showModalBottomSheet(
        useRootNavigator: !Platform.environment.containsKey(Constants.flutterTest),
        shape: const RoundedRectangleBorder(borderRadius: SharedDecoration.borderTopLeftRight10),
        context: context,
        builder: (_) => BlocProvider.value(
          value: context.read<UserBloc>(),
          child: const ProfilePhotoMenuModal(),
        ),
      );

  void listener(BuildContext context, UserState state) {
    if (state.userAvatarError.isNotEmpty) {
      showAppToast(context, message: state.userAvatarError);
    }
    pullToRefreshHandler(state);
  }

  void pullToRefreshHandler(UserState state) {
    if (!state.isUserDataLoading && !state.isUserAvatarLoading) {
      refreshController.refreshCompleted();
    } else if (state.userDataError.isNotEmpty || state.userAvatarError.isNotEmpty) {
      refreshController.refreshFailed();
    }
  }
}
