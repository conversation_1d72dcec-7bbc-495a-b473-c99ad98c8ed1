import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/logout_bottom_sheet.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class LogoutButton extends StatefulWidget {
  const LogoutButton({super.key});

  @override
  State<LogoutButton> createState() => _LogoutButtonState();
}

class _LogoutButtonState extends State<LogoutButton> {
  late AuthBloc authBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    authBloc = context.read<AuthBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    return IntrinsicWidth(
      child: AppButton(
        padding: EdgeInsets.zero,
        leading: Image.asset(AssetsPath.logoutIcon, height: 20),
        backgroundColor: Colors.transparent,
        onTap: () => showModalBottomSheet(
          backgroundColor: Colors.white,
          useRootNavigator: useRootNavigator,
          shape: const RoundedRectangleBorder(
            borderRadius: SharedDecoration.borderTopLeftRight10,
          ),
          context: context,
          builder: (context) => const LogoutBottomSheet(),
        ).then((value) {
          if (value == true) authBloc.add(const Logout());
        }),
        buttonText: LocaleKeys.header_logOut.tr(),
        textStyle: context.textTheme.textSmall.semiBold.statusWarning,
      ),
    );
  }
}
