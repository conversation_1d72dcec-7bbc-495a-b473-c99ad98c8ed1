import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

@injectable
class UpdateStudyStreams {
  static const _statusSortOrder = {
    MeetingStatus.Live: 0,
    MeetingStatus.Upcoming: 1,
    MeetingStatus.Passed: 2,
    MeetingStatus.Rescheduled: 3,
    MeetingStatus.Cancelled: 4,
  };

  // Fallback value for unexpected statuses
  static const _defaultSortOrder = 999;

  List<StudyStream?> call(TrainingDetailsModel trainingDetailsModel) {
    if (trainingDetailsModel.studyStreams.isEmpty) return [];

    final filtered = trainingDetailsModel.studyStreams.where(checkStreamAvailability).toList();

    final updatedStreams = filtered.map((studyStream) {
      final updatedMeetings = studyStream?.liveSession?.meetings?.map((meeting) {
        final updatedMeetingStatus = getLiveSessionStatus(meeting);
        return meeting.copyWith(meetingStatus: updatedMeetingStatus);
      }).toList();

      // Sorts the meetings in this order: live → upcoming → passed → cancelled
      // and if status is identical, sorts by date (closest at the top)
      updatedMeetings?.sort((a, b) {
        // Assign default sort order if the status isn't explicitly in the map
        final aOrder = _statusSortOrder[a.meetingStatus] ?? _defaultSortOrder;
        final bOrder = _statusSortOrder[b.meetingStatus] ?? _defaultSortOrder;

        // Compare by status priority
        final statusComparison = aOrder.compareTo(bOrder);

        if (statusComparison != 0) {
          return statusComparison;
        }

        // If statuses are the same, compare by date (closest first)
        return a.startDate.compareTo(b.startDate);
      });

      final updatedLiveSession = studyStream?.liveSession?.copyWith(meetings: updatedMeetings);
      return studyStream?.copyWith(liveSession: updatedLiveSession);
    }).toList();

    return updatedStreams;
  }

  bool checkStreamAvailability(StudyStream? stream) {
    if (stream == null) return false;

    final closedForEnrollmentDate = stream.closedForEnrollmentDate;
    if (closedForEnrollmentDate == null) return false;

    ///To get the current KSA time we get current UTC time and add 3 hours to it
    final nowKSA = truncateToSeconds(DateTime.now().toUtc().add(const Duration(hours: 3)));

    final isAvailable = (truncateToSeconds(closedForEnrollmentDate).isAfter(nowKSA)) &&
        (stream.noLimits || stream.currentNumberOfParticipants < stream.maxNumberOfParticipants);

    return isAvailable;
  }

  MeetingStatus getLiveSessionStatus(Meeting meeting) {
    if (meeting.cancelled) return MeetingStatus.Cancelled;

    if (meeting.cardNotes != null && meeting.cardNotes!.isNotEmpty) {
      return MeetingStatus.Rescheduled;
    }

    final currentDateTime = DateTime.now().toUtc();
    final startDateTimeUTC = meeting.startDate.toUtc();
    if (meeting.status == Constants.PUBLISHED) {
      final liveSessionEndDate = startDateTimeUTC.add(
        Duration(hours: meeting.durationHours, minutes: meeting.durationMinutes),
      );
      final isMeetingPassed = currentDateTime.isAfter(liveSessionEndDate);
      if (isMeetingPassed) {
        return MeetingStatus.Passed;
      }

      final isMeetingLive =
          currentDateTime.isAfter(startDateTimeUTC) && currentDateTime.isBefore(liveSessionEndDate);
      if (isMeetingLive) {
        return MeetingStatus.Live;
      }

      final isMeetingUpcoming = currentDateTime.isBefore(startDateTimeUTC);

      if (isMeetingUpcoming) return MeetingStatus.Upcoming;
    }

    return MeetingStatus.Upcoming;
  }
}
