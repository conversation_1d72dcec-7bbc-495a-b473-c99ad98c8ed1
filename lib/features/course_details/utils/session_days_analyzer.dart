import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SessionDaysAnalyzer {
  const SessionDaysAnalyzer();

  /// Analyzes the study streams to determine if sessions occur on weekends or weekdays
  /// Returns a formatted string indicating the session days pattern
  String getSessionDaysInfo(TrainingDetailsModel? trainingDetailsModel) {
    if (trainingDetailsModel == null) return '';
    if (trainingDetailsModel.studyStreams.isEmpty) return '';

    final pattern = analyzeSessionDays(trainingDetailsModel.studyStreams);
    return _formatSessionDaysPattern(pattern);
  }

  /// Analyzes study streams and returns a SessionDaysPattern indicating
  /// whether sessions occur on weekends, weekdays, or both
  SessionDaysPattern analyzeSessionDays(List<StudyStream?> studyStreams) {
    bool hasWeekendSessions = false;
    bool hasWeekdaySessions = false;

    for (final studyStream in studyStreams) {
      final daysOfWeek = studyStream?.liveSession?.daysOfWeek;
      if (daysOfWeek == null || daysOfWeek.isEmpty) continue;

      if (_isWeekendDay(daysOfWeek)) {
        hasWeekendSessions = true;
      }

      if (_isWeekday(daysOfWeek)) {
        hasWeekdaySessions = true;
      }
    }

    return _determinePattern(hasWeekendSessions, hasWeekdaySessions);
  }

  /// Determines the session days pattern based on weekend and weekday flags
  SessionDaysPattern _determinePattern(bool hasWeekendSessions, bool hasWeekdaySessions) {
    if (hasWeekendSessions && !hasWeekdaySessions) {
      return SessionDaysPattern.weekendsOnly;
    } else if (hasWeekdaySessions && !hasWeekendSessions) {
      return SessionDaysPattern.weekdaysOnly;
    } else if (hasWeekdaySessions && hasWeekendSessions) {
      return SessionDaysPattern.both;
    }
    return SessionDaysPattern.none;
  }

  /// Checks if any of the given days are weekend days (Friday or Saturday)
  bool _isWeekendDay(List<String> days) {
    final weekendDays = [
      LocaleKeys.freq_days_of_week_friday.tr().toUpperCase(),
      LocaleKeys.freq_days_of_week_saturday.tr().toUpperCase(),
    ];

    final upperDays = days.map((day) => day.toUpperCase()).toList();
    return upperDays.any((day) => weekendDays.contains(day));
  }

  /// Checks if any of the given days are weekday days (Sunday through Thursday)
  bool _isWeekday(List<String> days) {
    final weekdayDays = [
      LocaleKeys.freq_days_of_week_sunday.tr().toUpperCase(),
      LocaleKeys.freq_days_of_week_monday.tr().toUpperCase(),
      LocaleKeys.freq_days_of_week_tuesday.tr().toUpperCase(),
      LocaleKeys.freq_days_of_week_wednesday.tr().toUpperCase(),
      LocaleKeys.freq_days_of_week_thursday.tr().toUpperCase(),
    ];

    final upperDays = days.map((day) => day.toUpperCase()).toList();
    return upperDays.any((day) => weekdayDays.contains(day));
  }

  /// Formats the session days pattern into a localized string
  String _formatSessionDaysPattern(SessionDaysPattern pattern) {
    return switch (pattern) {
      SessionDaysPattern.weekendsOnly => ', ${LocaleKeys.freq_weekend.tr().capitalizeFirst()}',
      SessionDaysPattern.weekdaysOnly => ', ${LocaleKeys.freq_weekdays.tr().capitalizeFirst()}',
      SessionDaysPattern.both => '',
      SessionDaysPattern.none => '',
    };
  }
}

/// Represents the pattern of session days in a study stream
enum SessionDaysPattern {
  weekendsOnly,
  weekdaysOnly,
  both,
  none,
}

extension StringExtension on String {
  String capitalizeFirst() {
    if (isEmpty) return this;

    return this[0].toUpperCase() + substring(1);
  }
}
