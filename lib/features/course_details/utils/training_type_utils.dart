import 'package:easy_localization/easy_localization.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

mixin TrainingTypeUtils {
  String getTrainingTypeImage(TrainingDetailsModel training, [StudyStream? stream]) {
    if (training.type == TrainingType.SelfPaced) return AssetsPath.selfPacedIcon;

    if (training.type == TrainingType.InstructorLed) {
      if (stream != null) {
        if (stream.type == StudyStreamType.ONLINE) return AssetsPath.onlineIcon;

        if (stream.type == StudyStreamType.IN_PERSON || stream.location != null) {
          return AssetsPath.inPersonIcon;
        }
      }

      if (training.hasFutureOnline) return AssetsPath.onlineIcon;
      if (training.hasFutureInPerson) return AssetsPath.inPersonIcon;
      return AssetsPath.inPersonIcon;
    }

    return AssetsPath.learningTrackIcon;
  }

  String getTrainingTypeString(TrainingDetailsModel training, [StudyStream? stream]) {
    if (training.type == TrainingType.SelfPaced) return LocaleKeys.self_paced.tr();

    if (training.type == TrainingType.InstructorLed) {
      if (stream != null) {
        if (stream.type == StudyStreamType.ONLINE) return LocaleKeys.trainingDetails_online.tr();

        if (stream.type == StudyStreamType.IN_PERSON || stream.location != null) {
          return LocaleKeys.in_person.tr();
        }
      }

      if (training.hasFutureOnline) return LocaleKeys.trainingDetails_online.tr();
      if (training.hasFutureInPerson) return LocaleKeys.in_person.tr();
    }

    return LocaleKeys.training.tr();
  }
}
