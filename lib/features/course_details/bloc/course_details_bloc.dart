import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/domain/repositories/learning_track_details_repository.dart';
import 'package:national_skills_platform/features/course_details/training_details/domain/repositories/training_details_repository.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/usecases/update_study_streams.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

part 'course_details_bloc.freezed.dart';
part 'course_details_event.dart';
part 'course_details_state.dart';

@injectable
class CourseDetailsBloc extends Bloc<CourseDetailsEvent, CourseDetailsState> {
  CourseDetailsBloc({
    required TrainingDetailsRepository trainingDetailsRepository,
    required LearningTrackDetailsRepository learningTrackDetailsRepository,
    required MyLearningsBloc myLearningsBloc,
    required UpdateStudyStreams updateAndSortStudyStreams,
  })  : _trainingDetailsRepository = trainingDetailsRepository,
        _learningTrackDetailsRepository = learningTrackDetailsRepository,
        _myLearningsBloc = myLearningsBloc,
        _updateAndSortStudyStreams = updateAndSortStudyStreams,
        super(const CourseDetailsState()) {
    on<LoadTrainingDetailsEvent>(_loadTrainingDetails);
    on<LoadLearningTrackDetailsEvent>(_loadLearningTrackDetails);
    on<RefreshDetailsPageEvent>(_refreshDetailsPageEvent);
    on<EnrollTrainingEvent>(_enrollTraining);
    on<EnrollLearningTrackEvent>(_enrollLearningTrack);
    on<EnrollStudyStreamEvent>(_enrollStudyStream);
  }

  final TrainingDetailsRepository _trainingDetailsRepository;
  final LearningTrackDetailsRepository _learningTrackDetailsRepository;
  final MyLearningsBloc _myLearningsBloc;
  final UpdateStudyStreams _updateAndSortStudyStreams;

  /// Custom error handler for CourseDetailsBloc that extracts specific API error messages
  Future<void> _handleEnrollmentError<T>(
    Future<T> future, {
    required Future<void> Function(T result) onSuccess,
    required void Function(String error) onError,
  }) async {
    try {
      final result = await future;
      await onSuccess(result);
    } on DioException catch (error, stackTrace) {
      appPrint(error);
      appPrint(stackTrace);

      // Try to extract specific error message from API response
      String errorMsg;
      try {
        final responseData = error.response?.data;
        if (responseData != null) {
          // Handle both Map (already parsed JSON) and String (raw JSON) response data
          Map<String, dynamic>? jsonData;
          if (responseData is Map<String, dynamic>) {
            jsonData = responseData;
          } else if (responseData is String) {
            jsonData = jsonDecode(responseData) as Map<String, dynamic>?;
          }

          // Extract the message field if it exists and is not empty
          final apiMessage = jsonData?['message'] as String?;
          if (apiMessage != null && apiMessage.trim().isNotEmpty) {
            errorMsg = apiMessage.trim();
          } else {
            // Fall back to generic error message with request ID
            final responseId = error.response?.headers[Constants.xRequestId]?.first ??
                error.response?.requestOptions.headers[Constants.xRequestId] ??
                '';
            errorMsg = LocaleKeys.error_body.tr(args: [responseId]);
          }
        } else {
          // No response data, use generic error message with request ID
          final responseId = error.response?.headers[Constants.xRequestId]?.first ??
              error.response?.requestOptions.headers[Constants.xRequestId] ??
              '';
          errorMsg = LocaleKeys.error_body.tr(args: [responseId]);
        }
      } catch (parseError) {
        // If JSON parsing fails, fall back to generic error message with request ID
        appPrint('Error parsing API response: $parseError');
        final responseId = error.response?.headers[Constants.xRequestId]?.first ??
            error.response?.requestOptions.headers[Constants.xRequestId] ??
            '';
        errorMsg = LocaleKeys.error_body.tr(args: [responseId]);
      }

      onError(errorMsg);
    } catch (error, stackTrace) {
      appPrint(error);
      appPrint(stackTrace);
      onError(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr());
    }
  }

  Future<void> _loadTrainingDetails(
    LoadTrainingDetailsEvent event,
    Emitter<CourseDetailsState> emit,
  ) async {
    //loader will not be shown if page already contains data to show, it will just update it
    if (state.trainingDetailsModel == null) {
      emit(const CourseDetailsState(isCourseDetailsLoading: true));
    }

    await _trainingDetailsRepository.loadTrainingDetails(event.id).errorHandler(
          onSuccess: (TrainingDetailsModel trainingDetailsModel) async {
            final showEnrollButton = canShowEnrollButton(trainingDetailsModel);

            final studyStreams = _updateAndSortStudyStreams(trainingDetailsModel);

            final updatedModel = trainingDetailsModel.copyWith(studyStreams: studyStreams);

            emit(
              CourseDetailsState(
                trainingDetailsModel: updatedModel,
                showEnrollButton: showEnrollButton,
                enrollTrainingSuccess: event.enrollTrainingSuccess ?? false,
              ),
            );
          },
          onError: (errorMsg) => emit(CourseDetailsState(courseDetailsError: errorMsg)),
        );
  }

  Future<void> _loadLearningTrackDetails(
    LoadLearningTrackDetailsEvent event,
    Emitter<CourseDetailsState> emit,
  ) async {
    //loader will not be shown if our page already contains data to show, it will just update it
    if (state.learningTrackDetailsModel == null) {
      emit(state.copyWith(isCourseDetailsLoading: true));
    }

    await _learningTrackDetailsRepository.loadLearningTracks(event.id).errorHandler(
          onSuccess: (LearningTrackDetailsModel learningTrackDetailsModel) async {
            final showEnrollButton = canShowEnrollButtonForLearningTrack(learningTrackDetailsModel);

            emit(
              state.copyWith(
                learningTrackDetailsModel: learningTrackDetailsModel,
                enrollTrainingSuccess: event.enrollTrainingSuccess ?? false,
                isCourseDetailsLoading: false,
                showEnrollButton: showEnrollButton,
              ),
            );
          },
          onError: (errorMsg) => emit(
            state.copyWith(
              courseDetailsError: errorMsg,
              isCourseDetailsLoading: false,
            ),
          ),
        );
  }

  Future<void> _enrollTraining(EnrollTrainingEvent event, Emitter<CourseDetailsState> emit) async {
    final trainingDetailsModel = state.trainingDetailsModel;
    emit(
      CourseDetailsState(
        isTrainingEnrollmentInProgress: true,
        trainingDetailsModel: trainingDetailsModel,
      ),
    );

    await _handleEnrollmentError(
      _trainingDetailsRepository.enrollTraining(trainingId: trainingDetailsModel?.id ?? ''),
      onSuccess: (_) async {
        ///Updates MyLearnings tab after enrollment
        _myLearningsBloc.add(const GetAllMyLearnings());

        ///Order matters here, otherwise trainingDetailsModel won't be updated with lesson resources
        add(const RefreshDetailsPageEvent(CourseType.training, enrollTrainingSuccess: true));
      },
      onError: (errorMsg) => emit(
        CourseDetailsState(
          enrollTrainingError: errorMsg,
          trainingDetailsModel: trainingDetailsModel,
        ),
      ),
    );
  }

  Future<void> _enrollStudyStream(
    EnrollStudyStreamEvent event,
    Emitter<CourseDetailsState> emit,
  ) async {
    final trainingDetailsModel = state.trainingDetailsModel;
    emit(
      CourseDetailsState(
        isTrainingEnrollmentInProgress: true,
        trainingDetailsModel: trainingDetailsModel,
      ),
    );

    await _handleEnrollmentError(
      _trainingDetailsRepository.enrollStream(
        trainingId: trainingDetailsModel?.id ?? '',
        streamId: event.streamId ?? '',
      ),
      onSuccess: (_) async {
        ///Updates MyLearnings tab after enrollment
        _myLearningsBloc.add(const GetAllMyLearnings());

        ///Order matters here, otherwise trainingDetailsModel won't be updated with lesson resources
        add(const RefreshDetailsPageEvent(CourseType.training, enrollTrainingSuccess: true));
      },
      onError: (errorMsg) => emit(
        CourseDetailsState(
          enrollTrainingError: errorMsg,
          trainingDetailsModel: trainingDetailsModel,
        ),
      ),
    );
  }

  Future<void> _enrollLearningTrack(
    EnrollLearningTrackEvent event,
    Emitter<CourseDetailsState> emit,
  ) async {
    final learningTrackDetailsModel = state.learningTrackDetailsModel;

    emit(
      state.copyWith(
        isLearningTrackEnrollmentInProgress: true,
      ),
    );

    await _handleEnrollmentError(
      _learningTrackDetailsRepository.enrollLearningTrack(
        courseId: learningTrackDetailsModel?.id ?? '',
      ),
      onSuccess: (_) async {
        ///Updates MyLearnings tab after enrollment
        _myLearningsBloc.add(const GetAllMyLearnings());

        //todo learning track enrollment should be reviewed. compare it with training enrollment
        emit(
          state.copyWith(
            enrollLearningTrackSuccess: true,
            isLearningTrackEnrollmentInProgress: false,
          ),
        );
      },
      onError: (errorMsg) => emit(
        state.copyWith(
          enrollLearningTrackError: errorMsg,
          isLearningTrackEnrollmentInProgress: false,
        ),
      ),
    );
  }

  Future<void> _refreshDetailsPageEvent(
    RefreshDetailsPageEvent event,
    Emitter<CourseDetailsState> _,
  ) async {
    if (event.courseType == CourseType.training) {
      final trainingDetailsModel = state.trainingDetailsModel;
      if (trainingDetailsModel != null) {
        add(
          LoadTrainingDetailsEvent(
            id: trainingDetailsModel.id,
            enrollTrainingSuccess: event.enrollTrainingSuccess,
          ),
        );
      }
    } else {
      final learningTrackDetailsModel = state.learningTrackDetailsModel;
      if (learningTrackDetailsModel != null) {
        add(
          LoadLearningTrackDetailsEvent(
            id: learningTrackDetailsModel.id,
            enrollTrainingSuccess: event.enrollTrainingSuccess,
          ),
        );
      }
    }
  }

  bool canShowEnrollButton(TrainingDetailsModel trainingDetailsModel) {
    /// Self-Paced Training
    if (trainingDetailsModel.type == TrainingType.SelfPaced) {
      if (trainingDetailsModel.seatingCapacities.isEmpty) return false;

      for (final seating in trainingDetailsModel.seatingCapacities) {
        if (seating != null && seating.dtoStatus == DtoStatus.Active) {
          if (seating.noLimits) {
            return true;
          } else {
            return seating.actualNumberOfEnrollments < seating.maxNumberOfEnrollments;
          }
        }
      }
    }

    /// Instructor-Led Training
    if (trainingDetailsModel.type == TrainingType.InstructorLed) {
      if (trainingDetailsModel.studyStreams.isEmpty) return false;

      ///Check if the user is already enrolled in the training
      if (trainingDetailsModel.applicantDto?.studyStreams.any(
            (studyStream) => studyStream?.status == Constants.ENROLLED,
          ) ??
          false) {
        return true;
      }

      for (final studyStream in trainingDetailsModel.studyStreams) {
        if (_updateAndSortStudyStreams.checkStreamAvailability(studyStream)) return true;
      }
    }

    return false;
  }

  /// Check if the enroll button should be shown for learning tracks
  /// Returns true if user is NOT enrolled (should show enroll button)
  /// Returns false if user IS enrolled (should show "go to training" button)
  bool canShowEnrollButtonForLearningTrack(LearningTrackDetailsModel learningTrackDetailsModel) {
    // First check the applicantDto in the learning track details response (most reliable)
    if (learningTrackDetailsModel.applicantDto != null &&
        learningTrackDetailsModel.applicantDto!.status != null) {
      // User is enrolled if applicantDto exists with a status
      return false; // Don't show enroll button, show "go to training" button instead
    }

    // Fallback: Check MyLearnings data
    final enrolledLearningTracks =
        _myLearningsBloc.state.myLearningsModel.applicantLearningTrackViewList;

    // Check if the current learning track ID exists in the user's enrolled learning tracks
    final isEnrolled = enrolledLearningTracks.any(
      (learningTrack) => learningTrack.learningTrackId == learningTrackDetailsModel.id,
    );

    // Return true if NOT enrolled (show enroll button), false if enrolled (show go to training button)
    return !isEnrolled;
  }
}
