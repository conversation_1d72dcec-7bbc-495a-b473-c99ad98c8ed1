part of 'course_details_bloc.dart';

@freezed
abstract class CourseDetailsState with _$CourseDetailsState {
  const factory CourseDetailsState({
    @Default(false) bool isCourseDetailsLoading,
    TrainingDetailsModel? trainingDetailsModel,
    LearningTrackDetailsModel? learningTrackDetailsModel,
    @Default('') String courseDetailsError,
    @Default(true) bool showEnrollButton,
    //
    @Default(false) bool isTrainingEnrollmentInProgress,
    @Default(false) bool enrollTrainingSuccess,
    @Default('') String enrollTrainingError,
    //
    @Default(false) bool isLearningTrackEnrollmentInProgress,
    @Default(false) bool enrollLearningTrackSuccess,
    @Default('') String enrollLearningTrackError,
  }) = _CourseDetailsState;
}
