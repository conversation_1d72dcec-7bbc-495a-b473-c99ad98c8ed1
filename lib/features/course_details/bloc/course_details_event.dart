part of 'course_details_bloc.dart';

sealed class CourseDetailsEvent {
  const CourseDetailsEvent();
}

class LoadTrainingDetailsEvent extends CourseDetailsEvent {
  const LoadTrainingDetailsEvent({required this.id, this.enrollTrainingSuccess});

  final String id;
  final bool? enrollTrainingSuccess;
}

class RefreshDetailsPageEvent extends CourseDetailsEvent {
  const RefreshDetailsPageEvent(this.courseType, {this.enrollTrainingSuccess});

  final CourseType courseType;
  final bool? enrollTrainingSuccess;
}

class LoadLearningTrackDetailsEvent extends CourseDetailsEvent {
  const LoadLearningTrackDetailsEvent({required this.id, this.enrollTrainingSuccess});

  final String id;
  final bool? enrollTrainingSuccess;
}

class EnrollTrainingEvent extends CourseDetailsEvent {
  const EnrollTrainingEvent();
}

class EnrollLearningTrackEvent extends CourseDetailsEvent {
  const EnrollLearningTrackEvent();
}

class EnrollStudyStreamEvent extends CourseDetailsEvent {
  const EnrollStudyStreamEvent(this.streamId);

  final String? streamId;
}
