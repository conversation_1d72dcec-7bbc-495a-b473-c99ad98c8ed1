// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_details_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CourseDetailsState {
  bool get isCourseDetailsLoading;
  TrainingDetailsModel? get trainingDetailsModel;
  LearningTrackDetailsModel? get learningTrackDetailsModel;
  String get courseDetailsError;
  bool get showEnrollButton; //
  bool get isTrainingEnrollmentInProgress;
  bool get enrollTrainingSuccess;
  String get enrollTrainingError; //
  bool get isLearningTrackEnrollmentInProgress;
  bool get enrollLearningTrackSuccess;
  String get enrollLearningTrackError;

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CourseDetailsStateCopyWith<CourseDetailsState> get copyWith =>
      _$CourseDetailsStateCopyWithImpl<CourseDetailsState>(this as CourseDetailsState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CourseDetailsState &&
            (identical(other.isCourseDetailsLoading, isCourseDetailsLoading) ||
                other.isCourseDetailsLoading == isCourseDetailsLoading) &&
            (identical(other.trainingDetailsModel, trainingDetailsModel) ||
                other.trainingDetailsModel == trainingDetailsModel) &&
            (identical(other.learningTrackDetailsModel, learningTrackDetailsModel) ||
                other.learningTrackDetailsModel == learningTrackDetailsModel) &&
            (identical(other.courseDetailsError, courseDetailsError) ||
                other.courseDetailsError == courseDetailsError) &&
            (identical(other.showEnrollButton, showEnrollButton) ||
                other.showEnrollButton == showEnrollButton) &&
            (identical(other.isTrainingEnrollmentInProgress, isTrainingEnrollmentInProgress) ||
                other.isTrainingEnrollmentInProgress == isTrainingEnrollmentInProgress) &&
            (identical(other.enrollTrainingSuccess, enrollTrainingSuccess) ||
                other.enrollTrainingSuccess == enrollTrainingSuccess) &&
            (identical(other.enrollTrainingError, enrollTrainingError) ||
                other.enrollTrainingError == enrollTrainingError) &&
            (identical(other.isLearningTrackEnrollmentInProgress,
                    isLearningTrackEnrollmentInProgress) ||
                other.isLearningTrackEnrollmentInProgress == isLearningTrackEnrollmentInProgress) &&
            (identical(other.enrollLearningTrackSuccess, enrollLearningTrackSuccess) ||
                other.enrollLearningTrackSuccess == enrollLearningTrackSuccess) &&
            (identical(other.enrollLearningTrackError, enrollLearningTrackError) ||
                other.enrollLearningTrackError == enrollLearningTrackError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isCourseDetailsLoading,
      trainingDetailsModel,
      learningTrackDetailsModel,
      courseDetailsError,
      showEnrollButton,
      isTrainingEnrollmentInProgress,
      enrollTrainingSuccess,
      enrollTrainingError,
      isLearningTrackEnrollmentInProgress,
      enrollLearningTrackSuccess,
      enrollLearningTrackError);

  @override
  String toString() {
    return 'CourseDetailsState(isCourseDetailsLoading: $isCourseDetailsLoading, trainingDetailsModel: $trainingDetailsModel, learningTrackDetailsModel: $learningTrackDetailsModel, courseDetailsError: $courseDetailsError, showEnrollButton: $showEnrollButton, isTrainingEnrollmentInProgress: $isTrainingEnrollmentInProgress, enrollTrainingSuccess: $enrollTrainingSuccess, enrollTrainingError: $enrollTrainingError, isLearningTrackEnrollmentInProgress: $isLearningTrackEnrollmentInProgress, enrollLearningTrackSuccess: $enrollLearningTrackSuccess, enrollLearningTrackError: $enrollLearningTrackError)';
  }
}

/// @nodoc
abstract mixin class $CourseDetailsStateCopyWith<$Res> {
  factory $CourseDetailsStateCopyWith(
          CourseDetailsState value, $Res Function(CourseDetailsState) _then) =
      _$CourseDetailsStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isCourseDetailsLoading,
      TrainingDetailsModel? trainingDetailsModel,
      LearningTrackDetailsModel? learningTrackDetailsModel,
      String courseDetailsError,
      bool showEnrollButton,
      bool isTrainingEnrollmentInProgress,
      bool enrollTrainingSuccess,
      String enrollTrainingError,
      bool isLearningTrackEnrollmentInProgress,
      bool enrollLearningTrackSuccess,
      String enrollLearningTrackError});

  $TrainingDetailsModelCopyWith<$Res>? get trainingDetailsModel;
  $LearningTrackDetailsModelCopyWith<$Res>? get learningTrackDetailsModel;
}

/// @nodoc
class _$CourseDetailsStateCopyWithImpl<$Res> implements $CourseDetailsStateCopyWith<$Res> {
  _$CourseDetailsStateCopyWithImpl(this._self, this._then);

  final CourseDetailsState _self;
  final $Res Function(CourseDetailsState) _then;

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCourseDetailsLoading = null,
    Object? trainingDetailsModel = freezed,
    Object? learningTrackDetailsModel = freezed,
    Object? courseDetailsError = null,
    Object? showEnrollButton = null,
    Object? isTrainingEnrollmentInProgress = null,
    Object? enrollTrainingSuccess = null,
    Object? enrollTrainingError = null,
    Object? isLearningTrackEnrollmentInProgress = null,
    Object? enrollLearningTrackSuccess = null,
    Object? enrollLearningTrackError = null,
  }) {
    return _then(_self.copyWith(
      isCourseDetailsLoading: null == isCourseDetailsLoading
          ? _self.isCourseDetailsLoading
          : isCourseDetailsLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      trainingDetailsModel: freezed == trainingDetailsModel
          ? _self.trainingDetailsModel
          : trainingDetailsModel // ignore: cast_nullable_to_non_nullable
              as TrainingDetailsModel?,
      learningTrackDetailsModel: freezed == learningTrackDetailsModel
          ? _self.learningTrackDetailsModel
          : learningTrackDetailsModel // ignore: cast_nullable_to_non_nullable
              as LearningTrackDetailsModel?,
      courseDetailsError: null == courseDetailsError
          ? _self.courseDetailsError
          : courseDetailsError // ignore: cast_nullable_to_non_nullable
              as String,
      showEnrollButton: null == showEnrollButton
          ? _self.showEnrollButton
          : showEnrollButton // ignore: cast_nullable_to_non_nullable
              as bool,
      isTrainingEnrollmentInProgress: null == isTrainingEnrollmentInProgress
          ? _self.isTrainingEnrollmentInProgress
          : isTrainingEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollTrainingSuccess: null == enrollTrainingSuccess
          ? _self.enrollTrainingSuccess
          : enrollTrainingSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollTrainingError: null == enrollTrainingError
          ? _self.enrollTrainingError
          : enrollTrainingError // ignore: cast_nullable_to_non_nullable
              as String,
      isLearningTrackEnrollmentInProgress: null == isLearningTrackEnrollmentInProgress
          ? _self.isLearningTrackEnrollmentInProgress
          : isLearningTrackEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollLearningTrackSuccess: null == enrollLearningTrackSuccess
          ? _self.enrollLearningTrackSuccess
          : enrollLearningTrackSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollLearningTrackError: null == enrollLearningTrackError
          ? _self.enrollLearningTrackError
          : enrollLearningTrackError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingDetailsModelCopyWith<$Res>? get trainingDetailsModel {
    if (_self.trainingDetailsModel == null) {
      return null;
    }

    return $TrainingDetailsModelCopyWith<$Res>(_self.trainingDetailsModel!, (value) {
      return _then(_self.copyWith(trainingDetailsModel: value));
    });
  }

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LearningTrackDetailsModelCopyWith<$Res>? get learningTrackDetailsModel {
    if (_self.learningTrackDetailsModel == null) {
      return null;
    }

    return $LearningTrackDetailsModelCopyWith<$Res>(_self.learningTrackDetailsModel!, (value) {
      return _then(_self.copyWith(learningTrackDetailsModel: value));
    });
  }
}

/// @nodoc

class _CourseDetailsState implements CourseDetailsState {
  const _CourseDetailsState(
      {this.isCourseDetailsLoading = false,
      this.trainingDetailsModel,
      this.learningTrackDetailsModel,
      this.courseDetailsError = '',
      this.showEnrollButton = true,
      this.isTrainingEnrollmentInProgress = false,
      this.enrollTrainingSuccess = false,
      this.enrollTrainingError = '',
      this.isLearningTrackEnrollmentInProgress = false,
      this.enrollLearningTrackSuccess = false,
      this.enrollLearningTrackError = ''});

  @override
  @JsonKey()
  final bool isCourseDetailsLoading;
  @override
  final TrainingDetailsModel? trainingDetailsModel;
  @override
  final LearningTrackDetailsModel? learningTrackDetailsModel;
  @override
  @JsonKey()
  final String courseDetailsError;
  @override
  @JsonKey()
  final bool showEnrollButton;
//
  @override
  @JsonKey()
  final bool isTrainingEnrollmentInProgress;
  @override
  @JsonKey()
  final bool enrollTrainingSuccess;
  @override
  @JsonKey()
  final String enrollTrainingError;
//
  @override
  @JsonKey()
  final bool isLearningTrackEnrollmentInProgress;
  @override
  @JsonKey()
  final bool enrollLearningTrackSuccess;
  @override
  @JsonKey()
  final String enrollLearningTrackError;

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CourseDetailsStateCopyWith<_CourseDetailsState> get copyWith =>
      __$CourseDetailsStateCopyWithImpl<_CourseDetailsState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CourseDetailsState &&
            (identical(other.isCourseDetailsLoading, isCourseDetailsLoading) ||
                other.isCourseDetailsLoading == isCourseDetailsLoading) &&
            (identical(other.trainingDetailsModel, trainingDetailsModel) ||
                other.trainingDetailsModel == trainingDetailsModel) &&
            (identical(other.learningTrackDetailsModel, learningTrackDetailsModel) ||
                other.learningTrackDetailsModel == learningTrackDetailsModel) &&
            (identical(other.courseDetailsError, courseDetailsError) ||
                other.courseDetailsError == courseDetailsError) &&
            (identical(other.showEnrollButton, showEnrollButton) ||
                other.showEnrollButton == showEnrollButton) &&
            (identical(other.isTrainingEnrollmentInProgress, isTrainingEnrollmentInProgress) ||
                other.isTrainingEnrollmentInProgress == isTrainingEnrollmentInProgress) &&
            (identical(other.enrollTrainingSuccess, enrollTrainingSuccess) ||
                other.enrollTrainingSuccess == enrollTrainingSuccess) &&
            (identical(other.enrollTrainingError, enrollTrainingError) ||
                other.enrollTrainingError == enrollTrainingError) &&
            (identical(other.isLearningTrackEnrollmentInProgress,
                    isLearningTrackEnrollmentInProgress) ||
                other.isLearningTrackEnrollmentInProgress == isLearningTrackEnrollmentInProgress) &&
            (identical(other.enrollLearningTrackSuccess, enrollLearningTrackSuccess) ||
                other.enrollLearningTrackSuccess == enrollLearningTrackSuccess) &&
            (identical(other.enrollLearningTrackError, enrollLearningTrackError) ||
                other.enrollLearningTrackError == enrollLearningTrackError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isCourseDetailsLoading,
      trainingDetailsModel,
      learningTrackDetailsModel,
      courseDetailsError,
      showEnrollButton,
      isTrainingEnrollmentInProgress,
      enrollTrainingSuccess,
      enrollTrainingError,
      isLearningTrackEnrollmentInProgress,
      enrollLearningTrackSuccess,
      enrollLearningTrackError);

  @override
  String toString() {
    return 'CourseDetailsState(isCourseDetailsLoading: $isCourseDetailsLoading, trainingDetailsModel: $trainingDetailsModel, learningTrackDetailsModel: $learningTrackDetailsModel, courseDetailsError: $courseDetailsError, showEnrollButton: $showEnrollButton, isTrainingEnrollmentInProgress: $isTrainingEnrollmentInProgress, enrollTrainingSuccess: $enrollTrainingSuccess, enrollTrainingError: $enrollTrainingError, isLearningTrackEnrollmentInProgress: $isLearningTrackEnrollmentInProgress, enrollLearningTrackSuccess: $enrollLearningTrackSuccess, enrollLearningTrackError: $enrollLearningTrackError)';
  }
}

/// @nodoc
abstract mixin class _$CourseDetailsStateCopyWith<$Res>
    implements $CourseDetailsStateCopyWith<$Res> {
  factory _$CourseDetailsStateCopyWith(
          _CourseDetailsState value, $Res Function(_CourseDetailsState) _then) =
      __$CourseDetailsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isCourseDetailsLoading,
      TrainingDetailsModel? trainingDetailsModel,
      LearningTrackDetailsModel? learningTrackDetailsModel,
      String courseDetailsError,
      bool showEnrollButton,
      bool isTrainingEnrollmentInProgress,
      bool enrollTrainingSuccess,
      String enrollTrainingError,
      bool isLearningTrackEnrollmentInProgress,
      bool enrollLearningTrackSuccess,
      String enrollLearningTrackError});

  @override
  $TrainingDetailsModelCopyWith<$Res>? get trainingDetailsModel;
  @override
  $LearningTrackDetailsModelCopyWith<$Res>? get learningTrackDetailsModel;
}

/// @nodoc
class __$CourseDetailsStateCopyWithImpl<$Res> implements _$CourseDetailsStateCopyWith<$Res> {
  __$CourseDetailsStateCopyWithImpl(this._self, this._then);

  final _CourseDetailsState _self;
  final $Res Function(_CourseDetailsState) _then;

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isCourseDetailsLoading = null,
    Object? trainingDetailsModel = freezed,
    Object? learningTrackDetailsModel = freezed,
    Object? courseDetailsError = null,
    Object? showEnrollButton = null,
    Object? isTrainingEnrollmentInProgress = null,
    Object? enrollTrainingSuccess = null,
    Object? enrollTrainingError = null,
    Object? isLearningTrackEnrollmentInProgress = null,
    Object? enrollLearningTrackSuccess = null,
    Object? enrollLearningTrackError = null,
  }) {
    return _then(_CourseDetailsState(
      isCourseDetailsLoading: null == isCourseDetailsLoading
          ? _self.isCourseDetailsLoading
          : isCourseDetailsLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      trainingDetailsModel: freezed == trainingDetailsModel
          ? _self.trainingDetailsModel
          : trainingDetailsModel // ignore: cast_nullable_to_non_nullable
              as TrainingDetailsModel?,
      learningTrackDetailsModel: freezed == learningTrackDetailsModel
          ? _self.learningTrackDetailsModel
          : learningTrackDetailsModel // ignore: cast_nullable_to_non_nullable
              as LearningTrackDetailsModel?,
      courseDetailsError: null == courseDetailsError
          ? _self.courseDetailsError
          : courseDetailsError // ignore: cast_nullable_to_non_nullable
              as String,
      showEnrollButton: null == showEnrollButton
          ? _self.showEnrollButton
          : showEnrollButton // ignore: cast_nullable_to_non_nullable
              as bool,
      isTrainingEnrollmentInProgress: null == isTrainingEnrollmentInProgress
          ? _self.isTrainingEnrollmentInProgress
          : isTrainingEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollTrainingSuccess: null == enrollTrainingSuccess
          ? _self.enrollTrainingSuccess
          : enrollTrainingSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollTrainingError: null == enrollTrainingError
          ? _self.enrollTrainingError
          : enrollTrainingError // ignore: cast_nullable_to_non_nullable
              as String,
      isLearningTrackEnrollmentInProgress: null == isLearningTrackEnrollmentInProgress
          ? _self.isLearningTrackEnrollmentInProgress
          : isLearningTrackEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollLearningTrackSuccess: null == enrollLearningTrackSuccess
          ? _self.enrollLearningTrackSuccess
          : enrollLearningTrackSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      enrollLearningTrackError: null == enrollLearningTrackError
          ? _self.enrollLearningTrackError
          : enrollLearningTrackError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingDetailsModelCopyWith<$Res>? get trainingDetailsModel {
    if (_self.trainingDetailsModel == null) {
      return null;
    }

    return $TrainingDetailsModelCopyWith<$Res>(_self.trainingDetailsModel!, (value) {
      return _then(_self.copyWith(trainingDetailsModel: value));
    });
  }

  /// Create a copy of CourseDetailsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LearningTrackDetailsModelCopyWith<$Res>? get learningTrackDetailsModel {
    if (_self.learningTrackDetailsModel == null) {
      return null;
    }

    return $LearningTrackDetailsModelCopyWith<$Res>(_self.learningTrackDetailsModel!, (value) {
      return _then(_self.copyWith(learningTrackDetailsModel: value));
    });
  }
}

// dart format on
