import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/data/data_sources/training_details_datasource.dart';

@injectable
class TrainingDetailsRepository {
  const TrainingDetailsRepository({required TrainingDetailsDataSource dataSource})
      : _dataSource = dataSource;

  final TrainingDetailsDataSource _dataSource;

  Future<TrainingDetailsModel> loadTrainingDetails(String courseId) =>
      _dataSource.getTrainingDetails(courseId);

  Future<void> enrollTraining({required String trainingId}) =>
      _dataSource.enrollTraining(trainingId);

  Future<void> enrollStream({required String trainingId, required String streamId}) =>
      _dataSource.enrollStream(trainingId, streamId);
}
