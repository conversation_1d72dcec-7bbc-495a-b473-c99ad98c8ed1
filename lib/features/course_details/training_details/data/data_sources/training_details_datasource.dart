import 'dart:async';

import 'package:dio/dio.dart';
import 'package:hive/hive.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

@injectable
class TrainingDetailsDataSource {
  const TrainingDetailsDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<TrainingDetailsModel> getTrainingDetails(String courseId) async {
    final String locale = await Hive.box(
      HiveKeys.hiveNspStorage,
    ).get(HiveKeys.currentLocale, defaultValue: Constants.localeEN);
    final path = '${ApiConstants.trainingDetailsPath}/$courseId';
    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      path,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return TrainingDetailsModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<void> enrollTraining(String trainingId) async {
    final response = await _dio.post(ApiConstants.applicantsTrainings + trainingId);

    if (response.statusCode != Constants.statusCode200 &&
        response.statusCode != Constants.statusCode201) {
      throw DioException(requestOptions: response.requestOptions, response: response);
    }
  }

  Future<void> enrollStream(String trainingId, String streamId) async {
    final response = await _dio.post(
      ApiConstants.applicantsTrainings + trainingId + ApiConstants.streamsPath + streamId,
    );

    if (response.statusCode != Constants.statusCode200 &&
        response.statusCode != Constants.statusCode201) {
      throw DioException(requestOptions: response.requestOptions, response: response);
    }
  }
}
