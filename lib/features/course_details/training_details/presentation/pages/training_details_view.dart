import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_structure/upcoming_study_streams.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/enroll_bottom_sheet/enroll_course_bottom_sheet.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

class TrainingDetailsView extends StatelessWidget {
  const TrainingDetailsView({required this.trainingID, this.isBottomSheetState = false, super.key});

  final String trainingID;
  final bool isBottomSheetState;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<CourseDetailsBloc, CourseDetailsState>(
        listener: (context, state) {
          if (state.courseDetailsError.isNotEmpty) {
            showAppToast(context, message: state.courseDetailsError);
          }

          if (state.enrollTrainingSuccess) {
            showEnrollSuccessMessage(context, state.trainingDetailsModel);
          }

          if (state.enrollTrainingError.isNotEmpty) {
            showAppToast(context, message: state.enrollTrainingError);
          }
        },
        builder: (context, state) {
          if (state.courseDetailsError.isNotEmpty) return const SizedBox.shrink();
          if (state.isCourseDetailsLoading) return const BuildLoader();

          final trainingDetailsModel = state.trainingDetailsModel;
          if (trainingDetailsModel != null) {
            final trainingStructure = trainingDetailsModel.trainingStructure;

            return AppLoadingOverlay(
              isLoading: state.isTrainingEnrollmentInProgress,
              child: Scaffold(
                appBar: AppBar(
                  leading: isBottomSheetState ? const CloseButton() : const AppBackButton(),
                  actions: [ShareTraining(trainingID)],
                  bottom: const PreferredSize(
                    preferredSize: Size.fromHeight(0),
                    child: AppDivider(),
                  ),
                ),
                body: SingleChildScrollView(
                  child: Column(
                    children: [
                      CourseHeaderPanel(
                        trainingDetailsModel: trainingDetailsModel,
                        courseType: CourseType.training,
                        showEnrollButton: state.showEnrollButton,
                      ),

                      const AppDivider(),

                      CourseInfo(trainingDetailsModel: trainingDetailsModel),

                      const AppDivider(),

                      if (trainingDetailsModel.promoVideoUrl.isNotEmpty)
                        PromoVideo(trainingDetailsModel.promoVideoUrl),

                      if (trainingDetailsModel.description.isNotEmpty)
                        CourseDescription(trainingDetailsModel.description),

                      if (trainingDetailsModel.outcomes.isNotEmpty)
                        WhatYoullLearn(outcomes: trainingDetailsModel.outcomes),

                      if (trainingDetailsModel.skills.isNotEmpty)
                        SkillsYouGain(skills: trainingDetailsModel.skills),

                      if (trainingDetailsModel.requirements.isNotEmpty)
                        CourseRequirements(trainingDetailsModel: trainingDetailsModel),

                      if (trainingStructure != null)
                        TrainingSyllabus(trainingStructureModel: trainingStructure),

                      //show only if it has more than 1 study stream
                      if (trainingDetailsModel.studyStreams.length > 1)
                        UpcomingStudyStreams(trainingDetailsModel: trainingDetailsModel),

                      const AppDivider(padding: EdgeInsets.only(top: 40, bottom: 8)),

                      const TrainingDetailAligner(
                        child: CourseAccessButton(courseType: CourseType.training),
                      ),

                      const SizedBox(height: 30),

                      //
                    ],
                  ),
                ),
              ),
            );
          }

          return const Center(child: Text('Unknown state'));
        },
      ),
    );
  }

  Future<void> showEnrollSuccessMessage(
    BuildContext context,
    TrainingDetailsModel? trainingDetailsModel,
  ) =>
      showModalBottomSheet(
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(borderRadius: SharedDecoration.borderTopLeftRight10),
        context: context,
        builder: (_) => BlocProvider.value(
          value: context.read<CourseDetailsBloc>(),
          child: EnrollCourseBottomSheet(
            courseType: CourseType.training,
            trainingDetailsModel: trainingDetailsModel,
          ),
        ),
      );
}
