import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/pages/training_details_view.dart';

class TrainingDetailsPage extends StatelessWidget {
  const TrainingDetailsPage({required this.trainingID, this.isBottomSheetState = false, super.key});

  final String trainingID;
  final bool isBottomSheetState;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          GetIt.instance.get<CourseDetailsBloc>()..add(LoadTrainingDetailsEvent(id: trainingID)),
      child: TrainingDetailsView(trainingID: trainingID, isBottomSheetState: isBottomSheetState),
    );
  }
}
