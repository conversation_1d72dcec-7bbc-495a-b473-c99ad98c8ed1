import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:url_launcher/url_launcher.dart';

mixin StudyStreamDetailsHelperMixin {
  String getScheduleDisplay(StudyStream? stream, BuildContext context) {
    if (stream == null || stream.liveSession == null) return "";

    final liveSession = stream.liveSession!;

    if (liveSession.recurringMeeting != true || liveSession.recurringMeeting == null) {
      if (stream.startDate != null) {
        return DateFormat('EEEE').format(stream.startDate!);
      }
      return "";
    }

    final daysOfWeek = liveSession.daysOfWeek;

    if (daysOfWeek == null || daysOfWeek.isEmpty) return "";

    if (liveSession.repeatEvery != null) {
      if (daysOfWeek.length == 7) {
        if (liveSession.repeatEvery == 1) {
          return LocaleKeys.freq_daily.tr();
        } else {
          final repeatEvery = liveSession.repeatEvery!;
          // Use pluralization for days
          return context.plural(LocaleKeys.freq_days, repeatEvery);
        }
      }
    }

    if (liveSession.dayOfMonth != null) {
      final repeatEvery = liveSession.repeatEvery ?? 1;
      if (repeatEvery == 1) {
        return LocaleKeys.freq_monthly.tr();
      } else {
        // Use pluralization for months
        return context.plural(LocaleKeys.freq_months, repeatEvery);
      }
    }

    final isWeekdays = Constants.weekdaysList.every((day) => daysOfWeek.contains(day)) &&
        daysOfWeek.length == Constants.weekdaysList.length;

    final isWeekends = Constants.weekendsList.every((day) => daysOfWeek.contains(day)) &&
        daysOfWeek.length == Constants.weekendsList.length;

    if (isWeekdays) return LocaleKeys.freq_weekdays.tr();

    if (isWeekends) return LocaleKeys.freq_weekend.tr();

    final daysToDisplay =
        Constants.orderedDaysOfWeek.where((day) => daysOfWeek.contains(day)).toList();

    final formattedDays = daysToDisplay.map((day) {
      final dayIndex = Constants.orderedDaysOfWeek.indexOf(day);
      final now = DateTime.now();
      final daysUntil = (dayIndex - now.weekday + 7) % 7;
      final nextOccurrence = now.add(Duration(days: daysUntil));
      return DateFormat('EEE').format(nextOccurrence);
    }).toList();

    return formattedDays.join(', ');
  }

  String getSeatingCapacityText(StudyStream? studyStream) {
    if (studyStream == null) return "";

    if (studyStream.noLimits) return LocaleKeys.seats_unlimited.tr();

    if (studyStream.maxNumberOfParticipants == studyStream.currentNumberOfParticipants) {
      return LocaleKeys.seats_total.tr(args: ["${studyStream.maxNumberOfParticipants}"]);
    }

    return LocaleKeys.seats_available.tr(
      args: [
        "${studyStream.maxNumberOfParticipants - studyStream.currentNumberOfParticipants}",
        "${studyStream.maxNumberOfParticipants}",
      ],
    );
  }

  String getLocalizedAddress(BuildContext context, Location? location) {
    if (location == null) return "";

    final isEnglish = context.locale == const Locale(Constants.localeEN);
    return isEnglish ? location.addressNameEn : location.addressNameAr;
  }

  String getLocalizedCityRegion(BuildContext context, Location? location) {
    if (location == null || location.city == null) return "";

    final isEnglish = context.locale == const Locale(Constants.localeEN);
    final city = isEnglish ? location.city?.cityNameEn : location.city?.cityNameAr;
    final region =
        isEnglish ? location.city?.region?.regionNameEn : location.city?.region?.regionNameAr;

    if (city == null || region == null) return "";
    return "$city, $region";
  }

  Future<void> openLocationInMaps(Location? location) async {
    if (location == null) return;

    if (location.latitude != null && location.longitude != null) {
      final uri =
          Uri.parse('https://maps.google.com/?q=${location.latitude},${location.longitude}');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }
}
