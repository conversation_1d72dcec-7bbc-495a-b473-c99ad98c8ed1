import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:national_skills_platform/core/shared/converters/training_type_string.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/total_number_of_participants.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/training_location.dart';
import 'package:national_skills_platform/features/course_details/utils/training_type_utils.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

enum CourseType { training, learningTrack }

class CourseHeaderPanel extends StatelessWidget with TrainingTypeUtils {
  const CourseHeaderPanel({
    required this.trainingDetailsModel,
    required this.courseType,
    required this.showEnrollButton,
    super.key,
  });

  final CourseDetailsInterface trainingDetailsModel;
  final CourseType courseType;
  final bool showEnrollButton;

  @override
  Widget build(BuildContext context) {
    final organizationName = trainingDetailsModel.organizationName.isNotEmpty
        ? trainingDetailsModel.organizationName
        : trainingDetailsModel.trainingProviderName;

    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),

          ///This line is added to fix golden tests, because it gets stuck on infinite loading
          if (trainingDetailsModel.profileImageUrl.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: double.infinity,
                child: CachedNetworkImage(
                  height: 192,
                  fit: BoxFit.cover,
                  imageUrl: trainingDetailsModel.profileImageUrl,
                  placeholder: (context, url) => const ShimmerPlaceholder(height: 192),
                ),
              ),
            )
          else
            Container(width: double.infinity, color: Colors.grey, height: 192),

          const SizedBox(height: 16),

          Row(
            children: [
              if (courseType == CourseType.training) ...[
                Image.asset(
                  getTrainingTypeImage(trainingDetailsModel as TrainingDetailsModel),
                  width: 20,
                ),
                const SizedBox(width: 6),
              ],
              if (courseType == CourseType.learningTrack) ...[
                Image.asset(
                  AssetsPath.learningTrackIcon,
                  width: 20,
                ),
                const SizedBox(width: 6),
              ],
              Text(
                courseType == CourseType.training
                    ? trainingTypeString(trainingDetailsModel as TrainingDetailsModel)
                    : LocaleKeys.header_learningTracks.tr(),
                style: context.textTheme.textMedium.medium.copyWith(color: getColor()),
              ),
              if (trainingDetailsModel is TrainingDetailsModel)
                LocationDisplay(training: trainingDetailsModel as TrainingDetailsModel),
            ],
          ),

          const SizedBox(height: 8),

          Text(trainingDetailsModel.title, style: context.textTheme.h2.semiBold),

          const SizedBox(height: 12),

          if (trainingDetailsModel.overview.isNotEmpty) ...[
            Html(
              data: trainingDetailsModel.overview,
              style: {
                Constants.body: Style(
                  fontSize: FontSize(16),
                  margin: Margins.zero,
                  padding: HtmlPaddings.zero,
                ),
                Constants.p: Style(
                  fontSize: FontSize(16),
                  margin: Margins.zero,
                  padding: HtmlPaddings.zero,
                ),
              },
            ),
            const SizedBox(height: 16),
          ],

          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.transparent,
                child: Platform.environment.containsKey(Constants.flutterTest) ||
                        trainingDetailsModel.avatarUrl.isEmpty
                    ? const Icon(Icons.account_circle, size: 32)
                    : CachedNetworkImage(
                        imageUrl: trainingDetailsModel.avatarUrl,
                        placeholder: (context, url) => const Icon(Icons.account_circle, size: 32),
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.account_circle, size: 32),
                      ),
              ),
              const SizedBox(width: 8),
              Text(LocaleKeys.trainingDetails_createdBy.tr(), style: context.textTheme.textSmall),
              if (organizationName.isNotEmpty) ...[
                TextButton(
                  style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 4)),
                  child: Text(
                    organizationName,
                    style: context.textTheme.textXSmall.semiBold.accentGreenPrimary,
                  ),
                  onPressed: () {},
                ),
              ],
            ],
          ),
          const SizedBox(height: 24),

          CourseAccessButton(courseType: courseType),

          TotalNumberOfParticipants(trainingDetailsModel: trainingDetailsModel),

          const SizedBox(height: 20),

          //
        ],
      ),
    );
  }

  Color getColor() {
    return courseType == CourseType.training
        ? AppColors.greenAccentPrimary
        : AppColors.orangeAccentPrimary;
  }
}
