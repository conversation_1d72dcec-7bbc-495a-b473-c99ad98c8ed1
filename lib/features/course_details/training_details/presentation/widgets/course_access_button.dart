import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart'
    as course_details;
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class CourseAccessButton extends StatelessWidget {
  const CourseAccessButton({required this.courseType, super.key});

  final CourseType courseType;

  @override
  Widget build(BuildContext context) {
    final courseDetailsBloc = context.watch<course_details.CourseDetailsBloc>();
    final state = courseDetailsBloc.state;
    final showEnrollButton = state.showEnrollButton;
    final applicantDto = state.trainingDetailsModel?.applicantDto;

    return Row(
      children: [
        // For trainings: showEnrollButton = true means show enroll button, false means don't show button
        // For learning tracks: showEnrollButton = true means show enroll button, false means show "go to training" button
        if (courseType == CourseType.training && showEnrollButton)

          ///if already enrolled show "Go to training button" instead of "Enroll" button
          if (applicantDto != null && applicantDto.status != null)
            Expanded(
              child: AppButton(
                onTap: () => {
                  router.pushNamed(
                    Routes.trainingConsumptionPage.name,
                    extra: state.trainingDetailsModel,
                  ),
                },
                buttonText: LocaleKeys.trainingDetails_goToTraining.tr(),
              ),
            )
          else
            StreamBuilder<bool>(
              stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
              builder: (context, isLoggedInSnapshot) {
                return Expanded(
                  child: AppButton(
                    onTap: () {
                      if (isLoggedInSnapshot.data == false) {
                        router.pushNamed(Routes.login.name).then(
                              (value) => courseDetailsBloc
                                  .add(course_details.RefreshDetailsPageEvent(courseType)),
                            );
                      } else {
                        state.trainingDetailsModel?.type == TrainingType.InstructorLed
                            ? context.read<course_details.CourseDetailsBloc>().add(
                                  course_details.EnrollStudyStreamEvent(
                                    state.trainingDetailsModel?.studyStreams.firstOrNull?.id,
                                  ),
                                )
                            : context
                                .read<course_details.CourseDetailsBloc>()
                                .add(const course_details.EnrollTrainingEvent());
                      }
                    },
                    buttonText: LocaleKeys.trainingDetails_enroll.tr(),
                  ),
                );
              },
            )
        else if (courseType == CourseType.learningTrack)
          if (showEnrollButton)
            // User is not enrolled - show enroll button
            StreamBuilder<bool>(
              stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
              builder: (context, isLoggedInSnapshot) {
                return Expanded(
                  child: AppButton(
                    onTap: () {
                      if (isLoggedInSnapshot.data == false) {
                        router.pushNamed(Routes.login.name).then(
                              (value) => courseDetailsBloc
                                  .add(course_details.RefreshDetailsPageEvent(courseType)),
                            );
                      } else {
                        context
                            .read<course_details.CourseDetailsBloc>()
                            .add(const course_details.EnrollLearningTrackEvent());
                      }
                    },
                    buttonText: LocaleKeys.trainingDetails_enroll.tr(),
                  ),
                );
              },
            )
          else
            // User is enrolled - show "go to training" button that navigates to the in-progress training
            Expanded(
              child: AppButton(
                onTap: () async {
                  final learningTrackDetailsModel = state.learningTrackDetailsModel;
                  if (learningTrackDetailsModel != null) {
                    // Find the enrolled learning track from MyLearningsBloc to get training progress
                    final myLearningsBloc = GetIt.instance<MyLearningsBloc>();
                    final enrolledLearningTracks =
                        myLearningsBloc.state.myLearningsModel.applicantLearningTrackViewList;

                    final currentLearningTrack = enrolledLearningTracks.firstWhereOrNull(
                      (learningTrack) =>
                          learningTrack.learningTrackId == learningTrackDetailsModel.id,
                    );

                    if (currentLearningTrack != null) {
                      // Find the training that is in progress (ENROLLED status)
                      final inProgressTraining =
                          currentLearningTrack.applicantTrainings.firstWhereOrNull(
                        (training) =>
                            training.applicantTrainingStatus == ApplicantTrainingStatus.ENROLLED,
                      );

                      if (inProgressTraining != null) {
                        // Navigate to the in-progress training
                        await router.pushNamed(
                          Routes.trainingDetailsPage.name,
                          extra: inProgressTraining.trainingId,
                        );
                        return;
                      } else {
                        // No in-progress training found, navigate to the first training in the list
                        if (learningTrackDetailsModel.trainings.isNotEmpty) {
                          final firstTraining = learningTrackDetailsModel.trainings.first;
                          await router.pushNamed(
                            Routes.trainingDetailsPage.name,
                            extra: firstTraining.id,
                          );
                          return;
                        }
                      }
                    }
                  }

                  // Final fallback: Navigate to My Learnings page
                  router.go('${Routes.rootPage.path}${Routes.myLearningPage.path}');
                },
                buttonText: LocaleKeys.trainingDetails_goToTraining.tr(),
              ),
            ),
        // const SizedBox(width: 16),
        // Container(
        //   height: 48,
        //   width: 48,
        //   decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(8),
        //     border: Border.all(color: AppColors.accentLight),
        //   ),
        // child: GestureDetector(
        //   child: const Icon(Icons.favorite_border_outlined),
        // ),
        // ),
      ],
    );
  }
}
