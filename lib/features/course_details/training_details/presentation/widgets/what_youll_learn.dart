import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class WhatYoullLearn extends StatelessWidget {
  const WhatYoullLearn({required this.outcomes, super.key});

  final List<Outcome> outcomes;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      color: AppColors.accentExtraLight,
      width: MediaQuery.sizeOf(context).width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.trainingDetails_whatYouWillLearn.tr(),
            style: context.textTheme.h3.semiBold,
          ),

          const SizedBox(height: 14),

          for (int i = 0; i < outcomes.length; i++) ...[
            const SizedBox(height: 4),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(Icons.check_rounded, color: AppColors.orangeAccentPrimary),
                const SizedBox(width: 10),
                Flexible(
                  child: Text(
                    outcomes[i].value,
                    softWrap: true,
                    style: context.textTheme.textMedium,
                  ),
                ),
              ],
            ),
          ],
          //
        ],
      ),
    );
  }
}
