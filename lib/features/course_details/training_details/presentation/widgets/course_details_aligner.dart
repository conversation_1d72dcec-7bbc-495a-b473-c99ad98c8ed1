import 'package:flutter/material.dart';

class TrainingDetailAligner extends StatelessWidget {
  const TrainingDetailAligner({required this.child, super.key});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    final isDirectionRTL = Directionality.of(context) == TextDirection.rtl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Align(
        alignment: isDirectionRTL ? Alignment.centerRight : Alignment.centerLeft,
        child: child,
      ),
    );
  }
}
