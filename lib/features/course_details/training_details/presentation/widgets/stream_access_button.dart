import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/enroll_bottom_sheet/change_stream_enrollment_bottom_sheet.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class StreamAccessButton extends StatelessWidget {
  const StreamAccessButton({
    required this.trainingDetailsModel,
    required this.studyStream,
    super.key,
  });

  final TrainingDetailsModel trainingDetailsModel;
  final StudyStream? studyStream;

  @override
  Widget build(BuildContext context) {
    final applicantDtoStreams = trainingDetailsModel.applicantDto?.studyStreams ?? [];
    final streamStatus = getStreamStatus(applicantDtoStreams);

    final isEnrolled = streamStatus == Constants.ENROLLED;
    final isDisabled = streamStatus == Constants.DISABLED;

    return IntrinsicWidth(
      child: IgnorePointer(
        ignoring: isDisabled,
        child: AppButton(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
          backgroundColor: isDisabled ? AppColors.greyLight : AppColors.greenAccentPrimary,
          textStyle: context.textTheme.textSmall.semiBold.copyWith(
            color: isDisabled ? AppColors.greyPrimary : Colors.white,
          ),
          onTap: isEnrolled
              ? () => router.pushNamed(
                    Routes.trainingConsumptionPage.name,
                    extra: trainingDetailsModel,
                  )
              : () {
                  ///If user is already enrolled to ANY stream, show warning message
                  if (applicantDtoStreams.any((stream) => stream?.status == Constants.ENROLLED)) {
                    showChangingStreamEnrollmentWarning(context);
                  } else {
                    enroll(context, studyStream?.id ?? '', context.read<CourseDetailsBloc>());
                  }
                },
          buttonText: isEnrolled
              ? LocaleKeys.trainingDetails_goToTraining.tr()
              : LocaleKeys.trainingDetails_enroll.tr(),
        ),
      ),
    );
  }

  String? getStreamStatus(List<ApplicantStudyStream?> applicantDtoStreams) {
    for (final stream in applicantDtoStreams) {
      if (stream?.streamId == studyStream?.id) {
        return stream?.status;
      }
    }
    return null;
  }

  Future<void> showChangingStreamEnrollmentWarning(BuildContext context) {
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    return showModalBottomSheet(
      backgroundColor: Colors.white,
      useRootNavigator: useRootNavigator,
      shape: const RoundedRectangleBorder(borderRadius: SharedDecoration.borderTopLeftRight10),
      context: context,
      builder: (_) => ChangeStreamEnrollmentBottomSheet(
        enroll: () {
          router.pop(); //closes modal bottom sheet
          final courseDetailsBloc = context.read<CourseDetailsBloc>();
          enroll(context, studyStream?.id ?? '', courseDetailsBloc);
        },
      ),
    );
  }

  void enroll(BuildContext context, String streamID, CourseDetailsBloc courseDetailsBloc) {
    GetIt.instance.get<AuthTokenProvider>().authStateStream.first.then((isLoggedIn) {
      if (isLoggedIn == false) {
        router.pushNamed(Routes.login.name).then(
              (value) => courseDetailsBloc.add(const RefreshDetailsPageEvent(CourseType.training)),
            );
      } else {
        courseDetailsBloc.add(EnrollStudyStreamEvent(streamID));
      }
    });
  }
}
