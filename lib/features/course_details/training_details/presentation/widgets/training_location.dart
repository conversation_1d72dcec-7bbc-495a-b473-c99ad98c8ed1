import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model_extension.dart';
import 'package:url_launcher/url_launcher.dart';

class LocationDisplay extends StatelessWidget {
  const LocationDisplay({required this.training, super.key});

  final TrainingDetailsModel training;

  @override
  Widget build(BuildContext context) {
    final location = training.getFirstAvailableLocation();
    if (location == null) return const SizedBox.shrink();

    final isArabic = context.locale.languageCode == Constants.localeAR;
    final cityName = isArabic ? location.city?.cityNameAr : location.city?.cityNameEn;

    if (cityName != null && cityName.isEmpty) return const SizedBox.shrink();

    return GestureDetector(
      onTap: () async {
        final uri =
            Uri.parse('https://maps.google.com/?q=${location.latitude},${location.longitude}');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(width: 16),
          Image.asset(AssetsPath.locationIcon, width: 20, height: 20),
          const SizedBox(width: 4),
          Text(cityName ?? '', style: context.textTheme.textMedium.medium.accentGreenPrimary),
        ],
      ),
    );
  }
}
