import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/show_more_toggle.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CourseDescription extends StatefulWidget {
  const CourseDescription(this.description, {super.key});

  final String description;

  @override
  _CourseDescriptionState createState() => _CourseDescriptionState();
}

class _CourseDescriptionState extends State<CourseDescription> {
  bool isExpanded = false;
  final int charLimit = 301;

  void _toggleExpanded() {
    setState(() {
      isExpanded = !isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final String displayText =
        isExpanded ? widget.description : truncateText(widget.description, charLimit);

    return TrainingDetailAligner(
      child: SizedBox(
        width: MediaQuery.sizeOf(context).width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 20),
            Text(
              LocaleKeys.trainingDetails_trainingDescription.tr(),
              style: context.textTheme.h3.semiBold,
            ),
            const SizedBox(height: 12),
            Html(
              data: displayText,
              style: {
                Constants.body: Style(
                  fontSize: FontSize(context.textTheme.textMedium.fontSize ?? 0),
                ),
              },
            ),
            if (widget.description.length > charLimit)
              ShowMoreToggle(isExpanded: isExpanded, onTap: _toggleExpanded),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  String truncateText(String text, int charLimit) {
    return (text.length <= charLimit) ? text : '${text.substring(0, charLimit)}...';
  }
}
