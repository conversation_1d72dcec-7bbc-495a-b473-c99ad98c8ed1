import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/temp/plural_translations.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/show_more_toggle.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingSyllabus extends StatefulWidget {
  const TrainingSyllabus({required this.trainingStructureModel, super.key});

  final TrainingStructureModel trainingStructureModel;

  @override
  State<TrainingSyllabus> createState() => _TrainingSyllabusState();
}

class _TrainingSyllabusState extends State<TrainingSyllabus> {
  bool isExpanded = false;
  late final TrainingStructureModel trainingStructureModel;

  @override
  void initState() {
    super.initState();
    trainingStructureModel = widget.trainingStructureModel;
  }

  @override
  Widget build(BuildContext context) {
    final sectionCount = trainingStructureModel.sections.length;
    final lessonsCount = trainingStructureModel.sections.fold(
      0,
      (previousValue, element) => previousValue + element.lessons.length,
    );

    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),

          Text(
            LocaleKeys.learningTracks_builder_syllabus.tr(),
            style: context.textTheme.h3.semiBold,
          ),

          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                mapNumberToSections(sectionCount, context.locale.languageCode),
                style: context.textTheme.textSmall.greyPrimary,
              ),
              Container(
                width: 3,
                height: 3,
                margin: const EdgeInsets.symmetric(horizontal: 6),
                decoration: const BoxDecoration(
                  color: AppColors.greyTertiary,
                  shape: BoxShape.circle,
                ),
              ),
              Text(
                mapNumberToLessons(lessonsCount, context.locale.languageCode),
                style: context.textTheme.textSmall.greyPrimary,
              ),
            ],
          ),

          const SizedBox(height: 20),

          for (int i = 0; i < ((isExpanded || sectionCount < 4) ? sectionCount : 4); i++)
            CourseContentItem(trainingStructureModel.sections[i]),

          if (sectionCount > 4)
            ShowMoreToggle(
              isExpanded: isExpanded,
              onTap: () {
                setState(() {
                  isExpanded = !isExpanded;
                });
              },
            ),
          //
        ],
      ),
    );
  }
}
