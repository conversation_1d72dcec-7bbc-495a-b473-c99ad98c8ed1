import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class TrainingOverviewItem extends StatelessWidget {
  const TrainingOverviewItem(this.lesson, {super.key});

  final Lesson lesson;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Image.asset(
              getLessonIcon(lesson.lessonType),
              color: AppColors.additionalGrey,
              width: 18,
            ),
          ),
          const SizedBox(width: 8),
          Flexible(child: Text(lesson.title, style: context.textTheme.textMedium)),
        ],
      ),
    );
  }

  String getLessonIcon(LessonType? lessonType) {
    switch (lessonType) {
      case LessonType.Quiz:
        return AssetsPath.quizLessonIcon;
      case LessonType.Article:
        return AssetsPath.articleLessonIcon;
      case LessonType.File:
        return AssetsPath.fileLessonIcon;
      case LessonType.Slide:
        return AssetsPath.slidesLessonIcon;
      case LessonType.Video:
        return AssetsPath.videoLessonIcon;
      default:
        return AssetsPath.articleLessonIcon;
    }
  }
}
