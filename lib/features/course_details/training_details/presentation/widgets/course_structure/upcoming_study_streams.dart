import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/mixins/study_stream_details_helper_mixin.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_details_aligner.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/stream_access_button.dart';
import 'package:national_skills_platform/features/course_details/utils/session_days_analyzer.dart';
import 'package:national_skills_platform/features/course_details/utils/training_type_utils.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class UpcomingStudyStreams extends StatelessWidget
    with StudyStreamDetailsHelperMixin, TrainingTypeUtils {
  UpcomingStudyStreams({required this.trainingDetailsModel, super.key})
      : _sessionDaysAnalyzer = const SessionDaysAnalyzer();

  final TrainingDetailsModel trainingDetailsModel;
  final SessionDaysAnalyzer _sessionDaysAnalyzer;

  @override
  Widget build(BuildContext context) {
    final studyStreams = trainingDetailsModel.studyStreams;

    if (studyStreams.isEmpty) return const SizedBox();

    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(LocaleKeys.upcomingStudyStreams.tr(), style: context.textTheme.h3.semiBold),
          const SizedBox(height: 20),
          for (int i = 0; i < studyStreams.length; i++) ...[
            if (i > 0) const Divider(height: 32),

            /// Training type indicator with icon
            Row(
              children: [
                Image.asset(
                  getTrainingTypeImage(trainingDetailsModel, studyStreams[i]),
                  width: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  getTrainingTypeString(trainingDetailsModel, studyStreams[i]),
                  style: context.textTheme.textMedium.medium.accentGreenPrimary,
                ),
              ],
            ),

            const SizedBox(height: 6),

            /// Date range
            Text(
              formatDateRange(studyStreams[i]?.startDate, studyStreams[i]?.endDate),
              style: context.textTheme.textLarge.semiBold,
            ),

            const SizedBox(height: 8),

            /// Time range with schedule
            Text(
              '${formatTimeRange(studyStreams[i]?.startDate, studyStreams[i]?.endDate)}${_sessionDaysAnalyzer.getSessionDaysInfo(trainingDetailsModel)} ${getScheduleDisplay(studyStreams[i], context)}',
              style: context.textTheme.textSmall.medium,
            ),

            const SizedBox(height: 8),

            Text(getSeatingCapacityText(studyStreams[i]), style: context.textTheme.textSmall),

            const SizedBox(height: 16),

            if (studyStreams[i]?.location != null) ...[
              GestureDetector(
                onTap: () => openLocationInMaps(studyStreams[i]?.location),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.asset(
                      AssetsPath.locationIcon,
                      width: 20,
                      height: 20,
                      color: AppColors.neutralBlack,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            getLocalizedAddress(context, studyStreams[i]?.location),
                            style: context.textTheme.textSmall.medium,
                          ),
                          if (studyStreams[i]?.location?.city != null) ...[
                            const SizedBox(height: 2),
                            Text(
                              getLocalizedCityRegion(context, studyStreams[i]?.location),
                              style: context.textTheme.textSmall.medium,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            /// Enroll button
            StreamAccessButton(
              trainingDetailsModel: trainingDetailsModel,
              studyStream: studyStreams[i],
            ),
          ],
        ],
      ),
    );
  }
}
