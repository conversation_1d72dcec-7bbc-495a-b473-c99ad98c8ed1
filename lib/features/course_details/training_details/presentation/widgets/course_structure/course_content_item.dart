import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_structure/content_lessons_count.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_structure/training_overview_item.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_expander_icon.dart';

class CourseContentItem extends StatefulWidget {
  const CourseContentItem(this.section, {super.key});

  final Section section;

  @override
  State<CourseContentItem> createState() => _CourseContentItemState();
}

class _CourseContentItemState extends State<CourseContentItem> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppDivider(),

        const Sized<PERSON>ox(height: 16),

        GestureDetector(
          onTap: () => setState(() => isExpanded = !isExpanded),
          behavior: HitTestBehavior.opaque,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(widget.section.title, style: context.textTheme.textMedium.semiBold),
              ),
              AppExpanderIcon(isExpanded: isExpanded),
            ],
          ),
        ),
        const SizedBox(height: 4),

        ///widget: Section 2 . 3 Lessons
        ContentLessonsCount(widget.section),

        const SizedBox(height: 16),

        if (isExpanded) ...[
          for (int i = 0; i < widget.section.lessons.length; i++)
            TrainingOverviewItem(widget.section.lessons[i]),
        ],
        const AppDivider(),
      ],
    );
  }
}
