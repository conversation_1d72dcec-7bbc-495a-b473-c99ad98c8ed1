import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/temp/plural_translations.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ContentLessonsCount extends StatelessWidget {
  const ContentLessonsCount(this.section, {super.key});

  final Section section;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          LocaleKeys.trainingDetails_sectionIndex.tr(args: [(section.index + 1).toString()]),
          style: context.textTheme.textSmall.greyPrimary,
        ),
        Container(
          width: 3,
          height: 3,
          margin: const EdgeInsets.symmetric(horizontal: 6),
          decoration: const BoxDecoration(color: AppColors.greyTertiary, shape: BoxShape.circle),
        ),
        Text(
          mapNumberToLessons(section.lessons.length, context.locale.languageCode),
          style: context.textTheme.textSmall.greyPrimary,
        ),
      ],
    );
  }
}
