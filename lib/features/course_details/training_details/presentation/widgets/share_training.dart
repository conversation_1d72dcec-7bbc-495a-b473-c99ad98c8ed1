import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/environment/environment_configs.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:share_plus/share_plus.dart';

class ShareTraining extends StatelessWidget {
  const ShareTraining(this.trainingID, {super.key});

  final String trainingID;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      child: GestureDetector(
        excludeFromSemantics: true,
        onTap: () => SharePlus.instance.share(
          ShareParams(
            text: '${EnvironmentConfigs.websiteUrl}${ApiConstants.trainingDetailsPath}/$trainingID',
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 19, vertical: 8),
          child: Image.asset(AssetsPath.shareIcon, height: 24, semanticLabel: 'Share'),
        ),
      ),
    );
  }
}
