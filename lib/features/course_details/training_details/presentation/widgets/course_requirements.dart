import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CourseRequirements extends StatelessWidget {
  const CourseRequirements({required this.trainingDetailsModel, super.key});

  final CourseDetailsInterface trainingDetailsModel;

  @override
  Widget build(BuildContext context) {
    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(LocaleKeys.trainingDetails_requirements.tr(), style: context.textTheme.h3.semiBold),
          const SizedBox(height: 12),
          ...trainingDetailsModel.requirements
              .map((requirement) {
                if (requirement.value.isEmpty) return const SizedBox.shrink();

                return Text(
                  '• ${requirement.value}',
                  softWrap: true,
                  style: context.textTheme.textMedium,
                );
              })
              .toList()
              .divide(divider: const SizedBox(height: 4)),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
