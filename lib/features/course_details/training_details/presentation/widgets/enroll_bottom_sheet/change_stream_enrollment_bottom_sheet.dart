import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class ChangeStreamEnrollmentBottomSheet extends StatelessWidget {
  const ChangeStreamEnrollmentBottomSheet({super.key, required this.enroll});

  final VoidCallback enroll;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          const SizedBox(height: 24),

          CircleAvatar(
            backgroundColor: AppColors.orangeAccentLight,
            radius: 39,
            child: Image.asset(width: 40, height: 40, AssetsPath.warningIcon),
          ),

          const SizedBox(height: 24),

          Text(
            LocaleKeys.rYouSureToEnrollToStream.tr(),
            style: context.textTheme.textLarge.semiBold,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            LocaleKeys.previousStreamWillBeDisabled.tr(),
            style: context.textTheme.textSmall,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          AppButton(onTap: enroll, buttonText: LocaleKeys.yesEnroll.tr()),

          const SizedBox(height: 16),

          AppButton(
            onTap: () => router.pop(),
            buttonText: LocaleKeys.cancel.tr(),
            backgroundColor: AppColors.uiBackgroundPrimary,
            borderColor: AppColors.accentLight,
          ),

          const SizedBox(height: 45),

          //
        ],
      ),
    );
  }
}
