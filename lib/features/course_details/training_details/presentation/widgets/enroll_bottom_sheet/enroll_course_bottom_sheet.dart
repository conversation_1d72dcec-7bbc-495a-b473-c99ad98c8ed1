import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

/// Enroll Bottom Sheet used in both training and learning track
class EnrollCourseBottomSheet extends StatelessWidget {
  const EnrollCourseBottomSheet({
    required this.courseType,
    required this.trainingDetailsModel,
    super.key,
  });

  final CourseType courseType;
  final TrainingDetailsModel? trainingDetailsModel;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          Align(
            alignment: Alignment.topRight,
            child: GestureDetector(onTap: router.pop, child: const Icon(Icons.clear)),
          ),
          CircleAvatar(
            radius: 39,
            backgroundColor: courseType == CourseType.training
                ? AppColors.accentExtraLight
                : AppColors.orangeAccentLight,
            child: Image.asset(
              width: 40,
              height: 40,
              courseType == CourseType.training ? AssetsPath.bookIcon : AssetsPath.booksIcon,
              color: courseType == CourseType.training
                  ? AppColors.greenAccentPrimary
                  : AppColors.orangeAccentPrimary,
            ),
          ),

          const SizedBox(height: 24),

          Text(
            courseType == CourseType.training
                ? LocaleKeys.trainingDetails_trainingEnrollmentModalTitle.tr()
                : LocaleKeys.trainingDetails_lTrackEnrollmentModalTitle.tr(),
            style: context.textTheme.textLarge.semiBold,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              LocaleKeys.enrollSuccessBody.tr(),
              style: context.textTheme.textSmall.greyPrimary,
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 32),

          AppButton(
            onTap: () async {
              router.pop();
              if (courseType == CourseType.training) {
                await router.pushNamed(
                  Routes.trainingConsumptionPage.name,
                  extra: trainingDetailsModel,
                );
              } else {
                // For learning tracks, navigate to My Learning page
                router.go('${Routes.rootPage.path}${Routes.myLearningPage.path}');
              }
            },
            buttonText: courseType == CourseType.training
                ? LocaleKeys.trainingDetails_goToTraining.tr()
                : LocaleKeys.bottom_nav_bar_myLearnings.tr(),
          ),

          const SizedBox(height: 45),

          //
        ],
      ),
    );
  }
}
