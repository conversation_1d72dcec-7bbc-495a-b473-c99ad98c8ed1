import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_chip.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SkillsYouGain extends StatelessWidget {
  const SkillsYouGain({required this.skills, super.key});

  final List<String> skills;

  @override
  Widget build(BuildContext context) {
    return TrainingDetailAligner(
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            Text(
              LocaleKeys.trainingDetails_skillsYouWillGain.tr(),
              style: context.textTheme.h3.semiBold,
            ),
            const SizedBox(height: 8),
            if (skills.isNotEmpty)
              Wrap(
                runSpacing: 12,
                spacing: 12,
                children: skills.map((skill) => AppChip(text: skill, height: 34)).toList(),
              ),
            const SizedBox(height: 26),
          ],
        ),
      ),
    );
  }
}
