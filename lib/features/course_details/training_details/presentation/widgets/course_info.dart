import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';

class CourseInfoItem extends StatelessWidget {
  const CourseInfoItem({
    required this.title,
    required this.iconPath,
    this.valueText,
    this.valueWidget,
    super.key,
  }) : assert(
          (valueText == null) != (valueWidget == null),
          'Only one parameter should be provided, either value or valueWidget',
        );

  final String title;
  final String iconPath;
  final String? valueText;
  final Widget? valueWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Image.asset(
              iconPath,
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: context.textTheme.textMedium.semiBold.copyWith(
                  color: AppColors.neutralBlack,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (valueText != null)
          Text(
            valueText!,
            style: context.textTheme.textMedium.copyWith(
              color: AppColors.neutralBlack,
            ),
          )
        else
          valueWidget ?? const SizedBox.shrink(),
        const SizedBox(height: 24),
      ],
    );
  }
}
