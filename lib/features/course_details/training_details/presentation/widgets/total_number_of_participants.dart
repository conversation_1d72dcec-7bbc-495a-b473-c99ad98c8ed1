import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TotalNumberOfParticipants extends StatelessWidget {
  const TotalNumberOfParticipants({super.key, required this.trainingDetailsModel});

  final CourseDetailsInterface trainingDetailsModel;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
      builder: (context, isLoggedIn) {
        if (isLoggedIn.data == true) {
          return Column(
            children: [
              const SizedBox(height: 12),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '${trainingDetailsModel.enrolledCount} ',
                      style: context.textTheme.textSmall.semiBold,
                    ),
                    TextSpan(
                      text: LocaleKeys.seats_total.tr(args: ['']),
                      style: context.textTheme.textSmall,
                    ),
                  ],
                ),
              ),
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
