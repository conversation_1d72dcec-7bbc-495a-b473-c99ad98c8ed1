import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/video_player/app_video_player.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class PromoVideo extends StatelessWidget {
  const PromoVideo(this.promoVideoUrl, {super.key});
  final String promoVideoUrl;
  @override
  Widget build(BuildContext context) {
    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(LocaleKeys.trainingDetails_promoVideo.tr(), style: context.textTheme.h3.semiBold),
          const SizedBox(height: 14),
          AppVideoPlayer(promoVideoUrl),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
