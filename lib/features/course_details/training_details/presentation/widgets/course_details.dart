import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/shared/extensions/string_extensions.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_details_aligner.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_info.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CourseInfo extends StatelessWidget {
  const CourseInfo({required this.trainingDetailsModel, super.key});

  final CourseDetailsInterface trainingDetailsModel;

  @override
  Widget build(BuildContext context) {
    final language = trainingDetailsModel.language.isNotEmpty
        ? trainingDetailsModel.language
        : trainingDetailsModel.languageCode.languageCodeToName();
    final durationText = getDurationText(trainingDetailsModel);
    final seatingCapacity = trainingDetailsModel.seatingCapacities.firstOrNull;
    final firstStudyStream = trainingDetailsModel.studyStreams.firstOrNull;

    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          //Todo: delayed feature
          // CourseInfoItem(
          //   title: LocaleKeys.trainingDetails_averageRate.tr(),
          //   valueWidget: Row(
          //     children: [
          //       Text('4.0', style: context.textTheme.textSmall.semiBold),
          //       const SizedBox(width: 1),
          //       const Icon(Icons.star, color: AppColors.orangeAccentPrimary, size: 16),
          //       const SizedBox(width: 8),
          //       Text(
          //         "(${LocaleKeys.reviewsCount.tr(args: ['0'])})",
          //         style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
          //       ),
          //     ],
          //   ),
          // ),
          if (seatingCapacity != null)
            CourseInfoItem(
              iconPath: AssetsPath.seatingCapacityIcon,
              valueText:
                  '${seatingCapacity.actualNumberOfEnrollments} out of ${seatingCapacity.maxNumberOfEnrollments}',
              title: LocaleKeys.seatingCapacity.tr(),
            ),
          if (firstStudyStream != null)
            CourseInfoItem(
              iconPath: AssetsPath.startEndDateIcon,
              valueText: formatDateRange(firstStudyStream.startDate, firstStudyStream.endDate),
              title: LocaleKeys.start_end_date.tr(),
            ),
          CourseInfoItem(
            iconPath: AssetsPath.estimatedTimeIcon,
            valueText: durationText,
            title: LocaleKeys.trainingDetails_estimatedTime.tr(),
          ),
          CourseInfoItem(
            iconPath: AssetsPath.languageIcon,
            valueText: language,
            title: LocaleKeys.learningTracks_builder_languageLabel.tr(),
          ),
          CourseInfoItem(
            iconPath: AssetsPath.levelIcon,
            valueText: trainingDetailsModel.level,
            title: LocaleKeys.learningTracks_builder_levelLabel.tr(),
          ),
        ],
      ),
    );
  }

  int getRemainingSeatsCount(SeatingCapacity? seatingCapacity) {
    if (seatingCapacity == null) return 0;

    final seatsLeft =
        seatingCapacity.maxNumberOfEnrollments - seatingCapacity.actualNumberOfEnrollments;
    if (seatsLeft < 0) return 0;

    return seatsLeft;
  }

  String getDurationText(CourseDetailsInterface trainingDetailsModel) {
    final parsedDuration = int.tryParse(trainingDetailsModel.duration) ?? 0;
    final cappedDuration = parsedDuration > 100 ? 100 : parsedDuration;
    final durationTranslation = LocaleKeys.trainingDetails_estimatedTimeValue.tr(
      args: [trainingDetailsModel.duration],
    );
    final durationText = cappedDuration > 100 ? '>$durationTranslation' : durationTranslation;

    return durationText;
  }
}
