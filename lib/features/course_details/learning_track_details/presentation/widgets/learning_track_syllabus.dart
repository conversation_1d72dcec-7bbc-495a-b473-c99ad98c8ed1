import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/presentation/widgets/timeline_card.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/ui_components/show_more_toggle.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:timelines_plus/timelines_plus.dart';

class LearningTrackSyllabus extends StatefulWidget {
  const LearningTrackSyllabus(
    this.trainingsContentModel, {
    super.key,
  });

  final List<TrainingContentModel> trainingsContentModel;

  @override
  State<LearningTrackSyllabus> createState() => _LearningTrackSyllabusState();
}

class _LearningTrackSyllabusState extends State<LearningTrackSyllabus> {
  bool isExpanded = false;
  final hideTrainingsAfter = 4;
  late int itemCount;

  @override
  void initState() {
    super.initState();
    itemCount = widget.trainingsContentModel.length;
  }

  @override
  Widget build(BuildContext context) {
    return TrainingDetailAligner(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 20),
          Text(
            LocaleKeys.learningTracks_builder_syllabus.tr(),
            style: context.textTheme.h3.semiBold,
          ),

          const SizedBox(height: 4),
          Text(
            LocaleKeys.learningTracks_trainingsCount.plural(itemCount),
            style: context.textTheme.textSmall.greyPrimary,
          ),
          const SizedBox(height: 20),
          if (widget.trainingsContentModel.isNotEmpty)
            Timeline.tileBuilder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              theme: TimelineThemeData(nodePosition: 0, indicatorPosition: 0),
              builder: TimelineTileBuilder.connected(
                connectionDirection: ConnectionDirection.before,
                itemCount: (itemCount <= hideTrainingsAfter || isExpanded)
                    ? itemCount
                    : hideTrainingsAfter,
                contentsBuilder: (_, index) =>
                    TimelineCard(trainingModel: widget.trainingsContentModel[index]),
                connectorBuilder: (_, index, ___) =>
                    const SolidLineConnector(color: AppColors.accentLight, thickness: 1),
                indicatorBuilder: (_, index) => Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.accentExtraLight,
                    border: Border.all(color: AppColors.greenAccentPrimary),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: context.locale == const Locale(Constants.localeAR) ? 4 : 0,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: context.textTheme.textXSmall.accentGreenPrimary.semiBold,
                      ),
                    ),
                  ),
                ),
              ),
            ),

          if (itemCount > hideTrainingsAfter)
            ShowMoreToggle(isExpanded: isExpanded, onTap: _toggleExpanded),

          const SizedBox(height: 12),
          //
        ],
      ),
    );
  }

  void _toggleExpanded() {
    setState(() {
      isExpanded = !isExpanded;
    });
  }
}
