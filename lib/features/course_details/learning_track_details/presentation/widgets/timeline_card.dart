import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/training_type_string.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/training_card_bottom_info_panel.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/router.dart';

class TimelineCard extends StatelessWidget {
  const TimelineCard({required this.trainingModel, super.key});

  final TrainingContentModel trainingModel;

  @override
  Widget build(BuildContext context) {
    final profileImageUrl = trainingModel.profileImageUrl;
    const imageHeight = 174.0;

    return GestureDetector(
      excludeFromSemantics: true,
      onTap: () {
        if (trainingModel.id.isNotEmpty) {
          router.pushNamed(Routes.trainingDetailsPage.name, extra: trainingModel.id);
        } else {
          showAppToast(context, message: 'Failed to open the card');
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12, left: 16, right: 16),
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.accentLight),
          borderRadius: BorderRadius.circular(8),
        ),
        foregroundDecoration: BoxDecoration(
          border: Border.all(color: AppColors.accentLight),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Flex(
          crossAxisAlignment: CrossAxisAlignment.start,
          direction: Axis.vertical,
          children: [
            if (profileImageUrl != null && profileImageUrl.isNotEmpty)
              CachedNetworkImage(
                fit: BoxFit.fitWidth,
                imageUrl: profileImageUrl,
                errorWidget: (ctx, _, __) => const SizedBox.shrink(),
                placeholder: (context, url) => const ShimmerPlaceholder(height: imageHeight),
              )
            else
              //This line is added to fix golden tests, because it gets stuck on infinite loading
              Container(height: imageHeight, color: Colors.grey),
            Flexible(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      trainingContentTypeString(trainingModel),
                      style: context.textTheme.textSmall.medium.accentGreenPrimary,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      trainingModel.title ?? '',
                      style: context.textTheme.textSmall.semiBold,
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      trainingModel.organizationName,
                      style: context.textTheme.textXSmall.greyPrimary.medium,
                      maxLines: 2,
                      softWrap: true,
                    ),
                    const AppDivider(padding: EdgeInsets.symmetric(vertical: 12)),
                    const Spacer(),
                    TrainingCardBottomInfoPanel(trainingModel: trainingModel),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
