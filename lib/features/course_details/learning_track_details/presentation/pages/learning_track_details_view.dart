import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/enroll_bottom_sheet/enroll_course_bottom_sheet.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

class LearningTrackDetailsView extends StatelessWidget {
  const LearningTrackDetailsView({
    required this.learningTrackID,
    this.isBottomSheetState = false,
    super.key,
  });

  final String learningTrackID;
  final bool isBottomSheetState;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<CourseDetailsBloc, CourseDetailsState>(
        listener: (context, state) {
          if (state.courseDetailsError.isNotEmpty) {
            showAppToast(context, message: state.courseDetailsError);
          }

          if (state.enrollLearningTrackSuccess) {
            showEnrollSuccessMessage(context, state.trainingDetailsModel);
          }

          if (state.enrollLearningTrackError.isNotEmpty) {
            showAppToast(context, message: state.enrollLearningTrackError);
          }
        },
        builder: (context, state) {
          if (state.courseDetailsError.isNotEmpty) return const SizedBox.shrink();
          if (state.isCourseDetailsLoading) return const BuildLoader();

          final learningTrackDetailsModel = state.learningTrackDetailsModel;
          if (learningTrackDetailsModel != null) {
            final learningTrackTrainings = learningTrackDetailsModel.trainings;
            final skills = learningTrackDetailsModel.trainings
                .expand((training) => training.skills)
                .toSet()
                .toList();

            return SizedBox(
              height: MediaQuery.sizeOf(context).height,
              width: MediaQuery.sizeOf(context).width,
              child: AppLoadingOverlay(
                isLoading: state.isLearningTrackEnrollmentInProgress,
                child: Scaffold(
                  appBar: AppBar(
                    leading: isBottomSheetState ? const CloseButton() : const AppBackButton(),
                    actions: [ShareTraining(learningTrackID)],
                    bottom: const PreferredSize(
                      preferredSize: Size.fromHeight(0),
                      child: AppDivider(),
                    ),
                  ),
                  body: ListView(
                    children: [
                      CourseHeaderPanel(
                        trainingDetailsModel: learningTrackDetailsModel,
                        courseType: CourseType.learningTrack,
                        showEnrollButton: state.showEnrollButton,
                      ),

                      const AppDivider(),

                      CourseInfo(trainingDetailsModel: learningTrackDetailsModel),

                      const AppDivider(),

                      if (learningTrackDetailsModel.description.isNotEmpty)
                        CourseDescription(learningTrackDetailsModel.description),

                      if (learningTrackDetailsModel.requirements.isNotEmpty)
                        CourseRequirements(trainingDetailsModel: learningTrackDetailsModel),

                      if (learningTrackDetailsModel.outcomes.isNotEmpty)
                        WhatYoullLearn(outcomes: learningTrackDetailsModel.outcomes),

                      if (skills.isNotEmpty) SkillsYouGain(skills: skills),

                      if (learningTrackTrainings.isNotEmpty)
                        LearningTrackSyllabus(learningTrackDetailsModel.trainings),

                      const AppDivider(padding: EdgeInsets.only(top: 40, bottom: 8)),

                      const TrainingDetailAligner(
                        child: CourseAccessButton(courseType: CourseType.learningTrack),
                      ),

                      const SizedBox(height: 30),

                      //
                    ],
                  ),
                ),
              ),
            );
          }

          return const Center(child: Text('Unknown state'));
        },
      ),
    );
  }

  Future<void> showEnrollSuccessMessage(
    BuildContext context,
    TrainingDetailsModel? trainingDetailsModel,
  ) =>
      showModalBottomSheet(
        backgroundColor: Colors.white,
        shape: const RoundedRectangleBorder(borderRadius: SharedDecoration.borderTopLeftRight10),
        context: context,
        builder: (_) => BlocProvider.value(
          value: context.read<CourseDetailsBloc>(),
          child: EnrollCourseBottomSheet(
            courseType: CourseType.learningTrack,
            trainingDetailsModel: trainingDetailsModel,
          ),
        ),
      );
}
