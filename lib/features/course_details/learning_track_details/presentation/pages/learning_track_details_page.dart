import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/presentation/pages/learning_track_details_view.dart';

class LearningTrackDetailsPage extends StatelessWidget {
  const LearningTrackDetailsPage({
    required this.learningTrackID,
    this.isBottomSheetState = false,
    super.key,
  });

  final String learningTrackID;
  final bool isBottomSheetState;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance.get<CourseDetailsBloc>()
        ..add(LoadLearningTrackDetailsEvent(id: learningTrackID)),
      child: LearningTrackDetailsView(
        learningTrackID: learningTrackID,
        isBottomSheetState: isBottomSheetState,
      ),
    );
  }
}
