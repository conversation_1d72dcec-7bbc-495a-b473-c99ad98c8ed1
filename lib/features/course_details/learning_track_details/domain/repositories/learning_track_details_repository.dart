import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/data_sources/learning_track_details_datasource.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';

@injectable
class LearningTrackDetailsRepository {
  const LearningTrackDetailsRepository({required LearningTrackDetailsDataSource dataSource})
      : _dataSource = dataSource;

  final LearningTrackDetailsDataSource _dataSource;

  Future<LearningTrackDetailsModel> loadLearningTracks(String courseId) {
    return _dataSource.getLearningTracks(courseId);
  }

  Future<void> enrollLearningTrack({required String courseId}) {
    return _dataSource.enrollLearningTrack(courseId);
  }
}
