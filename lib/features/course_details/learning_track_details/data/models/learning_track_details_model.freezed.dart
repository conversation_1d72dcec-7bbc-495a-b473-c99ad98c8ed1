// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'learning_track_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LearningTrackDetailsModel {
  String get id;
  String get title;
  String get description;
  String get level;
  String get status;
  ImageModel? get profileImage;
  String get profileImageUrl;
  Sector? get sector;
  Domain? get domain;
  @JsonKey(defaultValue: '')
  String get organizationName;
  bool get forNominationOnly;
  @JsonKey(defaultValue: '')
  String get languageCode;
  @JsonKey(defaultValue: '')
  String get trainingProviderName;
  @JsonKey(defaultValue: 0)
  int get trainingCount;
  @JsonKey(defaultValue: [])
  List<TrainingContentModel> get trainings;
  @JsonKey(defaultValue: [])
  List<Requirement> get requirements;
  @JsonKey(defaultValue: [])
  List<Outcome> get outcomes;
  ApplicantDto? get applicantDto;
  DateTime? get createdDate;
  String? get titleAr;
  String? get descriptionAr;
  String? get descriptionArJson;
  String? get skillLevel;
  int? get version;
  String? get rootId;
  String? get copiedFromId;

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LearningTrackDetailsModelCopyWith<LearningTrackDetailsModel> get copyWith =>
      _$LearningTrackDetailsModelCopyWithImpl<LearningTrackDetailsModel>(
          this as LearningTrackDetailsModel, _$identity);

  /// Serializes this LearningTrackDetailsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LearningTrackDetailsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.sector, sector) || other.sector == sector) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.forNominationOnly, forNominationOnly) ||
                other.forNominationOnly == forNominationOnly) &&
            (identical(other.languageCode, languageCode) || other.languageCode == languageCode) &&
            (identical(other.trainingProviderName, trainingProviderName) ||
                other.trainingProviderName == trainingProviderName) &&
            (identical(other.trainingCount, trainingCount) ||
                other.trainingCount == trainingCount) &&
            const DeepCollectionEquality().equals(other.trainings, trainings) &&
            const DeepCollectionEquality().equals(other.requirements, requirements) &&
            const DeepCollectionEquality().equals(other.outcomes, outcomes) &&
            (identical(other.applicantDto, applicantDto) || other.applicantDto == applicantDto) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.titleAr, titleAr) || other.titleAr == titleAr) &&
            (identical(other.descriptionAr, descriptionAr) ||
                other.descriptionAr == descriptionAr) &&
            (identical(other.descriptionArJson, descriptionArJson) ||
                other.descriptionArJson == descriptionArJson) &&
            (identical(other.skillLevel, skillLevel) || other.skillLevel == skillLevel) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            (identical(other.copiedFromId, copiedFromId) || other.copiedFromId == copiedFromId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        description,
        level,
        status,
        profileImage,
        profileImageUrl,
        sector,
        domain,
        organizationName,
        forNominationOnly,
        languageCode,
        trainingProviderName,
        trainingCount,
        const DeepCollectionEquality().hash(trainings),
        const DeepCollectionEquality().hash(requirements),
        const DeepCollectionEquality().hash(outcomes),
        applicantDto,
        createdDate,
        titleAr,
        descriptionAr,
        descriptionArJson,
        skillLevel,
        version,
        rootId,
        copiedFromId
      ]);

  @override
  String toString() {
    return 'LearningTrackDetailsModel(id: $id, title: $title, description: $description, level: $level, status: $status, profileImage: $profileImage, profileImageUrl: $profileImageUrl, sector: $sector, domain: $domain, organizationName: $organizationName, forNominationOnly: $forNominationOnly, languageCode: $languageCode, trainingProviderName: $trainingProviderName, trainingCount: $trainingCount, trainings: $trainings, requirements: $requirements, outcomes: $outcomes, applicantDto: $applicantDto, createdDate: $createdDate, titleAr: $titleAr, descriptionAr: $descriptionAr, descriptionArJson: $descriptionArJson, skillLevel: $skillLevel, version: $version, rootId: $rootId, copiedFromId: $copiedFromId)';
  }
}

/// @nodoc
abstract mixin class $LearningTrackDetailsModelCopyWith<$Res> {
  factory $LearningTrackDetailsModelCopyWith(
          LearningTrackDetailsModel value, $Res Function(LearningTrackDetailsModel) _then) =
      _$LearningTrackDetailsModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String level,
      String status,
      ImageModel? profileImage,
      String profileImageUrl,
      Sector? sector,
      Domain? domain,
      @JsonKey(defaultValue: '') String organizationName,
      bool forNominationOnly,
      @JsonKey(defaultValue: '') String languageCode,
      @JsonKey(defaultValue: '') String trainingProviderName,
      @JsonKey(defaultValue: 0) int trainingCount,
      @JsonKey(defaultValue: []) List<TrainingContentModel> trainings,
      @JsonKey(defaultValue: []) List<Requirement> requirements,
      @JsonKey(defaultValue: []) List<Outcome> outcomes,
      ApplicantDto? applicantDto,
      DateTime? createdDate,
      String? titleAr,
      String? descriptionAr,
      String? descriptionArJson,
      String? skillLevel,
      int? version,
      String? rootId,
      String? copiedFromId});

  $ImageModelCopyWith<$Res>? get profileImage;
  $SectorCopyWith<$Res>? get sector;
  $DomainCopyWith<$Res>? get domain;
  $ApplicantDtoCopyWith<$Res>? get applicantDto;
}

/// @nodoc
class _$LearningTrackDetailsModelCopyWithImpl<$Res>
    implements $LearningTrackDetailsModelCopyWith<$Res> {
  _$LearningTrackDetailsModelCopyWithImpl(this._self, this._then);

  final LearningTrackDetailsModel _self;
  final $Res Function(LearningTrackDetailsModel) _then;

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? level = null,
    Object? status = null,
    Object? profileImage = freezed,
    Object? profileImageUrl = null,
    Object? sector = freezed,
    Object? domain = freezed,
    Object? organizationName = null,
    Object? forNominationOnly = null,
    Object? languageCode = null,
    Object? trainingProviderName = null,
    Object? trainingCount = null,
    Object? trainings = null,
    Object? requirements = null,
    Object? outcomes = null,
    Object? applicantDto = freezed,
    Object? createdDate = freezed,
    Object? titleAr = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionArJson = freezed,
    Object? skillLevel = freezed,
    Object? version = freezed,
    Object? rootId = freezed,
    Object? copiedFromId = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      sector: freezed == sector
          ? _self.sector
          : sector // ignore: cast_nullable_to_non_nullable
              as Sector?,
      domain: freezed == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as Domain?,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      forNominationOnly: null == forNominationOnly
          ? _self.forNominationOnly
          : forNominationOnly // ignore: cast_nullable_to_non_nullable
              as bool,
      languageCode: null == languageCode
          ? _self.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      trainingProviderName: null == trainingProviderName
          ? _self.trainingProviderName
          : trainingProviderName // ignore: cast_nullable_to_non_nullable
              as String,
      trainingCount: null == trainingCount
          ? _self.trainingCount
          : trainingCount // ignore: cast_nullable_to_non_nullable
              as int,
      trainings: null == trainings
          ? _self.trainings
          : trainings // ignore: cast_nullable_to_non_nullable
              as List<TrainingContentModel>,
      requirements: null == requirements
          ? _self.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<Requirement>,
      outcomes: null == outcomes
          ? _self.outcomes
          : outcomes // ignore: cast_nullable_to_non_nullable
              as List<Outcome>,
      applicantDto: freezed == applicantDto
          ? _self.applicantDto
          : applicantDto // ignore: cast_nullable_to_non_nullable
              as ApplicantDto?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      titleAr: freezed == titleAr
          ? _self.titleAr
          : titleAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _self.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionArJson: freezed == descriptionArJson
          ? _self.descriptionArJson
          : descriptionArJson // ignore: cast_nullable_to_non_nullable
              as String?,
      skillLevel: freezed == skillLevel
          ? _self.skillLevel
          : skillLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      copiedFromId: freezed == copiedFromId
          ? _self.copiedFromId
          : copiedFromId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorCopyWith<$Res>? get sector {
    if (_self.sector == null) {
      return null;
    }

    return $SectorCopyWith<$Res>(_self.sector!, (value) {
      return _then(_self.copyWith(sector: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DomainCopyWith<$Res>? get domain {
    if (_self.domain == null) {
      return null;
    }

    return $DomainCopyWith<$Res>(_self.domain!, (value) {
      return _then(_self.copyWith(domain: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApplicantDtoCopyWith<$Res>? get applicantDto {
    if (_self.applicantDto == null) {
      return null;
    }

    return $ApplicantDtoCopyWith<$Res>(_self.applicantDto!, (value) {
      return _then(_self.copyWith(applicantDto: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _LearningTrackDetailsModel extends LearningTrackDetailsModel {
  const _LearningTrackDetailsModel(
      {required this.id,
      required this.title,
      required this.description,
      required this.level,
      required this.status,
      required this.profileImage,
      required this.profileImageUrl,
      required this.sector,
      required this.domain,
      @JsonKey(defaultValue: '') required this.organizationName,
      required this.forNominationOnly,
      @JsonKey(defaultValue: '') required this.languageCode,
      @JsonKey(defaultValue: '') required this.trainingProviderName,
      @JsonKey(defaultValue: 0) required this.trainingCount,
      @JsonKey(defaultValue: []) required final List<TrainingContentModel> trainings,
      @JsonKey(defaultValue: []) required final List<Requirement> requirements,
      @JsonKey(defaultValue: []) required final List<Outcome> outcomes,
      this.applicantDto,
      this.createdDate,
      this.titleAr,
      this.descriptionAr,
      this.descriptionArJson,
      this.skillLevel,
      this.version,
      this.rootId,
      this.copiedFromId})
      : _trainings = trainings,
        _requirements = requirements,
        _outcomes = outcomes,
        super._();
  factory _LearningTrackDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$LearningTrackDetailsModelFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String description;
  @override
  final String level;
  @override
  final String status;
  @override
  final ImageModel? profileImage;
  @override
  final String profileImageUrl;
  @override
  final Sector? sector;
  @override
  final Domain? domain;
  @override
  @JsonKey(defaultValue: '')
  final String organizationName;
  @override
  final bool forNominationOnly;
  @override
  @JsonKey(defaultValue: '')
  final String languageCode;
  @override
  @JsonKey(defaultValue: '')
  final String trainingProviderName;
  @override
  @JsonKey(defaultValue: 0)
  final int trainingCount;
  final List<TrainingContentModel> _trainings;
  @override
  @JsonKey(defaultValue: [])
  List<TrainingContentModel> get trainings {
    if (_trainings is EqualUnmodifiableListView) return _trainings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trainings);
  }

  final List<Requirement> _requirements;
  @override
  @JsonKey(defaultValue: [])
  List<Requirement> get requirements {
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requirements);
  }

  final List<Outcome> _outcomes;
  @override
  @JsonKey(defaultValue: [])
  List<Outcome> get outcomes {
    if (_outcomes is EqualUnmodifiableListView) return _outcomes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_outcomes);
  }

  @override
  final ApplicantDto? applicantDto;
  @override
  final DateTime? createdDate;
  @override
  final String? titleAr;
  @override
  final String? descriptionAr;
  @override
  final String? descriptionArJson;
  @override
  final String? skillLevel;
  @override
  final int? version;
  @override
  final String? rootId;
  @override
  final String? copiedFromId;

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LearningTrackDetailsModelCopyWith<_LearningTrackDetailsModel> get copyWith =>
      __$LearningTrackDetailsModelCopyWithImpl<_LearningTrackDetailsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LearningTrackDetailsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LearningTrackDetailsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.sector, sector) || other.sector == sector) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.forNominationOnly, forNominationOnly) ||
                other.forNominationOnly == forNominationOnly) &&
            (identical(other.languageCode, languageCode) || other.languageCode == languageCode) &&
            (identical(other.trainingProviderName, trainingProviderName) ||
                other.trainingProviderName == trainingProviderName) &&
            (identical(other.trainingCount, trainingCount) ||
                other.trainingCount == trainingCount) &&
            const DeepCollectionEquality().equals(other._trainings, _trainings) &&
            const DeepCollectionEquality().equals(other._requirements, _requirements) &&
            const DeepCollectionEquality().equals(other._outcomes, _outcomes) &&
            (identical(other.applicantDto, applicantDto) || other.applicantDto == applicantDto) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.titleAr, titleAr) || other.titleAr == titleAr) &&
            (identical(other.descriptionAr, descriptionAr) ||
                other.descriptionAr == descriptionAr) &&
            (identical(other.descriptionArJson, descriptionArJson) ||
                other.descriptionArJson == descriptionArJson) &&
            (identical(other.skillLevel, skillLevel) || other.skillLevel == skillLevel) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            (identical(other.copiedFromId, copiedFromId) || other.copiedFromId == copiedFromId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        description,
        level,
        status,
        profileImage,
        profileImageUrl,
        sector,
        domain,
        organizationName,
        forNominationOnly,
        languageCode,
        trainingProviderName,
        trainingCount,
        const DeepCollectionEquality().hash(_trainings),
        const DeepCollectionEquality().hash(_requirements),
        const DeepCollectionEquality().hash(_outcomes),
        applicantDto,
        createdDate,
        titleAr,
        descriptionAr,
        descriptionArJson,
        skillLevel,
        version,
        rootId,
        copiedFromId
      ]);

  @override
  String toString() {
    return 'LearningTrackDetailsModel(id: $id, title: $title, description: $description, level: $level, status: $status, profileImage: $profileImage, profileImageUrl: $profileImageUrl, sector: $sector, domain: $domain, organizationName: $organizationName, forNominationOnly: $forNominationOnly, languageCode: $languageCode, trainingProviderName: $trainingProviderName, trainingCount: $trainingCount, trainings: $trainings, requirements: $requirements, outcomes: $outcomes, applicantDto: $applicantDto, createdDate: $createdDate, titleAr: $titleAr, descriptionAr: $descriptionAr, descriptionArJson: $descriptionArJson, skillLevel: $skillLevel, version: $version, rootId: $rootId, copiedFromId: $copiedFromId)';
  }
}

/// @nodoc
abstract mixin class _$LearningTrackDetailsModelCopyWith<$Res>
    implements $LearningTrackDetailsModelCopyWith<$Res> {
  factory _$LearningTrackDetailsModelCopyWith(
          _LearningTrackDetailsModel value, $Res Function(_LearningTrackDetailsModel) _then) =
      __$LearningTrackDetailsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String level,
      String status,
      ImageModel? profileImage,
      String profileImageUrl,
      Sector? sector,
      Domain? domain,
      @JsonKey(defaultValue: '') String organizationName,
      bool forNominationOnly,
      @JsonKey(defaultValue: '') String languageCode,
      @JsonKey(defaultValue: '') String trainingProviderName,
      @JsonKey(defaultValue: 0) int trainingCount,
      @JsonKey(defaultValue: []) List<TrainingContentModel> trainings,
      @JsonKey(defaultValue: []) List<Requirement> requirements,
      @JsonKey(defaultValue: []) List<Outcome> outcomes,
      ApplicantDto? applicantDto,
      DateTime? createdDate,
      String? titleAr,
      String? descriptionAr,
      String? descriptionArJson,
      String? skillLevel,
      int? version,
      String? rootId,
      String? copiedFromId});

  @override
  $ImageModelCopyWith<$Res>? get profileImage;
  @override
  $SectorCopyWith<$Res>? get sector;
  @override
  $DomainCopyWith<$Res>? get domain;
  @override
  $ApplicantDtoCopyWith<$Res>? get applicantDto;
}

/// @nodoc
class __$LearningTrackDetailsModelCopyWithImpl<$Res>
    implements _$LearningTrackDetailsModelCopyWith<$Res> {
  __$LearningTrackDetailsModelCopyWithImpl(this._self, this._then);

  final _LearningTrackDetailsModel _self;
  final $Res Function(_LearningTrackDetailsModel) _then;

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? level = null,
    Object? status = null,
    Object? profileImage = freezed,
    Object? profileImageUrl = null,
    Object? sector = freezed,
    Object? domain = freezed,
    Object? organizationName = null,
    Object? forNominationOnly = null,
    Object? languageCode = null,
    Object? trainingProviderName = null,
    Object? trainingCount = null,
    Object? trainings = null,
    Object? requirements = null,
    Object? outcomes = null,
    Object? applicantDto = freezed,
    Object? createdDate = freezed,
    Object? titleAr = freezed,
    Object? descriptionAr = freezed,
    Object? descriptionArJson = freezed,
    Object? skillLevel = freezed,
    Object? version = freezed,
    Object? rootId = freezed,
    Object? copiedFromId = freezed,
  }) {
    return _then(_LearningTrackDetailsModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      sector: freezed == sector
          ? _self.sector
          : sector // ignore: cast_nullable_to_non_nullable
              as Sector?,
      domain: freezed == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as Domain?,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      forNominationOnly: null == forNominationOnly
          ? _self.forNominationOnly
          : forNominationOnly // ignore: cast_nullable_to_non_nullable
              as bool,
      languageCode: null == languageCode
          ? _self.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      trainingProviderName: null == trainingProviderName
          ? _self.trainingProviderName
          : trainingProviderName // ignore: cast_nullable_to_non_nullable
              as String,
      trainingCount: null == trainingCount
          ? _self.trainingCount
          : trainingCount // ignore: cast_nullable_to_non_nullable
              as int,
      trainings: null == trainings
          ? _self._trainings
          : trainings // ignore: cast_nullable_to_non_nullable
              as List<TrainingContentModel>,
      requirements: null == requirements
          ? _self._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<Requirement>,
      outcomes: null == outcomes
          ? _self._outcomes
          : outcomes // ignore: cast_nullable_to_non_nullable
              as List<Outcome>,
      applicantDto: freezed == applicantDto
          ? _self.applicantDto
          : applicantDto // ignore: cast_nullable_to_non_nullable
              as ApplicantDto?,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      titleAr: freezed == titleAr
          ? _self.titleAr
          : titleAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAr: freezed == descriptionAr
          ? _self.descriptionAr
          : descriptionAr // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionArJson: freezed == descriptionArJson
          ? _self.descriptionArJson
          : descriptionArJson // ignore: cast_nullable_to_non_nullable
              as String?,
      skillLevel: freezed == skillLevel
          ? _self.skillLevel
          : skillLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      version: freezed == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      copiedFromId: freezed == copiedFromId
          ? _self.copiedFromId
          : copiedFromId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorCopyWith<$Res>? get sector {
    if (_self.sector == null) {
      return null;
    }

    return $SectorCopyWith<$Res>(_self.sector!, (value) {
      return _then(_self.copyWith(sector: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DomainCopyWith<$Res>? get domain {
    if (_self.domain == null) {
      return null;
    }

    return $DomainCopyWith<$Res>(_self.domain!, (value) {
      return _then(_self.copyWith(domain: value));
    });
  }

  /// Create a copy of LearningTrackDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApplicantDtoCopyWith<$Res>? get applicantDto {
    if (_self.applicantDto == null) {
      return null;
    }

    return $ApplicantDtoCopyWith<$Res>(_self.applicantDto!, (value) {
      return _then(_self.copyWith(applicantDto: value));
    });
  }
}

// dart format on
