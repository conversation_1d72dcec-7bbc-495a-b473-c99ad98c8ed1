// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_track_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LearningTrackDetailsModel _$LearningTrackDetailsModelFromJson(Map<String, dynamic> json) =>
    _LearningTrackDetailsModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      level: json['level'] as String,
      status: json['status'] as String,
      profileImage: json['profileImage'] == null
          ? null
          : ImageModel.fromJson(json['profileImage'] as Map<String, dynamic>),
      profileImageUrl: json['profileImageUrl'] as String,
      sector:
          json['sector'] == null ? null : Sector.fromJson(json['sector'] as Map<String, dynamic>),
      domain:
          json['domain'] == null ? null : Domain.fromJson(json['domain'] as Map<String, dynamic>),
      organizationName: json['organizationName'] as String? ?? '',
      forNominationOnly: json['forNominationOnly'] as bool,
      languageCode: json['languageCode'] as String? ?? '',
      trainingProviderName: json['trainingProviderName'] as String? ?? '',
      trainingCount: (json['trainingCount'] as num?)?.toInt() ?? 0,
      trainings: (json['trainings'] as List<dynamic>?)
              ?.map((e) => TrainingContentModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      requirements: (json['requirements'] as List<dynamic>?)
              ?.map((e) => Requirement.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      outcomes: (json['outcomes'] as List<dynamic>?)
              ?.map((e) => Outcome.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantDto: json['applicantDto'] == null
          ? null
          : ApplicantDto.fromJson(json['applicantDto'] as Map<String, dynamic>),
      createdDate:
          json['createdDate'] == null ? null : DateTime.parse(json['createdDate'] as String),
      titleAr: json['titleAr'] as String?,
      descriptionAr: json['descriptionAr'] as String?,
      descriptionArJson: json['descriptionArJson'] as String?,
      skillLevel: json['skillLevel'] as String?,
      version: (json['version'] as num?)?.toInt(),
      rootId: json['rootId'] as String?,
      copiedFromId: json['copiedFromId'] as String?,
    );

Map<String, dynamic> _$LearningTrackDetailsModelToJson(_LearningTrackDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'level': instance.level,
      'status': instance.status,
      'profileImage': instance.profileImage,
      'profileImageUrl': instance.profileImageUrl,
      'sector': instance.sector,
      'domain': instance.domain,
      'organizationName': instance.organizationName,
      'forNominationOnly': instance.forNominationOnly,
      'languageCode': instance.languageCode,
      'trainingProviderName': instance.trainingProviderName,
      'trainingCount': instance.trainingCount,
      'trainings': instance.trainings,
      'requirements': instance.requirements,
      'outcomes': instance.outcomes,
      'applicantDto': instance.applicantDto,
      'createdDate': instance.createdDate?.toIso8601String(),
      'titleAr': instance.titleAr,
      'descriptionAr': instance.descriptionAr,
      'descriptionArJson': instance.descriptionArJson,
      'skillLevel': instance.skillLevel,
      'version': instance.version,
      'rootId': instance.rootId,
      'copiedFromId': instance.copiedFromId,
    };
