import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

part 'learning_track_details_model.freezed.dart';

part 'learning_track_details_model.g.dart';

@freezed
abstract class LearningTrackDetailsModel
    with _$LearningTrackDetailsModel
    implements CourseDetailsInterface {
  const factory LearningTrackDetailsModel({
    required String id,
    required String title,
    required String description,
    required String level,
    required String status,
    required ImageModel? profileImage,
    required String profileImageUrl,
    required Sector? sector,
    required Domain? domain,
    @JsonKey(defaultValue: '') required String organizationName,
    required bool forNominationOnly,
    @JsonKey(defaultValue: '') required String languageCode,
    @JsonKey(defaultValue: '') required String trainingProviderName,
    @JsonKey(defaultValue: 0) required int trainingCount,
    @JsonKey(defaultValue: []) required List<TrainingContentModel> trainings,
    @JsonKey(defaultValue: []) required List<Requirement> requirements,
    @JsonKey(defaultValue: []) required List<Outcome> outcomes,
    ApplicantDto? applicantDto,
    DateTime? createdDate,
    String? titleAr,
    String? descriptionAr,
    String? descriptionArJson,
    String? skillLevel,
    int? version,
    String? rootId,
    String? copiedFromId,
  }) = _LearningTrackDetailsModel;

  const LearningTrackDetailsModel._();

  @override
  String get overview => description;

  @override
  TrainingType get type => TrainingType.none;

  @override
  List<SeatingCapacity?> get seatingCapacities => const [];

  @override
  List<StudyStream?> get studyStreams => [];

  @override
  String get duration => '';

  @override
  DateTime? get lastModifiedDate => createdDate;

  @override
  String get status => '';

  @override
  int get enrolledCount => 0;

  @override
  String get language => '';

  @override
  String get languageCode => '';

  @override
  String get level => '';

  @override
  String get profileImageUrl => '';

  @override
  String get organizationName => '';

  @override
  String get trainingProviderName => '';

  @override
  String get avatarUrl => '';

  @override
  ImageModel? get profileImage => null;

  factory LearningTrackDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$LearningTrackDetailsModelFromJson(json);
}
