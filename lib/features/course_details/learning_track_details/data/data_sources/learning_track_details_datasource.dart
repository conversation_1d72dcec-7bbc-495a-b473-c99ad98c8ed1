import 'dart:async';

import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';

@injectable
class LearningTrackDetailsDataSource {
  const LearningTrackDetailsDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<LearningTrackDetailsModel> getLearningTracks(String courseId) async {
    final String locale = await Hive.box(
      HiveKeys.hiveNspStorage,
    ).get(HiveKeys.currentLocale, defaultValue: Constants.localeEN);

    final path = '${ApiConstants.learningTracksPath}/$courseId';
    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      path,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return LearningTrackDetailsModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<void> enrollLearningTrack(String courseId) async {
    final response = await _dio.post(ApiConstants.applicantsLearningTrack + courseId);

    if (response.statusCode != Constants.statusCode200 &&
        response.statusCode != Constants.statusCode201) {
      throw DioException(requestOptions: response.requestOptions, response: response);
    }
  }
}
