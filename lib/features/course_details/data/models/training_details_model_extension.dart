import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

extension TrainingDetailsModelExtension on TrainingDetailsModel {
  Location? getFirstAvailableLocation() {
    if (studyStreams.isEmpty) return null;

    for (final stream in studyStreams) {
      if (stream != null && stream.location != null) return stream.location;
    }
    return null;
  }
}
