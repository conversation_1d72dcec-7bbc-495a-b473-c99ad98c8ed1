import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

/// An interface that defines the common properties needed by learning-track-details-model and training-details-model
abstract class CourseDetailsInterface {
  String get id;
  String get title;
  String get description;
  String get overview;
  String get language;
  String get languageCode;
  String get duration;
  String get level;
  String get profileImageUrl;
  String get status;
  String get organizationName;
  String get trainingProviderName;
  String get avatarUrl;
  ImageModel? get profileImage;
  List<Requirement> get requirements;
  List<Outcome> get outcomes;
  List<StudyStream?> get studyStreams;
  List<SeatingCapacity?> get seatingCapacities;
  DateTime? get createdDate;
  DateTime? get lastModifiedDate;
  TrainingType get type;
  int get enrolledCount;
}
