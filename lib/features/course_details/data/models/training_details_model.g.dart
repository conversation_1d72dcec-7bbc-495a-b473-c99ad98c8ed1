// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'training_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TrainingDetailsModel _$TrainingDetailsModelFromJson(Map<String, dynamic> json) =>
    _TrainingDetailsModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String? ?? '',
      overview: json['overview'] as String? ?? '',
      language: json['language'] == null ? '' : languageCodeToText(json['language'] as String),
      languageCode: json['languageCode'] as String? ?? '',
      duration: json['duration'] == null ? '' : fromJsonToDuration(json['duration'] as Object),
      durationMin: (json['durationMin'] as num?)?.toInt(),
      durationMax: (json['durationMax'] as num?)?.toInt(),
      level: json['level'] == null ? '' : levelEnumToText(json['level'] as String),
      skillLevel: json['skillLevel'] as String? ?? '',
      profileImage: json['profileImage'] == null
          ? null
          : ImageModel.fromJson(json['profileImage'] as Map<String, dynamic>),
      profileImageUrl: json['profileImageUrl'] as String? ?? '',
      status: json['status'] as String? ?? '',
      createdDate:
          json['createdDate'] == null ? null : DateTime.parse(json['createdDate'] as String),
      lastModifiedDate: json['lastModifiedDate'] == null
          ? null
          : DateTime.parse(json['lastModifiedDate'] as String),
      requirements: (json['requirements'] as List<dynamic>?)
              ?.map((e) => Requirement.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      outcomes: (json['outcomes'] as List<dynamic>?)
              ?.map((e) => Outcome.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      skills: (json['skills'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      trainingStructure: json['trainingStructure'] == null
          ? null
          : TrainingStructureModel.fromJson(json['trainingStructure'] as Map<String, dynamic>),
      qualificationTests: (json['qualificationTests'] as List<dynamic>?)
              ?.map((e) => QualificationTest.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      sector:
          json['sector'] == null ? null : Sector.fromJson(json['sector'] as Map<String, dynamic>),
      domain:
          json['domain'] == null ? null : Domain.fromJson(json['domain'] as Map<String, dynamic>),
      organizationName: json['organizationName'] as String? ?? '',
      trainingProviderName: json['trainingProviderName'] as String? ?? '',
      enrolledCount: (json['enrolledCount'] as num?)?.toInt() ?? 0,
      avatarUrl: json['avatarUrl'] as String? ?? '',
      organizationId: json['organizationId'] as String? ?? '',
      promoVideoUrl: json['promoVideoUrl'] as String? ?? '',
      type: stringToTrainingType(json['type'] as String?),
      seatingCapacities: (json['seatingCapacities'] as List<dynamic>?)
              ?.map((e) => e == null ? null : SeatingCapacity.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      studyStreams: (json['studyStreams'] as List<dynamic>?)
              ?.map((e) => e == null ? null : StudyStream.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      copiedFromId: json['copiedFromId'] as String? ?? '',
      version: (json['version'] as num).toInt(),
      rootId: json['rootId'] as String? ?? '',
      createdBy: json['createdBy'] as String? ?? '',
      instructorPermission: json['instructorPermission'] as String? ?? '',
      promoVideo: json['promoVideo'] == null
          ? null
          : PromoVideoModel.fromJson(json['promoVideo'] as Map<String, dynamic>),
      applicantDto: json['applicantDto'] == null
          ? null
          : ApplicantDto.fromJson(json['applicantDto'] as Map<String, dynamic>),
      hasFutureOnline: json['hasFutureOnline'] as bool? ?? false,
      hasFutureInPerson: json['hasFutureInPerson'] as bool? ?? false,
    );

Map<String, dynamic> _$TrainingDetailsModelToJson(_TrainingDetailsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'overview': instance.overview,
      'language': instance.language,
      'languageCode': instance.languageCode,
      'duration': instance.duration,
      'durationMin': instance.durationMin,
      'durationMax': instance.durationMax,
      'level': instance.level,
      'skillLevel': instance.skillLevel,
      'profileImage': instance.profileImage,
      'profileImageUrl': instance.profileImageUrl,
      'status': instance.status,
      'createdDate': instance.createdDate?.toIso8601String(),
      'lastModifiedDate': instance.lastModifiedDate?.toIso8601String(),
      'requirements': instance.requirements,
      'outcomes': instance.outcomes,
      'skills': instance.skills,
      'trainingStructure': instance.trainingStructure,
      'qualificationTests': instance.qualificationTests,
      'sector': instance.sector,
      'domain': instance.domain,
      'organizationName': instance.organizationName,
      'trainingProviderName': instance.trainingProviderName,
      'enrolledCount': instance.enrolledCount,
      'avatarUrl': instance.avatarUrl,
      'organizationId': instance.organizationId,
      'promoVideoUrl': instance.promoVideoUrl,
      'type': _$TrainingTypeEnumMap[instance.type]!,
      'seatingCapacities': instance.seatingCapacities,
      'studyStreams': instance.studyStreams,
      'copiedFromId': instance.copiedFromId,
      'version': instance.version,
      'rootId': instance.rootId,
      'createdBy': instance.createdBy,
      'instructorPermission': instance.instructorPermission,
      'promoVideo': instance.promoVideo,
      'applicantDto': instance.applicantDto,
      'hasFutureOnline': instance.hasFutureOnline,
      'hasFutureInPerson': instance.hasFutureInPerson,
    };

const _$TrainingTypeEnumMap = {
  TrainingType.SelfPaced: 'SelfPaced',
  TrainingType.InstructorLed: 'InstructorLed',
  TrainingType.none: 'none',
};

_ImageModel _$ImageModelFromJson(Map<String, dynamic> json) => _ImageModel(
      originalFilename: json['originalFilename'] as String,
      key: json['key'] as String,
      size: (json['size'] as num).toInt(),
    );

Map<String, dynamic> _$ImageModelToJson(_ImageModel instance) => <String, dynamic>{
      'originalFilename': instance.originalFilename,
      'key': instance.key,
      'size': instance.size,
    };

_Requirement _$RequirementFromJson(Map<String, dynamic> json) => _Requirement(
      value: json['value'] as String,
      index: (json['index'] as num).toInt(),
    );

Map<String, dynamic> _$RequirementToJson(_Requirement instance) => <String, dynamic>{
      'value': instance.value,
      'index': instance.index,
    };

_Outcome _$OutcomeFromJson(Map<String, dynamic> json) => _Outcome(
      value: json['value'] as String,
      index: (json['index'] as num).toInt(),
    );

Map<String, dynamic> _$OutcomeToJson(_Outcome instance) => <String, dynamic>{
      'value': instance.value,
      'index': instance.index,
    };

_TrainingStructureModel _$TrainingStructureModelFromJson(Map<String, dynamic> json) =>
    _TrainingStructureModel(
      sections: (json['sections'] as List<dynamic>)
          .map((e) => Section.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TrainingStructureModelToJson(_TrainingStructureModel instance) =>
    <String, dynamic>{
      'sections': instance.sections,
    };

_Section _$SectionFromJson(Map<String, dynamic> json) => _Section(
      id: json['id'] as String,
      title: json['title'] as String,
      index: (json['index'] as num).toInt(),
      lessons: (json['lessons'] as List<dynamic>)
          .map((e) => Lesson.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SectionToJson(_Section instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'index': instance.index,
      'lessons': instance.lessons,
    };

_Lesson _$LessonFromJson(Map<String, dynamic> json) => _Lesson(
      id: json['id'] as String,
      title: json['title'] as String? ?? '',
      index: (json['index'] as num?)?.toInt(),
      lessonType: stringToLessonType(json['type'] as String?),
      text: json['text'] as String?,
      resources: (json['resources'] as List<dynamic>?)
              ?.map((e) => Resource.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      quiz: json['quiz'] == null ? null : Quiz.fromJson(json['quiz'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LessonToJson(_Lesson instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'index': instance.index,
      'type': _$LessonTypeEnumMap[instance.lessonType],
      'text': instance.text,
      'resources': instance.resources,
      'quiz': instance.quiz,
    };

const _$LessonTypeEnumMap = {
  LessonType.File: 'File',
  LessonType.Quiz: 'Quiz',
  LessonType.Video: 'Video',
  LessonType.Slide: 'Slide',
  LessonType.Article: 'Article',
};

_Resource _$ResourceFromJson(Map<String, dynamic> json) => _Resource(
      originalFilename: json['originalFilename'] as String?,
      key: json['key'] as String?,
      size: (json['size'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ResourceToJson(_Resource instance) => <String, dynamic>{
      'originalFilename': instance.originalFilename,
      'key': instance.key,
      'size': instance.size,
    };

_Quiz _$QuizFromJson(Map<String, dynamic> json) => _Quiz(
      id: json['id'] as String,
      randomized: json['randomized'] as bool?,
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => e == null ? null : Question.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$QuizToJson(_Quiz instance) => <String, dynamic>{
      'id': instance.id,
      'randomized': instance.randomized,
      'questions': instance.questions,
    };

_QualificationTest _$QualificationTestFromJson(Map<String, dynamic> json) => _QualificationTest(
      id: json['id'] as String,
      questions: (json['questions'] as List<dynamic>)
          .map((e) => Question.fromJson(e as Map<String, dynamic>))
          .toList(),
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      minimumScore: (json['minimumScore'] as num?)?.toInt() ?? 0,
      mandatory: json['mandatory'] as bool,
      qualificationTestType: json['qualificationTestType'] as String? ?? '',
      qualificationTestStatus: json['qualificationTestStatus'] as String? ?? '',
      hidden: json['hidden'] as bool,
      lastModifiedDate: json['lastModifiedDate'] == null
          ? null
          : DateTime.parse(json['lastModifiedDate'] as String),
      answersMap: Map<String, String>.from(json['answersMap'] as Map),
    );

Map<String, dynamic> _$QualificationTestToJson(_QualificationTest instance) => <String, dynamic>{
      'id': instance.id,
      'questions': instance.questions,
      'title': instance.title,
      'description': instance.description,
      'minimumScore': instance.minimumScore,
      'mandatory': instance.mandatory,
      'qualificationTestType': instance.qualificationTestType,
      'qualificationTestStatus': instance.qualificationTestStatus,
      'hidden': instance.hidden,
      'lastModifiedDate': instance.lastModifiedDate?.toIso8601String(),
      'answersMap': instance.answersMap,
    };

_Answer _$AnswerFromJson(Map<String, dynamic> json) => _Answer(
      id: json['id'] as String?,
      answer: json['answer'] as String?,
      correct: json['correct'] as bool?,
      index: (json['index'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AnswerToJson(_Answer instance) => <String, dynamic>{
      'id': instance.id,
      'answer': instance.answer,
      'correct': instance.correct,
      'index': instance.index,
    };

_Sector _$SectorFromJson(Map<String, dynamic> json) => _Sector(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
    );

Map<String, dynamic> _$SectorToJson(_Sector instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

_Domain _$DomainFromJson(Map<String, dynamic> json) => _Domain(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
    );

Map<String, dynamic> _$DomainToJson(_Domain instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

_PromoVideoModel _$PromoVideoModelFromJson(Map<String, dynamic> json) => _PromoVideoModel(
      originalFilename: json['originalFilename'] as String,
      key: json['key'] as String,
      size: (json['size'] as num).toInt(),
    );

Map<String, dynamic> _$PromoVideoModelToJson(_PromoVideoModel instance) => <String, dynamic>{
      'originalFilename': instance.originalFilename,
      'key': instance.key,
      'size': instance.size,
    };

_SeatingCapacity _$SeatingCapacityFromJson(Map<String, dynamic> json) => _SeatingCapacity(
      seatingCapacityId: json['seatingCapacityId'] as String,
      dtoStatus: stringToDtoStatus(json['dtoStatus'] as String?),
      maxNumberOfEnrollments: (json['maxNumberOfEnrollments'] as num?)?.toInt() ?? 0,
      noLimits: json['noLimits'] as bool,
      actualNumberOfEnrollments: (json['actualNumberOfEnrollments'] as num?)?.toInt() ?? 0,
      endDate: DateTime.parse(json['endDate'] as String),
      lastModifiedDate: DateTime.parse(json['lastModifiedDate'] as String),
    );

Map<String, dynamic> _$SeatingCapacityToJson(_SeatingCapacity instance) => <String, dynamic>{
      'seatingCapacityId': instance.seatingCapacityId,
      'dtoStatus': _$DtoStatusEnumMap[instance.dtoStatus]!,
      'maxNumberOfEnrollments': instance.maxNumberOfEnrollments,
      'noLimits': instance.noLimits,
      'actualNumberOfEnrollments': instance.actualNumberOfEnrollments,
      'endDate': instance.endDate.toIso8601String(),
      'lastModifiedDate': instance.lastModifiedDate.toIso8601String(),
    };

const _$DtoStatusEnumMap = {
  DtoStatus.Active: 'Active',
  DtoStatus.Draft: 'Draft',
};

_Region _$RegionFromJson(Map<String, dynamic> json) => _Region(
      id: json['id'] as String,
      regionPlaceId: json['regionPlaceId'] as String,
      regionNameEn: json['regionNameEn'] as String,
      regionNameAr: json['regionNameAr'] as String,
    );

Map<String, dynamic> _$RegionToJson(_Region instance) => <String, dynamic>{
      'id': instance.id,
      'regionPlaceId': instance.regionPlaceId,
      'regionNameEn': instance.regionNameEn,
      'regionNameAr': instance.regionNameAr,
    };

_City _$CityFromJson(Map<String, dynamic> json) => _City(
      id: json['id'] as String,
      cityPlaceId: json['cityPlaceId'] as String,
      cityNameEn: json['cityNameEn'] as String?,
      cityNameAr: json['cityNameAr'] as String?,
      region:
          json['region'] == null ? null : Region.fromJson(json['region'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CityToJson(_City instance) => <String, dynamic>{
      'id': instance.id,
      'cityPlaceId': instance.cityPlaceId,
      'cityNameEn': instance.cityNameEn,
      'cityNameAr': instance.cityNameAr,
      'region': instance.region,
    };

_Location _$LocationFromJson(Map<String, dynamic> json) => _Location(
      id: json['id'] as String,
      addressPlaceId: json['addressPlaceId'] as String?,
      addressNameEn: json['addressNameEn'] as String,
      addressNameAr: json['addressNameAr'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      city: json['city'] == null ? null : City.fromJson(json['city'] as Map<String, dynamic>),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$LocationToJson(_Location instance) => <String, dynamic>{
      'id': instance.id,
      'addressPlaceId': instance.addressPlaceId,
      'addressNameEn': instance.addressNameEn,
      'addressNameAr': instance.addressNameAr,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'city': instance.city,
      'comment': instance.comment,
    };

_StudyStream _$StudyStreamFromJson(Map<String, dynamic> json) => _StudyStream(
      id: json['id'] as String?,
      startDate: json['startDate'] == null ? null : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null ? null : DateTime.parse(json['endDate'] as String),
      maxNumberOfParticipants: (json['maxNumberOfParticipants'] as num?)?.toInt() ?? 0,
      currentNumberOfParticipants: (json['currentNumberOfParticipants'] as num?)?.toInt() ?? 0,
      noLimits: json['noLimits'] as bool? ?? false,
      liveSession: json['liveSession'] == null
          ? null
          : LiveSession.fromJson(json['liveSession'] as Map<String, dynamic>),
      trainingId: json['trainingId'] as String?,
      type: stringToStudyStreamType(json['type'] as String?),
      status: json['status'] as String?,
      cancelled: json['cancelled'] as bool? ?? false,
      closedForEnrollmentDate: json['closedForEnrollmentDate'] == null
          ? null
          : DateTime.parse(json['closedForEnrollmentDate'] as String),
      rootId: json['rootId'] as String?,
      cardNotes: (json['cardNotes'] as List<dynamic>?)
              ?.map((e) => CardNote.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      location: json['location'] == null
          ? null
          : Location.fromJson(json['location'] as Map<String, dynamic>),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$StudyStreamToJson(_StudyStream instance) => <String, dynamic>{
      'id': instance.id,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'maxNumberOfParticipants': instance.maxNumberOfParticipants,
      'currentNumberOfParticipants': instance.currentNumberOfParticipants,
      'noLimits': instance.noLimits,
      'liveSession': instance.liveSession,
      'trainingId': instance.trainingId,
      'type': _$StudyStreamTypeEnumMap[instance.type],
      'status': instance.status,
      'cancelled': instance.cancelled,
      'closedForEnrollmentDate': instance.closedForEnrollmentDate?.toIso8601String(),
      'rootId': instance.rootId,
      'cardNotes': instance.cardNotes,
      'location': instance.location,
      'comment': instance.comment,
    };

const _$StudyStreamTypeEnumMap = {
  StudyStreamType.ONLINE: 'ONLINE',
  StudyStreamType.IN_PERSON: 'IN_PERSON',
  StudyStreamType.NONE: 'NONE',
};

_Meeting _$MeetingFromJson(Map<String, dynamic> json) => _Meeting(
      id: json['id'] as String?,
      title: json['title'] as String?,
      trainingId: json['trainingId'] as String?,
      liveSessionId: json['liveSessionId'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      durationHours: (json['durationHours'] as num).toInt(),
      durationMinutes: (json['durationMinutes'] as num).toInt(),
      zoomLink: json['zoomLink'] as String?,
      zoomPassword: json['zoomPassword'] as String?,
      meetingId: json['meetingId'] as String?,
      status: json['status'] as String,
      cancelled: json['cancelled'] as bool,
      cancellationReason: json['cancellationReason'] as String?,
      cancellationDate: json['cancellationDate'] == null
          ? null
          : DateTime.parse(json['cancellationDate'] as String),
      rootId: json['rootId'] as String?,
      cardNotes: (json['cardNotes'] as List<dynamic>?)
          ?.map((e) => CardNote.fromJson(e as Map<String, dynamic>))
          .toList(),
      sections: (json['section'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$MeetingToJson(_Meeting instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'trainingId': instance.trainingId,
      'liveSessionId': instance.liveSessionId,
      'startDate': instance.startDate.toIso8601String(),
      'durationHours': instance.durationHours,
      'durationMinutes': instance.durationMinutes,
      'zoomLink': instance.zoomLink,
      'zoomPassword': instance.zoomPassword,
      'meetingId': instance.meetingId,
      'status': instance.status,
      'cancelled': instance.cancelled,
      'cancellationReason': instance.cancellationReason,
      'cancellationDate': instance.cancellationDate?.toIso8601String(),
      'rootId': instance.rootId,
      'cardNotes': instance.cardNotes,
      'section': instance.sections,
    };

_CardNote _$CardNoteFromJson(Map<String, dynamic> json) => _CardNote(
      createdDate: DateTime.parse(json['createdDate'] as String),
      localizedContents: (json['localizedContents'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, LocalizedContent.fromJson(e as Map<String, dynamic>)),
      ),
    );

Map<String, dynamic> _$CardNoteToJson(_CardNote instance) => <String, dynamic>{
      'createdDate': instance.createdDate.toIso8601String(),
      'localizedContents': instance.localizedContents,
    };

_LocalizedContent _$LocalizedContentFromJson(Map<String, dynamic> json) => _LocalizedContent(
      title: json['title'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$LocalizedContentToJson(_LocalizedContent instance) => <String, dynamic>{
      'title': instance.title,
      'message': instance.message,
    };

_LiveSession _$LiveSessionFromJson(Map<String, dynamic> json) => _LiveSession(
      id: json['id'] as String?,
      startDate: json['startDate'] == null ? null : DateTime.parse(json['startDate'] as String),
      durationHours: (json['durationHours'] as num?)?.toInt(),
      durationMinutes: (json['durationMinutes'] as num?)?.toInt(),
      recurringMeeting: json['recurringMeeting'] as bool?,
      zoomLink: json['zoomLink'] as String?,
      zoomPassword: json['zoomPassword'] as String?,
      meetingId: json['meetingId'] as String?,
      status: json['status'] as String?,
      repeatEvery: (json['repeatEvery'] as num?)?.toInt(),
      endDate: json['endDate'] == null ? null : DateTime.parse(json['endDate'] as String),
      endAfterOccurrences: (json['endAfterOccurrences'] as num?)?.toInt(),
      daysOfWeek: (json['daysOfWeek'] as List<dynamic>?)?.map((e) => e as String).toList(),
      weekOfMonth: json['weekOfMonth'] as String?,
      dayOfMonth: (json['dayOfMonth'] as num?)?.toInt(),
      meetings: (json['meetings'] as List<dynamic>?)
          ?.map((e) => Meeting.fromJson(e as Map<String, dynamic>))
          .toList(),
      rootId: json['rootId'] as String?,
    );

Map<String, dynamic> _$LiveSessionToJson(_LiveSession instance) => <String, dynamic>{
      'id': instance.id,
      'startDate': instance.startDate?.toIso8601String(),
      'durationHours': instance.durationHours,
      'durationMinutes': instance.durationMinutes,
      'recurringMeeting': instance.recurringMeeting,
      'zoomLink': instance.zoomLink,
      'zoomPassword': instance.zoomPassword,
      'meetingId': instance.meetingId,
      'status': instance.status,
      'repeatEvery': instance.repeatEvery,
      'endDate': instance.endDate?.toIso8601String(),
      'endAfterOccurrences': instance.endAfterOccurrences,
      'daysOfWeek': instance.daysOfWeek,
      'weekOfMonth': instance.weekOfMonth,
      'dayOfMonth': instance.dayOfMonth,
      'meetings': instance.meetings,
      'rootId': instance.rootId,
    };

_ApplicantDto _$ApplicantDtoFromJson(Map<String, dynamic> json) => _ApplicantDto(
      id: json['id'] as String?,
      status: json['status'] as String?,
      requestId: json['requestId'] as String?,
      studyStreams: (json['studyStreams'] as List<dynamic>?)
              ?.map((e) =>
                  e == null ? null : ApplicantStudyStream.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApplicantDtoToJson(_ApplicantDto instance) => <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'requestId': instance.requestId,
      'studyStreams': instance.studyStreams,
    };

_ApplicantStudyStream _$ApplicantStudyStreamFromJson(Map<String, dynamic> json) =>
    _ApplicantStudyStream(
      streamId: json['streamId'] as String,
      status: json['status'] as String,
      startDate: json['startDate'] == null ? null : DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] == null ? null : DateTime.parse(json['endDate'] as String),
      nextSessionDate: json['nextSessionDate'] == null
          ? null
          : DateTime.parse(json['nextSessionDate'] as String),
      cancelled: json['cancelled'] as bool,
      cancellationDate: json['cancellationDate'] == null
          ? null
          : DateTime.parse(json['cancellationDate'] as String),
    );

Map<String, dynamic> _$ApplicantStudyStreamToJson(_ApplicantStudyStream instance) =>
    <String, dynamic>{
      'streamId': instance.streamId,
      'status': instance.status,
      'startDate': instance.startDate?.toIso8601String(),
      'endDate': instance.endDate?.toIso8601String(),
      'nextSessionDate': instance.nextSessionDate?.toIso8601String(),
      'cancelled': instance.cancelled,
      'cancellationDate': instance.cancellationDate?.toIso8601String(),
    };
