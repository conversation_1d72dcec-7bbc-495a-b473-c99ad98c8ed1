import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/string_converters.dart';
import 'package:national_skills_platform/features/course_details/data/models/course_details_interface.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/content_item.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

part 'training_details_model.freezed.dart';
part 'training_details_model.g.dart';

enum TrainingType { SelfPaced, InstructorLed, none }

enum DtoStatus { Active, Draft }

enum LessonType { File, Quiz, Video, Slide, Article }

enum MeetingStatus { Passed, Live, Upcoming, Cancelled, Rescheduled }

enum QuestionType { TRUEFALSE, RADIO }

enum StudyStreamType { ONLINE, IN_PERSON, NONE }

StudyStreamType stringToStudyStreamType(String? value) {
  switch (value) {
    case Constants.ONLINE:
      return StudyStreamType.ONLINE;
    case Constants.IN_PERSON:
      return StudyStreamType.IN_PERSON;
    default:
      return StudyStreamType.NONE;
  }
}

@freezed
abstract class TrainingDetailsModel with _$TrainingDetailsModel implements CourseDetailsInterface {
  const factory TrainingDetailsModel({
    required String id,
    required String title,
    @JsonKey(defaultValue: '') required String description,
    @JsonKey(defaultValue: '') required String overview,
    @JsonKey(fromJson: languageCodeToText, defaultValue: '') required String language,
    @JsonKey(defaultValue: '') required String languageCode,
    @JsonKey(fromJson: fromJsonToDuration, defaultValue: '') required String duration,
    required int? durationMin,
    required int? durationMax,
    @JsonKey(fromJson: levelEnumToText, defaultValue: '') required String level,
    @JsonKey(defaultValue: '') required String skillLevel,
    required ImageModel? profileImage,
    @JsonKey(defaultValue: '') required String profileImageUrl,
    @JsonKey(defaultValue: '') required String status,
    required DateTime? createdDate,
    required DateTime? lastModifiedDate,
    @JsonKey(defaultValue: []) required List<Requirement> requirements,
    @JsonKey(defaultValue: []) required List<Outcome> outcomes,
    @JsonKey(defaultValue: []) required List<String> skills,
    required TrainingStructureModel? trainingStructure,
    @JsonKey(defaultValue: []) required List<QualificationTest> qualificationTests,
    required Sector? sector,
    required Domain? domain,
    @JsonKey(defaultValue: '') required String organizationName,
    @JsonKey(defaultValue: '') required String trainingProviderName,
    @JsonKey(defaultValue: 0) required int enrolledCount,
    @JsonKey(defaultValue: '') required String avatarUrl,
    @JsonKey(defaultValue: '') required String organizationId,
    @JsonKey(defaultValue: '') required String promoVideoUrl,
    @JsonKey(fromJson: stringToTrainingType) required TrainingType type,
    @JsonKey(defaultValue: []) required List<SeatingCapacity?> seatingCapacities,
    @JsonKey(defaultValue: []) required List<StudyStream?> studyStreams,
    @JsonKey(defaultValue: '') required String copiedFromId,
    required int version,
    @JsonKey(defaultValue: '') required String rootId,
    @JsonKey(defaultValue: '') required String createdBy,
    @JsonKey(defaultValue: '') required String instructorPermission,
    PromoVideoModel? promoVideo,
    ApplicantDto? applicantDto,
    @JsonKey(defaultValue: false) required bool hasFutureOnline,
    @JsonKey(defaultValue: false) required bool hasFutureInPerson,
  }) = _TrainingDetailsModel;

  factory TrainingDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingDetailsModelFromJson(json);
}

@freezed
abstract class ImageModel with _$ImageModel {
  const factory ImageModel({
    required String originalFilename,
    required String key,
    required int size,
  }) = _ImageModel;

  factory ImageModel.fromJson(Map<String, dynamic> json) => _$ImageModelFromJson(json);
}

@freezed
abstract class Requirement with _$Requirement {
  const factory Requirement({required String value, required int index}) = _Requirement;

  factory Requirement.fromJson(Map<String, dynamic> json) => _$RequirementFromJson(json);
}

@freezed
abstract class Outcome with _$Outcome {
  const factory Outcome({required String value, required int index}) = _Outcome;

  factory Outcome.fromJson(Map<String, dynamic> json) => _$OutcomeFromJson(json);
}

@freezed
abstract class TrainingStructureModel with _$TrainingStructureModel {
  const factory TrainingStructureModel({required List<Section> sections}) = _TrainingStructureModel;

  factory TrainingStructureModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingStructureModelFromJson(json);
}

@freezed
abstract class Section with _$Section {
  const factory Section({
    required String id,
    required String title,
    required int index,
    required List<Lesson> lessons,
  }) = _Section;

  factory Section.fromJson(Map<String, dynamic> json) => _$SectionFromJson(json);
}

@freezed
abstract class Lesson with _$Lesson implements ContentItem {
  const factory Lesson({
    required String id,
    @JsonKey(defaultValue: '') required String title,
    required int? index,
    @JsonKey(fromJson: stringToLessonType, name: 'type') required LessonType? lessonType,
    String? text,
    @JsonKey(defaultValue: []) List<Resource>? resources,
    Quiz? quiz,
  }) = _Lesson;

  factory Lesson.fromJson(Map<String, dynamic> json) => _$LessonFromJson(json);
}

@freezed
abstract class Resource with _$Resource {
  const factory Resource({
    required String? originalFilename,
    required String? key,
    required int? size,
  }) = _Resource;

  factory Resource.fromJson(Map<String, dynamic> json) => _$ResourceFromJson(json);
}

@freezed
abstract class Quiz with _$Quiz {
  const factory Quiz({
    required String id,
    required bool? randomized,
    @JsonKey(defaultValue: []) required List<Question?> questions,
  }) = _Quiz;

  factory Quiz.fromJson(Map<String, dynamic> json) => _$QuizFromJson(json);
}

@freezed
abstract class QualificationTest with _$QualificationTest {
  const factory QualificationTest({
    required String id,
    required List<Question> questions,
    @JsonKey(defaultValue: '') required String title,
    @JsonKey(defaultValue: '') required String description,
    @JsonKey(defaultValue: 0) required int minimumScore,
    required bool mandatory,
    @JsonKey(defaultValue: '') required String qualificationTestType,
    @JsonKey(defaultValue: '') required String qualificationTestStatus,
    required bool hidden,
    required DateTime? lastModifiedDate,
    required Map<String, String> answersMap,
  }) = _QualificationTest;

  factory QualificationTest.fromJson(Map<String, dynamic> json) =>
      _$QualificationTestFromJson(json);
}

@freezed
abstract class Answer with _$Answer {
  const factory Answer({
    required String? id,
    required String? answer,
    required bool? correct,
    required int? index,
  }) = _Answer;

  factory Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);
}

@freezed
abstract class Sector with _$Sector {
  const factory Sector({
    @JsonKey(defaultValue: '') required String id,
    @JsonKey(defaultValue: '') required String title,
  }) = _Sector;

  factory Sector.fromJson(Map<String, dynamic> json) => _$SectorFromJson(json);
}

@freezed
abstract class Domain with _$Domain {
  const factory Domain({
    @JsonKey(defaultValue: '') required String id,
    @JsonKey(defaultValue: '') required String title,
  }) = _Domain;

  factory Domain.fromJson(Map<String, dynamic> json) => _$DomainFromJson(json);
}

// Define the PromoVideoModel class
@freezed
abstract class PromoVideoModel with _$PromoVideoModel {
  const factory PromoVideoModel({
    required String originalFilename,
    required String key,
    required int size,
  }) = _PromoVideoModel;

  factory PromoVideoModel.fromJson(Map<String, dynamic> json) => _$PromoVideoModelFromJson(json);
}

@freezed
abstract class SeatingCapacity with _$SeatingCapacity {
  const factory SeatingCapacity({
    required String seatingCapacityId,
    @JsonKey(fromJson: stringToDtoStatus) required DtoStatus dtoStatus,
    @JsonKey(defaultValue: 0) required int maxNumberOfEnrollments,
    required bool noLimits,
    @JsonKey(defaultValue: 0) required int actualNumberOfEnrollments,
    required DateTime endDate,
    required DateTime lastModifiedDate,
  }) = _SeatingCapacity;

  factory SeatingCapacity.fromJson(Map<String, dynamic> json) => _$SeatingCapacityFromJson(json);
}

@freezed
abstract class Region with _$Region {
  const factory Region({
    required String id,
    required String regionPlaceId,
    required String regionNameEn,
    required String regionNameAr,
  }) = _Region;

  factory Region.fromJson(Map<String, dynamic> json) => _$RegionFromJson(json);
}

@freezed
abstract class City with _$City {
  const factory City({
    required String id,
    required String cityPlaceId,
    required String? cityNameEn,
    required String? cityNameAr,
    required Region? region,
  }) = _City;

  factory City.fromJson(Map<String, dynamic> json) => _$CityFromJson(json);
}

@freezed
abstract class Location with _$Location {
  const factory Location({
    required String id,
    required String? addressPlaceId,
    required String addressNameEn,
    required String addressNameAr,
    required double? latitude,
    required double? longitude,
    required City? city,
    required String? comment,
  }) = _Location;

  factory Location.fromJson(Map<String, dynamic> json) => _$LocationFromJson(json);
}

@freezed
abstract class StudyStream with _$StudyStream {
  const factory StudyStream({
    required String? id,
    required DateTime? startDate,
    required DateTime? endDate,
    @JsonKey(defaultValue: 0) required int maxNumberOfParticipants,
    @JsonKey(defaultValue: 0) required int currentNumberOfParticipants,
    @JsonKey(defaultValue: false) required bool noLimits,
    required LiveSession? liveSession,
    required String? trainingId,
    @JsonKey(fromJson: stringToStudyStreamType) required StudyStreamType? type,
    required String? status,
    @JsonKey(defaultValue: false) required bool cancelled,
    DateTime? closedForEnrollmentDate,
    String? rootId,
    @JsonKey(defaultValue: []) required List<CardNote> cardNotes,
    required Location? location,
    required String? comment,
  }) = _StudyStream;

  factory StudyStream.fromJson(Map<String, dynamic> json) => _$StudyStreamFromJson(json);
}

@freezed
abstract class Meeting with _$Meeting {
  const factory Meeting({
    required String? id,
    required String? title,
    required String? trainingId,
    required String? liveSessionId,
    required DateTime startDate,
    required int durationHours,
    required int durationMinutes,
    required String? zoomLink,
    required String? zoomPassword,
    required String? meetingId,
    required String status,
    required bool cancelled,
    @JsonKey(includeFromJson: false, includeToJson: false)
    @Default(MeetingStatus.Upcoming)
    MeetingStatus meetingStatus,
    String? cancellationReason,
    DateTime? cancellationDate,
    String? rootId,
    List<CardNote>? cardNotes,
    @JsonKey(name: 'section') List<String>? sections,
  }) = _Meeting;

  factory Meeting.fromJson(Map<String, dynamic> json) => _$MeetingFromJson(json);
}

@freezed
abstract class CardNote with _$CardNote {
  const factory CardNote({
    required DateTime createdDate,
    required Map<String, LocalizedContent> localizedContents,
  }) = _CardNote;

  factory CardNote.fromJson(Map<String, dynamic> json) => _$CardNoteFromJson(json);
}

@freezed
abstract class LocalizedContent with _$LocalizedContent {
  const factory LocalizedContent({required String title, required String message}) =
      _LocalizedContent;

  factory LocalizedContent.fromJson(Map<String, dynamic> json) => _$LocalizedContentFromJson(json);
}

@freezed
abstract class LiveSession with _$LiveSession {
  const factory LiveSession({
    required String? id,
    required DateTime? startDate,
    required int? durationHours,
    required int? durationMinutes,
    required bool? recurringMeeting,
    required String? zoomLink,
    required String? zoomPassword,
    required String? meetingId,
    required String? status,
    int? repeatEvery,
    DateTime? endDate,
    int? endAfterOccurrences,
    List<String>? daysOfWeek,
    String? weekOfMonth,
    int? dayOfMonth,
    List<Meeting>? meetings,
    String? rootId,
  }) = _LiveSession;

  factory LiveSession.fromJson(Map<String, dynamic> json) => _$LiveSessionFromJson(json);
}

@freezed
abstract class ApplicantDto with _$ApplicantDto {
  const factory ApplicantDto({
    required String? id,
    required String? status,
    required String? requestId,
    @JsonKey(defaultValue: []) required List<ApplicantStudyStream?> studyStreams,
  }) = _ApplicantDto;

  factory ApplicantDto.fromJson(Map<String, dynamic> json) => _$ApplicantDtoFromJson(json);
}

@freezed
abstract class ApplicantStudyStream with _$ApplicantStudyStream {
  const factory ApplicantStudyStream({
    required String streamId,
    required String status,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? nextSessionDate,
    required bool cancelled,
    DateTime? cancellationDate,
  }) = _ApplicantStudyStream;

  factory ApplicantStudyStream.fromJson(Map<String, dynamic> json) =>
      _$ApplicantStudyStreamFromJson(json);
}
