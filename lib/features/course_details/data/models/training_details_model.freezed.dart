// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_details_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingDetailsModel {
  String get id;
  String get title;
  @JsonKey(defaultValue: '')
  String get description;
  @JsonKey(defaultValue: '')
  String get overview;
  @JsonKey(fromJson: languageCodeToText, defaultValue: '')
  String get language;
  @JsonKey(defaultValue: '')
  String get languageCode;
  @JsonKey(fromJson: fromJsonToDuration, defaultValue: '')
  String get duration;
  int? get durationMin;
  int? get durationMax;
  @JsonKey(fromJson: levelEnumToText, defaultValue: '')
  String get level;
  @JsonKey(defaultValue: '')
  String get skillLevel;
  ImageModel? get profileImage;
  @JsonKey(defaultValue: '')
  String get profileImageUrl;
  @JsonKey(defaultValue: '')
  String get status;
  DateTime? get createdDate;
  DateTime? get lastModifiedDate;
  @JsonKey(defaultValue: [])
  List<Requirement> get requirements;
  @JsonKey(defaultValue: [])
  List<Outcome> get outcomes;
  @JsonKey(defaultValue: [])
  List<String> get skills;
  TrainingStructureModel? get trainingStructure;
  @JsonKey(defaultValue: [])
  List<QualificationTest> get qualificationTests;
  Sector? get sector;
  Domain? get domain;
  @JsonKey(defaultValue: '')
  String get organizationName;
  @JsonKey(defaultValue: '')
  String get trainingProviderName;
  @JsonKey(defaultValue: 0)
  int get enrolledCount;
  @JsonKey(defaultValue: '')
  String get avatarUrl;
  @JsonKey(defaultValue: '')
  String get organizationId;
  @JsonKey(defaultValue: '')
  String get promoVideoUrl;
  @JsonKey(fromJson: stringToTrainingType)
  TrainingType get type;
  @JsonKey(defaultValue: [])
  List<SeatingCapacity?> get seatingCapacities;
  @JsonKey(defaultValue: [])
  List<StudyStream?> get studyStreams;
  @JsonKey(defaultValue: '')
  String get copiedFromId;
  int get version;
  @JsonKey(defaultValue: '')
  String get rootId;
  @JsonKey(defaultValue: '')
  String get createdBy;
  @JsonKey(defaultValue: '')
  String get instructorPermission;
  PromoVideoModel? get promoVideo;
  ApplicantDto? get applicantDto;
  @JsonKey(defaultValue: false)
  bool get hasFutureOnline;
  @JsonKey(defaultValue: false)
  bool get hasFutureInPerson;

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingDetailsModelCopyWith<TrainingDetailsModel> get copyWith =>
      _$TrainingDetailsModelCopyWithImpl<TrainingDetailsModel>(
          this as TrainingDetailsModel, _$identity);

  /// Serializes this TrainingDetailsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingDetailsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.overview, overview) || other.overview == overview) &&
            (identical(other.language, language) || other.language == language) &&
            (identical(other.languageCode, languageCode) || other.languageCode == languageCode) &&
            (identical(other.duration, duration) || other.duration == duration) &&
            (identical(other.durationMin, durationMin) || other.durationMin == durationMin) &&
            (identical(other.durationMax, durationMax) || other.durationMax == durationMax) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.skillLevel, skillLevel) || other.skillLevel == skillLevel) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            const DeepCollectionEquality().equals(other.requirements, requirements) &&
            const DeepCollectionEquality().equals(other.outcomes, outcomes) &&
            const DeepCollectionEquality().equals(other.skills, skills) &&
            (identical(other.trainingStructure, trainingStructure) ||
                other.trainingStructure == trainingStructure) &&
            const DeepCollectionEquality().equals(other.qualificationTests, qualificationTests) &&
            (identical(other.sector, sector) || other.sector == sector) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.trainingProviderName, trainingProviderName) ||
                other.trainingProviderName == trainingProviderName) &&
            (identical(other.enrolledCount, enrolledCount) ||
                other.enrolledCount == enrolledCount) &&
            (identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.promoVideoUrl, promoVideoUrl) ||
                other.promoVideoUrl == promoVideoUrl) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other.seatingCapacities, seatingCapacities) &&
            const DeepCollectionEquality().equals(other.studyStreams, studyStreams) &&
            (identical(other.copiedFromId, copiedFromId) || other.copiedFromId == copiedFromId) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            (identical(other.createdBy, createdBy) || other.createdBy == createdBy) &&
            (identical(other.instructorPermission, instructorPermission) ||
                other.instructorPermission == instructorPermission) &&
            (identical(other.promoVideo, promoVideo) || other.promoVideo == promoVideo) &&
            (identical(other.applicantDto, applicantDto) || other.applicantDto == applicantDto) &&
            (identical(other.hasFutureOnline, hasFutureOnline) ||
                other.hasFutureOnline == hasFutureOnline) &&
            (identical(other.hasFutureInPerson, hasFutureInPerson) ||
                other.hasFutureInPerson == hasFutureInPerson));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        description,
        overview,
        language,
        languageCode,
        duration,
        durationMin,
        durationMax,
        level,
        skillLevel,
        profileImage,
        profileImageUrl,
        status,
        createdDate,
        lastModifiedDate,
        const DeepCollectionEquality().hash(requirements),
        const DeepCollectionEquality().hash(outcomes),
        const DeepCollectionEquality().hash(skills),
        trainingStructure,
        const DeepCollectionEquality().hash(qualificationTests),
        sector,
        domain,
        organizationName,
        trainingProviderName,
        enrolledCount,
        avatarUrl,
        organizationId,
        promoVideoUrl,
        type,
        const DeepCollectionEquality().hash(seatingCapacities),
        const DeepCollectionEquality().hash(studyStreams),
        copiedFromId,
        version,
        rootId,
        createdBy,
        instructorPermission,
        promoVideo,
        applicantDto,
        hasFutureOnline,
        hasFutureInPerson
      ]);

  @override
  String toString() {
    return 'TrainingDetailsModel(id: $id, title: $title, description: $description, overview: $overview, language: $language, languageCode: $languageCode, duration: $duration, durationMin: $durationMin, durationMax: $durationMax, level: $level, skillLevel: $skillLevel, profileImage: $profileImage, profileImageUrl: $profileImageUrl, status: $status, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate, requirements: $requirements, outcomes: $outcomes, skills: $skills, trainingStructure: $trainingStructure, qualificationTests: $qualificationTests, sector: $sector, domain: $domain, organizationName: $organizationName, trainingProviderName: $trainingProviderName, enrolledCount: $enrolledCount, avatarUrl: $avatarUrl, organizationId: $organizationId, promoVideoUrl: $promoVideoUrl, type: $type, seatingCapacities: $seatingCapacities, studyStreams: $studyStreams, copiedFromId: $copiedFromId, version: $version, rootId: $rootId, createdBy: $createdBy, instructorPermission: $instructorPermission, promoVideo: $promoVideo, applicantDto: $applicantDto, hasFutureOnline: $hasFutureOnline, hasFutureInPerson: $hasFutureInPerson)';
  }
}

/// @nodoc
abstract mixin class $TrainingDetailsModelCopyWith<$Res> {
  factory $TrainingDetailsModelCopyWith(
          TrainingDetailsModel value, $Res Function(TrainingDetailsModel) _then) =
      _$TrainingDetailsModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String title,
      @JsonKey(defaultValue: '') String description,
      @JsonKey(defaultValue: '') String overview,
      @JsonKey(fromJson: languageCodeToText, defaultValue: '') String language,
      @JsonKey(defaultValue: '') String languageCode,
      @JsonKey(fromJson: fromJsonToDuration, defaultValue: '') String duration,
      int? durationMin,
      int? durationMax,
      @JsonKey(fromJson: levelEnumToText, defaultValue: '') String level,
      @JsonKey(defaultValue: '') String skillLevel,
      ImageModel? profileImage,
      @JsonKey(defaultValue: '') String profileImageUrl,
      @JsonKey(defaultValue: '') String status,
      DateTime? createdDate,
      DateTime? lastModifiedDate,
      @JsonKey(defaultValue: []) List<Requirement> requirements,
      @JsonKey(defaultValue: []) List<Outcome> outcomes,
      @JsonKey(defaultValue: []) List<String> skills,
      TrainingStructureModel? trainingStructure,
      @JsonKey(defaultValue: []) List<QualificationTest> qualificationTests,
      Sector? sector,
      Domain? domain,
      @JsonKey(defaultValue: '') String organizationName,
      @JsonKey(defaultValue: '') String trainingProviderName,
      @JsonKey(defaultValue: 0) int enrolledCount,
      @JsonKey(defaultValue: '') String avatarUrl,
      @JsonKey(defaultValue: '') String organizationId,
      @JsonKey(defaultValue: '') String promoVideoUrl,
      @JsonKey(fromJson: stringToTrainingType) TrainingType type,
      @JsonKey(defaultValue: []) List<SeatingCapacity?> seatingCapacities,
      @JsonKey(defaultValue: []) List<StudyStream?> studyStreams,
      @JsonKey(defaultValue: '') String copiedFromId,
      int version,
      @JsonKey(defaultValue: '') String rootId,
      @JsonKey(defaultValue: '') String createdBy,
      @JsonKey(defaultValue: '') String instructorPermission,
      PromoVideoModel? promoVideo,
      ApplicantDto? applicantDto,
      @JsonKey(defaultValue: false) bool hasFutureOnline,
      @JsonKey(defaultValue: false) bool hasFutureInPerson});

  $ImageModelCopyWith<$Res>? get profileImage;
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure;
  $SectorCopyWith<$Res>? get sector;
  $DomainCopyWith<$Res>? get domain;
  $PromoVideoModelCopyWith<$Res>? get promoVideo;
  $ApplicantDtoCopyWith<$Res>? get applicantDto;
}

/// @nodoc
class _$TrainingDetailsModelCopyWithImpl<$Res> implements $TrainingDetailsModelCopyWith<$Res> {
  _$TrainingDetailsModelCopyWithImpl(this._self, this._then);

  final TrainingDetailsModel _self;
  final $Res Function(TrainingDetailsModel) _then;

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? overview = null,
    Object? language = null,
    Object? languageCode = null,
    Object? duration = null,
    Object? durationMin = freezed,
    Object? durationMax = freezed,
    Object? level = null,
    Object? skillLevel = null,
    Object? profileImage = freezed,
    Object? profileImageUrl = null,
    Object? status = null,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
    Object? requirements = null,
    Object? outcomes = null,
    Object? skills = null,
    Object? trainingStructure = freezed,
    Object? qualificationTests = null,
    Object? sector = freezed,
    Object? domain = freezed,
    Object? organizationName = null,
    Object? trainingProviderName = null,
    Object? enrolledCount = null,
    Object? avatarUrl = null,
    Object? organizationId = null,
    Object? promoVideoUrl = null,
    Object? type = null,
    Object? seatingCapacities = null,
    Object? studyStreams = null,
    Object? copiedFromId = null,
    Object? version = null,
    Object? rootId = null,
    Object? createdBy = null,
    Object? instructorPermission = null,
    Object? promoVideo = freezed,
    Object? applicantDto = freezed,
    Object? hasFutureOnline = null,
    Object? hasFutureInPerson = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      overview: null == overview
          ? _self.overview
          : overview // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _self.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String,
      durationMin: freezed == durationMin
          ? _self.durationMin
          : durationMin // ignore: cast_nullable_to_non_nullable
              as int?,
      durationMax: freezed == durationMax
          ? _self.durationMax
          : durationMax // ignore: cast_nullable_to_non_nullable
              as int?,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      skillLevel: null == skillLevel
          ? _self.skillLevel
          : skillLevel // ignore: cast_nullable_to_non_nullable
              as String,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      requirements: null == requirements
          ? _self.requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<Requirement>,
      outcomes: null == outcomes
          ? _self.outcomes
          : outcomes // ignore: cast_nullable_to_non_nullable
              as List<Outcome>,
      skills: null == skills
          ? _self.skills
          : skills // ignore: cast_nullable_to_non_nullable
              as List<String>,
      trainingStructure: freezed == trainingStructure
          ? _self.trainingStructure
          : trainingStructure // ignore: cast_nullable_to_non_nullable
              as TrainingStructureModel?,
      qualificationTests: null == qualificationTests
          ? _self.qualificationTests
          : qualificationTests // ignore: cast_nullable_to_non_nullable
              as List<QualificationTest>,
      sector: freezed == sector
          ? _self.sector
          : sector // ignore: cast_nullable_to_non_nullable
              as Sector?,
      domain: freezed == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as Domain?,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      trainingProviderName: null == trainingProviderName
          ? _self.trainingProviderName
          : trainingProviderName // ignore: cast_nullable_to_non_nullable
              as String,
      enrolledCount: null == enrolledCount
          ? _self.enrolledCount
          : enrolledCount // ignore: cast_nullable_to_non_nullable
              as int,
      avatarUrl: null == avatarUrl
          ? _self.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String,
      organizationId: null == organizationId
          ? _self.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String,
      promoVideoUrl: null == promoVideoUrl
          ? _self.promoVideoUrl
          : promoVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as TrainingType,
      seatingCapacities: null == seatingCapacities
          ? _self.seatingCapacities
          : seatingCapacities // ignore: cast_nullable_to_non_nullable
              as List<SeatingCapacity?>,
      studyStreams: null == studyStreams
          ? _self.studyStreams
          : studyStreams // ignore: cast_nullable_to_non_nullable
              as List<StudyStream?>,
      copiedFromId: null == copiedFromId
          ? _self.copiedFromId
          : copiedFromId // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      rootId: null == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String,
      createdBy: null == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      instructorPermission: null == instructorPermission
          ? _self.instructorPermission
          : instructorPermission // ignore: cast_nullable_to_non_nullable
              as String,
      promoVideo: freezed == promoVideo
          ? _self.promoVideo
          : promoVideo // ignore: cast_nullable_to_non_nullable
              as PromoVideoModel?,
      applicantDto: freezed == applicantDto
          ? _self.applicantDto
          : applicantDto // ignore: cast_nullable_to_non_nullable
              as ApplicantDto?,
      hasFutureOnline: null == hasFutureOnline
          ? _self.hasFutureOnline
          : hasFutureOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureInPerson: null == hasFutureInPerson
          ? _self.hasFutureInPerson
          : hasFutureInPerson // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure {
    if (_self.trainingStructure == null) {
      return null;
    }

    return $TrainingStructureModelCopyWith<$Res>(_self.trainingStructure!, (value) {
      return _then(_self.copyWith(trainingStructure: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorCopyWith<$Res>? get sector {
    if (_self.sector == null) {
      return null;
    }

    return $SectorCopyWith<$Res>(_self.sector!, (value) {
      return _then(_self.copyWith(sector: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DomainCopyWith<$Res>? get domain {
    if (_self.domain == null) {
      return null;
    }

    return $DomainCopyWith<$Res>(_self.domain!, (value) {
      return _then(_self.copyWith(domain: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PromoVideoModelCopyWith<$Res>? get promoVideo {
    if (_self.promoVideo == null) {
      return null;
    }

    return $PromoVideoModelCopyWith<$Res>(_self.promoVideo!, (value) {
      return _then(_self.copyWith(promoVideo: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApplicantDtoCopyWith<$Res>? get applicantDto {
    if (_self.applicantDto == null) {
      return null;
    }

    return $ApplicantDtoCopyWith<$Res>(_self.applicantDto!, (value) {
      return _then(_self.copyWith(applicantDto: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingDetailsModel implements TrainingDetailsModel {
  const _TrainingDetailsModel(
      {required this.id,
      required this.title,
      @JsonKey(defaultValue: '') required this.description,
      @JsonKey(defaultValue: '') required this.overview,
      @JsonKey(fromJson: languageCodeToText, defaultValue: '') required this.language,
      @JsonKey(defaultValue: '') required this.languageCode,
      @JsonKey(fromJson: fromJsonToDuration, defaultValue: '') required this.duration,
      required this.durationMin,
      required this.durationMax,
      @JsonKey(fromJson: levelEnumToText, defaultValue: '') required this.level,
      @JsonKey(defaultValue: '') required this.skillLevel,
      required this.profileImage,
      @JsonKey(defaultValue: '') required this.profileImageUrl,
      @JsonKey(defaultValue: '') required this.status,
      required this.createdDate,
      required this.lastModifiedDate,
      @JsonKey(defaultValue: []) required final List<Requirement> requirements,
      @JsonKey(defaultValue: []) required final List<Outcome> outcomes,
      @JsonKey(defaultValue: []) required final List<String> skills,
      required this.trainingStructure,
      @JsonKey(defaultValue: []) required final List<QualificationTest> qualificationTests,
      required this.sector,
      required this.domain,
      @JsonKey(defaultValue: '') required this.organizationName,
      @JsonKey(defaultValue: '') required this.trainingProviderName,
      @JsonKey(defaultValue: 0) required this.enrolledCount,
      @JsonKey(defaultValue: '') required this.avatarUrl,
      @JsonKey(defaultValue: '') required this.organizationId,
      @JsonKey(defaultValue: '') required this.promoVideoUrl,
      @JsonKey(fromJson: stringToTrainingType) required this.type,
      @JsonKey(defaultValue: []) required final List<SeatingCapacity?> seatingCapacities,
      @JsonKey(defaultValue: []) required final List<StudyStream?> studyStreams,
      @JsonKey(defaultValue: '') required this.copiedFromId,
      required this.version,
      @JsonKey(defaultValue: '') required this.rootId,
      @JsonKey(defaultValue: '') required this.createdBy,
      @JsonKey(defaultValue: '') required this.instructorPermission,
      this.promoVideo,
      this.applicantDto,
      @JsonKey(defaultValue: false) required this.hasFutureOnline,
      @JsonKey(defaultValue: false) required this.hasFutureInPerson})
      : _requirements = requirements,
        _outcomes = outcomes,
        _skills = skills,
        _qualificationTests = qualificationTests,
        _seatingCapacities = seatingCapacities,
        _studyStreams = studyStreams;
  factory _TrainingDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingDetailsModelFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  @JsonKey(defaultValue: '')
  final String description;
  @override
  @JsonKey(defaultValue: '')
  final String overview;
  @override
  @JsonKey(fromJson: languageCodeToText, defaultValue: '')
  final String language;
  @override
  @JsonKey(defaultValue: '')
  final String languageCode;
  @override
  @JsonKey(fromJson: fromJsonToDuration, defaultValue: '')
  final String duration;
  @override
  final int? durationMin;
  @override
  final int? durationMax;
  @override
  @JsonKey(fromJson: levelEnumToText, defaultValue: '')
  final String level;
  @override
  @JsonKey(defaultValue: '')
  final String skillLevel;
  @override
  final ImageModel? profileImage;
  @override
  @JsonKey(defaultValue: '')
  final String profileImageUrl;
  @override
  @JsonKey(defaultValue: '')
  final String status;
  @override
  final DateTime? createdDate;
  @override
  final DateTime? lastModifiedDate;
  final List<Requirement> _requirements;
  @override
  @JsonKey(defaultValue: [])
  List<Requirement> get requirements {
    if (_requirements is EqualUnmodifiableListView) return _requirements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requirements);
  }

  final List<Outcome> _outcomes;
  @override
  @JsonKey(defaultValue: [])
  List<Outcome> get outcomes {
    if (_outcomes is EqualUnmodifiableListView) return _outcomes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_outcomes);
  }

  final List<String> _skills;
  @override
  @JsonKey(defaultValue: [])
  List<String> get skills {
    if (_skills is EqualUnmodifiableListView) return _skills;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skills);
  }

  @override
  final TrainingStructureModel? trainingStructure;
  final List<QualificationTest> _qualificationTests;
  @override
  @JsonKey(defaultValue: [])
  List<QualificationTest> get qualificationTests {
    if (_qualificationTests is EqualUnmodifiableListView) return _qualificationTests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_qualificationTests);
  }

  @override
  final Sector? sector;
  @override
  final Domain? domain;
  @override
  @JsonKey(defaultValue: '')
  final String organizationName;
  @override
  @JsonKey(defaultValue: '')
  final String trainingProviderName;
  @override
  @JsonKey(defaultValue: 0)
  final int enrolledCount;
  @override
  @JsonKey(defaultValue: '')
  final String avatarUrl;
  @override
  @JsonKey(defaultValue: '')
  final String organizationId;
  @override
  @JsonKey(defaultValue: '')
  final String promoVideoUrl;
  @override
  @JsonKey(fromJson: stringToTrainingType)
  final TrainingType type;
  final List<SeatingCapacity?> _seatingCapacities;
  @override
  @JsonKey(defaultValue: [])
  List<SeatingCapacity?> get seatingCapacities {
    if (_seatingCapacities is EqualUnmodifiableListView) return _seatingCapacities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_seatingCapacities);
  }

  final List<StudyStream?> _studyStreams;
  @override
  @JsonKey(defaultValue: [])
  List<StudyStream?> get studyStreams {
    if (_studyStreams is EqualUnmodifiableListView) return _studyStreams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_studyStreams);
  }

  @override
  @JsonKey(defaultValue: '')
  final String copiedFromId;
  @override
  final int version;
  @override
  @JsonKey(defaultValue: '')
  final String rootId;
  @override
  @JsonKey(defaultValue: '')
  final String createdBy;
  @override
  @JsonKey(defaultValue: '')
  final String instructorPermission;
  @override
  final PromoVideoModel? promoVideo;
  @override
  final ApplicantDto? applicantDto;
  @override
  @JsonKey(defaultValue: false)
  final bool hasFutureOnline;
  @override
  @JsonKey(defaultValue: false)
  final bool hasFutureInPerson;

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingDetailsModelCopyWith<_TrainingDetailsModel> get copyWith =>
      __$TrainingDetailsModelCopyWithImpl<_TrainingDetailsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingDetailsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingDetailsModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.overview, overview) || other.overview == overview) &&
            (identical(other.language, language) || other.language == language) &&
            (identical(other.languageCode, languageCode) || other.languageCode == languageCode) &&
            (identical(other.duration, duration) || other.duration == duration) &&
            (identical(other.durationMin, durationMin) || other.durationMin == durationMin) &&
            (identical(other.durationMax, durationMax) || other.durationMax == durationMax) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.skillLevel, skillLevel) || other.skillLevel == skillLevel) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            const DeepCollectionEquality().equals(other._requirements, _requirements) &&
            const DeepCollectionEquality().equals(other._outcomes, _outcomes) &&
            const DeepCollectionEquality().equals(other._skills, _skills) &&
            (identical(other.trainingStructure, trainingStructure) ||
                other.trainingStructure == trainingStructure) &&
            const DeepCollectionEquality().equals(other._qualificationTests, _qualificationTests) &&
            (identical(other.sector, sector) || other.sector == sector) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.trainingProviderName, trainingProviderName) ||
                other.trainingProviderName == trainingProviderName) &&
            (identical(other.enrolledCount, enrolledCount) ||
                other.enrolledCount == enrolledCount) &&
            (identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl) &&
            (identical(other.organizationId, organizationId) ||
                other.organizationId == organizationId) &&
            (identical(other.promoVideoUrl, promoVideoUrl) ||
                other.promoVideoUrl == promoVideoUrl) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._seatingCapacities, _seatingCapacities) &&
            const DeepCollectionEquality().equals(other._studyStreams, _studyStreams) &&
            (identical(other.copiedFromId, copiedFromId) || other.copiedFromId == copiedFromId) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            (identical(other.createdBy, createdBy) || other.createdBy == createdBy) &&
            (identical(other.instructorPermission, instructorPermission) ||
                other.instructorPermission == instructorPermission) &&
            (identical(other.promoVideo, promoVideo) || other.promoVideo == promoVideo) &&
            (identical(other.applicantDto, applicantDto) || other.applicantDto == applicantDto) &&
            (identical(other.hasFutureOnline, hasFutureOnline) ||
                other.hasFutureOnline == hasFutureOnline) &&
            (identical(other.hasFutureInPerson, hasFutureInPerson) ||
                other.hasFutureInPerson == hasFutureInPerson));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        description,
        overview,
        language,
        languageCode,
        duration,
        durationMin,
        durationMax,
        level,
        skillLevel,
        profileImage,
        profileImageUrl,
        status,
        createdDate,
        lastModifiedDate,
        const DeepCollectionEquality().hash(_requirements),
        const DeepCollectionEquality().hash(_outcomes),
        const DeepCollectionEquality().hash(_skills),
        trainingStructure,
        const DeepCollectionEquality().hash(_qualificationTests),
        sector,
        domain,
        organizationName,
        trainingProviderName,
        enrolledCount,
        avatarUrl,
        organizationId,
        promoVideoUrl,
        type,
        const DeepCollectionEquality().hash(_seatingCapacities),
        const DeepCollectionEquality().hash(_studyStreams),
        copiedFromId,
        version,
        rootId,
        createdBy,
        instructorPermission,
        promoVideo,
        applicantDto,
        hasFutureOnline,
        hasFutureInPerson
      ]);

  @override
  String toString() {
    return 'TrainingDetailsModel(id: $id, title: $title, description: $description, overview: $overview, language: $language, languageCode: $languageCode, duration: $duration, durationMin: $durationMin, durationMax: $durationMax, level: $level, skillLevel: $skillLevel, profileImage: $profileImage, profileImageUrl: $profileImageUrl, status: $status, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate, requirements: $requirements, outcomes: $outcomes, skills: $skills, trainingStructure: $trainingStructure, qualificationTests: $qualificationTests, sector: $sector, domain: $domain, organizationName: $organizationName, trainingProviderName: $trainingProviderName, enrolledCount: $enrolledCount, avatarUrl: $avatarUrl, organizationId: $organizationId, promoVideoUrl: $promoVideoUrl, type: $type, seatingCapacities: $seatingCapacities, studyStreams: $studyStreams, copiedFromId: $copiedFromId, version: $version, rootId: $rootId, createdBy: $createdBy, instructorPermission: $instructorPermission, promoVideo: $promoVideo, applicantDto: $applicantDto, hasFutureOnline: $hasFutureOnline, hasFutureInPerson: $hasFutureInPerson)';
  }
}

/// @nodoc
abstract mixin class _$TrainingDetailsModelCopyWith<$Res>
    implements $TrainingDetailsModelCopyWith<$Res> {
  factory _$TrainingDetailsModelCopyWith(
          _TrainingDetailsModel value, $Res Function(_TrainingDetailsModel) _then) =
      __$TrainingDetailsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      @JsonKey(defaultValue: '') String description,
      @JsonKey(defaultValue: '') String overview,
      @JsonKey(fromJson: languageCodeToText, defaultValue: '') String language,
      @JsonKey(defaultValue: '') String languageCode,
      @JsonKey(fromJson: fromJsonToDuration, defaultValue: '') String duration,
      int? durationMin,
      int? durationMax,
      @JsonKey(fromJson: levelEnumToText, defaultValue: '') String level,
      @JsonKey(defaultValue: '') String skillLevel,
      ImageModel? profileImage,
      @JsonKey(defaultValue: '') String profileImageUrl,
      @JsonKey(defaultValue: '') String status,
      DateTime? createdDate,
      DateTime? lastModifiedDate,
      @JsonKey(defaultValue: []) List<Requirement> requirements,
      @JsonKey(defaultValue: []) List<Outcome> outcomes,
      @JsonKey(defaultValue: []) List<String> skills,
      TrainingStructureModel? trainingStructure,
      @JsonKey(defaultValue: []) List<QualificationTest> qualificationTests,
      Sector? sector,
      Domain? domain,
      @JsonKey(defaultValue: '') String organizationName,
      @JsonKey(defaultValue: '') String trainingProviderName,
      @JsonKey(defaultValue: 0) int enrolledCount,
      @JsonKey(defaultValue: '') String avatarUrl,
      @JsonKey(defaultValue: '') String organizationId,
      @JsonKey(defaultValue: '') String promoVideoUrl,
      @JsonKey(fromJson: stringToTrainingType) TrainingType type,
      @JsonKey(defaultValue: []) List<SeatingCapacity?> seatingCapacities,
      @JsonKey(defaultValue: []) List<StudyStream?> studyStreams,
      @JsonKey(defaultValue: '') String copiedFromId,
      int version,
      @JsonKey(defaultValue: '') String rootId,
      @JsonKey(defaultValue: '') String createdBy,
      @JsonKey(defaultValue: '') String instructorPermission,
      PromoVideoModel? promoVideo,
      ApplicantDto? applicantDto,
      @JsonKey(defaultValue: false) bool hasFutureOnline,
      @JsonKey(defaultValue: false) bool hasFutureInPerson});

  @override
  $ImageModelCopyWith<$Res>? get profileImage;
  @override
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure;
  @override
  $SectorCopyWith<$Res>? get sector;
  @override
  $DomainCopyWith<$Res>? get domain;
  @override
  $PromoVideoModelCopyWith<$Res>? get promoVideo;
  @override
  $ApplicantDtoCopyWith<$Res>? get applicantDto;
}

/// @nodoc
class __$TrainingDetailsModelCopyWithImpl<$Res> implements _$TrainingDetailsModelCopyWith<$Res> {
  __$TrainingDetailsModelCopyWithImpl(this._self, this._then);

  final _TrainingDetailsModel _self;
  final $Res Function(_TrainingDetailsModel) _then;

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? overview = null,
    Object? language = null,
    Object? languageCode = null,
    Object? duration = null,
    Object? durationMin = freezed,
    Object? durationMax = freezed,
    Object? level = null,
    Object? skillLevel = null,
    Object? profileImage = freezed,
    Object? profileImageUrl = null,
    Object? status = null,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
    Object? requirements = null,
    Object? outcomes = null,
    Object? skills = null,
    Object? trainingStructure = freezed,
    Object? qualificationTests = null,
    Object? sector = freezed,
    Object? domain = freezed,
    Object? organizationName = null,
    Object? trainingProviderName = null,
    Object? enrolledCount = null,
    Object? avatarUrl = null,
    Object? organizationId = null,
    Object? promoVideoUrl = null,
    Object? type = null,
    Object? seatingCapacities = null,
    Object? studyStreams = null,
    Object? copiedFromId = null,
    Object? version = null,
    Object? rootId = null,
    Object? createdBy = null,
    Object? instructorPermission = null,
    Object? promoVideo = freezed,
    Object? applicantDto = freezed,
    Object? hasFutureOnline = null,
    Object? hasFutureInPerson = null,
  }) {
    return _then(_TrainingDetailsModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      overview: null == overview
          ? _self.overview
          : overview // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
      languageCode: null == languageCode
          ? _self.languageCode
          : languageCode // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String,
      durationMin: freezed == durationMin
          ? _self.durationMin
          : durationMin // ignore: cast_nullable_to_non_nullable
              as int?,
      durationMax: freezed == durationMax
          ? _self.durationMax
          : durationMax // ignore: cast_nullable_to_non_nullable
              as int?,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String,
      skillLevel: null == skillLevel
          ? _self.skillLevel
          : skillLevel // ignore: cast_nullable_to_non_nullable
              as String,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      requirements: null == requirements
          ? _self._requirements
          : requirements // ignore: cast_nullable_to_non_nullable
              as List<Requirement>,
      outcomes: null == outcomes
          ? _self._outcomes
          : outcomes // ignore: cast_nullable_to_non_nullable
              as List<Outcome>,
      skills: null == skills
          ? _self._skills
          : skills // ignore: cast_nullable_to_non_nullable
              as List<String>,
      trainingStructure: freezed == trainingStructure
          ? _self.trainingStructure
          : trainingStructure // ignore: cast_nullable_to_non_nullable
              as TrainingStructureModel?,
      qualificationTests: null == qualificationTests
          ? _self._qualificationTests
          : qualificationTests // ignore: cast_nullable_to_non_nullable
              as List<QualificationTest>,
      sector: freezed == sector
          ? _self.sector
          : sector // ignore: cast_nullable_to_non_nullable
              as Sector?,
      domain: freezed == domain
          ? _self.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as Domain?,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      trainingProviderName: null == trainingProviderName
          ? _self.trainingProviderName
          : trainingProviderName // ignore: cast_nullable_to_non_nullable
              as String,
      enrolledCount: null == enrolledCount
          ? _self.enrolledCount
          : enrolledCount // ignore: cast_nullable_to_non_nullable
              as int,
      avatarUrl: null == avatarUrl
          ? _self.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String,
      organizationId: null == organizationId
          ? _self.organizationId
          : organizationId // ignore: cast_nullable_to_non_nullable
              as String,
      promoVideoUrl: null == promoVideoUrl
          ? _self.promoVideoUrl
          : promoVideoUrl // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as TrainingType,
      seatingCapacities: null == seatingCapacities
          ? _self._seatingCapacities
          : seatingCapacities // ignore: cast_nullable_to_non_nullable
              as List<SeatingCapacity?>,
      studyStreams: null == studyStreams
          ? _self._studyStreams
          : studyStreams // ignore: cast_nullable_to_non_nullable
              as List<StudyStream?>,
      copiedFromId: null == copiedFromId
          ? _self.copiedFromId
          : copiedFromId // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      rootId: null == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String,
      createdBy: null == createdBy
          ? _self.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      instructorPermission: null == instructorPermission
          ? _self.instructorPermission
          : instructorPermission // ignore: cast_nullable_to_non_nullable
              as String,
      promoVideo: freezed == promoVideo
          ? _self.promoVideo
          : promoVideo // ignore: cast_nullable_to_non_nullable
              as PromoVideoModel?,
      applicantDto: freezed == applicantDto
          ? _self.applicantDto
          : applicantDto // ignore: cast_nullable_to_non_nullable
              as ApplicantDto?,
      hasFutureOnline: null == hasFutureOnline
          ? _self.hasFutureOnline
          : hasFutureOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureInPerson: null == hasFutureInPerson
          ? _self.hasFutureInPerson
          : hasFutureInPerson // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure {
    if (_self.trainingStructure == null) {
      return null;
    }

    return $TrainingStructureModelCopyWith<$Res>(_self.trainingStructure!, (value) {
      return _then(_self.copyWith(trainingStructure: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorCopyWith<$Res>? get sector {
    if (_self.sector == null) {
      return null;
    }

    return $SectorCopyWith<$Res>(_self.sector!, (value) {
      return _then(_self.copyWith(sector: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DomainCopyWith<$Res>? get domain {
    if (_self.domain == null) {
      return null;
    }

    return $DomainCopyWith<$Res>(_self.domain!, (value) {
      return _then(_self.copyWith(domain: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PromoVideoModelCopyWith<$Res>? get promoVideo {
    if (_self.promoVideo == null) {
      return null;
    }

    return $PromoVideoModelCopyWith<$Res>(_self.promoVideo!, (value) {
      return _then(_self.copyWith(promoVideo: value));
    });
  }

  /// Create a copy of TrainingDetailsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApplicantDtoCopyWith<$Res>? get applicantDto {
    if (_self.applicantDto == null) {
      return null;
    }

    return $ApplicantDtoCopyWith<$Res>(_self.applicantDto!, (value) {
      return _then(_self.copyWith(applicantDto: value));
    });
  }
}

/// @nodoc
mixin _$ImageModel {
  String get originalFilename;
  String get key;
  int get size;

  /// Create a copy of ImageModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<ImageModel> get copyWith =>
      _$ImageModelCopyWithImpl<ImageModel>(this as ImageModel, _$identity);

  /// Serializes this ImageModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ImageModel &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'ImageModel(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class $ImageModelCopyWith<$Res> {
  factory $ImageModelCopyWith(ImageModel value, $Res Function(ImageModel) _then) =
      _$ImageModelCopyWithImpl;
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class _$ImageModelCopyWithImpl<$Res> implements $ImageModelCopyWith<$Res> {
  _$ImageModelCopyWithImpl(this._self, this._then);

  final ImageModel _self;
  final $Res Function(ImageModel) _then;

  /// Create a copy of ImageModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_self.copyWith(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ImageModel implements ImageModel {
  const _ImageModel({required this.originalFilename, required this.key, required this.size});
  factory _ImageModel.fromJson(Map<String, dynamic> json) => _$ImageModelFromJson(json);

  @override
  final String originalFilename;
  @override
  final String key;
  @override
  final int size;

  /// Create a copy of ImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ImageModelCopyWith<_ImageModel> get copyWith =>
      __$ImageModelCopyWithImpl<_ImageModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ImageModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ImageModel &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'ImageModel(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class _$ImageModelCopyWith<$Res> implements $ImageModelCopyWith<$Res> {
  factory _$ImageModelCopyWith(_ImageModel value, $Res Function(_ImageModel) _then) =
      __$ImageModelCopyWithImpl;
  @override
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class __$ImageModelCopyWithImpl<$Res> implements _$ImageModelCopyWith<$Res> {
  __$ImageModelCopyWithImpl(this._self, this._then);

  final _ImageModel _self;
  final $Res Function(_ImageModel) _then;

  /// Create a copy of ImageModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_ImageModel(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$Requirement {
  String get value;
  int get index;

  /// Create a copy of Requirement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RequirementCopyWith<Requirement> get copyWith =>
      _$RequirementCopyWithImpl<Requirement>(this as Requirement, _$identity);

  /// Serializes this Requirement to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Requirement &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, index);

  @override
  String toString() {
    return 'Requirement(value: $value, index: $index)';
  }
}

/// @nodoc
abstract mixin class $RequirementCopyWith<$Res> {
  factory $RequirementCopyWith(Requirement value, $Res Function(Requirement) _then) =
      _$RequirementCopyWithImpl;
  @useResult
  $Res call({String value, int index});
}

/// @nodoc
class _$RequirementCopyWithImpl<$Res> implements $RequirementCopyWith<$Res> {
  _$RequirementCopyWithImpl(this._self, this._then);

  final Requirement _self;
  final $Res Function(Requirement) _then;

  /// Create a copy of Requirement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
    Object? index = null,
  }) {
    return _then(_self.copyWith(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Requirement implements Requirement {
  const _Requirement({required this.value, required this.index});
  factory _Requirement.fromJson(Map<String, dynamic> json) => _$RequirementFromJson(json);

  @override
  final String value;
  @override
  final int index;

  /// Create a copy of Requirement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RequirementCopyWith<_Requirement> get copyWith =>
      __$RequirementCopyWithImpl<_Requirement>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RequirementToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Requirement &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, index);

  @override
  String toString() {
    return 'Requirement(value: $value, index: $index)';
  }
}

/// @nodoc
abstract mixin class _$RequirementCopyWith<$Res> implements $RequirementCopyWith<$Res> {
  factory _$RequirementCopyWith(_Requirement value, $Res Function(_Requirement) _then) =
      __$RequirementCopyWithImpl;
  @override
  @useResult
  $Res call({String value, int index});
}

/// @nodoc
class __$RequirementCopyWithImpl<$Res> implements _$RequirementCopyWith<$Res> {
  __$RequirementCopyWithImpl(this._self, this._then);

  final _Requirement _self;
  final $Res Function(_Requirement) _then;

  /// Create a copy of Requirement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
    Object? index = null,
  }) {
    return _then(_Requirement(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$Outcome {
  String get value;
  int get index;

  /// Create a copy of Outcome
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OutcomeCopyWith<Outcome> get copyWith =>
      _$OutcomeCopyWithImpl<Outcome>(this as Outcome, _$identity);

  /// Serializes this Outcome to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Outcome &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, index);

  @override
  String toString() {
    return 'Outcome(value: $value, index: $index)';
  }
}

/// @nodoc
abstract mixin class $OutcomeCopyWith<$Res> {
  factory $OutcomeCopyWith(Outcome value, $Res Function(Outcome) _then) = _$OutcomeCopyWithImpl;
  @useResult
  $Res call({String value, int index});
}

/// @nodoc
class _$OutcomeCopyWithImpl<$Res> implements $OutcomeCopyWith<$Res> {
  _$OutcomeCopyWithImpl(this._self, this._then);

  final Outcome _self;
  final $Res Function(Outcome) _then;

  /// Create a copy of Outcome
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
    Object? index = null,
  }) {
    return _then(_self.copyWith(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Outcome implements Outcome {
  const _Outcome({required this.value, required this.index});
  factory _Outcome.fromJson(Map<String, dynamic> json) => _$OutcomeFromJson(json);

  @override
  final String value;
  @override
  final int index;

  /// Create a copy of Outcome
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OutcomeCopyWith<_Outcome> get copyWith => __$OutcomeCopyWithImpl<_Outcome>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OutcomeToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Outcome &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, index);

  @override
  String toString() {
    return 'Outcome(value: $value, index: $index)';
  }
}

/// @nodoc
abstract mixin class _$OutcomeCopyWith<$Res> implements $OutcomeCopyWith<$Res> {
  factory _$OutcomeCopyWith(_Outcome value, $Res Function(_Outcome) _then) = __$OutcomeCopyWithImpl;
  @override
  @useResult
  $Res call({String value, int index});
}

/// @nodoc
class __$OutcomeCopyWithImpl<$Res> implements _$OutcomeCopyWith<$Res> {
  __$OutcomeCopyWithImpl(this._self, this._then);

  final _Outcome _self;
  final $Res Function(_Outcome) _then;

  /// Create a copy of Outcome
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
    Object? index = null,
  }) {
    return _then(_Outcome(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$TrainingStructureModel {
  List<Section> get sections;

  /// Create a copy of TrainingStructureModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingStructureModelCopyWith<TrainingStructureModel> get copyWith =>
      _$TrainingStructureModelCopyWithImpl<TrainingStructureModel>(
          this as TrainingStructureModel, _$identity);

  /// Serializes this TrainingStructureModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingStructureModel &&
            const DeepCollectionEquality().equals(other.sections, sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(sections));

  @override
  String toString() {
    return 'TrainingStructureModel(sections: $sections)';
  }
}

/// @nodoc
abstract mixin class $TrainingStructureModelCopyWith<$Res> {
  factory $TrainingStructureModelCopyWith(
          TrainingStructureModel value, $Res Function(TrainingStructureModel) _then) =
      _$TrainingStructureModelCopyWithImpl;
  @useResult
  $Res call({List<Section> sections});
}

/// @nodoc
class _$TrainingStructureModelCopyWithImpl<$Res> implements $TrainingStructureModelCopyWith<$Res> {
  _$TrainingStructureModelCopyWithImpl(this._self, this._then);

  final TrainingStructureModel _self;
  final $Res Function(TrainingStructureModel) _then;

  /// Create a copy of TrainingStructureModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sections = null,
  }) {
    return _then(_self.copyWith(
      sections: null == sections
          ? _self.sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Section>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingStructureModel implements TrainingStructureModel {
  const _TrainingStructureModel({required final List<Section> sections}) : _sections = sections;
  factory _TrainingStructureModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingStructureModelFromJson(json);

  final List<Section> _sections;
  @override
  List<Section> get sections {
    if (_sections is EqualUnmodifiableListView) return _sections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sections);
  }

  /// Create a copy of TrainingStructureModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingStructureModelCopyWith<_TrainingStructureModel> get copyWith =>
      __$TrainingStructureModelCopyWithImpl<_TrainingStructureModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingStructureModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingStructureModel &&
            const DeepCollectionEquality().equals(other._sections, _sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(_sections));

  @override
  String toString() {
    return 'TrainingStructureModel(sections: $sections)';
  }
}

/// @nodoc
abstract mixin class _$TrainingStructureModelCopyWith<$Res>
    implements $TrainingStructureModelCopyWith<$Res> {
  factory _$TrainingStructureModelCopyWith(
          _TrainingStructureModel value, $Res Function(_TrainingStructureModel) _then) =
      __$TrainingStructureModelCopyWithImpl;
  @override
  @useResult
  $Res call({List<Section> sections});
}

/// @nodoc
class __$TrainingStructureModelCopyWithImpl<$Res>
    implements _$TrainingStructureModelCopyWith<$Res> {
  __$TrainingStructureModelCopyWithImpl(this._self, this._then);

  final _TrainingStructureModel _self;
  final $Res Function(_TrainingStructureModel) _then;

  /// Create a copy of TrainingStructureModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sections = null,
  }) {
    return _then(_TrainingStructureModel(
      sections: null == sections
          ? _self._sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<Section>,
    ));
  }
}

/// @nodoc
mixin _$Section {
  String get id;
  String get title;
  int get index;
  List<Lesson> get lessons;

  /// Create a copy of Section
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SectionCopyWith<Section> get copyWith =>
      _$SectionCopyWithImpl<Section>(this as Section, _$identity);

  /// Serializes this Section to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Section &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.index, index) || other.index == index) &&
            const DeepCollectionEquality().equals(other.lessons, lessons));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, title, index, const DeepCollectionEquality().hash(lessons));

  @override
  String toString() {
    return 'Section(id: $id, title: $title, index: $index, lessons: $lessons)';
  }
}

/// @nodoc
abstract mixin class $SectionCopyWith<$Res> {
  factory $SectionCopyWith(Section value, $Res Function(Section) _then) = _$SectionCopyWithImpl;
  @useResult
  $Res call({String id, String title, int index, List<Lesson> lessons});
}

/// @nodoc
class _$SectionCopyWithImpl<$Res> implements $SectionCopyWith<$Res> {
  _$SectionCopyWithImpl(this._self, this._then);

  final Section _self;
  final $Res Function(Section) _then;

  /// Create a copy of Section
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? index = null,
    Object? lessons = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      lessons: null == lessons
          ? _self.lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<Lesson>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Section implements Section {
  const _Section(
      {required this.id,
      required this.title,
      required this.index,
      required final List<Lesson> lessons})
      : _lessons = lessons;
  factory _Section.fromJson(Map<String, dynamic> json) => _$SectionFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final int index;
  final List<Lesson> _lessons;
  @override
  List<Lesson> get lessons {
    if (_lessons is EqualUnmodifiableListView) return _lessons;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_lessons);
  }

  /// Create a copy of Section
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SectionCopyWith<_Section> get copyWith => __$SectionCopyWithImpl<_Section>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SectionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Section &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.index, index) || other.index == index) &&
            const DeepCollectionEquality().equals(other._lessons, _lessons));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, title, index, const DeepCollectionEquality().hash(_lessons));

  @override
  String toString() {
    return 'Section(id: $id, title: $title, index: $index, lessons: $lessons)';
  }
}

/// @nodoc
abstract mixin class _$SectionCopyWith<$Res> implements $SectionCopyWith<$Res> {
  factory _$SectionCopyWith(_Section value, $Res Function(_Section) _then) = __$SectionCopyWithImpl;
  @override
  @useResult
  $Res call({String id, String title, int index, List<Lesson> lessons});
}

/// @nodoc
class __$SectionCopyWithImpl<$Res> implements _$SectionCopyWith<$Res> {
  __$SectionCopyWithImpl(this._self, this._then);

  final _Section _self;
  final $Res Function(_Section) _then;

  /// Create a copy of Section
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? index = null,
    Object? lessons = null,
  }) {
    return _then(_Section(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      index: null == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int,
      lessons: null == lessons
          ? _self._lessons
          : lessons // ignore: cast_nullable_to_non_nullable
              as List<Lesson>,
    ));
  }
}

/// @nodoc
mixin _$Lesson {
  String get id;
  @JsonKey(defaultValue: '')
  String get title;
  int? get index;
  @JsonKey(fromJson: stringToLessonType, name: 'type')
  LessonType? get lessonType;
  String? get text;
  @JsonKey(defaultValue: [])
  List<Resource>? get resources;
  Quiz? get quiz;

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LessonCopyWith<Lesson> get copyWith => _$LessonCopyWithImpl<Lesson>(this as Lesson, _$identity);

  /// Serializes this Lesson to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Lesson &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.lessonType, lessonType) || other.lessonType == lessonType) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other.resources, resources) &&
            (identical(other.quiz, quiz) || other.quiz == quiz));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, index, lessonType, text,
      const DeepCollectionEquality().hash(resources), quiz);

  @override
  String toString() {
    return 'Lesson(id: $id, title: $title, index: $index, lessonType: $lessonType, text: $text, resources: $resources, quiz: $quiz)';
  }
}

/// @nodoc
abstract mixin class $LessonCopyWith<$Res> {
  factory $LessonCopyWith(Lesson value, $Res Function(Lesson) _then) = _$LessonCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      @JsonKey(defaultValue: '') String title,
      int? index,
      @JsonKey(fromJson: stringToLessonType, name: 'type') LessonType? lessonType,
      String? text,
      @JsonKey(defaultValue: []) List<Resource>? resources,
      Quiz? quiz});

  $QuizCopyWith<$Res>? get quiz;
}

/// @nodoc
class _$LessonCopyWithImpl<$Res> implements $LessonCopyWith<$Res> {
  _$LessonCopyWithImpl(this._self, this._then);

  final Lesson _self;
  final $Res Function(Lesson) _then;

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? index = freezed,
    Object? lessonType = freezed,
    Object? text = freezed,
    Object? resources = freezed,
    Object? quiz = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonType: freezed == lessonType
          ? _self.lessonType
          : lessonType // ignore: cast_nullable_to_non_nullable
              as LessonType?,
      text: freezed == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      resources: freezed == resources
          ? _self.resources
          : resources // ignore: cast_nullable_to_non_nullable
              as List<Resource>?,
      quiz: freezed == quiz
          ? _self.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
    ));
  }

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizCopyWith<$Res>? get quiz {
    if (_self.quiz == null) {
      return null;
    }

    return $QuizCopyWith<$Res>(_self.quiz!, (value) {
      return _then(_self.copyWith(quiz: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Lesson implements Lesson {
  const _Lesson(
      {required this.id,
      @JsonKey(defaultValue: '') required this.title,
      required this.index,
      @JsonKey(fromJson: stringToLessonType, name: 'type') required this.lessonType,
      this.text,
      @JsonKey(defaultValue: []) final List<Resource>? resources,
      this.quiz})
      : _resources = resources;
  factory _Lesson.fromJson(Map<String, dynamic> json) => _$LessonFromJson(json);

  @override
  final String id;
  @override
  @JsonKey(defaultValue: '')
  final String title;
  @override
  final int? index;
  @override
  @JsonKey(fromJson: stringToLessonType, name: 'type')
  final LessonType? lessonType;
  @override
  final String? text;
  final List<Resource>? _resources;
  @override
  @JsonKey(defaultValue: [])
  List<Resource>? get resources {
    final value = _resources;
    if (value == null) return null;
    if (_resources is EqualUnmodifiableListView) return _resources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final Quiz? quiz;

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LessonCopyWith<_Lesson> get copyWith => __$LessonCopyWithImpl<_Lesson>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LessonToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Lesson &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.index, index) || other.index == index) &&
            (identical(other.lessonType, lessonType) || other.lessonType == lessonType) &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other._resources, _resources) &&
            (identical(other.quiz, quiz) || other.quiz == quiz));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, index, lessonType, text,
      const DeepCollectionEquality().hash(_resources), quiz);

  @override
  String toString() {
    return 'Lesson(id: $id, title: $title, index: $index, lessonType: $lessonType, text: $text, resources: $resources, quiz: $quiz)';
  }
}

/// @nodoc
abstract mixin class _$LessonCopyWith<$Res> implements $LessonCopyWith<$Res> {
  factory _$LessonCopyWith(_Lesson value, $Res Function(_Lesson) _then) = __$LessonCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(defaultValue: '') String title,
      int? index,
      @JsonKey(fromJson: stringToLessonType, name: 'type') LessonType? lessonType,
      String? text,
      @JsonKey(defaultValue: []) List<Resource>? resources,
      Quiz? quiz});

  @override
  $QuizCopyWith<$Res>? get quiz;
}

/// @nodoc
class __$LessonCopyWithImpl<$Res> implements _$LessonCopyWith<$Res> {
  __$LessonCopyWithImpl(this._self, this._then);

  final _Lesson _self;
  final $Res Function(_Lesson) _then;

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? index = freezed,
    Object? lessonType = freezed,
    Object? text = freezed,
    Object? resources = freezed,
    Object? quiz = freezed,
  }) {
    return _then(_Lesson(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonType: freezed == lessonType
          ? _self.lessonType
          : lessonType // ignore: cast_nullable_to_non_nullable
              as LessonType?,
      text: freezed == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      resources: freezed == resources
          ? _self._resources
          : resources // ignore: cast_nullable_to_non_nullable
              as List<Resource>?,
      quiz: freezed == quiz
          ? _self.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
    ));
  }

  /// Create a copy of Lesson
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizCopyWith<$Res>? get quiz {
    if (_self.quiz == null) {
      return null;
    }

    return $QuizCopyWith<$Res>(_self.quiz!, (value) {
      return _then(_self.copyWith(quiz: value));
    });
  }
}

/// @nodoc
mixin _$Resource {
  String? get originalFilename;
  String? get key;
  int? get size;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ResourceCopyWith<Resource> get copyWith =>
      _$ResourceCopyWithImpl<Resource>(this as Resource, _$identity);

  /// Serializes this Resource to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Resource &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'Resource(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class $ResourceCopyWith<$Res> {
  factory $ResourceCopyWith(Resource value, $Res Function(Resource) _then) = _$ResourceCopyWithImpl;
  @useResult
  $Res call({String? originalFilename, String? key, int? size});
}

/// @nodoc
class _$ResourceCopyWithImpl<$Res> implements $ResourceCopyWith<$Res> {
  _$ResourceCopyWithImpl(this._self, this._then);

  final Resource _self;
  final $Res Function(Resource) _then;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalFilename = freezed,
    Object? key = freezed,
    Object? size = freezed,
  }) {
    return _then(_self.copyWith(
      originalFilename: freezed == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      size: freezed == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Resource implements Resource {
  const _Resource({required this.originalFilename, required this.key, required this.size});
  factory _Resource.fromJson(Map<String, dynamic> json) => _$ResourceFromJson(json);

  @override
  final String? originalFilename;
  @override
  final String? key;
  @override
  final int? size;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ResourceCopyWith<_Resource> get copyWith =>
      __$ResourceCopyWithImpl<_Resource>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ResourceToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Resource &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'Resource(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class _$ResourceCopyWith<$Res> implements $ResourceCopyWith<$Res> {
  factory _$ResourceCopyWith(_Resource value, $Res Function(_Resource) _then) =
      __$ResourceCopyWithImpl;
  @override
  @useResult
  $Res call({String? originalFilename, String? key, int? size});
}

/// @nodoc
class __$ResourceCopyWithImpl<$Res> implements _$ResourceCopyWith<$Res> {
  __$ResourceCopyWithImpl(this._self, this._then);

  final _Resource _self;
  final $Res Function(_Resource) _then;

  /// Create a copy of Resource
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? originalFilename = freezed,
    Object? key = freezed,
    Object? size = freezed,
  }) {
    return _then(_Resource(
      originalFilename: freezed == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String?,
      key: freezed == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String?,
      size: freezed == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$Quiz {
  String get id;
  bool? get randomized;
  @JsonKey(defaultValue: [])
  List<Question?> get questions;

  /// Create a copy of Quiz
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizCopyWith<Quiz> get copyWith => _$QuizCopyWithImpl<Quiz>(this as Quiz, _$identity);

  /// Serializes this Quiz to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Quiz &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            const DeepCollectionEquality().equals(other.questions, questions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, randomized, const DeepCollectionEquality().hash(questions));

  @override
  String toString() {
    return 'Quiz(id: $id, randomized: $randomized, questions: $questions)';
  }
}

/// @nodoc
abstract mixin class $QuizCopyWith<$Res> {
  factory $QuizCopyWith(Quiz value, $Res Function(Quiz) _then) = _$QuizCopyWithImpl;
  @useResult
  $Res call({String id, bool? randomized, @JsonKey(defaultValue: []) List<Question?> questions});
}

/// @nodoc
class _$QuizCopyWithImpl<$Res> implements $QuizCopyWith<$Res> {
  _$QuizCopyWithImpl(this._self, this._then);

  final Quiz _self;
  final $Res Function(Quiz) _then;

  /// Create a copy of Quiz
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? randomized = freezed,
    Object? questions = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      questions: null == questions
          ? _self.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Quiz implements Quiz {
  const _Quiz(
      {required this.id,
      required this.randomized,
      @JsonKey(defaultValue: []) required final List<Question?> questions})
      : _questions = questions;
  factory _Quiz.fromJson(Map<String, dynamic> json) => _$QuizFromJson(json);

  @override
  final String id;
  @override
  final bool? randomized;
  final List<Question?> _questions;
  @override
  @JsonKey(defaultValue: [])
  List<Question?> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  /// Create a copy of Quiz
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizCopyWith<_Quiz> get copyWith => __$QuizCopyWithImpl<_Quiz>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Quiz &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            const DeepCollectionEquality().equals(other._questions, _questions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, randomized, const DeepCollectionEquality().hash(_questions));

  @override
  String toString() {
    return 'Quiz(id: $id, randomized: $randomized, questions: $questions)';
  }
}

/// @nodoc
abstract mixin class _$QuizCopyWith<$Res> implements $QuizCopyWith<$Res> {
  factory _$QuizCopyWith(_Quiz value, $Res Function(_Quiz) _then) = __$QuizCopyWithImpl;
  @override
  @useResult
  $Res call({String id, bool? randomized, @JsonKey(defaultValue: []) List<Question?> questions});
}

/// @nodoc
class __$QuizCopyWithImpl<$Res> implements _$QuizCopyWith<$Res> {
  __$QuizCopyWithImpl(this._self, this._then);

  final _Quiz _self;
  final $Res Function(_Quiz) _then;

  /// Create a copy of Quiz
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? randomized = freezed,
    Object? questions = null,
  }) {
    return _then(_Quiz(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      questions: null == questions
          ? _self._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question?>,
    ));
  }
}

/// @nodoc
mixin _$QualificationTest {
  String get id;
  List<Question> get questions;
  @JsonKey(defaultValue: '')
  String get title;
  @JsonKey(defaultValue: '')
  String get description;
  @JsonKey(defaultValue: 0)
  int get minimumScore;
  bool get mandatory;
  @JsonKey(defaultValue: '')
  String get qualificationTestType;
  @JsonKey(defaultValue: '')
  String get qualificationTestStatus;
  bool get hidden;
  DateTime? get lastModifiedDate;
  Map<String, String> get answersMap;

  /// Create a copy of QualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QualificationTestCopyWith<QualificationTest> get copyWith =>
      _$QualificationTestCopyWithImpl<QualificationTest>(this as QualificationTest, _$identity);

  /// Serializes this QualificationTest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QualificationTest &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other.questions, questions) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.qualificationTestType, qualificationTestType) ||
                other.qualificationTestType == qualificationTestType) &&
            (identical(other.qualificationTestStatus, qualificationTestStatus) ||
                other.qualificationTestStatus == qualificationTestStatus) &&
            (identical(other.hidden, hidden) || other.hidden == hidden) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            const DeepCollectionEquality().equals(other.answersMap, answersMap));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      const DeepCollectionEquality().hash(questions),
      title,
      description,
      minimumScore,
      mandatory,
      qualificationTestType,
      qualificationTestStatus,
      hidden,
      lastModifiedDate,
      const DeepCollectionEquality().hash(answersMap));

  @override
  String toString() {
    return 'QualificationTest(id: $id, questions: $questions, title: $title, description: $description, minimumScore: $minimumScore, mandatory: $mandatory, qualificationTestType: $qualificationTestType, qualificationTestStatus: $qualificationTestStatus, hidden: $hidden, lastModifiedDate: $lastModifiedDate, answersMap: $answersMap)';
  }
}

/// @nodoc
abstract mixin class $QualificationTestCopyWith<$Res> {
  factory $QualificationTestCopyWith(
          QualificationTest value, $Res Function(QualificationTest) _then) =
      _$QualificationTestCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      List<Question> questions,
      @JsonKey(defaultValue: '') String title,
      @JsonKey(defaultValue: '') String description,
      @JsonKey(defaultValue: 0) int minimumScore,
      bool mandatory,
      @JsonKey(defaultValue: '') String qualificationTestType,
      @JsonKey(defaultValue: '') String qualificationTestStatus,
      bool hidden,
      DateTime? lastModifiedDate,
      Map<String, String> answersMap});
}

/// @nodoc
class _$QualificationTestCopyWithImpl<$Res> implements $QualificationTestCopyWith<$Res> {
  _$QualificationTestCopyWithImpl(this._self, this._then);

  final QualificationTest _self;
  final $Res Function(QualificationTest) _then;

  /// Create a copy of QualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? questions = null,
    Object? title = null,
    Object? description = null,
    Object? minimumScore = null,
    Object? mandatory = null,
    Object? qualificationTestType = null,
    Object? qualificationTestStatus = null,
    Object? hidden = null,
    Object? lastModifiedDate = freezed,
    Object? answersMap = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _self.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      minimumScore: null == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int,
      mandatory: null == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool,
      qualificationTestType: null == qualificationTestType
          ? _self.qualificationTestType
          : qualificationTestType // ignore: cast_nullable_to_non_nullable
              as String,
      qualificationTestStatus: null == qualificationTestStatus
          ? _self.qualificationTestStatus
          : qualificationTestStatus // ignore: cast_nullable_to_non_nullable
              as String,
      hidden: null == hidden
          ? _self.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      answersMap: null == answersMap
          ? _self.answersMap
          : answersMap // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QualificationTest implements QualificationTest {
  const _QualificationTest(
      {required this.id,
      required final List<Question> questions,
      @JsonKey(defaultValue: '') required this.title,
      @JsonKey(defaultValue: '') required this.description,
      @JsonKey(defaultValue: 0) required this.minimumScore,
      required this.mandatory,
      @JsonKey(defaultValue: '') required this.qualificationTestType,
      @JsonKey(defaultValue: '') required this.qualificationTestStatus,
      required this.hidden,
      required this.lastModifiedDate,
      required final Map<String, String> answersMap})
      : _questions = questions,
        _answersMap = answersMap;
  factory _QualificationTest.fromJson(Map<String, dynamic> json) =>
      _$QualificationTestFromJson(json);

  @override
  final String id;
  final List<Question> _questions;
  @override
  List<Question> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  @override
  @JsonKey(defaultValue: '')
  final String title;
  @override
  @JsonKey(defaultValue: '')
  final String description;
  @override
  @JsonKey(defaultValue: 0)
  final int minimumScore;
  @override
  final bool mandatory;
  @override
  @JsonKey(defaultValue: '')
  final String qualificationTestType;
  @override
  @JsonKey(defaultValue: '')
  final String qualificationTestStatus;
  @override
  final bool hidden;
  @override
  final DateTime? lastModifiedDate;
  final Map<String, String> _answersMap;
  @override
  Map<String, String> get answersMap {
    if (_answersMap is EqualUnmodifiableMapView) return _answersMap;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_answersMap);
  }

  /// Create a copy of QualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QualificationTestCopyWith<_QualificationTest> get copyWith =>
      __$QualificationTestCopyWithImpl<_QualificationTest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QualificationTestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QualificationTest &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._questions, _questions) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.qualificationTestType, qualificationTestType) ||
                other.qualificationTestType == qualificationTestType) &&
            (identical(other.qualificationTestStatus, qualificationTestStatus) ||
                other.qualificationTestStatus == qualificationTestStatus) &&
            (identical(other.hidden, hidden) || other.hidden == hidden) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            const DeepCollectionEquality().equals(other._answersMap, _answersMap));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      const DeepCollectionEquality().hash(_questions),
      title,
      description,
      minimumScore,
      mandatory,
      qualificationTestType,
      qualificationTestStatus,
      hidden,
      lastModifiedDate,
      const DeepCollectionEquality().hash(_answersMap));

  @override
  String toString() {
    return 'QualificationTest(id: $id, questions: $questions, title: $title, description: $description, minimumScore: $minimumScore, mandatory: $mandatory, qualificationTestType: $qualificationTestType, qualificationTestStatus: $qualificationTestStatus, hidden: $hidden, lastModifiedDate: $lastModifiedDate, answersMap: $answersMap)';
  }
}

/// @nodoc
abstract mixin class _$QualificationTestCopyWith<$Res> implements $QualificationTestCopyWith<$Res> {
  factory _$QualificationTestCopyWith(
          _QualificationTest value, $Res Function(_QualificationTest) _then) =
      __$QualificationTestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      List<Question> questions,
      @JsonKey(defaultValue: '') String title,
      @JsonKey(defaultValue: '') String description,
      @JsonKey(defaultValue: 0) int minimumScore,
      bool mandatory,
      @JsonKey(defaultValue: '') String qualificationTestType,
      @JsonKey(defaultValue: '') String qualificationTestStatus,
      bool hidden,
      DateTime? lastModifiedDate,
      Map<String, String> answersMap});
}

/// @nodoc
class __$QualificationTestCopyWithImpl<$Res> implements _$QualificationTestCopyWith<$Res> {
  __$QualificationTestCopyWithImpl(this._self, this._then);

  final _QualificationTest _self;
  final $Res Function(_QualificationTest) _then;

  /// Create a copy of QualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? questions = null,
    Object? title = null,
    Object? description = null,
    Object? minimumScore = null,
    Object? mandatory = null,
    Object? qualificationTestType = null,
    Object? qualificationTestStatus = null,
    Object? hidden = null,
    Object? lastModifiedDate = freezed,
    Object? answersMap = null,
  }) {
    return _then(_QualificationTest(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      questions: null == questions
          ? _self._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      minimumScore: null == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int,
      mandatory: null == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool,
      qualificationTestType: null == qualificationTestType
          ? _self.qualificationTestType
          : qualificationTestType // ignore: cast_nullable_to_non_nullable
              as String,
      qualificationTestStatus: null == qualificationTestStatus
          ? _self.qualificationTestStatus
          : qualificationTestStatus // ignore: cast_nullable_to_non_nullable
              as String,
      hidden: null == hidden
          ? _self.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      answersMap: null == answersMap
          ? _self._answersMap
          : answersMap // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

/// @nodoc
mixin _$Answer {
  String? get id;
  String? get answer;
  bool? get correct;
  int? get index;

  /// Create a copy of Answer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AnswerCopyWith<Answer> get copyWith => _$AnswerCopyWithImpl<Answer>(this as Answer, _$identity);

  /// Serializes this Answer to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Answer &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.correct, correct) || other.correct == correct) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, answer, correct, index);

  @override
  String toString() {
    return 'Answer(id: $id, answer: $answer, correct: $correct, index: $index)';
  }
}

/// @nodoc
abstract mixin class $AnswerCopyWith<$Res> {
  factory $AnswerCopyWith(Answer value, $Res Function(Answer) _then) = _$AnswerCopyWithImpl;
  @useResult
  $Res call({String? id, String? answer, bool? correct, int? index});
}

/// @nodoc
class _$AnswerCopyWithImpl<$Res> implements $AnswerCopyWith<$Res> {
  _$AnswerCopyWithImpl(this._self, this._then);

  final Answer _self;
  final $Res Function(Answer) _then;

  /// Create a copy of Answer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? answer = freezed,
    Object? correct = freezed,
    Object? index = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      answer: freezed == answer
          ? _self.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Answer implements Answer {
  const _Answer(
      {required this.id, required this.answer, required this.correct, required this.index});
  factory _Answer.fromJson(Map<String, dynamic> json) => _$AnswerFromJson(json);

  @override
  final String? id;
  @override
  final String? answer;
  @override
  final bool? correct;
  @override
  final int? index;

  /// Create a copy of Answer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AnswerCopyWith<_Answer> get copyWith => __$AnswerCopyWithImpl<_Answer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AnswerToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Answer &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.correct, correct) || other.correct == correct) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, answer, correct, index);

  @override
  String toString() {
    return 'Answer(id: $id, answer: $answer, correct: $correct, index: $index)';
  }
}

/// @nodoc
abstract mixin class _$AnswerCopyWith<$Res> implements $AnswerCopyWith<$Res> {
  factory _$AnswerCopyWith(_Answer value, $Res Function(_Answer) _then) = __$AnswerCopyWithImpl;
  @override
  @useResult
  $Res call({String? id, String? answer, bool? correct, int? index});
}

/// @nodoc
class __$AnswerCopyWithImpl<$Res> implements _$AnswerCopyWith<$Res> {
  __$AnswerCopyWithImpl(this._self, this._then);

  final _Answer _self;
  final $Res Function(_Answer) _then;

  /// Create a copy of Answer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? answer = freezed,
    Object? correct = freezed,
    Object? index = freezed,
  }) {
    return _then(_Answer(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      answer: freezed == answer
          ? _self.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$Sector {
  @JsonKey(defaultValue: '')
  String get id;
  @JsonKey(defaultValue: '')
  String get title;

  /// Create a copy of Sector
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SectorCopyWith<Sector> get copyWith => _$SectorCopyWithImpl<Sector>(this as Sector, _$identity);

  /// Serializes this Sector to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Sector &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title);

  @override
  String toString() {
    return 'Sector(id: $id, title: $title)';
  }
}

/// @nodoc
abstract mixin class $SectorCopyWith<$Res> {
  factory $SectorCopyWith(Sector value, $Res Function(Sector) _then) = _$SectorCopyWithImpl;
  @useResult
  $Res call({@JsonKey(defaultValue: '') String id, @JsonKey(defaultValue: '') String title});
}

/// @nodoc
class _$SectorCopyWithImpl<$Res> implements $SectorCopyWith<$Res> {
  _$SectorCopyWithImpl(this._self, this._then);

  final Sector _self;
  final $Res Function(Sector) _then;

  /// Create a copy of Sector
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Sector implements Sector {
  const _Sector(
      {@JsonKey(defaultValue: '') required this.id,
      @JsonKey(defaultValue: '') required this.title});
  factory _Sector.fromJson(Map<String, dynamic> json) => _$SectorFromJson(json);

  @override
  @JsonKey(defaultValue: '')
  final String id;
  @override
  @JsonKey(defaultValue: '')
  final String title;

  /// Create a copy of Sector
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SectorCopyWith<_Sector> get copyWith => __$SectorCopyWithImpl<_Sector>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SectorToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Sector &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title);

  @override
  String toString() {
    return 'Sector(id: $id, title: $title)';
  }
}

/// @nodoc
abstract mixin class _$SectorCopyWith<$Res> implements $SectorCopyWith<$Res> {
  factory _$SectorCopyWith(_Sector value, $Res Function(_Sector) _then) = __$SectorCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(defaultValue: '') String id, @JsonKey(defaultValue: '') String title});
}

/// @nodoc
class __$SectorCopyWithImpl<$Res> implements _$SectorCopyWith<$Res> {
  __$SectorCopyWithImpl(this._self, this._then);

  final _Sector _self;
  final $Res Function(_Sector) _then;

  /// Create a copy of Sector
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_Sector(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$Domain {
  @JsonKey(defaultValue: '')
  String get id;
  @JsonKey(defaultValue: '')
  String get title;

  /// Create a copy of Domain
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DomainCopyWith<Domain> get copyWith => _$DomainCopyWithImpl<Domain>(this as Domain, _$identity);

  /// Serializes this Domain to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Domain &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title);

  @override
  String toString() {
    return 'Domain(id: $id, title: $title)';
  }
}

/// @nodoc
abstract mixin class $DomainCopyWith<$Res> {
  factory $DomainCopyWith(Domain value, $Res Function(Domain) _then) = _$DomainCopyWithImpl;
  @useResult
  $Res call({@JsonKey(defaultValue: '') String id, @JsonKey(defaultValue: '') String title});
}

/// @nodoc
class _$DomainCopyWithImpl<$Res> implements $DomainCopyWith<$Res> {
  _$DomainCopyWithImpl(this._self, this._then);

  final Domain _self;
  final $Res Function(Domain) _then;

  /// Create a copy of Domain
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Domain implements Domain {
  const _Domain(
      {@JsonKey(defaultValue: '') required this.id,
      @JsonKey(defaultValue: '') required this.title});
  factory _Domain.fromJson(Map<String, dynamic> json) => _$DomainFromJson(json);

  @override
  @JsonKey(defaultValue: '')
  final String id;
  @override
  @JsonKey(defaultValue: '')
  final String title;

  /// Create a copy of Domain
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DomainCopyWith<_Domain> get copyWith => __$DomainCopyWithImpl<_Domain>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DomainToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Domain &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title);

  @override
  String toString() {
    return 'Domain(id: $id, title: $title)';
  }
}

/// @nodoc
abstract mixin class _$DomainCopyWith<$Res> implements $DomainCopyWith<$Res> {
  factory _$DomainCopyWith(_Domain value, $Res Function(_Domain) _then) = __$DomainCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(defaultValue: '') String id, @JsonKey(defaultValue: '') String title});
}

/// @nodoc
class __$DomainCopyWithImpl<$Res> implements _$DomainCopyWith<$Res> {
  __$DomainCopyWithImpl(this._self, this._then);

  final _Domain _self;
  final $Res Function(_Domain) _then;

  /// Create a copy of Domain
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_Domain(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$PromoVideoModel {
  String get originalFilename;
  String get key;
  int get size;

  /// Create a copy of PromoVideoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PromoVideoModelCopyWith<PromoVideoModel> get copyWith =>
      _$PromoVideoModelCopyWithImpl<PromoVideoModel>(this as PromoVideoModel, _$identity);

  /// Serializes this PromoVideoModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PromoVideoModel &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'PromoVideoModel(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class $PromoVideoModelCopyWith<$Res> {
  factory $PromoVideoModelCopyWith(PromoVideoModel value, $Res Function(PromoVideoModel) _then) =
      _$PromoVideoModelCopyWithImpl;
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class _$PromoVideoModelCopyWithImpl<$Res> implements $PromoVideoModelCopyWith<$Res> {
  _$PromoVideoModelCopyWithImpl(this._self, this._then);

  final PromoVideoModel _self;
  final $Res Function(PromoVideoModel) _then;

  /// Create a copy of PromoVideoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_self.copyWith(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PromoVideoModel implements PromoVideoModel {
  const _PromoVideoModel({required this.originalFilename, required this.key, required this.size});
  factory _PromoVideoModel.fromJson(Map<String, dynamic> json) => _$PromoVideoModelFromJson(json);

  @override
  final String originalFilename;
  @override
  final String key;
  @override
  final int size;

  /// Create a copy of PromoVideoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PromoVideoModelCopyWith<_PromoVideoModel> get copyWith =>
      __$PromoVideoModelCopyWithImpl<_PromoVideoModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PromoVideoModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PromoVideoModel &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'PromoVideoModel(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class _$PromoVideoModelCopyWith<$Res> implements $PromoVideoModelCopyWith<$Res> {
  factory _$PromoVideoModelCopyWith(_PromoVideoModel value, $Res Function(_PromoVideoModel) _then) =
      __$PromoVideoModelCopyWithImpl;
  @override
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class __$PromoVideoModelCopyWithImpl<$Res> implements _$PromoVideoModelCopyWith<$Res> {
  __$PromoVideoModelCopyWithImpl(this._self, this._then);

  final _PromoVideoModel _self;
  final $Res Function(_PromoVideoModel) _then;

  /// Create a copy of PromoVideoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_PromoVideoModel(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$SeatingCapacity {
  String get seatingCapacityId;
  @JsonKey(fromJson: stringToDtoStatus)
  DtoStatus get dtoStatus;
  @JsonKey(defaultValue: 0)
  int get maxNumberOfEnrollments;
  bool get noLimits;
  @JsonKey(defaultValue: 0)
  int get actualNumberOfEnrollments;
  DateTime get endDate;
  DateTime get lastModifiedDate;

  /// Create a copy of SeatingCapacity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SeatingCapacityCopyWith<SeatingCapacity> get copyWith =>
      _$SeatingCapacityCopyWithImpl<SeatingCapacity>(this as SeatingCapacity, _$identity);

  /// Serializes this SeatingCapacity to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SeatingCapacity &&
            (identical(other.seatingCapacityId, seatingCapacityId) ||
                other.seatingCapacityId == seatingCapacityId) &&
            (identical(other.dtoStatus, dtoStatus) || other.dtoStatus == dtoStatus) &&
            (identical(other.maxNumberOfEnrollments, maxNumberOfEnrollments) ||
                other.maxNumberOfEnrollments == maxNumberOfEnrollments) &&
            (identical(other.noLimits, noLimits) || other.noLimits == noLimits) &&
            (identical(other.actualNumberOfEnrollments, actualNumberOfEnrollments) ||
                other.actualNumberOfEnrollments == actualNumberOfEnrollments) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, seatingCapacityId, dtoStatus, maxNumberOfEnrollments,
      noLimits, actualNumberOfEnrollments, endDate, lastModifiedDate);

  @override
  String toString() {
    return 'SeatingCapacity(seatingCapacityId: $seatingCapacityId, dtoStatus: $dtoStatus, maxNumberOfEnrollments: $maxNumberOfEnrollments, noLimits: $noLimits, actualNumberOfEnrollments: $actualNumberOfEnrollments, endDate: $endDate, lastModifiedDate: $lastModifiedDate)';
  }
}

/// @nodoc
abstract mixin class $SeatingCapacityCopyWith<$Res> {
  factory $SeatingCapacityCopyWith(SeatingCapacity value, $Res Function(SeatingCapacity) _then) =
      _$SeatingCapacityCopyWithImpl;
  @useResult
  $Res call(
      {String seatingCapacityId,
      @JsonKey(fromJson: stringToDtoStatus) DtoStatus dtoStatus,
      @JsonKey(defaultValue: 0) int maxNumberOfEnrollments,
      bool noLimits,
      @JsonKey(defaultValue: 0) int actualNumberOfEnrollments,
      DateTime endDate,
      DateTime lastModifiedDate});
}

/// @nodoc
class _$SeatingCapacityCopyWithImpl<$Res> implements $SeatingCapacityCopyWith<$Res> {
  _$SeatingCapacityCopyWithImpl(this._self, this._then);

  final SeatingCapacity _self;
  final $Res Function(SeatingCapacity) _then;

  /// Create a copy of SeatingCapacity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seatingCapacityId = null,
    Object? dtoStatus = null,
    Object? maxNumberOfEnrollments = null,
    Object? noLimits = null,
    Object? actualNumberOfEnrollments = null,
    Object? endDate = null,
    Object? lastModifiedDate = null,
  }) {
    return _then(_self.copyWith(
      seatingCapacityId: null == seatingCapacityId
          ? _self.seatingCapacityId
          : seatingCapacityId // ignore: cast_nullable_to_non_nullable
              as String,
      dtoStatus: null == dtoStatus
          ? _self.dtoStatus
          : dtoStatus // ignore: cast_nullable_to_non_nullable
              as DtoStatus,
      maxNumberOfEnrollments: null == maxNumberOfEnrollments
          ? _self.maxNumberOfEnrollments
          : maxNumberOfEnrollments // ignore: cast_nullable_to_non_nullable
              as int,
      noLimits: null == noLimits
          ? _self.noLimits
          : noLimits // ignore: cast_nullable_to_non_nullable
              as bool,
      actualNumberOfEnrollments: null == actualNumberOfEnrollments
          ? _self.actualNumberOfEnrollments
          : actualNumberOfEnrollments // ignore: cast_nullable_to_non_nullable
              as int,
      endDate: null == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastModifiedDate: null == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SeatingCapacity implements SeatingCapacity {
  const _SeatingCapacity(
      {required this.seatingCapacityId,
      @JsonKey(fromJson: stringToDtoStatus) required this.dtoStatus,
      @JsonKey(defaultValue: 0) required this.maxNumberOfEnrollments,
      required this.noLimits,
      @JsonKey(defaultValue: 0) required this.actualNumberOfEnrollments,
      required this.endDate,
      required this.lastModifiedDate});
  factory _SeatingCapacity.fromJson(Map<String, dynamic> json) => _$SeatingCapacityFromJson(json);

  @override
  final String seatingCapacityId;
  @override
  @JsonKey(fromJson: stringToDtoStatus)
  final DtoStatus dtoStatus;
  @override
  @JsonKey(defaultValue: 0)
  final int maxNumberOfEnrollments;
  @override
  final bool noLimits;
  @override
  @JsonKey(defaultValue: 0)
  final int actualNumberOfEnrollments;
  @override
  final DateTime endDate;
  @override
  final DateTime lastModifiedDate;

  /// Create a copy of SeatingCapacity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SeatingCapacityCopyWith<_SeatingCapacity> get copyWith =>
      __$SeatingCapacityCopyWithImpl<_SeatingCapacity>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SeatingCapacityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SeatingCapacity &&
            (identical(other.seatingCapacityId, seatingCapacityId) ||
                other.seatingCapacityId == seatingCapacityId) &&
            (identical(other.dtoStatus, dtoStatus) || other.dtoStatus == dtoStatus) &&
            (identical(other.maxNumberOfEnrollments, maxNumberOfEnrollments) ||
                other.maxNumberOfEnrollments == maxNumberOfEnrollments) &&
            (identical(other.noLimits, noLimits) || other.noLimits == noLimits) &&
            (identical(other.actualNumberOfEnrollments, actualNumberOfEnrollments) ||
                other.actualNumberOfEnrollments == actualNumberOfEnrollments) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, seatingCapacityId, dtoStatus, maxNumberOfEnrollments,
      noLimits, actualNumberOfEnrollments, endDate, lastModifiedDate);

  @override
  String toString() {
    return 'SeatingCapacity(seatingCapacityId: $seatingCapacityId, dtoStatus: $dtoStatus, maxNumberOfEnrollments: $maxNumberOfEnrollments, noLimits: $noLimits, actualNumberOfEnrollments: $actualNumberOfEnrollments, endDate: $endDate, lastModifiedDate: $lastModifiedDate)';
  }
}

/// @nodoc
abstract mixin class _$SeatingCapacityCopyWith<$Res> implements $SeatingCapacityCopyWith<$Res> {
  factory _$SeatingCapacityCopyWith(_SeatingCapacity value, $Res Function(_SeatingCapacity) _then) =
      __$SeatingCapacityCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String seatingCapacityId,
      @JsonKey(fromJson: stringToDtoStatus) DtoStatus dtoStatus,
      @JsonKey(defaultValue: 0) int maxNumberOfEnrollments,
      bool noLimits,
      @JsonKey(defaultValue: 0) int actualNumberOfEnrollments,
      DateTime endDate,
      DateTime lastModifiedDate});
}

/// @nodoc
class __$SeatingCapacityCopyWithImpl<$Res> implements _$SeatingCapacityCopyWith<$Res> {
  __$SeatingCapacityCopyWithImpl(this._self, this._then);

  final _SeatingCapacity _self;
  final $Res Function(_SeatingCapacity) _then;

  /// Create a copy of SeatingCapacity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? seatingCapacityId = null,
    Object? dtoStatus = null,
    Object? maxNumberOfEnrollments = null,
    Object? noLimits = null,
    Object? actualNumberOfEnrollments = null,
    Object? endDate = null,
    Object? lastModifiedDate = null,
  }) {
    return _then(_SeatingCapacity(
      seatingCapacityId: null == seatingCapacityId
          ? _self.seatingCapacityId
          : seatingCapacityId // ignore: cast_nullable_to_non_nullable
              as String,
      dtoStatus: null == dtoStatus
          ? _self.dtoStatus
          : dtoStatus // ignore: cast_nullable_to_non_nullable
              as DtoStatus,
      maxNumberOfEnrollments: null == maxNumberOfEnrollments
          ? _self.maxNumberOfEnrollments
          : maxNumberOfEnrollments // ignore: cast_nullable_to_non_nullable
              as int,
      noLimits: null == noLimits
          ? _self.noLimits
          : noLimits // ignore: cast_nullable_to_non_nullable
              as bool,
      actualNumberOfEnrollments: null == actualNumberOfEnrollments
          ? _self.actualNumberOfEnrollments
          : actualNumberOfEnrollments // ignore: cast_nullable_to_non_nullable
              as int,
      endDate: null == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastModifiedDate: null == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$Region {
  String get id;
  String get regionPlaceId;
  String get regionNameEn;
  String get regionNameAr;

  /// Create a copy of Region
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RegionCopyWith<Region> get copyWith => _$RegionCopyWithImpl<Region>(this as Region, _$identity);

  /// Serializes this Region to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Region &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.regionPlaceId, regionPlaceId) ||
                other.regionPlaceId == regionPlaceId) &&
            (identical(other.regionNameEn, regionNameEn) || other.regionNameEn == regionNameEn) &&
            (identical(other.regionNameAr, regionNameAr) || other.regionNameAr == regionNameAr));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, regionPlaceId, regionNameEn, regionNameAr);

  @override
  String toString() {
    return 'Region(id: $id, regionPlaceId: $regionPlaceId, regionNameEn: $regionNameEn, regionNameAr: $regionNameAr)';
  }
}

/// @nodoc
abstract mixin class $RegionCopyWith<$Res> {
  factory $RegionCopyWith(Region value, $Res Function(Region) _then) = _$RegionCopyWithImpl;
  @useResult
  $Res call({String id, String regionPlaceId, String regionNameEn, String regionNameAr});
}

/// @nodoc
class _$RegionCopyWithImpl<$Res> implements $RegionCopyWith<$Res> {
  _$RegionCopyWithImpl(this._self, this._then);

  final Region _self;
  final $Res Function(Region) _then;

  /// Create a copy of Region
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? regionPlaceId = null,
    Object? regionNameEn = null,
    Object? regionNameAr = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      regionPlaceId: null == regionPlaceId
          ? _self.regionPlaceId
          : regionPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      regionNameEn: null == regionNameEn
          ? _self.regionNameEn
          : regionNameEn // ignore: cast_nullable_to_non_nullable
              as String,
      regionNameAr: null == regionNameAr
          ? _self.regionNameAr
          : regionNameAr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Region implements Region {
  const _Region(
      {required this.id,
      required this.regionPlaceId,
      required this.regionNameEn,
      required this.regionNameAr});
  factory _Region.fromJson(Map<String, dynamic> json) => _$RegionFromJson(json);

  @override
  final String id;
  @override
  final String regionPlaceId;
  @override
  final String regionNameEn;
  @override
  final String regionNameAr;

  /// Create a copy of Region
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RegionCopyWith<_Region> get copyWith => __$RegionCopyWithImpl<_Region>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RegionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Region &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.regionPlaceId, regionPlaceId) ||
                other.regionPlaceId == regionPlaceId) &&
            (identical(other.regionNameEn, regionNameEn) || other.regionNameEn == regionNameEn) &&
            (identical(other.regionNameAr, regionNameAr) || other.regionNameAr == regionNameAr));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, regionPlaceId, regionNameEn, regionNameAr);

  @override
  String toString() {
    return 'Region(id: $id, regionPlaceId: $regionPlaceId, regionNameEn: $regionNameEn, regionNameAr: $regionNameAr)';
  }
}

/// @nodoc
abstract mixin class _$RegionCopyWith<$Res> implements $RegionCopyWith<$Res> {
  factory _$RegionCopyWith(_Region value, $Res Function(_Region) _then) = __$RegionCopyWithImpl;
  @override
  @useResult
  $Res call({String id, String regionPlaceId, String regionNameEn, String regionNameAr});
}

/// @nodoc
class __$RegionCopyWithImpl<$Res> implements _$RegionCopyWith<$Res> {
  __$RegionCopyWithImpl(this._self, this._then);

  final _Region _self;
  final $Res Function(_Region) _then;

  /// Create a copy of Region
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? regionPlaceId = null,
    Object? regionNameEn = null,
    Object? regionNameAr = null,
  }) {
    return _then(_Region(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      regionPlaceId: null == regionPlaceId
          ? _self.regionPlaceId
          : regionPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      regionNameEn: null == regionNameEn
          ? _self.regionNameEn
          : regionNameEn // ignore: cast_nullable_to_non_nullable
              as String,
      regionNameAr: null == regionNameAr
          ? _self.regionNameAr
          : regionNameAr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$City {
  String get id;
  String get cityPlaceId;
  String? get cityNameEn;
  String? get cityNameAr;
  Region? get region;

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CityCopyWith<City> get copyWith => _$CityCopyWithImpl<City>(this as City, _$identity);

  /// Serializes this City to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is City &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityPlaceId, cityPlaceId) || other.cityPlaceId == cityPlaceId) &&
            (identical(other.cityNameEn, cityNameEn) || other.cityNameEn == cityNameEn) &&
            (identical(other.cityNameAr, cityNameAr) || other.cityNameAr == cityNameAr) &&
            (identical(other.region, region) || other.region == region));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, cityPlaceId, cityNameEn, cityNameAr, region);

  @override
  String toString() {
    return 'City(id: $id, cityPlaceId: $cityPlaceId, cityNameEn: $cityNameEn, cityNameAr: $cityNameAr, region: $region)';
  }
}

/// @nodoc
abstract mixin class $CityCopyWith<$Res> {
  factory $CityCopyWith(City value, $Res Function(City) _then) = _$CityCopyWithImpl;
  @useResult
  $Res call(
      {String id, String cityPlaceId, String? cityNameEn, String? cityNameAr, Region? region});

  $RegionCopyWith<$Res>? get region;
}

/// @nodoc
class _$CityCopyWithImpl<$Res> implements $CityCopyWith<$Res> {
  _$CityCopyWithImpl(this._self, this._then);

  final City _self;
  final $Res Function(City) _then;

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cityPlaceId = null,
    Object? cityNameEn = freezed,
    Object? cityNameAr = freezed,
    Object? region = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityPlaceId: null == cityPlaceId
          ? _self.cityPlaceId
          : cityPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      cityNameEn: freezed == cityNameEn
          ? _self.cityNameEn
          : cityNameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      cityNameAr: freezed == cityNameAr
          ? _self.cityNameAr
          : cityNameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _self.region
          : region // ignore: cast_nullable_to_non_nullable
              as Region?,
    ));
  }

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RegionCopyWith<$Res>? get region {
    if (_self.region == null) {
      return null;
    }

    return $RegionCopyWith<$Res>(_self.region!, (value) {
      return _then(_self.copyWith(region: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _City implements City {
  const _City(
      {required this.id,
      required this.cityPlaceId,
      required this.cityNameEn,
      required this.cityNameAr,
      required this.region});
  factory _City.fromJson(Map<String, dynamic> json) => _$CityFromJson(json);

  @override
  final String id;
  @override
  final String cityPlaceId;
  @override
  final String? cityNameEn;
  @override
  final String? cityNameAr;
  @override
  final Region? region;

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CityCopyWith<_City> get copyWith => __$CityCopyWithImpl<_City>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CityToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _City &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityPlaceId, cityPlaceId) || other.cityPlaceId == cityPlaceId) &&
            (identical(other.cityNameEn, cityNameEn) || other.cityNameEn == cityNameEn) &&
            (identical(other.cityNameAr, cityNameAr) || other.cityNameAr == cityNameAr) &&
            (identical(other.region, region) || other.region == region));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, cityPlaceId, cityNameEn, cityNameAr, region);

  @override
  String toString() {
    return 'City(id: $id, cityPlaceId: $cityPlaceId, cityNameEn: $cityNameEn, cityNameAr: $cityNameAr, region: $region)';
  }
}

/// @nodoc
abstract mixin class _$CityCopyWith<$Res> implements $CityCopyWith<$Res> {
  factory _$CityCopyWith(_City value, $Res Function(_City) _then) = __$CityCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id, String cityPlaceId, String? cityNameEn, String? cityNameAr, Region? region});

  @override
  $RegionCopyWith<$Res>? get region;
}

/// @nodoc
class __$CityCopyWithImpl<$Res> implements _$CityCopyWith<$Res> {
  __$CityCopyWithImpl(this._self, this._then);

  final _City _self;
  final $Res Function(_City) _then;

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? cityPlaceId = null,
    Object? cityNameEn = freezed,
    Object? cityNameAr = freezed,
    Object? region = freezed,
  }) {
    return _then(_City(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityPlaceId: null == cityPlaceId
          ? _self.cityPlaceId
          : cityPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      cityNameEn: freezed == cityNameEn
          ? _self.cityNameEn
          : cityNameEn // ignore: cast_nullable_to_non_nullable
              as String?,
      cityNameAr: freezed == cityNameAr
          ? _self.cityNameAr
          : cityNameAr // ignore: cast_nullable_to_non_nullable
              as String?,
      region: freezed == region
          ? _self.region
          : region // ignore: cast_nullable_to_non_nullable
              as Region?,
    ));
  }

  /// Create a copy of City
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RegionCopyWith<$Res>? get region {
    if (_self.region == null) {
      return null;
    }

    return $RegionCopyWith<$Res>(_self.region!, (value) {
      return _then(_self.copyWith(region: value));
    });
  }
}

/// @nodoc
mixin _$Location {
  String get id;
  String? get addressPlaceId;
  String get addressNameEn;
  String get addressNameAr;
  double? get latitude;
  double? get longitude;
  City? get city;
  String? get comment;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationCopyWith<Location> get copyWith =>
      _$LocationCopyWithImpl<Location>(this as Location, _$identity);

  /// Serializes this Location to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Location &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.addressPlaceId, addressPlaceId) ||
                other.addressPlaceId == addressPlaceId) &&
            (identical(other.addressNameEn, addressNameEn) ||
                other.addressNameEn == addressNameEn) &&
            (identical(other.addressNameAr, addressNameAr) ||
                other.addressNameAr == addressNameAr) &&
            (identical(other.latitude, latitude) || other.latitude == latitude) &&
            (identical(other.longitude, longitude) || other.longitude == longitude) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, addressPlaceId, addressNameEn, addressNameAr,
      latitude, longitude, city, comment);

  @override
  String toString() {
    return 'Location(id: $id, addressPlaceId: $addressPlaceId, addressNameEn: $addressNameEn, addressNameAr: $addressNameAr, latitude: $latitude, longitude: $longitude, city: $city, comment: $comment)';
  }
}

/// @nodoc
abstract mixin class $LocationCopyWith<$Res> {
  factory $LocationCopyWith(Location value, $Res Function(Location) _then) = _$LocationCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? addressPlaceId,
      String addressNameEn,
      String addressNameAr,
      double? latitude,
      double? longitude,
      City? city,
      String? comment});

  $CityCopyWith<$Res>? get city;
}

/// @nodoc
class _$LocationCopyWithImpl<$Res> implements $LocationCopyWith<$Res> {
  _$LocationCopyWithImpl(this._self, this._then);

  final Location _self;
  final $Res Function(Location) _then;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? addressPlaceId = freezed,
    Object? addressNameEn = null,
    Object? addressNameAr = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? city = freezed,
    Object? comment = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      addressPlaceId: freezed == addressPlaceId
          ? _self.addressPlaceId
          : addressPlaceId // ignore: cast_nullable_to_non_nullable
              as String?,
      addressNameEn: null == addressNameEn
          ? _self.addressNameEn
          : addressNameEn // ignore: cast_nullable_to_non_nullable
              as String,
      addressNameAr: null == addressNameAr
          ? _self.addressNameAr
          : addressNameAr // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: freezed == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
      longitude: freezed == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as City?,
      comment: freezed == comment
          ? _self.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CityCopyWith<$Res>? get city {
    if (_self.city == null) {
      return null;
    }

    return $CityCopyWith<$Res>(_self.city!, (value) {
      return _then(_self.copyWith(city: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Location implements Location {
  const _Location(
      {required this.id,
      required this.addressPlaceId,
      required this.addressNameEn,
      required this.addressNameAr,
      required this.latitude,
      required this.longitude,
      required this.city,
      required this.comment});
  factory _Location.fromJson(Map<String, dynamic> json) => _$LocationFromJson(json);

  @override
  final String id;
  @override
  final String? addressPlaceId;
  @override
  final String addressNameEn;
  @override
  final String addressNameAr;
  @override
  final double? latitude;
  @override
  final double? longitude;
  @override
  final City? city;
  @override
  final String? comment;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationCopyWith<_Location> get copyWith =>
      __$LocationCopyWithImpl<_Location>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LocationToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Location &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.addressPlaceId, addressPlaceId) ||
                other.addressPlaceId == addressPlaceId) &&
            (identical(other.addressNameEn, addressNameEn) ||
                other.addressNameEn == addressNameEn) &&
            (identical(other.addressNameAr, addressNameAr) ||
                other.addressNameAr == addressNameAr) &&
            (identical(other.latitude, latitude) || other.latitude == latitude) &&
            (identical(other.longitude, longitude) || other.longitude == longitude) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, addressPlaceId, addressNameEn, addressNameAr,
      latitude, longitude, city, comment);

  @override
  String toString() {
    return 'Location(id: $id, addressPlaceId: $addressPlaceId, addressNameEn: $addressNameEn, addressNameAr: $addressNameAr, latitude: $latitude, longitude: $longitude, city: $city, comment: $comment)';
  }
}

/// @nodoc
abstract mixin class _$LocationCopyWith<$Res> implements $LocationCopyWith<$Res> {
  factory _$LocationCopyWith(_Location value, $Res Function(_Location) _then) =
      __$LocationCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? addressPlaceId,
      String addressNameEn,
      String addressNameAr,
      double? latitude,
      double? longitude,
      City? city,
      String? comment});

  @override
  $CityCopyWith<$Res>? get city;
}

/// @nodoc
class __$LocationCopyWithImpl<$Res> implements _$LocationCopyWith<$Res> {
  __$LocationCopyWithImpl(this._self, this._then);

  final _Location _self;
  final $Res Function(_Location) _then;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? addressPlaceId = freezed,
    Object? addressNameEn = null,
    Object? addressNameAr = null,
    Object? latitude = freezed,
    Object? longitude = freezed,
    Object? city = freezed,
    Object? comment = freezed,
  }) {
    return _then(_Location(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      addressPlaceId: freezed == addressPlaceId
          ? _self.addressPlaceId
          : addressPlaceId // ignore: cast_nullable_to_non_nullable
              as String?,
      addressNameEn: null == addressNameEn
          ? _self.addressNameEn
          : addressNameEn // ignore: cast_nullable_to_non_nullable
              as String,
      addressNameAr: null == addressNameAr
          ? _self.addressNameAr
          : addressNameAr // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: freezed == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double?,
      longitude: freezed == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as City?,
      comment: freezed == comment
          ? _self.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CityCopyWith<$Res>? get city {
    if (_self.city == null) {
      return null;
    }

    return $CityCopyWith<$Res>(_self.city!, (value) {
      return _then(_self.copyWith(city: value));
    });
  }
}

/// @nodoc
mixin _$StudyStream {
  String? get id;
  DateTime? get startDate;
  DateTime? get endDate;
  @JsonKey(defaultValue: 0)
  int get maxNumberOfParticipants;
  @JsonKey(defaultValue: 0)
  int get currentNumberOfParticipants;
  @JsonKey(defaultValue: false)
  bool get noLimits;
  LiveSession? get liveSession;
  String? get trainingId;
  @JsonKey(fromJson: stringToStudyStreamType)
  StudyStreamType? get type;
  String? get status;
  @JsonKey(defaultValue: false)
  bool get cancelled;
  DateTime? get closedForEnrollmentDate;
  String? get rootId;
  @JsonKey(defaultValue: [])
  List<CardNote> get cardNotes;
  Location? get location;
  String? get comment;

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $StudyStreamCopyWith<StudyStream> get copyWith =>
      _$StudyStreamCopyWithImpl<StudyStream>(this as StudyStream, _$identity);

  /// Serializes this StudyStream to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is StudyStream &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.maxNumberOfParticipants, maxNumberOfParticipants) ||
                other.maxNumberOfParticipants == maxNumberOfParticipants) &&
            (identical(other.currentNumberOfParticipants, currentNumberOfParticipants) ||
                other.currentNumberOfParticipants == currentNumberOfParticipants) &&
            (identical(other.noLimits, noLimits) || other.noLimits == noLimits) &&
            (identical(other.liveSession, liveSession) || other.liveSession == liveSession) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.closedForEnrollmentDate, closedForEnrollmentDate) ||
                other.closedForEnrollmentDate == closedForEnrollmentDate) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            const DeepCollectionEquality().equals(other.cardNotes, cardNotes) &&
            (identical(other.location, location) || other.location == location) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      startDate,
      endDate,
      maxNumberOfParticipants,
      currentNumberOfParticipants,
      noLimits,
      liveSession,
      trainingId,
      type,
      status,
      cancelled,
      closedForEnrollmentDate,
      rootId,
      const DeepCollectionEquality().hash(cardNotes),
      location,
      comment);

  @override
  String toString() {
    return 'StudyStream(id: $id, startDate: $startDate, endDate: $endDate, maxNumberOfParticipants: $maxNumberOfParticipants, currentNumberOfParticipants: $currentNumberOfParticipants, noLimits: $noLimits, liveSession: $liveSession, trainingId: $trainingId, type: $type, status: $status, cancelled: $cancelled, closedForEnrollmentDate: $closedForEnrollmentDate, rootId: $rootId, cardNotes: $cardNotes, location: $location, comment: $comment)';
  }
}

/// @nodoc
abstract mixin class $StudyStreamCopyWith<$Res> {
  factory $StudyStreamCopyWith(StudyStream value, $Res Function(StudyStream) _then) =
      _$StudyStreamCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      DateTime? startDate,
      DateTime? endDate,
      @JsonKey(defaultValue: 0) int maxNumberOfParticipants,
      @JsonKey(defaultValue: 0) int currentNumberOfParticipants,
      @JsonKey(defaultValue: false) bool noLimits,
      LiveSession? liveSession,
      String? trainingId,
      @JsonKey(fromJson: stringToStudyStreamType) StudyStreamType? type,
      String? status,
      @JsonKey(defaultValue: false) bool cancelled,
      DateTime? closedForEnrollmentDate,
      String? rootId,
      @JsonKey(defaultValue: []) List<CardNote> cardNotes,
      Location? location,
      String? comment});

  $LiveSessionCopyWith<$Res>? get liveSession;
  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class _$StudyStreamCopyWithImpl<$Res> implements $StudyStreamCopyWith<$Res> {
  _$StudyStreamCopyWithImpl(this._self, this._then);

  final StudyStream _self;
  final $Res Function(StudyStream) _then;

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? maxNumberOfParticipants = null,
    Object? currentNumberOfParticipants = null,
    Object? noLimits = null,
    Object? liveSession = freezed,
    Object? trainingId = freezed,
    Object? type = freezed,
    Object? status = freezed,
    Object? cancelled = null,
    Object? closedForEnrollmentDate = freezed,
    Object? rootId = freezed,
    Object? cardNotes = null,
    Object? location = freezed,
    Object? comment = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxNumberOfParticipants: null == maxNumberOfParticipants
          ? _self.maxNumberOfParticipants
          : maxNumberOfParticipants // ignore: cast_nullable_to_non_nullable
              as int,
      currentNumberOfParticipants: null == currentNumberOfParticipants
          ? _self.currentNumberOfParticipants
          : currentNumberOfParticipants // ignore: cast_nullable_to_non_nullable
              as int,
      noLimits: null == noLimits
          ? _self.noLimits
          : noLimits // ignore: cast_nullable_to_non_nullable
              as bool,
      liveSession: freezed == liveSession
          ? _self.liveSession
          : liveSession // ignore: cast_nullable_to_non_nullable
              as LiveSession?,
      trainingId: freezed == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as StudyStreamType?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      closedForEnrollmentDate: freezed == closedForEnrollmentDate
          ? _self.closedForEnrollmentDate
          : closedForEnrollmentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNotes: null == cardNotes
          ? _self.cardNotes
          : cardNotes // ignore: cast_nullable_to_non_nullable
              as List<CardNote>,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      comment: freezed == comment
          ? _self.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveSessionCopyWith<$Res>? get liveSession {
    if (_self.liveSession == null) {
      return null;
    }

    return $LiveSessionCopyWith<$Res>(_self.liveSession!, (value) {
      return _then(_self.copyWith(liveSession: value));
    });
  }

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCopyWith<$Res>? get location {
    if (_self.location == null) {
      return null;
    }

    return $LocationCopyWith<$Res>(_self.location!, (value) {
      return _then(_self.copyWith(location: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _StudyStream implements StudyStream {
  const _StudyStream(
      {required this.id,
      required this.startDate,
      required this.endDate,
      @JsonKey(defaultValue: 0) required this.maxNumberOfParticipants,
      @JsonKey(defaultValue: 0) required this.currentNumberOfParticipants,
      @JsonKey(defaultValue: false) required this.noLimits,
      required this.liveSession,
      required this.trainingId,
      @JsonKey(fromJson: stringToStudyStreamType) required this.type,
      required this.status,
      @JsonKey(defaultValue: false) required this.cancelled,
      this.closedForEnrollmentDate,
      this.rootId,
      @JsonKey(defaultValue: []) required final List<CardNote> cardNotes,
      required this.location,
      required this.comment})
      : _cardNotes = cardNotes;
  factory _StudyStream.fromJson(Map<String, dynamic> json) => _$StudyStreamFromJson(json);

  @override
  final String? id;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey(defaultValue: 0)
  final int maxNumberOfParticipants;
  @override
  @JsonKey(defaultValue: 0)
  final int currentNumberOfParticipants;
  @override
  @JsonKey(defaultValue: false)
  final bool noLimits;
  @override
  final LiveSession? liveSession;
  @override
  final String? trainingId;
  @override
  @JsonKey(fromJson: stringToStudyStreamType)
  final StudyStreamType? type;
  @override
  final String? status;
  @override
  @JsonKey(defaultValue: false)
  final bool cancelled;
  @override
  final DateTime? closedForEnrollmentDate;
  @override
  final String? rootId;
  final List<CardNote> _cardNotes;
  @override
  @JsonKey(defaultValue: [])
  List<CardNote> get cardNotes {
    if (_cardNotes is EqualUnmodifiableListView) return _cardNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cardNotes);
  }

  @override
  final Location? location;
  @override
  final String? comment;

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$StudyStreamCopyWith<_StudyStream> get copyWith =>
      __$StudyStreamCopyWithImpl<_StudyStream>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$StudyStreamToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _StudyStream &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.maxNumberOfParticipants, maxNumberOfParticipants) ||
                other.maxNumberOfParticipants == maxNumberOfParticipants) &&
            (identical(other.currentNumberOfParticipants, currentNumberOfParticipants) ||
                other.currentNumberOfParticipants == currentNumberOfParticipants) &&
            (identical(other.noLimits, noLimits) || other.noLimits == noLimits) &&
            (identical(other.liveSession, liveSession) || other.liveSession == liveSession) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.closedForEnrollmentDate, closedForEnrollmentDate) ||
                other.closedForEnrollmentDate == closedForEnrollmentDate) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            const DeepCollectionEquality().equals(other._cardNotes, _cardNotes) &&
            (identical(other.location, location) || other.location == location) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      startDate,
      endDate,
      maxNumberOfParticipants,
      currentNumberOfParticipants,
      noLimits,
      liveSession,
      trainingId,
      type,
      status,
      cancelled,
      closedForEnrollmentDate,
      rootId,
      const DeepCollectionEquality().hash(_cardNotes),
      location,
      comment);

  @override
  String toString() {
    return 'StudyStream(id: $id, startDate: $startDate, endDate: $endDate, maxNumberOfParticipants: $maxNumberOfParticipants, currentNumberOfParticipants: $currentNumberOfParticipants, noLimits: $noLimits, liveSession: $liveSession, trainingId: $trainingId, type: $type, status: $status, cancelled: $cancelled, closedForEnrollmentDate: $closedForEnrollmentDate, rootId: $rootId, cardNotes: $cardNotes, location: $location, comment: $comment)';
  }
}

/// @nodoc
abstract mixin class _$StudyStreamCopyWith<$Res> implements $StudyStreamCopyWith<$Res> {
  factory _$StudyStreamCopyWith(_StudyStream value, $Res Function(_StudyStream) _then) =
      __$StudyStreamCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      DateTime? startDate,
      DateTime? endDate,
      @JsonKey(defaultValue: 0) int maxNumberOfParticipants,
      @JsonKey(defaultValue: 0) int currentNumberOfParticipants,
      @JsonKey(defaultValue: false) bool noLimits,
      LiveSession? liveSession,
      String? trainingId,
      @JsonKey(fromJson: stringToStudyStreamType) StudyStreamType? type,
      String? status,
      @JsonKey(defaultValue: false) bool cancelled,
      DateTime? closedForEnrollmentDate,
      String? rootId,
      @JsonKey(defaultValue: []) List<CardNote> cardNotes,
      Location? location,
      String? comment});

  @override
  $LiveSessionCopyWith<$Res>? get liveSession;
  @override
  $LocationCopyWith<$Res>? get location;
}

/// @nodoc
class __$StudyStreamCopyWithImpl<$Res> implements _$StudyStreamCopyWith<$Res> {
  __$StudyStreamCopyWithImpl(this._self, this._then);

  final _StudyStream _self;
  final $Res Function(_StudyStream) _then;

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? maxNumberOfParticipants = null,
    Object? currentNumberOfParticipants = null,
    Object? noLimits = null,
    Object? liveSession = freezed,
    Object? trainingId = freezed,
    Object? type = freezed,
    Object? status = freezed,
    Object? cancelled = null,
    Object? closedForEnrollmentDate = freezed,
    Object? rootId = freezed,
    Object? cardNotes = null,
    Object? location = freezed,
    Object? comment = freezed,
  }) {
    return _then(_StudyStream(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxNumberOfParticipants: null == maxNumberOfParticipants
          ? _self.maxNumberOfParticipants
          : maxNumberOfParticipants // ignore: cast_nullable_to_non_nullable
              as int,
      currentNumberOfParticipants: null == currentNumberOfParticipants
          ? _self.currentNumberOfParticipants
          : currentNumberOfParticipants // ignore: cast_nullable_to_non_nullable
              as int,
      noLimits: null == noLimits
          ? _self.noLimits
          : noLimits // ignore: cast_nullable_to_non_nullable
              as bool,
      liveSession: freezed == liveSession
          ? _self.liveSession
          : liveSession // ignore: cast_nullable_to_non_nullable
              as LiveSession?,
      trainingId: freezed == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as StudyStreamType?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      closedForEnrollmentDate: freezed == closedForEnrollmentDate
          ? _self.closedForEnrollmentDate
          : closedForEnrollmentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNotes: null == cardNotes
          ? _self._cardNotes
          : cardNotes // ignore: cast_nullable_to_non_nullable
              as List<CardNote>,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as Location?,
      comment: freezed == comment
          ? _self.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveSessionCopyWith<$Res>? get liveSession {
    if (_self.liveSession == null) {
      return null;
    }

    return $LiveSessionCopyWith<$Res>(_self.liveSession!, (value) {
      return _then(_self.copyWith(liveSession: value));
    });
  }

  /// Create a copy of StudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationCopyWith<$Res>? get location {
    if (_self.location == null) {
      return null;
    }

    return $LocationCopyWith<$Res>(_self.location!, (value) {
      return _then(_self.copyWith(location: value));
    });
  }
}

/// @nodoc
mixin _$Meeting {
  String? get id;
  String? get title;
  String? get trainingId;
  String? get liveSessionId;
  DateTime get startDate;
  int get durationHours;
  int get durationMinutes;
  String? get zoomLink;
  String? get zoomPassword;
  String? get meetingId;
  String get status;
  bool get cancelled;
  @JsonKey(includeFromJson: false, includeToJson: false)
  MeetingStatus get meetingStatus;
  String? get cancellationReason;
  DateTime? get cancellationDate;
  String? get rootId;
  List<CardNote>? get cardNotes;
  @JsonKey(name: 'section')
  List<String>? get sections;

  /// Create a copy of Meeting
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MeetingCopyWith<Meeting> get copyWith =>
      _$MeetingCopyWithImpl<Meeting>(this as Meeting, _$identity);

  /// Serializes this Meeting to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Meeting &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.liveSessionId, liveSessionId) ||
                other.liveSessionId == liveSessionId) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.durationHours, durationHours) ||
                other.durationHours == durationHours) &&
            (identical(other.durationMinutes, durationMinutes) ||
                other.durationMinutes == durationMinutes) &&
            (identical(other.zoomLink, zoomLink) || other.zoomLink == zoomLink) &&
            (identical(other.zoomPassword, zoomPassword) || other.zoomPassword == zoomPassword) &&
            (identical(other.meetingId, meetingId) || other.meetingId == meetingId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.meetingStatus, meetingStatus) ||
                other.meetingStatus == meetingStatus) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            (identical(other.cancellationDate, cancellationDate) ||
                other.cancellationDate == cancellationDate) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            const DeepCollectionEquality().equals(other.cardNotes, cardNotes) &&
            const DeepCollectionEquality().equals(other.sections, sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      trainingId,
      liveSessionId,
      startDate,
      durationHours,
      durationMinutes,
      zoomLink,
      zoomPassword,
      meetingId,
      status,
      cancelled,
      meetingStatus,
      cancellationReason,
      cancellationDate,
      rootId,
      const DeepCollectionEquality().hash(cardNotes),
      const DeepCollectionEquality().hash(sections));

  @override
  String toString() {
    return 'Meeting(id: $id, title: $title, trainingId: $trainingId, liveSessionId: $liveSessionId, startDate: $startDate, durationHours: $durationHours, durationMinutes: $durationMinutes, zoomLink: $zoomLink, zoomPassword: $zoomPassword, meetingId: $meetingId, status: $status, cancelled: $cancelled, meetingStatus: $meetingStatus, cancellationReason: $cancellationReason, cancellationDate: $cancellationDate, rootId: $rootId, cardNotes: $cardNotes, sections: $sections)';
  }
}

/// @nodoc
abstract mixin class $MeetingCopyWith<$Res> {
  factory $MeetingCopyWith(Meeting value, $Res Function(Meeting) _then) = _$MeetingCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? trainingId,
      String? liveSessionId,
      DateTime startDate,
      int durationHours,
      int durationMinutes,
      String? zoomLink,
      String? zoomPassword,
      String? meetingId,
      String status,
      bool cancelled,
      @JsonKey(includeFromJson: false, includeToJson: false) MeetingStatus meetingStatus,
      String? cancellationReason,
      DateTime? cancellationDate,
      String? rootId,
      List<CardNote>? cardNotes,
      @JsonKey(name: 'section') List<String>? sections});
}

/// @nodoc
class _$MeetingCopyWithImpl<$Res> implements $MeetingCopyWith<$Res> {
  _$MeetingCopyWithImpl(this._self, this._then);

  final Meeting _self;
  final $Res Function(Meeting) _then;

  /// Create a copy of Meeting
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? trainingId = freezed,
    Object? liveSessionId = freezed,
    Object? startDate = null,
    Object? durationHours = null,
    Object? durationMinutes = null,
    Object? zoomLink = freezed,
    Object? zoomPassword = freezed,
    Object? meetingId = freezed,
    Object? status = null,
    Object? cancelled = null,
    Object? meetingStatus = null,
    Object? cancellationReason = freezed,
    Object? cancellationDate = freezed,
    Object? rootId = freezed,
    Object? cardNotes = freezed,
    Object? sections = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingId: freezed == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String?,
      liveSessionId: freezed == liveSessionId
          ? _self.liveSessionId
          : liveSessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: null == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationHours: null == durationHours
          ? _self.durationHours
          : durationHours // ignore: cast_nullable_to_non_nullable
              as int,
      durationMinutes: null == durationMinutes
          ? _self.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      zoomLink: freezed == zoomLink
          ? _self.zoomLink
          : zoomLink // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomPassword: freezed == zoomPassword
          ? _self.zoomPassword
          : zoomPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      meetingId: freezed == meetingId
          ? _self.meetingId
          : meetingId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      meetingStatus: null == meetingStatus
          ? _self.meetingStatus
          : meetingStatus // ignore: cast_nullable_to_non_nullable
              as MeetingStatus,
      cancellationReason: freezed == cancellationReason
          ? _self.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationDate: freezed == cancellationDate
          ? _self.cancellationDate
          : cancellationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNotes: freezed == cardNotes
          ? _self.cardNotes
          : cardNotes // ignore: cast_nullable_to_non_nullable
              as List<CardNote>?,
      sections: freezed == sections
          ? _self.sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Meeting implements Meeting {
  const _Meeting(
      {required this.id,
      required this.title,
      required this.trainingId,
      required this.liveSessionId,
      required this.startDate,
      required this.durationHours,
      required this.durationMinutes,
      required this.zoomLink,
      required this.zoomPassword,
      required this.meetingId,
      required this.status,
      required this.cancelled,
      @JsonKey(includeFromJson: false, includeToJson: false)
      this.meetingStatus = MeetingStatus.Upcoming,
      this.cancellationReason,
      this.cancellationDate,
      this.rootId,
      final List<CardNote>? cardNotes,
      @JsonKey(name: 'section') final List<String>? sections})
      : _cardNotes = cardNotes,
        _sections = sections;
  factory _Meeting.fromJson(Map<String, dynamic> json) => _$MeetingFromJson(json);

  @override
  final String? id;
  @override
  final String? title;
  @override
  final String? trainingId;
  @override
  final String? liveSessionId;
  @override
  final DateTime startDate;
  @override
  final int durationHours;
  @override
  final int durationMinutes;
  @override
  final String? zoomLink;
  @override
  final String? zoomPassword;
  @override
  final String? meetingId;
  @override
  final String status;
  @override
  final bool cancelled;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final MeetingStatus meetingStatus;
  @override
  final String? cancellationReason;
  @override
  final DateTime? cancellationDate;
  @override
  final String? rootId;
  final List<CardNote>? _cardNotes;
  @override
  List<CardNote>? get cardNotes {
    final value = _cardNotes;
    if (value == null) return null;
    if (_cardNotes is EqualUnmodifiableListView) return _cardNotes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _sections;
  @override
  @JsonKey(name: 'section')
  List<String>? get sections {
    final value = _sections;
    if (value == null) return null;
    if (_sections is EqualUnmodifiableListView) return _sections;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of Meeting
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MeetingCopyWith<_Meeting> get copyWith => __$MeetingCopyWithImpl<_Meeting>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MeetingToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Meeting &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.liveSessionId, liveSessionId) ||
                other.liveSessionId == liveSessionId) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.durationHours, durationHours) ||
                other.durationHours == durationHours) &&
            (identical(other.durationMinutes, durationMinutes) ||
                other.durationMinutes == durationMinutes) &&
            (identical(other.zoomLink, zoomLink) || other.zoomLink == zoomLink) &&
            (identical(other.zoomPassword, zoomPassword) || other.zoomPassword == zoomPassword) &&
            (identical(other.meetingId, meetingId) || other.meetingId == meetingId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.meetingStatus, meetingStatus) ||
                other.meetingStatus == meetingStatus) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            (identical(other.cancellationDate, cancellationDate) ||
                other.cancellationDate == cancellationDate) &&
            (identical(other.rootId, rootId) || other.rootId == rootId) &&
            const DeepCollectionEquality().equals(other._cardNotes, _cardNotes) &&
            const DeepCollectionEquality().equals(other._sections, _sections));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      trainingId,
      liveSessionId,
      startDate,
      durationHours,
      durationMinutes,
      zoomLink,
      zoomPassword,
      meetingId,
      status,
      cancelled,
      meetingStatus,
      cancellationReason,
      cancellationDate,
      rootId,
      const DeepCollectionEquality().hash(_cardNotes),
      const DeepCollectionEquality().hash(_sections));

  @override
  String toString() {
    return 'Meeting(id: $id, title: $title, trainingId: $trainingId, liveSessionId: $liveSessionId, startDate: $startDate, durationHours: $durationHours, durationMinutes: $durationMinutes, zoomLink: $zoomLink, zoomPassword: $zoomPassword, meetingId: $meetingId, status: $status, cancelled: $cancelled, meetingStatus: $meetingStatus, cancellationReason: $cancellationReason, cancellationDate: $cancellationDate, rootId: $rootId, cardNotes: $cardNotes, sections: $sections)';
  }
}

/// @nodoc
abstract mixin class _$MeetingCopyWith<$Res> implements $MeetingCopyWith<$Res> {
  factory _$MeetingCopyWith(_Meeting value, $Res Function(_Meeting) _then) = __$MeetingCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? trainingId,
      String? liveSessionId,
      DateTime startDate,
      int durationHours,
      int durationMinutes,
      String? zoomLink,
      String? zoomPassword,
      String? meetingId,
      String status,
      bool cancelled,
      @JsonKey(includeFromJson: false, includeToJson: false) MeetingStatus meetingStatus,
      String? cancellationReason,
      DateTime? cancellationDate,
      String? rootId,
      List<CardNote>? cardNotes,
      @JsonKey(name: 'section') List<String>? sections});
}

/// @nodoc
class __$MeetingCopyWithImpl<$Res> implements _$MeetingCopyWith<$Res> {
  __$MeetingCopyWithImpl(this._self, this._then);

  final _Meeting _self;
  final $Res Function(_Meeting) _then;

  /// Create a copy of Meeting
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? trainingId = freezed,
    Object? liveSessionId = freezed,
    Object? startDate = null,
    Object? durationHours = null,
    Object? durationMinutes = null,
    Object? zoomLink = freezed,
    Object? zoomPassword = freezed,
    Object? meetingId = freezed,
    Object? status = null,
    Object? cancelled = null,
    Object? meetingStatus = null,
    Object? cancellationReason = freezed,
    Object? cancellationDate = freezed,
    Object? rootId = freezed,
    Object? cardNotes = freezed,
    Object? sections = freezed,
  }) {
    return _then(_Meeting(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingId: freezed == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String?,
      liveSessionId: freezed == liveSessionId
          ? _self.liveSessionId
          : liveSessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: null == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      durationHours: null == durationHours
          ? _self.durationHours
          : durationHours // ignore: cast_nullable_to_non_nullable
              as int,
      durationMinutes: null == durationMinutes
          ? _self.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      zoomLink: freezed == zoomLink
          ? _self.zoomLink
          : zoomLink // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomPassword: freezed == zoomPassword
          ? _self.zoomPassword
          : zoomPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      meetingId: freezed == meetingId
          ? _self.meetingId
          : meetingId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      meetingStatus: null == meetingStatus
          ? _self.meetingStatus
          : meetingStatus // ignore: cast_nullable_to_non_nullable
              as MeetingStatus,
      cancellationReason: freezed == cancellationReason
          ? _self.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationDate: freezed == cancellationDate
          ? _self.cancellationDate
          : cancellationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNotes: freezed == cardNotes
          ? _self._cardNotes
          : cardNotes // ignore: cast_nullable_to_non_nullable
              as List<CardNote>?,
      sections: freezed == sections
          ? _self._sections
          : sections // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
mixin _$CardNote {
  DateTime get createdDate;
  Map<String, LocalizedContent> get localizedContents;

  /// Create a copy of CardNote
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardNoteCopyWith<CardNote> get copyWith =>
      _$CardNoteCopyWithImpl<CardNote>(this as CardNote, _$identity);

  /// Serializes this CardNote to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardNote &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            const DeepCollectionEquality().equals(other.localizedContents, localizedContents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createdDate, const DeepCollectionEquality().hash(localizedContents));

  @override
  String toString() {
    return 'CardNote(createdDate: $createdDate, localizedContents: $localizedContents)';
  }
}

/// @nodoc
abstract mixin class $CardNoteCopyWith<$Res> {
  factory $CardNoteCopyWith(CardNote value, $Res Function(CardNote) _then) = _$CardNoteCopyWithImpl;
  @useResult
  $Res call({DateTime createdDate, Map<String, LocalizedContent> localizedContents});
}

/// @nodoc
class _$CardNoteCopyWithImpl<$Res> implements $CardNoteCopyWith<$Res> {
  _$CardNoteCopyWithImpl(this._self, this._then);

  final CardNote _self;
  final $Res Function(CardNote) _then;

  /// Create a copy of CardNote
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createdDate = null,
    Object? localizedContents = null,
  }) {
    return _then(_self.copyWith(
      createdDate: null == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      localizedContents: null == localizedContents
          ? _self.localizedContents
          : localizedContents // ignore: cast_nullable_to_non_nullable
              as Map<String, LocalizedContent>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CardNote implements CardNote {
  const _CardNote(
      {required this.createdDate, required final Map<String, LocalizedContent> localizedContents})
      : _localizedContents = localizedContents;
  factory _CardNote.fromJson(Map<String, dynamic> json) => _$CardNoteFromJson(json);

  @override
  final DateTime createdDate;
  final Map<String, LocalizedContent> _localizedContents;
  @override
  Map<String, LocalizedContent> get localizedContents {
    if (_localizedContents is EqualUnmodifiableMapView) return _localizedContents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_localizedContents);
  }

  /// Create a copy of CardNote
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardNoteCopyWith<_CardNote> get copyWith =>
      __$CardNoteCopyWithImpl<_CardNote>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardNoteToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardNote &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            const DeepCollectionEquality().equals(other._localizedContents, _localizedContents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, createdDate, const DeepCollectionEquality().hash(_localizedContents));

  @override
  String toString() {
    return 'CardNote(createdDate: $createdDate, localizedContents: $localizedContents)';
  }
}

/// @nodoc
abstract mixin class _$CardNoteCopyWith<$Res> implements $CardNoteCopyWith<$Res> {
  factory _$CardNoteCopyWith(_CardNote value, $Res Function(_CardNote) _then) =
      __$CardNoteCopyWithImpl;
  @override
  @useResult
  $Res call({DateTime createdDate, Map<String, LocalizedContent> localizedContents});
}

/// @nodoc
class __$CardNoteCopyWithImpl<$Res> implements _$CardNoteCopyWith<$Res> {
  __$CardNoteCopyWithImpl(this._self, this._then);

  final _CardNote _self;
  final $Res Function(_CardNote) _then;

  /// Create a copy of CardNote
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createdDate = null,
    Object? localizedContents = null,
  }) {
    return _then(_CardNote(
      createdDate: null == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      localizedContents: null == localizedContents
          ? _self._localizedContents
          : localizedContents // ignore: cast_nullable_to_non_nullable
              as Map<String, LocalizedContent>,
    ));
  }
}

/// @nodoc
mixin _$LocalizedContent {
  String get title;
  String get message;

  /// Create a copy of LocalizedContent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocalizedContentCopyWith<LocalizedContent> get copyWith =>
      _$LocalizedContentCopyWithImpl<LocalizedContent>(this as LocalizedContent, _$identity);

  /// Serializes this LocalizedContent to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocalizedContent &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, message);

  @override
  String toString() {
    return 'LocalizedContent(title: $title, message: $message)';
  }
}

/// @nodoc
abstract mixin class $LocalizedContentCopyWith<$Res> {
  factory $LocalizedContentCopyWith(LocalizedContent value, $Res Function(LocalizedContent) _then) =
      _$LocalizedContentCopyWithImpl;
  @useResult
  $Res call({String title, String message});
}

/// @nodoc
class _$LocalizedContentCopyWithImpl<$Res> implements $LocalizedContentCopyWith<$Res> {
  _$LocalizedContentCopyWithImpl(this._self, this._then);

  final LocalizedContent _self;
  final $Res Function(LocalizedContent) _then;

  /// Create a copy of LocalizedContent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? message = null,
  }) {
    return _then(_self.copyWith(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LocalizedContent implements LocalizedContent {
  const _LocalizedContent({required this.title, required this.message});
  factory _LocalizedContent.fromJson(Map<String, dynamic> json) => _$LocalizedContentFromJson(json);

  @override
  final String title;
  @override
  final String message;

  /// Create a copy of LocalizedContent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocalizedContentCopyWith<_LocalizedContent> get copyWith =>
      __$LocalizedContentCopyWithImpl<_LocalizedContent>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LocalizedContentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocalizedContent &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, title, message);

  @override
  String toString() {
    return 'LocalizedContent(title: $title, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$LocalizedContentCopyWith<$Res> implements $LocalizedContentCopyWith<$Res> {
  factory _$LocalizedContentCopyWith(
          _LocalizedContent value, $Res Function(_LocalizedContent) _then) =
      __$LocalizedContentCopyWithImpl;
  @override
  @useResult
  $Res call({String title, String message});
}

/// @nodoc
class __$LocalizedContentCopyWithImpl<$Res> implements _$LocalizedContentCopyWith<$Res> {
  __$LocalizedContentCopyWithImpl(this._self, this._then);

  final _LocalizedContent _self;
  final $Res Function(_LocalizedContent) _then;

  /// Create a copy of LocalizedContent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? message = null,
  }) {
    return _then(_LocalizedContent(
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$LiveSession {
  String? get id;
  DateTime? get startDate;
  int? get durationHours;
  int? get durationMinutes;
  bool? get recurringMeeting;
  String? get zoomLink;
  String? get zoomPassword;
  String? get meetingId;
  String? get status;
  int? get repeatEvery;
  DateTime? get endDate;
  int? get endAfterOccurrences;
  List<String>? get daysOfWeek;
  String? get weekOfMonth;
  int? get dayOfMonth;
  List<Meeting>? get meetings;
  String? get rootId;

  /// Create a copy of LiveSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveSessionCopyWith<LiveSession> get copyWith =>
      _$LiveSessionCopyWithImpl<LiveSession>(this as LiveSession, _$identity);

  /// Serializes this LiveSession to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveSession &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.durationHours, durationHours) ||
                other.durationHours == durationHours) &&
            (identical(other.durationMinutes, durationMinutes) ||
                other.durationMinutes == durationMinutes) &&
            (identical(other.recurringMeeting, recurringMeeting) ||
                other.recurringMeeting == recurringMeeting) &&
            (identical(other.zoomLink, zoomLink) || other.zoomLink == zoomLink) &&
            (identical(other.zoomPassword, zoomPassword) || other.zoomPassword == zoomPassword) &&
            (identical(other.meetingId, meetingId) || other.meetingId == meetingId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.repeatEvery, repeatEvery) || other.repeatEvery == repeatEvery) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.endAfterOccurrences, endAfterOccurrences) ||
                other.endAfterOccurrences == endAfterOccurrences) &&
            const DeepCollectionEquality().equals(other.daysOfWeek, daysOfWeek) &&
            (identical(other.weekOfMonth, weekOfMonth) || other.weekOfMonth == weekOfMonth) &&
            (identical(other.dayOfMonth, dayOfMonth) || other.dayOfMonth == dayOfMonth) &&
            const DeepCollectionEquality().equals(other.meetings, meetings) &&
            (identical(other.rootId, rootId) || other.rootId == rootId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      startDate,
      durationHours,
      durationMinutes,
      recurringMeeting,
      zoomLink,
      zoomPassword,
      meetingId,
      status,
      repeatEvery,
      endDate,
      endAfterOccurrences,
      const DeepCollectionEquality().hash(daysOfWeek),
      weekOfMonth,
      dayOfMonth,
      const DeepCollectionEquality().hash(meetings),
      rootId);

  @override
  String toString() {
    return 'LiveSession(id: $id, startDate: $startDate, durationHours: $durationHours, durationMinutes: $durationMinutes, recurringMeeting: $recurringMeeting, zoomLink: $zoomLink, zoomPassword: $zoomPassword, meetingId: $meetingId, status: $status, repeatEvery: $repeatEvery, endDate: $endDate, endAfterOccurrences: $endAfterOccurrences, daysOfWeek: $daysOfWeek, weekOfMonth: $weekOfMonth, dayOfMonth: $dayOfMonth, meetings: $meetings, rootId: $rootId)';
  }
}

/// @nodoc
abstract mixin class $LiveSessionCopyWith<$Res> {
  factory $LiveSessionCopyWith(LiveSession value, $Res Function(LiveSession) _then) =
      _$LiveSessionCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      DateTime? startDate,
      int? durationHours,
      int? durationMinutes,
      bool? recurringMeeting,
      String? zoomLink,
      String? zoomPassword,
      String? meetingId,
      String? status,
      int? repeatEvery,
      DateTime? endDate,
      int? endAfterOccurrences,
      List<String>? daysOfWeek,
      String? weekOfMonth,
      int? dayOfMonth,
      List<Meeting>? meetings,
      String? rootId});
}

/// @nodoc
class _$LiveSessionCopyWithImpl<$Res> implements $LiveSessionCopyWith<$Res> {
  _$LiveSessionCopyWithImpl(this._self, this._then);

  final LiveSession _self;
  final $Res Function(LiveSession) _then;

  /// Create a copy of LiveSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? startDate = freezed,
    Object? durationHours = freezed,
    Object? durationMinutes = freezed,
    Object? recurringMeeting = freezed,
    Object? zoomLink = freezed,
    Object? zoomPassword = freezed,
    Object? meetingId = freezed,
    Object? status = freezed,
    Object? repeatEvery = freezed,
    Object? endDate = freezed,
    Object? endAfterOccurrences = freezed,
    Object? daysOfWeek = freezed,
    Object? weekOfMonth = freezed,
    Object? dayOfMonth = freezed,
    Object? meetings = freezed,
    Object? rootId = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      durationHours: freezed == durationHours
          ? _self.durationHours
          : durationHours // ignore: cast_nullable_to_non_nullable
              as int?,
      durationMinutes: freezed == durationMinutes
          ? _self.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      recurringMeeting: freezed == recurringMeeting
          ? _self.recurringMeeting
          : recurringMeeting // ignore: cast_nullable_to_non_nullable
              as bool?,
      zoomLink: freezed == zoomLink
          ? _self.zoomLink
          : zoomLink // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomPassword: freezed == zoomPassword
          ? _self.zoomPassword
          : zoomPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      meetingId: freezed == meetingId
          ? _self.meetingId
          : meetingId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      repeatEvery: freezed == repeatEvery
          ? _self.repeatEvery
          : repeatEvery // ignore: cast_nullable_to_non_nullable
              as int?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endAfterOccurrences: freezed == endAfterOccurrences
          ? _self.endAfterOccurrences
          : endAfterOccurrences // ignore: cast_nullable_to_non_nullable
              as int?,
      daysOfWeek: freezed == daysOfWeek
          ? _self.daysOfWeek
          : daysOfWeek // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      weekOfMonth: freezed == weekOfMonth
          ? _self.weekOfMonth
          : weekOfMonth // ignore: cast_nullable_to_non_nullable
              as String?,
      dayOfMonth: freezed == dayOfMonth
          ? _self.dayOfMonth
          : dayOfMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      meetings: freezed == meetings
          ? _self.meetings
          : meetings // ignore: cast_nullable_to_non_nullable
              as List<Meeting>?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LiveSession implements LiveSession {
  const _LiveSession(
      {required this.id,
      required this.startDate,
      required this.durationHours,
      required this.durationMinutes,
      required this.recurringMeeting,
      required this.zoomLink,
      required this.zoomPassword,
      required this.meetingId,
      required this.status,
      this.repeatEvery,
      this.endDate,
      this.endAfterOccurrences,
      final List<String>? daysOfWeek,
      this.weekOfMonth,
      this.dayOfMonth,
      final List<Meeting>? meetings,
      this.rootId})
      : _daysOfWeek = daysOfWeek,
        _meetings = meetings;
  factory _LiveSession.fromJson(Map<String, dynamic> json) => _$LiveSessionFromJson(json);

  @override
  final String? id;
  @override
  final DateTime? startDate;
  @override
  final int? durationHours;
  @override
  final int? durationMinutes;
  @override
  final bool? recurringMeeting;
  @override
  final String? zoomLink;
  @override
  final String? zoomPassword;
  @override
  final String? meetingId;
  @override
  final String? status;
  @override
  final int? repeatEvery;
  @override
  final DateTime? endDate;
  @override
  final int? endAfterOccurrences;
  final List<String>? _daysOfWeek;
  @override
  List<String>? get daysOfWeek {
    final value = _daysOfWeek;
    if (value == null) return null;
    if (_daysOfWeek is EqualUnmodifiableListView) return _daysOfWeek;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? weekOfMonth;
  @override
  final int? dayOfMonth;
  final List<Meeting>? _meetings;
  @override
  List<Meeting>? get meetings {
    final value = _meetings;
    if (value == null) return null;
    if (_meetings is EqualUnmodifiableListView) return _meetings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? rootId;

  /// Create a copy of LiveSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveSessionCopyWith<_LiveSession> get copyWith =>
      __$LiveSessionCopyWithImpl<_LiveSession>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveSessionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveSession &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.durationHours, durationHours) ||
                other.durationHours == durationHours) &&
            (identical(other.durationMinutes, durationMinutes) ||
                other.durationMinutes == durationMinutes) &&
            (identical(other.recurringMeeting, recurringMeeting) ||
                other.recurringMeeting == recurringMeeting) &&
            (identical(other.zoomLink, zoomLink) || other.zoomLink == zoomLink) &&
            (identical(other.zoomPassword, zoomPassword) || other.zoomPassword == zoomPassword) &&
            (identical(other.meetingId, meetingId) || other.meetingId == meetingId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.repeatEvery, repeatEvery) || other.repeatEvery == repeatEvery) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.endAfterOccurrences, endAfterOccurrences) ||
                other.endAfterOccurrences == endAfterOccurrences) &&
            const DeepCollectionEquality().equals(other._daysOfWeek, _daysOfWeek) &&
            (identical(other.weekOfMonth, weekOfMonth) || other.weekOfMonth == weekOfMonth) &&
            (identical(other.dayOfMonth, dayOfMonth) || other.dayOfMonth == dayOfMonth) &&
            const DeepCollectionEquality().equals(other._meetings, _meetings) &&
            (identical(other.rootId, rootId) || other.rootId == rootId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      startDate,
      durationHours,
      durationMinutes,
      recurringMeeting,
      zoomLink,
      zoomPassword,
      meetingId,
      status,
      repeatEvery,
      endDate,
      endAfterOccurrences,
      const DeepCollectionEquality().hash(_daysOfWeek),
      weekOfMonth,
      dayOfMonth,
      const DeepCollectionEquality().hash(_meetings),
      rootId);

  @override
  String toString() {
    return 'LiveSession(id: $id, startDate: $startDate, durationHours: $durationHours, durationMinutes: $durationMinutes, recurringMeeting: $recurringMeeting, zoomLink: $zoomLink, zoomPassword: $zoomPassword, meetingId: $meetingId, status: $status, repeatEvery: $repeatEvery, endDate: $endDate, endAfterOccurrences: $endAfterOccurrences, daysOfWeek: $daysOfWeek, weekOfMonth: $weekOfMonth, dayOfMonth: $dayOfMonth, meetings: $meetings, rootId: $rootId)';
  }
}

/// @nodoc
abstract mixin class _$LiveSessionCopyWith<$Res> implements $LiveSessionCopyWith<$Res> {
  factory _$LiveSessionCopyWith(_LiveSession value, $Res Function(_LiveSession) _then) =
      __$LiveSessionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      DateTime? startDate,
      int? durationHours,
      int? durationMinutes,
      bool? recurringMeeting,
      String? zoomLink,
      String? zoomPassword,
      String? meetingId,
      String? status,
      int? repeatEvery,
      DateTime? endDate,
      int? endAfterOccurrences,
      List<String>? daysOfWeek,
      String? weekOfMonth,
      int? dayOfMonth,
      List<Meeting>? meetings,
      String? rootId});
}

/// @nodoc
class __$LiveSessionCopyWithImpl<$Res> implements _$LiveSessionCopyWith<$Res> {
  __$LiveSessionCopyWithImpl(this._self, this._then);

  final _LiveSession _self;
  final $Res Function(_LiveSession) _then;

  /// Create a copy of LiveSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? startDate = freezed,
    Object? durationHours = freezed,
    Object? durationMinutes = freezed,
    Object? recurringMeeting = freezed,
    Object? zoomLink = freezed,
    Object? zoomPassword = freezed,
    Object? meetingId = freezed,
    Object? status = freezed,
    Object? repeatEvery = freezed,
    Object? endDate = freezed,
    Object? endAfterOccurrences = freezed,
    Object? daysOfWeek = freezed,
    Object? weekOfMonth = freezed,
    Object? dayOfMonth = freezed,
    Object? meetings = freezed,
    Object? rootId = freezed,
  }) {
    return _then(_LiveSession(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      durationHours: freezed == durationHours
          ? _self.durationHours
          : durationHours // ignore: cast_nullable_to_non_nullable
              as int?,
      durationMinutes: freezed == durationMinutes
          ? _self.durationMinutes
          : durationMinutes // ignore: cast_nullable_to_non_nullable
              as int?,
      recurringMeeting: freezed == recurringMeeting
          ? _self.recurringMeeting
          : recurringMeeting // ignore: cast_nullable_to_non_nullable
              as bool?,
      zoomLink: freezed == zoomLink
          ? _self.zoomLink
          : zoomLink // ignore: cast_nullable_to_non_nullable
              as String?,
      zoomPassword: freezed == zoomPassword
          ? _self.zoomPassword
          : zoomPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      meetingId: freezed == meetingId
          ? _self.meetingId
          : meetingId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      repeatEvery: freezed == repeatEvery
          ? _self.repeatEvery
          : repeatEvery // ignore: cast_nullable_to_non_nullable
              as int?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endAfterOccurrences: freezed == endAfterOccurrences
          ? _self.endAfterOccurrences
          : endAfterOccurrences // ignore: cast_nullable_to_non_nullable
              as int?,
      daysOfWeek: freezed == daysOfWeek
          ? _self._daysOfWeek
          : daysOfWeek // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      weekOfMonth: freezed == weekOfMonth
          ? _self.weekOfMonth
          : weekOfMonth // ignore: cast_nullable_to_non_nullable
              as String?,
      dayOfMonth: freezed == dayOfMonth
          ? _self.dayOfMonth
          : dayOfMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      meetings: freezed == meetings
          ? _self._meetings
          : meetings // ignore: cast_nullable_to_non_nullable
              as List<Meeting>?,
      rootId: freezed == rootId
          ? _self.rootId
          : rootId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$ApplicantDto {
  String? get id;
  String? get status;
  String? get requestId;
  @JsonKey(defaultValue: [])
  List<ApplicantStudyStream?> get studyStreams;

  /// Create a copy of ApplicantDto
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApplicantDtoCopyWith<ApplicantDto> get copyWith =>
      _$ApplicantDtoCopyWithImpl<ApplicantDto>(this as ApplicantDto, _$identity);

  /// Serializes this ApplicantDto to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApplicantDto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.requestId, requestId) || other.requestId == requestId) &&
            const DeepCollectionEquality().equals(other.studyStreams, studyStreams));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, status, requestId, const DeepCollectionEquality().hash(studyStreams));

  @override
  String toString() {
    return 'ApplicantDto(id: $id, status: $status, requestId: $requestId, studyStreams: $studyStreams)';
  }
}

/// @nodoc
abstract mixin class $ApplicantDtoCopyWith<$Res> {
  factory $ApplicantDtoCopyWith(ApplicantDto value, $Res Function(ApplicantDto) _then) =
      _$ApplicantDtoCopyWithImpl;
  @useResult
  $Res call(
      {String? id,
      String? status,
      String? requestId,
      @JsonKey(defaultValue: []) List<ApplicantStudyStream?> studyStreams});
}

/// @nodoc
class _$ApplicantDtoCopyWithImpl<$Res> implements $ApplicantDtoCopyWith<$Res> {
  _$ApplicantDtoCopyWithImpl(this._self, this._then);

  final ApplicantDto _self;
  final $Res Function(ApplicantDto) _then;

  /// Create a copy of ApplicantDto
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? requestId = freezed,
    Object? studyStreams = null,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      requestId: freezed == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      studyStreams: null == studyStreams
          ? _self.studyStreams
          : studyStreams // ignore: cast_nullable_to_non_nullable
              as List<ApplicantStudyStream?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ApplicantDto implements ApplicantDto {
  const _ApplicantDto(
      {required this.id,
      required this.status,
      required this.requestId,
      @JsonKey(defaultValue: []) required final List<ApplicantStudyStream?> studyStreams})
      : _studyStreams = studyStreams;
  factory _ApplicantDto.fromJson(Map<String, dynamic> json) => _$ApplicantDtoFromJson(json);

  @override
  final String? id;
  @override
  final String? status;
  @override
  final String? requestId;
  final List<ApplicantStudyStream?> _studyStreams;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantStudyStream?> get studyStreams {
    if (_studyStreams is EqualUnmodifiableListView) return _studyStreams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_studyStreams);
  }

  /// Create a copy of ApplicantDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplicantDtoCopyWith<_ApplicantDto> get copyWith =>
      __$ApplicantDtoCopyWithImpl<_ApplicantDto>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApplicantDtoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplicantDto &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.requestId, requestId) || other.requestId == requestId) &&
            const DeepCollectionEquality().equals(other._studyStreams, _studyStreams));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, status, requestId, const DeepCollectionEquality().hash(_studyStreams));

  @override
  String toString() {
    return 'ApplicantDto(id: $id, status: $status, requestId: $requestId, studyStreams: $studyStreams)';
  }
}

/// @nodoc
abstract mixin class _$ApplicantDtoCopyWith<$Res> implements $ApplicantDtoCopyWith<$Res> {
  factory _$ApplicantDtoCopyWith(_ApplicantDto value, $Res Function(_ApplicantDto) _then) =
      __$ApplicantDtoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? id,
      String? status,
      String? requestId,
      @JsonKey(defaultValue: []) List<ApplicantStudyStream?> studyStreams});
}

/// @nodoc
class __$ApplicantDtoCopyWithImpl<$Res> implements _$ApplicantDtoCopyWith<$Res> {
  __$ApplicantDtoCopyWithImpl(this._self, this._then);

  final _ApplicantDto _self;
  final $Res Function(_ApplicantDto) _then;

  /// Create a copy of ApplicantDto
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? status = freezed,
    Object? requestId = freezed,
    Object? studyStreams = null,
  }) {
    return _then(_ApplicantDto(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      requestId: freezed == requestId
          ? _self.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      studyStreams: null == studyStreams
          ? _self._studyStreams
          : studyStreams // ignore: cast_nullable_to_non_nullable
              as List<ApplicantStudyStream?>,
    ));
  }
}

/// @nodoc
mixin _$ApplicantStudyStream {
  String get streamId;
  String get status;
  DateTime? get startDate;
  DateTime? get endDate;
  DateTime? get nextSessionDate;
  bool get cancelled;
  DateTime? get cancellationDate;

  /// Create a copy of ApplicantStudyStream
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApplicantStudyStreamCopyWith<ApplicantStudyStream> get copyWith =>
      _$ApplicantStudyStreamCopyWithImpl<ApplicantStudyStream>(
          this as ApplicantStudyStream, _$identity);

  /// Serializes this ApplicantStudyStream to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApplicantStudyStream &&
            (identical(other.streamId, streamId) || other.streamId == streamId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.nextSessionDate, nextSessionDate) ||
                other.nextSessionDate == nextSessionDate) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.cancellationDate, cancellationDate) ||
                other.cancellationDate == cancellationDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, streamId, status, startDate, endDate,
      nextSessionDate, cancelled, cancellationDate);

  @override
  String toString() {
    return 'ApplicantStudyStream(streamId: $streamId, status: $status, startDate: $startDate, endDate: $endDate, nextSessionDate: $nextSessionDate, cancelled: $cancelled, cancellationDate: $cancellationDate)';
  }
}

/// @nodoc
abstract mixin class $ApplicantStudyStreamCopyWith<$Res> {
  factory $ApplicantStudyStreamCopyWith(
          ApplicantStudyStream value, $Res Function(ApplicantStudyStream) _then) =
      _$ApplicantStudyStreamCopyWithImpl;
  @useResult
  $Res call(
      {String streamId,
      String status,
      DateTime? startDate,
      DateTime? endDate,
      DateTime? nextSessionDate,
      bool cancelled,
      DateTime? cancellationDate});
}

/// @nodoc
class _$ApplicantStudyStreamCopyWithImpl<$Res> implements $ApplicantStudyStreamCopyWith<$Res> {
  _$ApplicantStudyStreamCopyWithImpl(this._self, this._then);

  final ApplicantStudyStream _self;
  final $Res Function(ApplicantStudyStream) _then;

  /// Create a copy of ApplicantStudyStream
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? streamId = null,
    Object? status = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? nextSessionDate = freezed,
    Object? cancelled = null,
    Object? cancellationDate = freezed,
  }) {
    return _then(_self.copyWith(
      streamId: null == streamId
          ? _self.streamId
          : streamId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      nextSessionDate: freezed == nextSessionDate
          ? _self.nextSessionDate
          : nextSessionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      cancellationDate: freezed == cancellationDate
          ? _self.cancellationDate
          : cancellationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ApplicantStudyStream implements ApplicantStudyStream {
  const _ApplicantStudyStream(
      {required this.streamId,
      required this.status,
      this.startDate,
      this.endDate,
      this.nextSessionDate,
      required this.cancelled,
      this.cancellationDate});
  factory _ApplicantStudyStream.fromJson(Map<String, dynamic> json) =>
      _$ApplicantStudyStreamFromJson(json);

  @override
  final String streamId;
  @override
  final String status;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  final DateTime? nextSessionDate;
  @override
  final bool cancelled;
  @override
  final DateTime? cancellationDate;

  /// Create a copy of ApplicantStudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplicantStudyStreamCopyWith<_ApplicantStudyStream> get copyWith =>
      __$ApplicantStudyStreamCopyWithImpl<_ApplicantStudyStream>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApplicantStudyStreamToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplicantStudyStream &&
            (identical(other.streamId, streamId) || other.streamId == streamId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) || other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.nextSessionDate, nextSessionDate) ||
                other.nextSessionDate == nextSessionDate) &&
            (identical(other.cancelled, cancelled) || other.cancelled == cancelled) &&
            (identical(other.cancellationDate, cancellationDate) ||
                other.cancellationDate == cancellationDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, streamId, status, startDate, endDate,
      nextSessionDate, cancelled, cancellationDate);

  @override
  String toString() {
    return 'ApplicantStudyStream(streamId: $streamId, status: $status, startDate: $startDate, endDate: $endDate, nextSessionDate: $nextSessionDate, cancelled: $cancelled, cancellationDate: $cancellationDate)';
  }
}

/// @nodoc
abstract mixin class _$ApplicantStudyStreamCopyWith<$Res>
    implements $ApplicantStudyStreamCopyWith<$Res> {
  factory _$ApplicantStudyStreamCopyWith(
          _ApplicantStudyStream value, $Res Function(_ApplicantStudyStream) _then) =
      __$ApplicantStudyStreamCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String streamId,
      String status,
      DateTime? startDate,
      DateTime? endDate,
      DateTime? nextSessionDate,
      bool cancelled,
      DateTime? cancellationDate});
}

/// @nodoc
class __$ApplicantStudyStreamCopyWithImpl<$Res> implements _$ApplicantStudyStreamCopyWith<$Res> {
  __$ApplicantStudyStreamCopyWithImpl(this._self, this._then);

  final _ApplicantStudyStream _self;
  final $Res Function(_ApplicantStudyStream) _then;

  /// Create a copy of ApplicantStudyStream
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? streamId = null,
    Object? status = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? nextSessionDate = freezed,
    Object? cancelled = null,
    Object? cancellationDate = freezed,
  }) {
    return _then(_ApplicantStudyStream(
      streamId: null == streamId
          ? _self.streamId
          : streamId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      nextSessionDate: freezed == nextSessionDate
          ? _self.nextSessionDate
          : nextSessionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelled: null == cancelled
          ? _self.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool,
      cancellationDate: freezed == cancellationDate
          ? _self.cancellationDate
          : cancellationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
