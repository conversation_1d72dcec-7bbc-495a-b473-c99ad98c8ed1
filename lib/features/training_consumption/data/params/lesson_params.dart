import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';

class LessonParams {
  const LessonParams({
    required this.section,
    required this.lesson,
    required this.completedLessonsInSection,
    required this.isCompleted,
    required this.trainingConsumptionBloc,
    this.lessonNavigationType = LessonNavigationType.initial,
    this.meeting,
  });

  final TrainingConsumptionBloc trainingConsumptionBloc;
  final Section section;
  final Lesson lesson;
  final int completedLessonsInSection;
  final bool isCompleted;
  final LessonNavigationType lessonNavigationType;
  final Meeting? meeting;

  LessonParams copyWith({
    Section? section,
    Lesson? lesson,
    int? completedLessonsInSection,
    bool? isCompleted,
    TrainingConsumptionBloc? trainingConsumptionBloc,
    LessonNavigationType? lessonNavigationType,
    Meeting? meeting,
  }) {
    return LessonParams(
      section: section ?? this.section,
      lesson: lesson ?? this.lesson,
      completedLessonsInSection: completedLessonsInSection ?? this.completedLessonsInSection,
      isCompleted: isCompleted ?? this.isCompleted,
      trainingConsumptionBloc: trainingConsumptionBloc ?? this.trainingConsumptionBloc,
      lessonNavigationType: lessonNavigationType ?? this.lessonNavigationType,
      meeting: meeting ?? this.meeting,
    );
  }
}
