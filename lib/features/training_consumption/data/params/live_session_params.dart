import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';

class LiveSessionParams {
  const LiveSessionParams({
    required this.meeting,
    required this.trainingConsumptionBloc,
    this.lessonNavigationType = LessonNavigationType.initial,
    this.sublesson,
  });

  final Meeting meeting;
  final TrainingConsumptionBloc trainingConsumptionBloc;
  final LessonNavigationType lessonNavigationType;
  final Lesson? sublesson;

  LiveSessionParams copyWith({
    Meeting? meeting,
    TrainingConsumptionBloc? trainingConsumptionBloc,
    LessonNavigationType? lessonNavigationType,
    Lesson? sublesson,
  }) {
    return LiveSessionParams(
      meeting: meeting ?? this.meeting,
      trainingConsumptionBloc: trainingConsumptionBloc ?? this.trainingConsumptionBloc,
      lessonNavigationType: lessonNavigationType ?? this.lessonNavigationType,
      sublesson: sublesson ?? this.sublesson,
    );
  }
}
