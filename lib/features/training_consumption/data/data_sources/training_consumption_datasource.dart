import 'dart:async';

import 'package:background_downloader/background_downloader.dart';
import 'package:dio/dio.dart';

import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:uuid/uuid.dart';

@injectable
class TrainingConsumptionDatasource {
  const TrainingConsumptionDatasource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<TrainingConsumptionModel> getTrainingConsumptionDetails(String trainingId) async {
    final response = await _dio.get(ApiConstants.applicantsTrainings + trainingId);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return TrainingConsumptionModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<void> markLessonAsCompleted({required String trainingId, required String lessonId}) async {
    final response = await _dio.put(
      ApiConstants.applicantsTrainings + trainingId,
      data: Progress(lessonId: lessonId, isPassed: true).toJson(),
    );

    if (response.statusCode != Constants.statusCode200 &&
        response.statusCode != Constants.statusCode201) {
      throw DioException(requestOptions: response.requestOptions, response: response);
    }
  }

  Future<String> getVideoUrl(String videoKey) async {
    final response = await _dio.get(ApiConstants.videoLessonPath + videoKey);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      if (data is Map) {
        return data[Constants.preSignedUrl];
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<void> downloadSlide(Resource resource, Function(double) onProgress) async {
    final authToken = await GetIt.instance<AuthTokenProvider>().getToken();
    final slideKey = resource.key ?? '';
    final fileName = resource.originalFilename ?? '';

    ///By Default plugin uses getApplicationDocumentsDirectory from path_provider package
    final task = DownloadTask(
      url: _dio.options.baseUrl + ApiConstants.usersFilesPath + slideKey,
      headers: {
        Constants.Authorization: '${Constants.Bearer} $authToken',
        Constants.xCurrentRole: Constants.TRAINEE,
        Constants.xRequestId: const Uuid().v1(),
      },
      filename: fileName,
      updates: Updates.statusAndProgress,
    );

    final taskUpdate = await FileDownloader().download(task, onProgress: onProgress);

    if (taskUpdate.status == TaskStatus.failed) {
      throw DioException(
        requestOptions: RequestOptions(path: task.url, headers: task.headers),
        response: Response(
          statusCode: taskUpdate.responseStatusCode,
          requestOptions: RequestOptions(path: task.url, headers: task.headers),
        ),
      );
    }
  }

  Future<void> downloadFile(String fileKey, String? fileName) async {
    final authToken = await GetIt.instance<AuthTokenProvider>().getToken();

    ///By Default plugin uses getApplicationDocumentsDirectory from path_provider package
    final task = DownloadTask(
      url: _dio.options.baseUrl + ApiConstants.trainingsFilesPath + fileKey,
      headers: {
        Constants.Authorization: '${Constants.Bearer} $authToken',
        Constants.xCurrentRole: Constants.TRAINEE,
        Constants.xRequestId: const Uuid().v1(),
      },
      filename: fileName,
    );

    final taskUpdate = await FileDownloader().download(task);

    if (taskUpdate.status == TaskStatus.failed) {
      throw DioException(
        requestOptions: RequestOptions(path: task.url, headers: task.headers),
        response: Response(
          statusCode: taskUpdate.responseStatusCode,
          requestOptions: RequestOptions(path: task.url, headers: task.headers),
        ),
      );
    }
  }
}
