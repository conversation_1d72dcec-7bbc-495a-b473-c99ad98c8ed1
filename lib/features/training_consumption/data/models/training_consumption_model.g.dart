// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'training_consumption_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TrainingConsumptionModel _$TrainingConsumptionModelFromJson(Map<String, dynamic> json) =>
    _TrainingConsumptionModel(
      id: json['id'] as String,
      trainingId: json['trainingId'] as String,
      status: json['status'] as String,
      progress: (json['progress'] as List<dynamic>)
          .map((e) => Progress.fromJson(e as Map<String, dynamic>))
          .toList(),
      passedScore: (json['passedScore'] as num).toInt(),
      allSections: (json['allSections'] as num?)?.toInt() ?? 0,
      passedSections: (json['passedSections'] as num).toInt(),
      enrolledDate:
          json['enrolledDate'] == null ? null : DateTime.parse(json['enrolledDate'] as String),
      historyOfTests: (json['historyOfTests'] as List<dynamic>?)
              ?.map((e) => HistoryOfTest.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      completedDate:
          json['completedDate'] == null ? null : DateTime.parse(json['completedDate'] as String),
      certificateId: (json['certificateId'] as num?)?.toInt(),
      preQualificationTest: json['preQualificationTest'] == null
          ? null
          : PreQualificationTest.fromJson(json['preQualificationTest'] as Map<String, dynamic>),
      postQualificationTest: json['postQualificationTest'] == null
          ? null
          : PostQualificationTest.fromJson(json['postQualificationTest'] as Map<String, dynamic>),
      preTestPassed: json['preTestPassed'] as bool?,
    );

Map<String, dynamic> _$TrainingConsumptionModelToJson(_TrainingConsumptionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'trainingId': instance.trainingId,
      'status': instance.status,
      'progress': instance.progress,
      'passedScore': instance.passedScore,
      'allSections': instance.allSections,
      'passedSections': instance.passedSections,
      'enrolledDate': instance.enrolledDate?.toIso8601String(),
      'historyOfTests': instance.historyOfTests,
      'completedDate': instance.completedDate?.toIso8601String(),
      'certificateId': instance.certificateId,
      'preQualificationTest': instance.preQualificationTest,
      'postQualificationTest': instance.postQualificationTest,
      'preTestPassed': instance.preTestPassed,
    };

_Progress _$ProgressFromJson(Map<String, dynamic> json) => _Progress(
      lessonId: json['lessonId'] as String,
      isPassed: json['isPassed'] as bool?,
      quizResult: json['quizResult'] == null
          ? null
          : QuizResult.fromJson(json['quizResult'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProgressToJson(_Progress instance) => <String, dynamic>{
      'lessonId': instance.lessonId,
      'isPassed': instance.isPassed,
    };

_QuestionAnswerPair _$QuestionAnswerPairFromJson(Map<String, dynamic> json) => _QuestionAnswerPair(
      answerIds: (json['answerIds'] as List<dynamic>?)?.map((e) => e as String?).toList() ?? [],
      questionId: json['questionId'] as String?,
      question: json['question'] as String?,
      correct: json['correct'] as bool?,
    );

Map<String, dynamic> _$QuestionAnswerPairToJson(_QuestionAnswerPair instance) => <String, dynamic>{
      'answerIds': instance.answerIds,
      'questionId': instance.questionId,
      'question': instance.question,
      'correct': instance.correct,
    };

_PreQualificationTest _$PreQualificationTestFromJson(Map<String, dynamic> json) =>
    _PreQualificationTest(
      applicantAnswers: (json['applicantAnswers'] as List<dynamic>?)
              ?.map((e) => ApplicantAnswer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => Question.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantSavedAnswers: (json['applicantSavedAnswers'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, ApplicantSavedAnswer.fromJson(e as Map<String, dynamic>)),
          ) ??
          {},
      id: json['id'] as String?,
      randomized: json['randomized'] as bool?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      minimumScore: (json['minimumScore'] as num?)?.toInt(),
      timeLimit: (json['timeLimit'] as num?)?.toInt(),
      mandatory: json['mandatory'] as bool?,
      finalScore: (json['finalScore'] as num?)?.toInt(),
      resolutionDate:
          json['resolutionDate'] == null ? null : DateTime.parse(json['resolutionDate'] as String),
      firstAttempt: json['firstAttempt'] as bool?,
      type: $enumDecodeNullable(_$QualificationTestTypeEnumMap, json['type']) ??
          QualificationTestType.PRE,
    );

Map<String, dynamic> _$PreQualificationTestToJson(_PreQualificationTest instance) =>
    <String, dynamic>{
      'applicantAnswers': instance.applicantAnswers,
      'questions': instance.questions,
      'applicantSavedAnswers': instance.applicantSavedAnswers,
      'id': instance.id,
      'randomized': instance.randomized,
      'title': instance.title,
      'description': instance.description,
      'minimumScore': instance.minimumScore,
      'timeLimit': instance.timeLimit,
      'mandatory': instance.mandatory,
      'finalScore': instance.finalScore,
      'resolutionDate': instance.resolutionDate?.toIso8601String(),
      'firstAttempt': instance.firstAttempt,
      'type': _$QualificationTestTypeEnumMap[instance.type],
    };

const _$QualificationTestTypeEnumMap = {
  QualificationTestType.PRE: 'PRE',
  QualificationTestType.POST: 'POST',
};

_PostQualificationTest _$PostQualificationTestFromJson(Map<String, dynamic> json) =>
    _PostQualificationTest(
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => Question.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantAnswers: (json['applicantAnswers'] as List<dynamic>?)
              ?.map((e) => ApplicantAnswer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantSavedAnswers: (json['applicantSavedAnswers'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, ApplicantSavedAnswer.fromJson(e as Map<String, dynamic>)),
          ) ??
          {},
      id: json['id'] as String?,
      randomized: json['randomized'] as bool?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      minimumScore: (json['minimumScore'] as num?)?.toInt(),
      timeLimit: (json['timeLimit'] as num?)?.toInt(),
      mandatory: json['mandatory'] as bool?,
      finalScore: (json['finalScore'] as num?)?.toInt(),
      resolutionDate:
          json['resolutionDate'] == null ? null : DateTime.parse(json['resolutionDate'] as String),
      firstAttempt: json['firstAttempt'] as bool?,
      type: $enumDecodeNullable(_$QualificationTestTypeEnumMap, json['type']) ??
          QualificationTestType.POST,
    );

Map<String, dynamic> _$PostQualificationTestToJson(_PostQualificationTest instance) =>
    <String, dynamic>{
      'questions': instance.questions,
      'applicantAnswers': instance.applicantAnswers,
      'applicantSavedAnswers': instance.applicantSavedAnswers,
      'id': instance.id,
      'randomized': instance.randomized,
      'title': instance.title,
      'description': instance.description,
      'minimumScore': instance.minimumScore,
      'timeLimit': instance.timeLimit,
      'mandatory': instance.mandatory,
      'finalScore': instance.finalScore,
      'resolutionDate': instance.resolutionDate?.toIso8601String(),
      'firstAttempt': instance.firstAttempt,
      'type': _$QualificationTestTypeEnumMap[instance.type],
    };

_Question _$QuestionFromJson(Map<String, dynamic> json) => _Question(
      id: json['id'] as String,
      answers: (json['answers'] as List<dynamic>?)
              ?.map((e) => Answer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      lastModifiedDate: json['lastModifiedDate'] == null
          ? null
          : DateTime.parse(json['lastModifiedDate'] as String),
      questionType: json['questionType'] as String?,
      question: json['question'] as String?,
      index: (json['index'] as num?)?.toInt(),
    );

Map<String, dynamic> _$QuestionToJson(_Question instance) => <String, dynamic>{
      'id': instance.id,
      'answers': instance.answers,
      'lastModifiedDate': instance.lastModifiedDate?.toIso8601String(),
      'questionType': instance.questionType,
      'question': instance.question,
      'index': instance.index,
    };

_ApplicantAnswer _$ApplicantAnswerFromJson(Map<String, dynamic> json) => _ApplicantAnswer(
      questionId: json['questionId'] as String?,
      correct: json['correct'] as bool?,
      questionTitle: json['questionTitle'] as String?,
    );

Map<String, dynamic> _$ApplicantAnswerToJson(_ApplicantAnswer instance) => <String, dynamic>{
      'questionId': instance.questionId,
      'correct': instance.correct,
      'questionTitle': instance.questionTitle,
    };

_ApplicantSavedAnswer _$ApplicantSavedAnswerFromJson(Map<String, dynamic> json) =>
    _ApplicantSavedAnswer(
      questionId: json['questionId'] as String?,
      question: json['question'] as String?,
      answerId: json['answerId'] as String?,
    );

Map<String, dynamic> _$ApplicantSavedAnswerToJson(_ApplicantSavedAnswer instance) =>
    <String, dynamic>{
      'questionId': instance.questionId,
      'question': instance.question,
      'answerId': instance.answerId,
    };

_HistoryOfTest _$HistoryOfTestFromJson(Map<String, dynamic> json) => _HistoryOfTest(
      questions: (json['questions'] as List<dynamic>?)
              ?.map((e) => Question.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantSavedAnswers: (json['applicantSavedAnswers'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, ApplicantSavedAnswer.fromJson(e as Map<String, dynamic>)),
          ) ??
          {},
      id: json['id'] as String?,
      randomized: json['randomized'] as bool?,
      title: json['title'] as String?,
      description: json['description'] as String?,
      minimumScore: (json['minimumScore'] as num?)?.toInt(),
      timeLimit: (json['timeLimit'] as num?)?.toInt(),
      mandatory: json['mandatory'] as bool?,
      finalScore: (json['finalScore'] as num?)?.toInt(),
      applicantAnswers: (json['applicantAnswers'] as List<dynamic>?)
              ?.map((e) => ApplicantAnswer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      resolutionDate:
          json['resolutionDate'] == null ? null : DateTime.parse(json['resolutionDate'] as String),
      firstAttempt: json['firstAttempt'] as bool?,
    );

Map<String, dynamic> _$HistoryOfTestToJson(_HistoryOfTest instance) => <String, dynamic>{
      'questions': instance.questions,
      'applicantSavedAnswers': instance.applicantSavedAnswers,
      'id': instance.id,
      'randomized': instance.randomized,
      'title': instance.title,
      'description': instance.description,
      'minimumScore': instance.minimumScore,
      'timeLimit': instance.timeLimit,
      'mandatory': instance.mandatory,
      'finalScore': instance.finalScore,
      'applicantAnswers': instance.applicantAnswers,
      'resolutionDate': instance.resolutionDate?.toIso8601String(),
      'firstAttempt': instance.firstAttempt,
    };
