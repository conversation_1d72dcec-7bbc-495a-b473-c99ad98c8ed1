// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_consumption_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingConsumptionModel {
  String get id;
  String get trainingId;
  String get status;
  List<Progress> get progress;
  int get passedScore;
  @JsonKey(defaultValue: 0)
  int get allSections;
  int get passedSections;
  DateTime? get enrolledDate;
  @JsonKey(defaultValue: [])
  List<HistoryOfTest> get historyOfTests;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Set<String> get completedLessonsIDs;
  DateTime? get completedDate;
  int? get certificateId;
  PreQualificationTest? get preQualificationTest;
  PostQualificationTest? get postQualificationTest;
  bool? get preTestPassed;

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingConsumptionModelCopyWith<TrainingConsumptionModel> get copyWith =>
      _$TrainingConsumptionModelCopyWithImpl<TrainingConsumptionModel>(
          this as TrainingConsumptionModel, _$identity);

  /// Serializes this TrainingConsumptionModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingConsumptionModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other.progress, progress) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allSections, allSections) || other.allSections == allSections) &&
            (identical(other.passedSections, passedSections) ||
                other.passedSections == passedSections) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            const DeepCollectionEquality().equals(other.historyOfTests, historyOfTests) &&
            const DeepCollectionEquality().equals(other.completedLessonsIDs, completedLessonsIDs) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId) &&
            (identical(other.preQualificationTest, preQualificationTest) ||
                other.preQualificationTest == preQualificationTest) &&
            (identical(other.postQualificationTest, postQualificationTest) ||
                other.postQualificationTest == postQualificationTest) &&
            (identical(other.preTestPassed, preTestPassed) ||
                other.preTestPassed == preTestPassed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      trainingId,
      status,
      const DeepCollectionEquality().hash(progress),
      passedScore,
      allSections,
      passedSections,
      enrolledDate,
      const DeepCollectionEquality().hash(historyOfTests),
      const DeepCollectionEquality().hash(completedLessonsIDs),
      completedDate,
      certificateId,
      preQualificationTest,
      postQualificationTest,
      preTestPassed);

  @override
  String toString() {
    return 'TrainingConsumptionModel(id: $id, trainingId: $trainingId, status: $status, progress: $progress, passedScore: $passedScore, allSections: $allSections, passedSections: $passedSections, enrolledDate: $enrolledDate, historyOfTests: $historyOfTests, completedLessonsIDs: $completedLessonsIDs, completedDate: $completedDate, certificateId: $certificateId, preQualificationTest: $preQualificationTest, postQualificationTest: $postQualificationTest, preTestPassed: $preTestPassed)';
  }
}

/// @nodoc
abstract mixin class $TrainingConsumptionModelCopyWith<$Res> {
  factory $TrainingConsumptionModelCopyWith(
          TrainingConsumptionModel value, $Res Function(TrainingConsumptionModel) _then) =
      _$TrainingConsumptionModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String trainingId,
      String status,
      List<Progress> progress,
      int passedScore,
      @JsonKey(defaultValue: 0) int allSections,
      int passedSections,
      DateTime? enrolledDate,
      @JsonKey(defaultValue: []) List<HistoryOfTest> historyOfTests,
      @JsonKey(includeFromJson: false, includeToJson: false) Set<String> completedLessonsIDs,
      DateTime? completedDate,
      int? certificateId,
      PreQualificationTest? preQualificationTest,
      PostQualificationTest? postQualificationTest,
      bool? preTestPassed});

  $PreQualificationTestCopyWith<$Res>? get preQualificationTest;
  $PostQualificationTestCopyWith<$Res>? get postQualificationTest;
}

/// @nodoc
class _$TrainingConsumptionModelCopyWithImpl<$Res>
    implements $TrainingConsumptionModelCopyWith<$Res> {
  _$TrainingConsumptionModelCopyWithImpl(this._self, this._then);

  final TrainingConsumptionModel _self;
  final $Res Function(TrainingConsumptionModel) _then;

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? trainingId = null,
    Object? status = null,
    Object? progress = null,
    Object? passedScore = null,
    Object? allSections = null,
    Object? passedSections = null,
    Object? enrolledDate = freezed,
    Object? historyOfTests = null,
    Object? completedLessonsIDs = null,
    Object? completedDate = freezed,
    Object? certificateId = freezed,
    Object? preQualificationTest = freezed,
    Object? postQualificationTest = freezed,
    Object? preTestPassed = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _self.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as List<Progress>,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allSections: null == allSections
          ? _self.allSections
          : allSections // ignore: cast_nullable_to_non_nullable
              as int,
      passedSections: null == passedSections
          ? _self.passedSections
          : passedSections // ignore: cast_nullable_to_non_nullable
              as int,
      enrolledDate: freezed == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      historyOfTests: null == historyOfTests
          ? _self.historyOfTests
          : historyOfTests // ignore: cast_nullable_to_non_nullable
              as List<HistoryOfTest>,
      completedLessonsIDs: null == completedLessonsIDs
          ? _self.completedLessonsIDs
          : completedLessonsIDs // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      certificateId: freezed == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int?,
      preQualificationTest: freezed == preQualificationTest
          ? _self.preQualificationTest
          : preQualificationTest // ignore: cast_nullable_to_non_nullable
              as PreQualificationTest?,
      postQualificationTest: freezed == postQualificationTest
          ? _self.postQualificationTest
          : postQualificationTest // ignore: cast_nullable_to_non_nullable
              as PostQualificationTest?,
      preTestPassed: freezed == preTestPassed
          ? _self.preTestPassed
          : preTestPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PreQualificationTestCopyWith<$Res>? get preQualificationTest {
    if (_self.preQualificationTest == null) {
      return null;
    }

    return $PreQualificationTestCopyWith<$Res>(_self.preQualificationTest!, (value) {
      return _then(_self.copyWith(preQualificationTest: value));
    });
  }

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PostQualificationTestCopyWith<$Res>? get postQualificationTest {
    if (_self.postQualificationTest == null) {
      return null;
    }

    return $PostQualificationTestCopyWith<$Res>(_self.postQualificationTest!, (value) {
      return _then(_self.copyWith(postQualificationTest: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingConsumptionModel implements TrainingConsumptionModel {
  const _TrainingConsumptionModel(
      {required this.id,
      required this.trainingId,
      required this.status,
      required final List<Progress> progress,
      required this.passedScore,
      @JsonKey(defaultValue: 0) required this.allSections,
      required this.passedSections,
      required this.enrolledDate,
      @JsonKey(defaultValue: []) required final List<HistoryOfTest> historyOfTests,
      @JsonKey(includeFromJson: false, includeToJson: false)
      final Set<String> completedLessonsIDs = const {},
      this.completedDate,
      this.certificateId,
      this.preQualificationTest,
      this.postQualificationTest,
      this.preTestPassed})
      : _progress = progress,
        _historyOfTests = historyOfTests,
        _completedLessonsIDs = completedLessonsIDs;
  factory _TrainingConsumptionModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingConsumptionModelFromJson(json);

  @override
  final String id;
  @override
  final String trainingId;
  @override
  final String status;
  final List<Progress> _progress;
  @override
  List<Progress> get progress {
    if (_progress is EqualUnmodifiableListView) return _progress;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_progress);
  }

  @override
  final int passedScore;
  @override
  @JsonKey(defaultValue: 0)
  final int allSections;
  @override
  final int passedSections;
  @override
  final DateTime? enrolledDate;
  final List<HistoryOfTest> _historyOfTests;
  @override
  @JsonKey(defaultValue: [])
  List<HistoryOfTest> get historyOfTests {
    if (_historyOfTests is EqualUnmodifiableListView) return _historyOfTests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_historyOfTests);
  }

  final Set<String> _completedLessonsIDs;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  Set<String> get completedLessonsIDs {
    if (_completedLessonsIDs is EqualUnmodifiableSetView) return _completedLessonsIDs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_completedLessonsIDs);
  }

  @override
  final DateTime? completedDate;
  @override
  final int? certificateId;
  @override
  final PreQualificationTest? preQualificationTest;
  @override
  final PostQualificationTest? postQualificationTest;
  @override
  final bool? preTestPassed;

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingConsumptionModelCopyWith<_TrainingConsumptionModel> get copyWith =>
      __$TrainingConsumptionModelCopyWithImpl<_TrainingConsumptionModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingConsumptionModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingConsumptionModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._progress, _progress) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allSections, allSections) || other.allSections == allSections) &&
            (identical(other.passedSections, passedSections) ||
                other.passedSections == passedSections) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            const DeepCollectionEquality().equals(other._historyOfTests, _historyOfTests) &&
            const DeepCollectionEquality()
                .equals(other._completedLessonsIDs, _completedLessonsIDs) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId) &&
            (identical(other.preQualificationTest, preQualificationTest) ||
                other.preQualificationTest == preQualificationTest) &&
            (identical(other.postQualificationTest, postQualificationTest) ||
                other.postQualificationTest == postQualificationTest) &&
            (identical(other.preTestPassed, preTestPassed) ||
                other.preTestPassed == preTestPassed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      trainingId,
      status,
      const DeepCollectionEquality().hash(_progress),
      passedScore,
      allSections,
      passedSections,
      enrolledDate,
      const DeepCollectionEquality().hash(_historyOfTests),
      const DeepCollectionEquality().hash(_completedLessonsIDs),
      completedDate,
      certificateId,
      preQualificationTest,
      postQualificationTest,
      preTestPassed);

  @override
  String toString() {
    return 'TrainingConsumptionModel(id: $id, trainingId: $trainingId, status: $status, progress: $progress, passedScore: $passedScore, allSections: $allSections, passedSections: $passedSections, enrolledDate: $enrolledDate, historyOfTests: $historyOfTests, completedLessonsIDs: $completedLessonsIDs, completedDate: $completedDate, certificateId: $certificateId, preQualificationTest: $preQualificationTest, postQualificationTest: $postQualificationTest, preTestPassed: $preTestPassed)';
  }
}

/// @nodoc
abstract mixin class _$TrainingConsumptionModelCopyWith<$Res>
    implements $TrainingConsumptionModelCopyWith<$Res> {
  factory _$TrainingConsumptionModelCopyWith(
          _TrainingConsumptionModel value, $Res Function(_TrainingConsumptionModel) _then) =
      __$TrainingConsumptionModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String trainingId,
      String status,
      List<Progress> progress,
      int passedScore,
      @JsonKey(defaultValue: 0) int allSections,
      int passedSections,
      DateTime? enrolledDate,
      @JsonKey(defaultValue: []) List<HistoryOfTest> historyOfTests,
      @JsonKey(includeFromJson: false, includeToJson: false) Set<String> completedLessonsIDs,
      DateTime? completedDate,
      int? certificateId,
      PreQualificationTest? preQualificationTest,
      PostQualificationTest? postQualificationTest,
      bool? preTestPassed});

  @override
  $PreQualificationTestCopyWith<$Res>? get preQualificationTest;
  @override
  $PostQualificationTestCopyWith<$Res>? get postQualificationTest;
}

/// @nodoc
class __$TrainingConsumptionModelCopyWithImpl<$Res>
    implements _$TrainingConsumptionModelCopyWith<$Res> {
  __$TrainingConsumptionModelCopyWithImpl(this._self, this._then);

  final _TrainingConsumptionModel _self;
  final $Res Function(_TrainingConsumptionModel) _then;

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? trainingId = null,
    Object? status = null,
    Object? progress = null,
    Object? passedScore = null,
    Object? allSections = null,
    Object? passedSections = null,
    Object? enrolledDate = freezed,
    Object? historyOfTests = null,
    Object? completedLessonsIDs = null,
    Object? completedDate = freezed,
    Object? certificateId = freezed,
    Object? preQualificationTest = freezed,
    Object? postQualificationTest = freezed,
    Object? preTestPassed = freezed,
  }) {
    return _then(_TrainingConsumptionModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _self._progress
          : progress // ignore: cast_nullable_to_non_nullable
              as List<Progress>,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allSections: null == allSections
          ? _self.allSections
          : allSections // ignore: cast_nullable_to_non_nullable
              as int,
      passedSections: null == passedSections
          ? _self.passedSections
          : passedSections // ignore: cast_nullable_to_non_nullable
              as int,
      enrolledDate: freezed == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      historyOfTests: null == historyOfTests
          ? _self._historyOfTests
          : historyOfTests // ignore: cast_nullable_to_non_nullable
              as List<HistoryOfTest>,
      completedLessonsIDs: null == completedLessonsIDs
          ? _self._completedLessonsIDs
          : completedLessonsIDs // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      certificateId: freezed == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int?,
      preQualificationTest: freezed == preQualificationTest
          ? _self.preQualificationTest
          : preQualificationTest // ignore: cast_nullable_to_non_nullable
              as PreQualificationTest?,
      postQualificationTest: freezed == postQualificationTest
          ? _self.postQualificationTest
          : postQualificationTest // ignore: cast_nullable_to_non_nullable
              as PostQualificationTest?,
      preTestPassed: freezed == preTestPassed
          ? _self.preTestPassed
          : preTestPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PreQualificationTestCopyWith<$Res>? get preQualificationTest {
    if (_self.preQualificationTest == null) {
      return null;
    }

    return $PreQualificationTestCopyWith<$Res>(_self.preQualificationTest!, (value) {
      return _then(_self.copyWith(preQualificationTest: value));
    });
  }

  /// Create a copy of TrainingConsumptionModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PostQualificationTestCopyWith<$Res>? get postQualificationTest {
    if (_self.postQualificationTest == null) {
      return null;
    }

    return $PostQualificationTestCopyWith<$Res>(_self.postQualificationTest!, (value) {
      return _then(_self.copyWith(postQualificationTest: value));
    });
  }
}

/// @nodoc
mixin _$Progress {
  String get lessonId;
  bool? get isPassed;
  @JsonKey(includeToJson: false)
  QuizResult? get quizResult;

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProgressCopyWith<Progress> get copyWith =>
      _$ProgressCopyWithImpl<Progress>(this as Progress, _$identity);

  /// Serializes this Progress to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Progress &&
            (identical(other.lessonId, lessonId) || other.lessonId == lessonId) &&
            (identical(other.isPassed, isPassed) || other.isPassed == isPassed) &&
            (identical(other.quizResult, quizResult) || other.quizResult == quizResult));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lessonId, isPassed, quizResult);

  @override
  String toString() {
    return 'Progress(lessonId: $lessonId, isPassed: $isPassed, quizResult: $quizResult)';
  }
}

/// @nodoc
abstract mixin class $ProgressCopyWith<$Res> {
  factory $ProgressCopyWith(Progress value, $Res Function(Progress) _then) = _$ProgressCopyWithImpl;
  @useResult
  $Res call(
      {String lessonId, bool? isPassed, @JsonKey(includeToJson: false) QuizResult? quizResult});

  $QuizResultCopyWith<$Res>? get quizResult;
}

/// @nodoc
class _$ProgressCopyWithImpl<$Res> implements $ProgressCopyWith<$Res> {
  _$ProgressCopyWithImpl(this._self, this._then);

  final Progress _self;
  final $Res Function(Progress) _then;

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonId = null,
    Object? isPassed = freezed,
    Object? quizResult = freezed,
  }) {
    return _then(_self.copyWith(
      lessonId: null == lessonId
          ? _self.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String,
      isPassed: freezed == isPassed
          ? _self.isPassed
          : isPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
      quizResult: freezed == quizResult
          ? _self.quizResult
          : quizResult // ignore: cast_nullable_to_non_nullable
              as QuizResult?,
    ));
  }

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizResultCopyWith<$Res>? get quizResult {
    if (_self.quizResult == null) {
      return null;
    }

    return $QuizResultCopyWith<$Res>(_self.quizResult!, (value) {
      return _then(_self.copyWith(quizResult: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Progress implements Progress {
  const _Progress(
      {required this.lessonId, this.isPassed, @JsonKey(includeToJson: false) this.quizResult});
  factory _Progress.fromJson(Map<String, dynamic> json) => _$ProgressFromJson(json);

  @override
  final String lessonId;
  @override
  final bool? isPassed;
  @override
  @JsonKey(includeToJson: false)
  final QuizResult? quizResult;

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProgressCopyWith<_Progress> get copyWith =>
      __$ProgressCopyWithImpl<_Progress>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ProgressToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Progress &&
            (identical(other.lessonId, lessonId) || other.lessonId == lessonId) &&
            (identical(other.isPassed, isPassed) || other.isPassed == isPassed) &&
            (identical(other.quizResult, quizResult) || other.quizResult == quizResult));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, lessonId, isPassed, quizResult);

  @override
  String toString() {
    return 'Progress(lessonId: $lessonId, isPassed: $isPassed, quizResult: $quizResult)';
  }
}

/// @nodoc
abstract mixin class _$ProgressCopyWith<$Res> implements $ProgressCopyWith<$Res> {
  factory _$ProgressCopyWith(_Progress value, $Res Function(_Progress) _then) =
      __$ProgressCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String lessonId, bool? isPassed, @JsonKey(includeToJson: false) QuizResult? quizResult});

  @override
  $QuizResultCopyWith<$Res>? get quizResult;
}

/// @nodoc
class __$ProgressCopyWithImpl<$Res> implements _$ProgressCopyWith<$Res> {
  __$ProgressCopyWithImpl(this._self, this._then);

  final _Progress _self;
  final $Res Function(_Progress) _then;

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? lessonId = null,
    Object? isPassed = freezed,
    Object? quizResult = freezed,
  }) {
    return _then(_Progress(
      lessonId: null == lessonId
          ? _self.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as String,
      isPassed: freezed == isPassed
          ? _self.isPassed
          : isPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
      quizResult: freezed == quizResult
          ? _self.quizResult
          : quizResult // ignore: cast_nullable_to_non_nullable
              as QuizResult?,
    ));
  }

  /// Create a copy of Progress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizResultCopyWith<$Res>? get quizResult {
    if (_self.quizResult == null) {
      return null;
    }

    return $QuizResultCopyWith<$Res>(_self.quizResult!, (value) {
      return _then(_self.copyWith(quizResult: value));
    });
  }
}

/// @nodoc
mixin _$QuestionAnswerPair {
  @JsonKey(defaultValue: [])
  List<String?> get answerIds;
  String? get questionId;
  String? get question;
  bool? get correct;

  /// Create a copy of QuestionAnswerPair
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuestionAnswerPairCopyWith<QuestionAnswerPair> get copyWith =>
      _$QuestionAnswerPairCopyWithImpl<QuestionAnswerPair>(this as QuestionAnswerPair, _$identity);

  /// Serializes this QuestionAnswerPair to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuestionAnswerPair &&
            const DeepCollectionEquality().equals(other.answerIds, answerIds) &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.correct, correct) || other.correct == correct));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(answerIds), questionId, question, correct);

  @override
  String toString() {
    return 'QuestionAnswerPair(answerIds: $answerIds, questionId: $questionId, question: $question, correct: $correct)';
  }
}

/// @nodoc
abstract mixin class $QuestionAnswerPairCopyWith<$Res> {
  factory $QuestionAnswerPairCopyWith(
          QuestionAnswerPair value, $Res Function(QuestionAnswerPair) _then) =
      _$QuestionAnswerPairCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<String?> answerIds,
      String? questionId,
      String? question,
      bool? correct});
}

/// @nodoc
class _$QuestionAnswerPairCopyWithImpl<$Res> implements $QuestionAnswerPairCopyWith<$Res> {
  _$QuestionAnswerPairCopyWithImpl(this._self, this._then);

  final QuestionAnswerPair _self;
  final $Res Function(QuestionAnswerPair) _then;

  /// Create a copy of QuestionAnswerPair
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? answerIds = null,
    Object? questionId = freezed,
    Object? question = freezed,
    Object? correct = freezed,
  }) {
    return _then(_self.copyWith(
      answerIds: null == answerIds
          ? _self.answerIds
          : answerIds // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuestionAnswerPair implements QuestionAnswerPair {
  const _QuestionAnswerPair(
      {@JsonKey(defaultValue: []) required final List<String?> answerIds,
      this.questionId,
      this.question,
      this.correct})
      : _answerIds = answerIds;
  factory _QuestionAnswerPair.fromJson(Map<String, dynamic> json) =>
      _$QuestionAnswerPairFromJson(json);

  final List<String?> _answerIds;
  @override
  @JsonKey(defaultValue: [])
  List<String?> get answerIds {
    if (_answerIds is EqualUnmodifiableListView) return _answerIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answerIds);
  }

  @override
  final String? questionId;
  @override
  final String? question;
  @override
  final bool? correct;

  /// Create a copy of QuestionAnswerPair
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuestionAnswerPairCopyWith<_QuestionAnswerPair> get copyWith =>
      __$QuestionAnswerPairCopyWithImpl<_QuestionAnswerPair>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuestionAnswerPairToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuestionAnswerPair &&
            const DeepCollectionEquality().equals(other._answerIds, _answerIds) &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.correct, correct) || other.correct == correct));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_answerIds), questionId, question, correct);

  @override
  String toString() {
    return 'QuestionAnswerPair(answerIds: $answerIds, questionId: $questionId, question: $question, correct: $correct)';
  }
}

/// @nodoc
abstract mixin class _$QuestionAnswerPairCopyWith<$Res>
    implements $QuestionAnswerPairCopyWith<$Res> {
  factory _$QuestionAnswerPairCopyWith(
          _QuestionAnswerPair value, $Res Function(_QuestionAnswerPair) _then) =
      __$QuestionAnswerPairCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<String?> answerIds,
      String? questionId,
      String? question,
      bool? correct});
}

/// @nodoc
class __$QuestionAnswerPairCopyWithImpl<$Res> implements _$QuestionAnswerPairCopyWith<$Res> {
  __$QuestionAnswerPairCopyWithImpl(this._self, this._then);

  final _QuestionAnswerPair _self;
  final $Res Function(_QuestionAnswerPair) _then;

  /// Create a copy of QuestionAnswerPair
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? answerIds = null,
    Object? questionId = freezed,
    Object? question = freezed,
    Object? correct = freezed,
  }) {
    return _then(_QuestionAnswerPair(
      answerIds: null == answerIds
          ? _self._answerIds
          : answerIds // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
mixin _$PreQualificationTest {
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer> get applicantAnswers;
  @JsonKey(defaultValue: [])
  List<Question> get questions;
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer> get applicantSavedAnswers;
  String? get id;
  bool? get randomized;
  String? get title;
  String? get description;
  int? get minimumScore;
  int? get timeLimit;
  bool? get mandatory;
  int? get finalScore;
  DateTime? get resolutionDate;
  bool? get firstAttempt;
  @JsonKey(defaultValue: QualificationTestType.PRE)
  QualificationTestType? get type;

  /// Create a copy of PreQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PreQualificationTestCopyWith<PreQualificationTest> get copyWith =>
      _$PreQualificationTestCopyWithImpl<PreQualificationTest>(
          this as PreQualificationTest, _$identity);

  /// Serializes this PreQualificationTest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PreQualificationTest &&
            const DeepCollectionEquality().equals(other.applicantAnswers, applicantAnswers) &&
            const DeepCollectionEquality().equals(other.questions, questions) &&
            const DeepCollectionEquality()
                .equals(other.applicantSavedAnswers, applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(applicantAnswers),
      const DeepCollectionEquality().hash(questions),
      const DeepCollectionEquality().hash(applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      resolutionDate,
      firstAttempt,
      type);

  @override
  String toString() {
    return 'PreQualificationTest(applicantAnswers: $applicantAnswers, questions: $questions, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt, type: $type)';
  }
}

/// @nodoc
abstract mixin class $PreQualificationTestCopyWith<$Res> {
  factory $PreQualificationTestCopyWith(
          PreQualificationTest value, $Res Function(PreQualificationTest) _then) =
      _$PreQualificationTestCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: []) List<Question> questions,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      DateTime? resolutionDate,
      bool? firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.PRE) QualificationTestType? type});
}

/// @nodoc
class _$PreQualificationTestCopyWithImpl<$Res> implements $PreQualificationTestCopyWith<$Res> {
  _$PreQualificationTestCopyWithImpl(this._self, this._then);

  final PreQualificationTest _self;
  final $Res Function(PreQualificationTest) _then;

  /// Create a copy of PreQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? applicantAnswers = null,
    Object? questions = null,
    Object? applicantSavedAnswers = null,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
    Object? type = freezed,
  }) {
    return _then(_self.copyWith(
      applicantAnswers: null == applicantAnswers
          ? _self.applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>,
      questions: null == questions
          ? _self.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      applicantSavedAnswers: null == applicantSavedAnswers
          ? _self.applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QualificationTestType?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PreQualificationTest implements PreQualificationTest {
  const _PreQualificationTest(
      {@JsonKey(defaultValue: []) required final List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: []) required final List<Question> questions,
      @JsonKey(defaultValue: {})
      required final Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      this.id,
      this.randomized,
      this.title,
      this.description,
      this.minimumScore,
      this.timeLimit,
      this.mandatory,
      this.finalScore,
      this.resolutionDate,
      this.firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.PRE) this.type})
      : _applicantAnswers = applicantAnswers,
        _questions = questions,
        _applicantSavedAnswers = applicantSavedAnswers;
  factory _PreQualificationTest.fromJson(Map<String, dynamic> json) =>
      _$PreQualificationTestFromJson(json);

  final List<ApplicantAnswer> _applicantAnswers;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer> get applicantAnswers {
    if (_applicantAnswers is EqualUnmodifiableListView) return _applicantAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantAnswers);
  }

  final List<Question> _questions;
  @override
  @JsonKey(defaultValue: [])
  List<Question> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  final Map<String, ApplicantSavedAnswer> _applicantSavedAnswers;
  @override
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer> get applicantSavedAnswers {
    if (_applicantSavedAnswers is EqualUnmodifiableMapView) return _applicantSavedAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_applicantSavedAnswers);
  }

  @override
  final String? id;
  @override
  final bool? randomized;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final int? minimumScore;
  @override
  final int? timeLimit;
  @override
  final bool? mandatory;
  @override
  final int? finalScore;
  @override
  final DateTime? resolutionDate;
  @override
  final bool? firstAttempt;
  @override
  @JsonKey(defaultValue: QualificationTestType.PRE)
  final QualificationTestType? type;

  /// Create a copy of PreQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PreQualificationTestCopyWith<_PreQualificationTest> get copyWith =>
      __$PreQualificationTestCopyWithImpl<_PreQualificationTest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PreQualificationTestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PreQualificationTest &&
            const DeepCollectionEquality().equals(other._applicantAnswers, _applicantAnswers) &&
            const DeepCollectionEquality().equals(other._questions, _questions) &&
            const DeepCollectionEquality()
                .equals(other._applicantSavedAnswers, _applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_applicantAnswers),
      const DeepCollectionEquality().hash(_questions),
      const DeepCollectionEquality().hash(_applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      resolutionDate,
      firstAttempt,
      type);

  @override
  String toString() {
    return 'PreQualificationTest(applicantAnswers: $applicantAnswers, questions: $questions, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt, type: $type)';
  }
}

/// @nodoc
abstract mixin class _$PreQualificationTestCopyWith<$Res>
    implements $PreQualificationTestCopyWith<$Res> {
  factory _$PreQualificationTestCopyWith(
          _PreQualificationTest value, $Res Function(_PreQualificationTest) _then) =
      __$PreQualificationTestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: []) List<Question> questions,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      DateTime? resolutionDate,
      bool? firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.PRE) QualificationTestType? type});
}

/// @nodoc
class __$PreQualificationTestCopyWithImpl<$Res> implements _$PreQualificationTestCopyWith<$Res> {
  __$PreQualificationTestCopyWithImpl(this._self, this._then);

  final _PreQualificationTest _self;
  final $Res Function(_PreQualificationTest) _then;

  /// Create a copy of PreQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? applicantAnswers = null,
    Object? questions = null,
    Object? applicantSavedAnswers = null,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
    Object? type = freezed,
  }) {
    return _then(_PreQualificationTest(
      applicantAnswers: null == applicantAnswers
          ? _self._applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>,
      questions: null == questions
          ? _self._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      applicantSavedAnswers: null == applicantSavedAnswers
          ? _self._applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QualificationTestType?,
    ));
  }
}

/// @nodoc
mixin _$PostQualificationTest {
  @JsonKey(defaultValue: [])
  List<Question> get questions;
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer> get applicantAnswers;
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer> get applicantSavedAnswers;
  String? get id;
  bool? get randomized;
  String? get title;
  String? get description;
  int? get minimumScore;
  int? get timeLimit;
  bool? get mandatory;
  int? get finalScore;
  DateTime? get resolutionDate;
  bool? get firstAttempt;
  @JsonKey(defaultValue: QualificationTestType.POST)
  QualificationTestType? get type;

  /// Create a copy of PostQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PostQualificationTestCopyWith<PostQualificationTest> get copyWith =>
      _$PostQualificationTestCopyWithImpl<PostQualificationTest>(
          this as PostQualificationTest, _$identity);

  /// Serializes this PostQualificationTest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PostQualificationTest &&
            const DeepCollectionEquality().equals(other.questions, questions) &&
            const DeepCollectionEquality().equals(other.applicantAnswers, applicantAnswers) &&
            const DeepCollectionEquality()
                .equals(other.applicantSavedAnswers, applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(questions),
      const DeepCollectionEquality().hash(applicantAnswers),
      const DeepCollectionEquality().hash(applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      resolutionDate,
      firstAttempt,
      type);

  @override
  String toString() {
    return 'PostQualificationTest(questions: $questions, applicantAnswers: $applicantAnswers, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt, type: $type)';
  }
}

/// @nodoc
abstract mixin class $PostQualificationTestCopyWith<$Res> {
  factory $PostQualificationTestCopyWith(
          PostQualificationTest value, $Res Function(PostQualificationTest) _then) =
      _$PostQualificationTestCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<Question> questions,
      @JsonKey(defaultValue: []) List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      DateTime? resolutionDate,
      bool? firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.POST) QualificationTestType? type});
}

/// @nodoc
class _$PostQualificationTestCopyWithImpl<$Res> implements $PostQualificationTestCopyWith<$Res> {
  _$PostQualificationTestCopyWithImpl(this._self, this._then);

  final PostQualificationTest _self;
  final $Res Function(PostQualificationTest) _then;

  /// Create a copy of PostQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? questions = null,
    Object? applicantAnswers = null,
    Object? applicantSavedAnswers = null,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
    Object? type = freezed,
  }) {
    return _then(_self.copyWith(
      questions: null == questions
          ? _self.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      applicantAnswers: null == applicantAnswers
          ? _self.applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>,
      applicantSavedAnswers: null == applicantSavedAnswers
          ? _self.applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QualificationTestType?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PostQualificationTest implements PostQualificationTest {
  const _PostQualificationTest(
      {@JsonKey(defaultValue: []) required final List<Question> questions,
      @JsonKey(defaultValue: []) required final List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: {})
      required final Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      this.id,
      this.randomized,
      this.title,
      this.description,
      this.minimumScore,
      this.timeLimit,
      this.mandatory,
      this.finalScore,
      this.resolutionDate,
      this.firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.POST) this.type})
      : _questions = questions,
        _applicantAnswers = applicantAnswers,
        _applicantSavedAnswers = applicantSavedAnswers;
  factory _PostQualificationTest.fromJson(Map<String, dynamic> json) =>
      _$PostQualificationTestFromJson(json);

  final List<Question> _questions;
  @override
  @JsonKey(defaultValue: [])
  List<Question> get questions {
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_questions);
  }

  final List<ApplicantAnswer> _applicantAnswers;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer> get applicantAnswers {
    if (_applicantAnswers is EqualUnmodifiableListView) return _applicantAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantAnswers);
  }

  final Map<String, ApplicantSavedAnswer> _applicantSavedAnswers;
  @override
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer> get applicantSavedAnswers {
    if (_applicantSavedAnswers is EqualUnmodifiableMapView) return _applicantSavedAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_applicantSavedAnswers);
  }

  @override
  final String? id;
  @override
  final bool? randomized;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final int? minimumScore;
  @override
  final int? timeLimit;
  @override
  final bool? mandatory;
  @override
  final int? finalScore;
  @override
  final DateTime? resolutionDate;
  @override
  final bool? firstAttempt;
  @override
  @JsonKey(defaultValue: QualificationTestType.POST)
  final QualificationTestType? type;

  /// Create a copy of PostQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PostQualificationTestCopyWith<_PostQualificationTest> get copyWith =>
      __$PostQualificationTestCopyWithImpl<_PostQualificationTest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PostQualificationTestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PostQualificationTest &&
            const DeepCollectionEquality().equals(other._questions, _questions) &&
            const DeepCollectionEquality().equals(other._applicantAnswers, _applicantAnswers) &&
            const DeepCollectionEquality()
                .equals(other._applicantSavedAnswers, _applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_questions),
      const DeepCollectionEquality().hash(_applicantAnswers),
      const DeepCollectionEquality().hash(_applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      resolutionDate,
      firstAttempt,
      type);

  @override
  String toString() {
    return 'PostQualificationTest(questions: $questions, applicantAnswers: $applicantAnswers, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt, type: $type)';
  }
}

/// @nodoc
abstract mixin class _$PostQualificationTestCopyWith<$Res>
    implements $PostQualificationTestCopyWith<$Res> {
  factory _$PostQualificationTestCopyWith(
          _PostQualificationTest value, $Res Function(_PostQualificationTest) _then) =
      __$PostQualificationTestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<Question> questions,
      @JsonKey(defaultValue: []) List<ApplicantAnswer> applicantAnswers,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      DateTime? resolutionDate,
      bool? firstAttempt,
      @JsonKey(defaultValue: QualificationTestType.POST) QualificationTestType? type});
}

/// @nodoc
class __$PostQualificationTestCopyWithImpl<$Res> implements _$PostQualificationTestCopyWith<$Res> {
  __$PostQualificationTestCopyWithImpl(this._self, this._then);

  final _PostQualificationTest _self;
  final $Res Function(_PostQualificationTest) _then;

  /// Create a copy of PostQualificationTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? questions = null,
    Object? applicantAnswers = null,
    Object? applicantSavedAnswers = null,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
    Object? type = freezed,
  }) {
    return _then(_PostQualificationTest(
      questions: null == questions
          ? _self._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>,
      applicantAnswers: null == applicantAnswers
          ? _self._applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>,
      applicantSavedAnswers: null == applicantSavedAnswers
          ? _self._applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: freezed == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as QualificationTestType?,
    ));
  }
}

/// @nodoc
mixin _$Question {
  String get id;
  @JsonKey(defaultValue: [])
  List<Answer> get answers;
  DateTime? get lastModifiedDate;
  String? get questionType;
  String? get question;
  int? get index;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuestionCopyWith<Question> get copyWith =>
      _$QuestionCopyWithImpl<Question>(this as Question, _$identity);

  /// Serializes this Question to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Question &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other.answers, answers) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.questionType, questionType) || other.questionType == questionType) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, const DeepCollectionEquality().hash(answers),
      lastModifiedDate, questionType, question, index);

  @override
  String toString() {
    return 'Question(id: $id, answers: $answers, lastModifiedDate: $lastModifiedDate, questionType: $questionType, question: $question, index: $index)';
  }
}

/// @nodoc
abstract mixin class $QuestionCopyWith<$Res> {
  factory $QuestionCopyWith(Question value, $Res Function(Question) _then) = _$QuestionCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      @JsonKey(defaultValue: []) List<Answer> answers,
      DateTime? lastModifiedDate,
      String? questionType,
      String? question,
      int? index});
}

/// @nodoc
class _$QuestionCopyWithImpl<$Res> implements $QuestionCopyWith<$Res> {
  _$QuestionCopyWithImpl(this._self, this._then);

  final Question _self;
  final $Res Function(Question) _then;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? answers = null,
    Object? lastModifiedDate = freezed,
    Object? questionType = freezed,
    Object? question = freezed,
    Object? index = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _self.answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<Answer>,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      questionType: freezed == questionType
          ? _self.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _Question implements Question {
  const _Question(
      {required this.id,
      @JsonKey(defaultValue: []) required final List<Answer> answers,
      this.lastModifiedDate,
      this.questionType,
      this.question,
      this.index})
      : _answers = answers;
  factory _Question.fromJson(Map<String, dynamic> json) => _$QuestionFromJson(json);

  @override
  final String id;
  final List<Answer> _answers;
  @override
  @JsonKey(defaultValue: [])
  List<Answer> get answers {
    if (_answers is EqualUnmodifiableListView) return _answers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_answers);
  }

  @override
  final DateTime? lastModifiedDate;
  @override
  final String? questionType;
  @override
  final String? question;
  @override
  final int? index;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuestionCopyWith<_Question> get copyWith =>
      __$QuestionCopyWithImpl<_Question>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuestionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Question &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._answers, _answers) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.questionType, questionType) || other.questionType == questionType) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.index, index) || other.index == index));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, const DeepCollectionEquality().hash(_answers),
      lastModifiedDate, questionType, question, index);

  @override
  String toString() {
    return 'Question(id: $id, answers: $answers, lastModifiedDate: $lastModifiedDate, questionType: $questionType, question: $question, index: $index)';
  }
}

/// @nodoc
abstract mixin class _$QuestionCopyWith<$Res> implements $QuestionCopyWith<$Res> {
  factory _$QuestionCopyWith(_Question value, $Res Function(_Question) _then) =
      __$QuestionCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      @JsonKey(defaultValue: []) List<Answer> answers,
      DateTime? lastModifiedDate,
      String? questionType,
      String? question,
      int? index});
}

/// @nodoc
class __$QuestionCopyWithImpl<$Res> implements _$QuestionCopyWith<$Res> {
  __$QuestionCopyWithImpl(this._self, this._then);

  final _Question _self;
  final $Res Function(_Question) _then;

  /// Create a copy of Question
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? answers = null,
    Object? lastModifiedDate = freezed,
    Object? questionType = freezed,
    Object? question = freezed,
    Object? index = freezed,
  }) {
    return _then(_Question(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      answers: null == answers
          ? _self._answers
          : answers // ignore: cast_nullable_to_non_nullable
              as List<Answer>,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      questionType: freezed == questionType
          ? _self.questionType
          : questionType // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      index: freezed == index
          ? _self.index
          : index // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
mixin _$ApplicantAnswer {
  String? get questionId;
  bool? get correct;
  String? get questionTitle;

  /// Create a copy of ApplicantAnswer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApplicantAnswerCopyWith<ApplicantAnswer> get copyWith =>
      _$ApplicantAnswerCopyWithImpl<ApplicantAnswer>(this as ApplicantAnswer, _$identity);

  /// Serializes this ApplicantAnswer to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApplicantAnswer &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.correct, correct) || other.correct == correct) &&
            (identical(other.questionTitle, questionTitle) ||
                other.questionTitle == questionTitle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, questionId, correct, questionTitle);

  @override
  String toString() {
    return 'ApplicantAnswer(questionId: $questionId, correct: $correct, questionTitle: $questionTitle)';
  }
}

/// @nodoc
abstract mixin class $ApplicantAnswerCopyWith<$Res> {
  factory $ApplicantAnswerCopyWith(ApplicantAnswer value, $Res Function(ApplicantAnswer) _then) =
      _$ApplicantAnswerCopyWithImpl;
  @useResult
  $Res call({String? questionId, bool? correct, String? questionTitle});
}

/// @nodoc
class _$ApplicantAnswerCopyWithImpl<$Res> implements $ApplicantAnswerCopyWith<$Res> {
  _$ApplicantAnswerCopyWithImpl(this._self, this._then);

  final ApplicantAnswer _self;
  final $Res Function(ApplicantAnswer) _then;

  /// Create a copy of ApplicantAnswer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? questionId = freezed,
    Object? correct = freezed,
    Object? questionTitle = freezed,
  }) {
    return _then(_self.copyWith(
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
      questionTitle: freezed == questionTitle
          ? _self.questionTitle
          : questionTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ApplicantAnswer implements ApplicantAnswer {
  const _ApplicantAnswer({this.questionId, this.correct, this.questionTitle});
  factory _ApplicantAnswer.fromJson(Map<String, dynamic> json) => _$ApplicantAnswerFromJson(json);

  @override
  final String? questionId;
  @override
  final bool? correct;
  @override
  final String? questionTitle;

  /// Create a copy of ApplicantAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplicantAnswerCopyWith<_ApplicantAnswer> get copyWith =>
      __$ApplicantAnswerCopyWithImpl<_ApplicantAnswer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApplicantAnswerToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplicantAnswer &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.correct, correct) || other.correct == correct) &&
            (identical(other.questionTitle, questionTitle) ||
                other.questionTitle == questionTitle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, questionId, correct, questionTitle);

  @override
  String toString() {
    return 'ApplicantAnswer(questionId: $questionId, correct: $correct, questionTitle: $questionTitle)';
  }
}

/// @nodoc
abstract mixin class _$ApplicantAnswerCopyWith<$Res> implements $ApplicantAnswerCopyWith<$Res> {
  factory _$ApplicantAnswerCopyWith(_ApplicantAnswer value, $Res Function(_ApplicantAnswer) _then) =
      __$ApplicantAnswerCopyWithImpl;
  @override
  @useResult
  $Res call({String? questionId, bool? correct, String? questionTitle});
}

/// @nodoc
class __$ApplicantAnswerCopyWithImpl<$Res> implements _$ApplicantAnswerCopyWith<$Res> {
  __$ApplicantAnswerCopyWithImpl(this._self, this._then);

  final _ApplicantAnswer _self;
  final $Res Function(_ApplicantAnswer) _then;

  /// Create a copy of ApplicantAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? questionId = freezed,
    Object? correct = freezed,
    Object? questionTitle = freezed,
  }) {
    return _then(_ApplicantAnswer(
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      correct: freezed == correct
          ? _self.correct
          : correct // ignore: cast_nullable_to_non_nullable
              as bool?,
      questionTitle: freezed == questionTitle
          ? _self.questionTitle
          : questionTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$ApplicantSavedAnswer {
  String? get questionId;
  String? get question;
  String? get answerId;

  /// Create a copy of ApplicantSavedAnswer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApplicantSavedAnswerCopyWith<ApplicantSavedAnswer> get copyWith =>
      _$ApplicantSavedAnswerCopyWithImpl<ApplicantSavedAnswer>(
          this as ApplicantSavedAnswer, _$identity);

  /// Serializes this ApplicantSavedAnswer to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApplicantSavedAnswer &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.answerId, answerId) || other.answerId == answerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, questionId, question, answerId);

  @override
  String toString() {
    return 'ApplicantSavedAnswer(questionId: $questionId, question: $question, answerId: $answerId)';
  }
}

/// @nodoc
abstract mixin class $ApplicantSavedAnswerCopyWith<$Res> {
  factory $ApplicantSavedAnswerCopyWith(
          ApplicantSavedAnswer value, $Res Function(ApplicantSavedAnswer) _then) =
      _$ApplicantSavedAnswerCopyWithImpl;
  @useResult
  $Res call({String? questionId, String? question, String? answerId});
}

/// @nodoc
class _$ApplicantSavedAnswerCopyWithImpl<$Res> implements $ApplicantSavedAnswerCopyWith<$Res> {
  _$ApplicantSavedAnswerCopyWithImpl(this._self, this._then);

  final ApplicantSavedAnswer _self;
  final $Res Function(ApplicantSavedAnswer) _then;

  /// Create a copy of ApplicantSavedAnswer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? questionId = freezed,
    Object? question = freezed,
    Object? answerId = freezed,
  }) {
    return _then(_self.copyWith(
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      answerId: freezed == answerId
          ? _self.answerId
          : answerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ApplicantSavedAnswer implements ApplicantSavedAnswer {
  const _ApplicantSavedAnswer({this.questionId, this.question, this.answerId});
  factory _ApplicantSavedAnswer.fromJson(Map<String, dynamic> json) =>
      _$ApplicantSavedAnswerFromJson(json);

  @override
  final String? questionId;
  @override
  final String? question;
  @override
  final String? answerId;

  /// Create a copy of ApplicantSavedAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplicantSavedAnswerCopyWith<_ApplicantSavedAnswer> get copyWith =>
      __$ApplicantSavedAnswerCopyWithImpl<_ApplicantSavedAnswer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApplicantSavedAnswerToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplicantSavedAnswer &&
            (identical(other.questionId, questionId) || other.questionId == questionId) &&
            (identical(other.question, question) || other.question == question) &&
            (identical(other.answerId, answerId) || other.answerId == answerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, questionId, question, answerId);

  @override
  String toString() {
    return 'ApplicantSavedAnswer(questionId: $questionId, question: $question, answerId: $answerId)';
  }
}

/// @nodoc
abstract mixin class _$ApplicantSavedAnswerCopyWith<$Res>
    implements $ApplicantSavedAnswerCopyWith<$Res> {
  factory _$ApplicantSavedAnswerCopyWith(
          _ApplicantSavedAnswer value, $Res Function(_ApplicantSavedAnswer) _then) =
      __$ApplicantSavedAnswerCopyWithImpl;
  @override
  @useResult
  $Res call({String? questionId, String? question, String? answerId});
}

/// @nodoc
class __$ApplicantSavedAnswerCopyWithImpl<$Res> implements _$ApplicantSavedAnswerCopyWith<$Res> {
  __$ApplicantSavedAnswerCopyWithImpl(this._self, this._then);

  final _ApplicantSavedAnswer _self;
  final $Res Function(_ApplicantSavedAnswer) _then;

  /// Create a copy of ApplicantSavedAnswer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? questionId = freezed,
    Object? question = freezed,
    Object? answerId = freezed,
  }) {
    return _then(_ApplicantSavedAnswer(
      questionId: freezed == questionId
          ? _self.questionId
          : questionId // ignore: cast_nullable_to_non_nullable
              as String?,
      question: freezed == question
          ? _self.question
          : question // ignore: cast_nullable_to_non_nullable
              as String?,
      answerId: freezed == answerId
          ? _self.answerId
          : answerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$HistoryOfTest {
  @JsonKey(defaultValue: [])
  List<Question>? get questions;
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer>? get applicantSavedAnswers;
  String? get id;
  bool? get randomized;
  String? get title;
  String? get description;
  int? get minimumScore;
  int? get timeLimit;
  bool? get mandatory;
  int? get finalScore;
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer>? get applicantAnswers;
  DateTime? get resolutionDate;
  bool? get firstAttempt;

  /// Create a copy of HistoryOfTest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HistoryOfTestCopyWith<HistoryOfTest> get copyWith =>
      _$HistoryOfTestCopyWithImpl<HistoryOfTest>(this as HistoryOfTest, _$identity);

  /// Serializes this HistoryOfTest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HistoryOfTest &&
            const DeepCollectionEquality().equals(other.questions, questions) &&
            const DeepCollectionEquality()
                .equals(other.applicantSavedAnswers, applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            const DeepCollectionEquality().equals(other.applicantAnswers, applicantAnswers) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(questions),
      const DeepCollectionEquality().hash(applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      const DeepCollectionEquality().hash(applicantAnswers),
      resolutionDate,
      firstAttempt);

  @override
  String toString() {
    return 'HistoryOfTest(questions: $questions, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, applicantAnswers: $applicantAnswers, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt)';
  }
}

/// @nodoc
abstract mixin class $HistoryOfTestCopyWith<$Res> {
  factory $HistoryOfTestCopyWith(HistoryOfTest value, $Res Function(HistoryOfTest) _then) =
      _$HistoryOfTestCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<Question>? questions,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer>? applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      @JsonKey(defaultValue: []) List<ApplicantAnswer>? applicantAnswers,
      DateTime? resolutionDate,
      bool? firstAttempt});
}

/// @nodoc
class _$HistoryOfTestCopyWithImpl<$Res> implements $HistoryOfTestCopyWith<$Res> {
  _$HistoryOfTestCopyWithImpl(this._self, this._then);

  final HistoryOfTest _self;
  final $Res Function(HistoryOfTest) _then;

  /// Create a copy of HistoryOfTest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? questions = freezed,
    Object? applicantSavedAnswers = freezed,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? applicantAnswers = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
  }) {
    return _then(_self.copyWith(
      questions: freezed == questions
          ? _self.questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>?,
      applicantSavedAnswers: freezed == applicantSavedAnswers
          ? _self.applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      applicantAnswers: freezed == applicantAnswers
          ? _self.applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _HistoryOfTest implements HistoryOfTest {
  const _HistoryOfTest(
      {@JsonKey(defaultValue: []) final List<Question>? questions,
      @JsonKey(defaultValue: {}) final Map<String, ApplicantSavedAnswer>? applicantSavedAnswers,
      this.id,
      this.randomized,
      this.title,
      this.description,
      this.minimumScore,
      this.timeLimit,
      this.mandatory,
      this.finalScore,
      @JsonKey(defaultValue: []) final List<ApplicantAnswer>? applicantAnswers,
      this.resolutionDate,
      this.firstAttempt})
      : _questions = questions,
        _applicantSavedAnswers = applicantSavedAnswers,
        _applicantAnswers = applicantAnswers;
  factory _HistoryOfTest.fromJson(Map<String, dynamic> json) => _$HistoryOfTestFromJson(json);

  final List<Question>? _questions;
  @override
  @JsonKey(defaultValue: [])
  List<Question>? get questions {
    final value = _questions;
    if (value == null) return null;
    if (_questions is EqualUnmodifiableListView) return _questions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final Map<String, ApplicantSavedAnswer>? _applicantSavedAnswers;
  @override
  @JsonKey(defaultValue: {})
  Map<String, ApplicantSavedAnswer>? get applicantSavedAnswers {
    final value = _applicantSavedAnswers;
    if (value == null) return null;
    if (_applicantSavedAnswers is EqualUnmodifiableMapView) return _applicantSavedAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final String? id;
  @override
  final bool? randomized;
  @override
  final String? title;
  @override
  final String? description;
  @override
  final int? minimumScore;
  @override
  final int? timeLimit;
  @override
  final bool? mandatory;
  @override
  final int? finalScore;
  final List<ApplicantAnswer>? _applicantAnswers;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantAnswer>? get applicantAnswers {
    final value = _applicantAnswers;
    if (value == null) return null;
    if (_applicantAnswers is EqualUnmodifiableListView) return _applicantAnswers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? resolutionDate;
  @override
  final bool? firstAttempt;

  /// Create a copy of HistoryOfTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HistoryOfTestCopyWith<_HistoryOfTest> get copyWith =>
      __$HistoryOfTestCopyWithImpl<_HistoryOfTest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$HistoryOfTestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HistoryOfTest &&
            const DeepCollectionEquality().equals(other._questions, _questions) &&
            const DeepCollectionEquality()
                .equals(other._applicantSavedAnswers, _applicantSavedAnswers) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.randomized, randomized) || other.randomized == randomized) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) || other.description == description) &&
            (identical(other.minimumScore, minimumScore) || other.minimumScore == minimumScore) &&
            (identical(other.timeLimit, timeLimit) || other.timeLimit == timeLimit) &&
            (identical(other.mandatory, mandatory) || other.mandatory == mandatory) &&
            (identical(other.finalScore, finalScore) || other.finalScore == finalScore) &&
            const DeepCollectionEquality().equals(other._applicantAnswers, _applicantAnswers) &&
            (identical(other.resolutionDate, resolutionDate) ||
                other.resolutionDate == resolutionDate) &&
            (identical(other.firstAttempt, firstAttempt) || other.firstAttempt == firstAttempt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_questions),
      const DeepCollectionEquality().hash(_applicantSavedAnswers),
      id,
      randomized,
      title,
      description,
      minimumScore,
      timeLimit,
      mandatory,
      finalScore,
      const DeepCollectionEquality().hash(_applicantAnswers),
      resolutionDate,
      firstAttempt);

  @override
  String toString() {
    return 'HistoryOfTest(questions: $questions, applicantSavedAnswers: $applicantSavedAnswers, id: $id, randomized: $randomized, title: $title, description: $description, minimumScore: $minimumScore, timeLimit: $timeLimit, mandatory: $mandatory, finalScore: $finalScore, applicantAnswers: $applicantAnswers, resolutionDate: $resolutionDate, firstAttempt: $firstAttempt)';
  }
}

/// @nodoc
abstract mixin class _$HistoryOfTestCopyWith<$Res> implements $HistoryOfTestCopyWith<$Res> {
  factory _$HistoryOfTestCopyWith(_HistoryOfTest value, $Res Function(_HistoryOfTest) _then) =
      __$HistoryOfTestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<Question>? questions,
      @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer>? applicantSavedAnswers,
      String? id,
      bool? randomized,
      String? title,
      String? description,
      int? minimumScore,
      int? timeLimit,
      bool? mandatory,
      int? finalScore,
      @JsonKey(defaultValue: []) List<ApplicantAnswer>? applicantAnswers,
      DateTime? resolutionDate,
      bool? firstAttempt});
}

/// @nodoc
class __$HistoryOfTestCopyWithImpl<$Res> implements _$HistoryOfTestCopyWith<$Res> {
  __$HistoryOfTestCopyWithImpl(this._self, this._then);

  final _HistoryOfTest _self;
  final $Res Function(_HistoryOfTest) _then;

  /// Create a copy of HistoryOfTest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? questions = freezed,
    Object? applicantSavedAnswers = freezed,
    Object? id = freezed,
    Object? randomized = freezed,
    Object? title = freezed,
    Object? description = freezed,
    Object? minimumScore = freezed,
    Object? timeLimit = freezed,
    Object? mandatory = freezed,
    Object? finalScore = freezed,
    Object? applicantAnswers = freezed,
    Object? resolutionDate = freezed,
    Object? firstAttempt = freezed,
  }) {
    return _then(_HistoryOfTest(
      questions: freezed == questions
          ? _self._questions
          : questions // ignore: cast_nullable_to_non_nullable
              as List<Question>?,
      applicantSavedAnswers: freezed == applicantSavedAnswers
          ? _self._applicantSavedAnswers
          : applicantSavedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, ApplicantSavedAnswer>?,
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      randomized: freezed == randomized
          ? _self.randomized
          : randomized // ignore: cast_nullable_to_non_nullable
              as bool?,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      minimumScore: freezed == minimumScore
          ? _self.minimumScore
          : minimumScore // ignore: cast_nullable_to_non_nullable
              as int?,
      timeLimit: freezed == timeLimit
          ? _self.timeLimit
          : timeLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      mandatory: freezed == mandatory
          ? _self.mandatory
          : mandatory // ignore: cast_nullable_to_non_nullable
              as bool?,
      finalScore: freezed == finalScore
          ? _self.finalScore
          : finalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      applicantAnswers: freezed == applicantAnswers
          ? _self._applicantAnswers
          : applicantAnswers // ignore: cast_nullable_to_non_nullable
              as List<ApplicantAnswer>?,
      resolutionDate: freezed == resolutionDate
          ? _self.resolutionDate
          : resolutionDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      firstAttempt: freezed == firstAttempt
          ? _self.firstAttempt
          : firstAttempt // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

// dart format on
