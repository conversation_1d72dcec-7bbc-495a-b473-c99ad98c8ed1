import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';

part 'training_consumption_model.freezed.dart';

part 'training_consumption_model.g.dart';

abstract class QualificationTestModel {
  List<ApplicantAnswer> get applicantAnswers;

  List<Question> get questions;

  Map<String, ApplicantSavedAnswer> get applicantSavedAnswers;

  String? get id;

  bool? get randomized;

  String? get title;

  String? get description;

  int? get minimumScore;

  int? get timeLimit;

  bool? get mandatory;

  int? get finalScore;

  DateTime? get resolutionDate;

  bool? get firstAttempt;

  QualificationTestType? get type;
}

@freezed
abstract class TrainingConsumptionModel with _$TrainingConsumptionModel {
  const factory TrainingConsumptionModel({
    required String id,
    required String trainingId,
    required String status,
    required List<Progress> progress,
    required int passedScore,
    @JsonKey(defaultValue: 0) required int allSections,
    required int passedSections,
    required DateTime? enrolledDate,
    @JsonKey(defaultValue: []) required List<HistoryOfTest> historyOfTests,
    @Default({})
    @JsonKey(includeFromJson: false, includeToJson: false)
    Set<String> completedLessonsIDs,
    DateTime? completedDate,
    int? certificateId,
    PreQualificationTest? preQualificationTest,
    PostQualificationTest? postQualificationTest,
    bool? preTestPassed,
  }) = _TrainingConsumptionModel;

  factory TrainingConsumptionModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingConsumptionModelFromJson(json);
}

@freezed
abstract class Progress with _$Progress {
  const factory Progress({
    required String lessonId,
    bool? isPassed,
    @JsonKey(includeToJson: false) QuizResult? quizResult,
  }) = _Progress;

  factory Progress.fromJson(Map<String, dynamic> json) => _$ProgressFromJson(json);
}

@freezed
abstract class QuestionAnswerPair with _$QuestionAnswerPair {
  const factory QuestionAnswerPair({
    @JsonKey(defaultValue: []) required List<String?> answerIds,
    String? questionId,
    String? question,
    bool? correct,
  }) = _QuestionAnswerPair;

  factory QuestionAnswerPair.fromJson(Map<String, dynamic> json) =>
      _$QuestionAnswerPairFromJson(json);
}

@freezed
abstract class PreQualificationTest with _$PreQualificationTest implements QualificationTestModel {
  const factory PreQualificationTest({
    @JsonKey(defaultValue: []) required List<ApplicantAnswer> applicantAnswers,
    @JsonKey(defaultValue: []) required List<Question> questions,
    @JsonKey(defaultValue: {}) required Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
    String? id,
    bool? randomized,
    String? title,
    String? description,
    int? minimumScore,
    int? timeLimit,
    bool? mandatory,
    int? finalScore,
    DateTime? resolutionDate,
    bool? firstAttempt,
    @JsonKey(defaultValue: QualificationTestType.PRE) QualificationTestType? type,
  }) = _PreQualificationTest;

  factory PreQualificationTest.fromJson(Map<String, dynamic> json) =>
      _$PreQualificationTestFromJson(json);
}

@freezed
abstract class PostQualificationTest
    with _$PostQualificationTest
    implements QualificationTestModel {
  const factory PostQualificationTest({
    @JsonKey(defaultValue: []) required List<Question> questions,
    @JsonKey(defaultValue: []) required List<ApplicantAnswer> applicantAnswers,
    @JsonKey(defaultValue: {}) required Map<String, ApplicantSavedAnswer> applicantSavedAnswers,
    String? id,
    bool? randomized,
    String? title,
    String? description,
    int? minimumScore,
    int? timeLimit,
    bool? mandatory,
    int? finalScore,
    DateTime? resolutionDate,
    bool? firstAttempt,
    @JsonKey(defaultValue: QualificationTestType.POST) QualificationTestType? type,
  }) = _PostQualificationTest;

  factory PostQualificationTest.fromJson(Map<String, dynamic> json) =>
      _$PostQualificationTestFromJson(json);
}

@freezed
abstract class Question with _$Question {
  const factory Question({
    required String id,
    @JsonKey(defaultValue: []) required List<Answer> answers,
    DateTime? lastModifiedDate,
    String? questionType,
    String? question,
    int? index,
  }) = _Question;

  factory Question.fromJson(Map<String, dynamic> json) => _$QuestionFromJson(json);
}

@freezed
abstract class ApplicantAnswer with _$ApplicantAnswer {
  const factory ApplicantAnswer({String? questionId, bool? correct, String? questionTitle}) =
      _ApplicantAnswer;

  factory ApplicantAnswer.fromJson(Map<String, dynamic> json) => _$ApplicantAnswerFromJson(json);
}

@freezed
abstract class ApplicantSavedAnswer with _$ApplicantSavedAnswer {
  const factory ApplicantSavedAnswer({String? questionId, String? question, String? answerId}) =
      _ApplicantSavedAnswer;

  factory ApplicantSavedAnswer.fromJson(Map<String, dynamic> json) =>
      _$ApplicantSavedAnswerFromJson(json);
}

@freezed
abstract class HistoryOfTest with _$HistoryOfTest {
  const factory HistoryOfTest({
    @JsonKey(defaultValue: []) List<Question>? questions,
    @JsonKey(defaultValue: {}) Map<String, ApplicantSavedAnswer>? applicantSavedAnswers,
    String? id,
    bool? randomized,
    String? title,
    String? description,
    int? minimumScore,
    int? timeLimit,
    bool? mandatory,
    int? finalScore,
    @JsonKey(defaultValue: []) List<ApplicantAnswer>? applicantAnswers,
    DateTime? resolutionDate,
    bool? firstAttempt,
  }) = _HistoryOfTest;

  factory HistoryOfTest.fromJson(Map<String, dynamic> json) => _$HistoryOfTestFromJson(json);
}
