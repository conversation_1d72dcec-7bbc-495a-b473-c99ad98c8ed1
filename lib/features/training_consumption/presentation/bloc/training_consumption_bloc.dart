import 'dart:async';
import 'dart:io';

import 'package:background_downloader/background_downloader.dart';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/params/qualification_test_params.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/live_session_params.dart';
import 'package:national_skills_platform/features/training_consumption/domain/repositories/training_consumption_repository.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

part 'training_consumption_bloc.freezed.dart';

part 'training_consumption_event.dart';

part 'training_consumption_state.dart';

@injectable
class TrainingConsumptionBloc extends Bloc<TrainingConsumptionEvent, TrainingConsumptionState> {
  TrainingConsumptionBloc({required TrainingConsumptionRepository trainingConsumptionRepository})
      : _trainingConsumptionRepository = trainingConsumptionRepository,
        super(TrainingConsumptionState()) {
    on<LoadTrainingConsumptionPageEvent>(_loadTrainingConsumptionPageEvent);
    on<OpenLessonEvent>(_openLessonEvent);
    on<OpenLiveSessionLessonEvent>(_openLiveSessionLessonEvent);
    on<MarkLessonAsCompletedEvent>(_markLessonAsCompletedEvent);
    on<RefreshTrainingConsumptionPageEvent>(_refreshTrainingConsumptionPageEvent);
    on<GetVideoUrlEvent>(_getVideoUrl);
    on<GetSlideFilePathEvent>(_getSlideFilePath);
    on<DownloadFileEvent>(_downloadFile);
    on<OpenFileEvent>(_openFile);
    on<CancelAllDownloadsEvent>(_cancelAllDownloads);
    on<OpenPostQualificationTestEvent>(_openPostQualificationTest);
    on<OpenPreQualificationTestEvent>(_openPreQualificationTest);
    on<UpdatePreQualificationTestEvent>(_updatePreQualificationTest);
    on<UpdatePostQualificationTestEvent>(_updatePostQualificationTest);
  }

  final TrainingConsumptionRepository _trainingConsumptionRepository;
  late TrainingDetailsModel trainingDetailsModel;

  Future<void> _loadTrainingConsumptionPageEvent(
    LoadTrainingConsumptionPageEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    emit(TrainingConsumptionState(isLoading: true));
    trainingDetailsModel = event.trainingDetailsModel;

    await _trainingConsumptionRepository
        .getTrainingConsumptionDetails(trainingDetailsModel.id)
        .errorHandler(
          onSuccess: (trainingConsumptionModel) async {
            final uncompletedSectionIndex = findUncompletedSectionIndex(
              trainingConsumptionModel,
              trainingDetailsModel.trainingStructure,
            );
            emit(
              TrainingConsumptionState(
                trainingConsumptionModel: trainingConsumptionModel,
                uncompletedSectionIndex: uncompletedSectionIndex,
                trainingStructure: trainingDetailsModel.trainingStructure,
              ),
            );
          },
          onError: (errorMsg) => emit(TrainingConsumptionState(errorMessage: errorMsg)),
        );
  }

  Future<void> _openLiveSessionLessonEvent(
    OpenLiveSessionLessonEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    if (event.lessonNavigationType == LessonNavigationType.initial) {
      await router.pushNamed(
        Routes.liveSessionPage.name,
        extra: LiveSessionParams(
          meeting: event.meeting,
          trainingConsumptionBloc: this,
          sublesson: event.subLesson,
        ),
      );
    } else {
      await router.pushReplacementNamed(
        Routes.liveSessionPage.name,
        extra: LiveSessionParams(
          meeting: event.meeting,
          trainingConsumptionBloc: this,
          sublesson: event.subLesson,
        ),
      );
    }
  }

  Future<void> _openLessonEvent(OpenLessonEvent event, _) async {
    final lessonType = event.lessonParams.lesson.lessonType;

    Future<void> navigate(String pageName, {Object? extra}) async {
      final pushReplacement =
          event.lessonParams.lessonNavigationType != LessonNavigationType.initial;
      if (pushReplacement) {
        await router.pushReplacementNamed(pageName, extra: extra ?? event.lessonParams);
      } else {
        await router.pushNamed(pageName, extra: extra ?? event.lessonParams);
      }
    }

    if (lessonType != null) {
      switch (lessonType) {
        case LessonType.Article:
          await navigate(Routes.articlePage.name);
          _refreshTrainingConsumptionPage(event);
        case LessonType.Video:
          add(GetVideoUrlEvent(event.lessonParams.lesson));
          await navigate(Routes.videoLessonPage.name);
          _refreshTrainingConsumptionPage(event);
        case LessonType.File:
          await navigate(Routes.fileLessonPage.name);
          _refreshTrainingConsumptionPage(event);
        case LessonType.Slide:
          await navigate(Routes.slideLessonPage.name);
          _refreshTrainingConsumptionPage(event);
        case LessonType.Quiz:
          final quizResult = state.trainingConsumptionModel?.progress
              .firstWhereOrNull((progress) => progress.lessonId == event.lessonParams.lesson.id)
              ?.quizResult;
          await navigate(Routes.quizPage.name, extra: [event.lessonParams, quizResult]);
          _refreshTrainingConsumptionPage(event);
      }
    }
  }

  void _refreshTrainingConsumptionPage(OpenLessonEvent event) {
    if (!event.lessonParams.isCompleted) {
      //when you pop the lesson page, the training consumption page should be refreshed if lesson is not completed
      add(const RefreshTrainingConsumptionPageEvent());
    }
  }

  Future<void> _markLessonAsCompletedEvent(
    MarkLessonAsCompletedEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    final trainingConsumptionModel = state.trainingConsumptionModel;
    final trainingId = trainingConsumptionModel?.trainingId ?? '';
    final lessonId = event.lesson.id;

    await _trainingConsumptionRepository
        .markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId)
        .errorHandler(
          onSuccess: (_) async {
            if (trainingConsumptionModel == null) return;
            emit(
              state.copyWith(
                errorMessage: '', //clear error message if any
                trainingConsumptionModel: trainingConsumptionModel.copyWith(
                  completedLessonsIDs: {...trainingConsumptionModel.completedLessonsIDs, lessonId},
                ),
              ),
            );
          },
          onError: (errorMsg) => emit(state.copyWith(errorMessage: errorMsg)),
        );
  }

  Future<void> _refreshTrainingConsumptionPageEvent(
    RefreshTrainingConsumptionPageEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async =>
      add(LoadTrainingConsumptionPageEvent(trainingDetailsModel));

  Future<void> _getVideoUrl(GetVideoUrlEvent event, Emitter<TrainingConsumptionState> emit) async {
    final videoKey = event.lesson.resources?.first.key ?? '';
    emit(state.copyWith(isLoading: true, errorMessage: ''));

    await _trainingConsumptionRepository.getVideoUrl(videoKey).errorHandler(
          onSuccess: (videoLessonUrl) async =>
              emit(state.copyWith(videoLessonUrl: videoLessonUrl, isLoading: false)),
          onError: (errorMsg) => emit(state.copyWith(errorMessage: errorMsg, isLoading: false)),
        );
  }

  Future<void> _downloadFile(
    DownloadFileEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    emit(state.copyWith(downloadingFiles: {...state.downloadingFiles, event.fileKey}));

    await _trainingConsumptionRepository.downloadFile(event.fileKey, event.fileName).errorHandler(
      onSuccess: (_) async {
        final downloadingFiles = Set<String>.from(state.downloadingFiles)..remove(event.fileKey);
        emit(state.copyWith(downloadingFiles: downloadingFiles));
      },
      onError: (errorMsg) {
        final downloadingFiles = Set<String>.from(state.downloadingFiles)..remove(event.fileKey);
        emit(state.copyWith(errorMessage: errorMsg, downloadingFiles: downloadingFiles));
      },
    );
  }

  Future<void> _openFile(OpenFileEvent event, Emitter<TrainingConsumptionState> emit) async {
    final appDocumentsDir = await getApplicationDocumentsDirectory();

    await OpenFilex.open('${appDocumentsDir.absolute.path}/${event.fileName}');
  }

  int findUncompletedSectionIndex(
    TrainingConsumptionModel trainingConsumptionModel,
    TrainingStructureModel? trainingStructure,
  ) {
    if (trainingStructure == null) return trainingConsumptionModel.completedLessonsIDs.length;

    int uncompletedLessonIndexInList = trainingConsumptionModel.completedLessonsIDs.length;
    bool found = false;

    for (int sectionIndex = 0; sectionIndex < trainingStructure.sections.length; sectionIndex++) {
      final section = trainingStructure.sections[sectionIndex];

      for (int lessonIndex = 0; lessonIndex < section.lessons.length; lessonIndex++) {
        final lesson = section.lessons[lessonIndex];

        //if not completed
        if (!trainingConsumptionModel.completedLessonsIDs.contains(lesson.id)) {
          uncompletedLessonIndexInList = sectionIndex;
          found = true;
          break;
        }
      }

      if (found) break;
    }

    return uncompletedLessonIndexInList;
  }

  Future<void> _getSlideFilePath(
    GetSlideFilePathEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        slideFilePath: null,
        slideDownloadProgress: null,
        errorMessage: '',
      ),
    );

    final resource = event.lesson.resources?.first;

    if (resource == null) {
      return emit(
        state.copyWith(
          isLoading: false,
          errorMessage: LocaleKeys.somethingWentWrongPage_errorMessage.tr(),
        ),
      );
    }

    final slideName = resource.originalFilename ?? '';

    // Check if file already exists
    var slideFilePath = await getFilePath(slideName);

    if (slideFilePath != null) {
      return emit(state.copyWith(slideFilePath: slideFilePath, isLoading: false));
    }

    ///Starts downloading the slide if it doesn't exist
    await _trainingConsumptionRepository.downloadSlide(resource, (progress) {
      //clear loading because we are about to start downloading and emit download progress instead
      emit(state.copyWith(slideDownloadProgress: progress, isLoading: false));
    }).errorHandler(
      onSuccess: (_) async {
        slideFilePath = await getFilePath(slideName);
        emit(state.copyWith(slideFilePath: slideFilePath, slideDownloadProgress: null));
      },
      onError: (errorMsg) {
        emit(
          state.copyWith(
            errorMessage: errorMsg,
            slideDownloadProgress: null,
            isLoading: false,
          ),
        );
      },
    );
  }

  Future<String?> getFilePath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.absolute.path}/$fileName';

    if (File(filePath).existsSync()) {
      return filePath;
    }
    return null;
  }

  Future<void> _cancelAllDownloads(
    CancelAllDownloadsEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    emit(state.copyWith(downloadingFiles: {}));
    await FileDownloader().reset();
  }

  Future<void> _openPostQualificationTest(OpenPostQualificationTestEvent event, _) async {
    if (state.trainingConsumptionModel?.postQualificationTest != null) {
      await router.pushReplacementNamed(
        Routes.qualificationTestPage.name,
        extra: QualificationTestParams(
          qualificationTestModel: state.trainingConsumptionModel?.postQualificationTest,
          trainingConsumptionBloc: this,
          trainingId: state.trainingConsumptionModel?.trainingId ?? '',
          navigationType: LessonNavigationType.forward,
        ),
      );
      add(const RefreshTrainingConsumptionPageEvent());
    }
  }

  Future<void> _openPreQualificationTest(
    OpenPreQualificationTestEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) async {
    final preQualificationTest = state.trainingConsumptionModel?.preQualificationTest;
    if (preQualificationTest == null) return;

    await router.pushReplacementNamed(
      Routes.qualificationTestPage.name,
      extra: QualificationTestParams(
        qualificationTestModel: preQualificationTest,
        trainingConsumptionBloc: this,
        trainingId: state.trainingConsumptionModel?.trainingId ?? '',
        navigationType: LessonNavigationType.backward,
      ),
    );
    add(const RefreshTrainingConsumptionPageEvent());
  }

  void _updatePreQualificationTest(
    UpdatePreQualificationTestEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) {
    if (state.trainingConsumptionModel == null) return;

    final updatedModel = state.trainingConsumptionModel!.copyWith(
      preQualificationTest: event.preQualificationTest,
    );

    emit(state.copyWith(trainingConsumptionModel: updatedModel));
  }

  void _updatePostQualificationTest(
    UpdatePostQualificationTestEvent event,
    Emitter<TrainingConsumptionState> emit,
  ) {
    if (state.trainingConsumptionModel == null) return;

    final updatedModel = state.trainingConsumptionModel!.copyWith(
      postQualificationTest: event.postQualificationTest,
    );

    emit(state.copyWith(trainingConsumptionModel: updatedModel));
  }
}
