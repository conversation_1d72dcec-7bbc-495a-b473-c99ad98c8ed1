part of 'training_consumption_bloc.dart';

sealed class TrainingConsumptionEvent {
  const TrainingConsumptionEvent();
}

class LoadTrainingConsumptionPageEvent extends TrainingConsumptionEvent {
  const LoadTrainingConsumptionPageEvent(this.trainingDetailsModel);

  final TrainingDetailsModel trainingDetailsModel;
}

class RefreshTrainingConsumptionPageEvent extends TrainingConsumptionEvent {
  const RefreshTrainingConsumptionPageEvent();
}

class CancelAllDownloadsEvent extends TrainingConsumptionEvent {
  const CancelAllDownloadsEvent();
}

class OpenLessonEvent extends TrainingConsumptionEvent {
  const OpenLessonEvent(this.lessonParams);

  final LessonParams lessonParams;
}

class OpenLiveSessionLessonEvent extends TrainingConsumptionEvent {
  const OpenLiveSessionLessonEvent({
    required this.meeting,
    this.lessonNavigationType = LessonNavigationType.initial,
    this.subLesson,
  });

  final Meeting meeting;
  final LessonNavigationType lessonNavigationType;
  final Lesson? subLesson;
}

class MarkLessonAsCompletedEvent extends TrainingConsumptionEvent {
  const MarkLessonAsCompletedEvent({required this.lesson});

  final Lesson lesson;
}

class GetVideoUrlEvent extends TrainingConsumptionEvent {
  const GetVideoUrlEvent(this.lesson);

  final Lesson lesson;
}

class GetSlideFilePathEvent extends TrainingConsumptionEvent {
  const GetSlideFilePathEvent(this.lesson);

  final Lesson lesson;
}

class DownloadFileEvent extends TrainingConsumptionEvent {
  const DownloadFileEvent({required this.fileKey, required this.fileName});

  final String fileKey;
  final String? fileName;
}

class OpenFileEvent extends TrainingConsumptionEvent {
  const OpenFileEvent(this.fileName);

  final String? fileName;
}

class OpenPreQualificationTestEvent extends TrainingConsumptionEvent {
  const OpenPreQualificationTestEvent();
}

class OpenPostQualificationTestEvent extends TrainingConsumptionEvent {
  const OpenPostQualificationTestEvent();
}

class UpdatePreQualificationTestEvent extends TrainingConsumptionEvent {
  final PreQualificationTest preQualificationTest;
  const UpdatePreQualificationTestEvent(this.preQualificationTest);
}

class UpdatePostQualificationTestEvent extends TrainingConsumptionEvent {
  final PostQualificationTest postQualificationTest;
  const UpdatePostQualificationTestEvent(this.postQualificationTest);
}
