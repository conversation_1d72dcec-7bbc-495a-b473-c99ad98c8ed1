part of 'training_consumption_bloc.dart';

@freezed
abstract class TrainingConsumptionState with _$TrainingConsumptionState {
  factory TrainingConsumptionState({
    @Default(false) bool isLoading,
    TrainingConsumptionModel? trainingConsumptionModel,
    TrainingStructureModel? trainingStructure,
    @Default(0) int uncompletedSectionIndex,
    @Default('') String errorMessage,
    String? videoLessonUrl,
    double? slideDownloadProgress,
    String? slideFilePath,
    @Default(<String>{}) Set<String> downloadingFiles,
  }) = _TrainingConsumptionState;
}
