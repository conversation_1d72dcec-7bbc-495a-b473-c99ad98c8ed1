// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_consumption_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingConsumptionState {
  bool get isLoading;
  TrainingConsumptionModel? get trainingConsumptionModel;
  TrainingStructureModel? get trainingStructure;
  int get uncompletedSectionIndex;
  String get errorMessage;
  String? get videoLessonUrl;
  double? get slideDownloadProgress;
  String? get slideFilePath;
  Set<String> get downloadingFiles;

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingConsumptionStateCopyWith<TrainingConsumptionState> get copyWith =>
      _$TrainingConsumptionStateCopyWithImpl<TrainingConsumptionState>(
          this as TrainingConsumptionState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingConsumptionState &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.trainingConsumptionModel, trainingConsumptionModel) ||
                other.trainingConsumptionModel == trainingConsumptionModel) &&
            (identical(other.trainingStructure, trainingStructure) ||
                other.trainingStructure == trainingStructure) &&
            (identical(other.uncompletedSectionIndex, uncompletedSectionIndex) ||
                other.uncompletedSectionIndex == uncompletedSectionIndex) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.videoLessonUrl, videoLessonUrl) ||
                other.videoLessonUrl == videoLessonUrl) &&
            (identical(other.slideDownloadProgress, slideDownloadProgress) ||
                other.slideDownloadProgress == slideDownloadProgress) &&
            (identical(other.slideFilePath, slideFilePath) ||
                other.slideFilePath == slideFilePath) &&
            const DeepCollectionEquality().equals(other.downloadingFiles, downloadingFiles));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      trainingConsumptionModel,
      trainingStructure,
      uncompletedSectionIndex,
      errorMessage,
      videoLessonUrl,
      slideDownloadProgress,
      slideFilePath,
      const DeepCollectionEquality().hash(downloadingFiles));

  @override
  String toString() {
    return 'TrainingConsumptionState(isLoading: $isLoading, trainingConsumptionModel: $trainingConsumptionModel, trainingStructure: $trainingStructure, uncompletedSectionIndex: $uncompletedSectionIndex, errorMessage: $errorMessage, videoLessonUrl: $videoLessonUrl, slideDownloadProgress: $slideDownloadProgress, slideFilePath: $slideFilePath, downloadingFiles: $downloadingFiles)';
  }
}

/// @nodoc
abstract mixin class $TrainingConsumptionStateCopyWith<$Res> {
  factory $TrainingConsumptionStateCopyWith(
          TrainingConsumptionState value, $Res Function(TrainingConsumptionState) _then) =
      _$TrainingConsumptionStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isLoading,
      TrainingConsumptionModel? trainingConsumptionModel,
      TrainingStructureModel? trainingStructure,
      int uncompletedSectionIndex,
      String errorMessage,
      String? videoLessonUrl,
      double? slideDownloadProgress,
      String? slideFilePath,
      Set<String> downloadingFiles});

  $TrainingConsumptionModelCopyWith<$Res>? get trainingConsumptionModel;
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure;
}

/// @nodoc
class _$TrainingConsumptionStateCopyWithImpl<$Res>
    implements $TrainingConsumptionStateCopyWith<$Res> {
  _$TrainingConsumptionStateCopyWithImpl(this._self, this._then);

  final TrainingConsumptionState _self;
  final $Res Function(TrainingConsumptionState) _then;

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? trainingConsumptionModel = freezed,
    Object? trainingStructure = freezed,
    Object? uncompletedSectionIndex = null,
    Object? errorMessage = null,
    Object? videoLessonUrl = freezed,
    Object? slideDownloadProgress = freezed,
    Object? slideFilePath = freezed,
    Object? downloadingFiles = null,
  }) {
    return _then(_self.copyWith(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      trainingConsumptionModel: freezed == trainingConsumptionModel
          ? _self.trainingConsumptionModel
          : trainingConsumptionModel // ignore: cast_nullable_to_non_nullable
              as TrainingConsumptionModel?,
      trainingStructure: freezed == trainingStructure
          ? _self.trainingStructure
          : trainingStructure // ignore: cast_nullable_to_non_nullable
              as TrainingStructureModel?,
      uncompletedSectionIndex: null == uncompletedSectionIndex
          ? _self.uncompletedSectionIndex
          : uncompletedSectionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      videoLessonUrl: freezed == videoLessonUrl
          ? _self.videoLessonUrl
          : videoLessonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      slideDownloadProgress: freezed == slideDownloadProgress
          ? _self.slideDownloadProgress
          : slideDownloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      slideFilePath: freezed == slideFilePath
          ? _self.slideFilePath
          : slideFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadingFiles: null == downloadingFiles
          ? _self.downloadingFiles
          : downloadingFiles // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingConsumptionModelCopyWith<$Res>? get trainingConsumptionModel {
    if (_self.trainingConsumptionModel == null) {
      return null;
    }

    return $TrainingConsumptionModelCopyWith<$Res>(_self.trainingConsumptionModel!, (value) {
      return _then(_self.copyWith(trainingConsumptionModel: value));
    });
  }

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure {
    if (_self.trainingStructure == null) {
      return null;
    }

    return $TrainingStructureModelCopyWith<$Res>(_self.trainingStructure!, (value) {
      return _then(_self.copyWith(trainingStructure: value));
    });
  }
}

/// @nodoc

class _TrainingConsumptionState implements TrainingConsumptionState {
  _TrainingConsumptionState(
      {this.isLoading = false,
      this.trainingConsumptionModel,
      this.trainingStructure,
      this.uncompletedSectionIndex = 0,
      this.errorMessage = '',
      this.videoLessonUrl,
      this.slideDownloadProgress,
      this.slideFilePath,
      final Set<String> downloadingFiles = const <String>{}})
      : _downloadingFiles = downloadingFiles;

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final TrainingConsumptionModel? trainingConsumptionModel;
  @override
  final TrainingStructureModel? trainingStructure;
  @override
  @JsonKey()
  final int uncompletedSectionIndex;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  final String? videoLessonUrl;
  @override
  final double? slideDownloadProgress;
  @override
  final String? slideFilePath;
  final Set<String> _downloadingFiles;
  @override
  @JsonKey()
  Set<String> get downloadingFiles {
    if (_downloadingFiles is EqualUnmodifiableSetView) return _downloadingFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_downloadingFiles);
  }

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingConsumptionStateCopyWith<_TrainingConsumptionState> get copyWith =>
      __$TrainingConsumptionStateCopyWithImpl<_TrainingConsumptionState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingConsumptionState &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.trainingConsumptionModel, trainingConsumptionModel) ||
                other.trainingConsumptionModel == trainingConsumptionModel) &&
            (identical(other.trainingStructure, trainingStructure) ||
                other.trainingStructure == trainingStructure) &&
            (identical(other.uncompletedSectionIndex, uncompletedSectionIndex) ||
                other.uncompletedSectionIndex == uncompletedSectionIndex) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.videoLessonUrl, videoLessonUrl) ||
                other.videoLessonUrl == videoLessonUrl) &&
            (identical(other.slideDownloadProgress, slideDownloadProgress) ||
                other.slideDownloadProgress == slideDownloadProgress) &&
            (identical(other.slideFilePath, slideFilePath) ||
                other.slideFilePath == slideFilePath) &&
            const DeepCollectionEquality().equals(other._downloadingFiles, _downloadingFiles));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      trainingConsumptionModel,
      trainingStructure,
      uncompletedSectionIndex,
      errorMessage,
      videoLessonUrl,
      slideDownloadProgress,
      slideFilePath,
      const DeepCollectionEquality().hash(_downloadingFiles));

  @override
  String toString() {
    return 'TrainingConsumptionState(isLoading: $isLoading, trainingConsumptionModel: $trainingConsumptionModel, trainingStructure: $trainingStructure, uncompletedSectionIndex: $uncompletedSectionIndex, errorMessage: $errorMessage, videoLessonUrl: $videoLessonUrl, slideDownloadProgress: $slideDownloadProgress, slideFilePath: $slideFilePath, downloadingFiles: $downloadingFiles)';
  }
}

/// @nodoc
abstract mixin class _$TrainingConsumptionStateCopyWith<$Res>
    implements $TrainingConsumptionStateCopyWith<$Res> {
  factory _$TrainingConsumptionStateCopyWith(
          _TrainingConsumptionState value, $Res Function(_TrainingConsumptionState) _then) =
      __$TrainingConsumptionStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      TrainingConsumptionModel? trainingConsumptionModel,
      TrainingStructureModel? trainingStructure,
      int uncompletedSectionIndex,
      String errorMessage,
      String? videoLessonUrl,
      double? slideDownloadProgress,
      String? slideFilePath,
      Set<String> downloadingFiles});

  @override
  $TrainingConsumptionModelCopyWith<$Res>? get trainingConsumptionModel;
  @override
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure;
}

/// @nodoc
class __$TrainingConsumptionStateCopyWithImpl<$Res>
    implements _$TrainingConsumptionStateCopyWith<$Res> {
  __$TrainingConsumptionStateCopyWithImpl(this._self, this._then);

  final _TrainingConsumptionState _self;
  final $Res Function(_TrainingConsumptionState) _then;

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isLoading = null,
    Object? trainingConsumptionModel = freezed,
    Object? trainingStructure = freezed,
    Object? uncompletedSectionIndex = null,
    Object? errorMessage = null,
    Object? videoLessonUrl = freezed,
    Object? slideDownloadProgress = freezed,
    Object? slideFilePath = freezed,
    Object? downloadingFiles = null,
  }) {
    return _then(_TrainingConsumptionState(
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      trainingConsumptionModel: freezed == trainingConsumptionModel
          ? _self.trainingConsumptionModel
          : trainingConsumptionModel // ignore: cast_nullable_to_non_nullable
              as TrainingConsumptionModel?,
      trainingStructure: freezed == trainingStructure
          ? _self.trainingStructure
          : trainingStructure // ignore: cast_nullable_to_non_nullable
              as TrainingStructureModel?,
      uncompletedSectionIndex: null == uncompletedSectionIndex
          ? _self.uncompletedSectionIndex
          : uncompletedSectionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      videoLessonUrl: freezed == videoLessonUrl
          ? _self.videoLessonUrl
          : videoLessonUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      slideDownloadProgress: freezed == slideDownloadProgress
          ? _self.slideDownloadProgress
          : slideDownloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      slideFilePath: freezed == slideFilePath
          ? _self.slideFilePath
          : slideFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
      downloadingFiles: null == downloadingFiles
          ? _self._downloadingFiles
          : downloadingFiles // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingConsumptionModelCopyWith<$Res>? get trainingConsumptionModel {
    if (_self.trainingConsumptionModel == null) {
      return null;
    }

    return $TrainingConsumptionModelCopyWith<$Res>(_self.trainingConsumptionModel!, (value) {
      return _then(_self.copyWith(trainingConsumptionModel: value));
    });
  }

  /// Create a copy of TrainingConsumptionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingStructureModelCopyWith<$Res>? get trainingStructure {
    if (_self.trainingStructure == null) {
      return null;
    }

    return $TrainingStructureModelCopyWith<$Res>(_self.trainingStructure!, (value) {
      return _then(_self.copyWith(trainingStructure: value));
    });
  }
}

// dart format on
