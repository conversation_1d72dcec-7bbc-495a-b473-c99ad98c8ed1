import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/features/shared/ui_components/video_player/app_video_player.dart';
import 'package:national_skills_platform/features/shared/ui_components/video_player/widgets/player_custom_controls.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_bottom_navigation.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_header.dart';

class VideoLesson extends StatelessWidget {
  const VideoLesson({required this.lessonParams, super.key});

  final LessonParams lessonParams;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      appBar: AppBar(backgroundColor: Colors.black, toolbarHeight: 0),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [_VideoPlayerWrapper(lessonParams: lessonParams), LessonHeader(lessonParams)],
      ),
      bottomNavigationBar: BlocSelector<TrainingConsumptionBloc, TrainingConsumptionState, bool>(
        bloc: lessonParams.trainingConsumptionBloc,
        selector: (state) =>
            state.trainingConsumptionModel?.completedLessonsIDs.contains(
              lessonParams.lesson.id,
            ) ??
            false,
        builder: (context, isCompleted) {
          return LessonBottomNavigation(lessonParams: lessonParams, canNavigateNext: isCompleted);
        },
      ),
    );
  }
}

class _VideoPlayerWrapper extends StatefulWidget {
  const _VideoPlayerWrapper({required this.lessonParams});

  final LessonParams lessonParams;

  @override
  State<_VideoPlayerWrapper> createState() => _VideoPlayerWrapperState();
}

class _VideoPlayerWrapperState extends State<_VideoPlayerWrapper> {
  bool isMarkedAsRead = false;

  @override
  void initState() {
    super.initState();
    isMarkedAsRead = widget.lessonParams.isCompleted;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
      bloc: widget.lessonParams.trainingConsumptionBloc,
      listener: (context, state) {
        if (state.errorMessage.isNotEmpty) showAppToast(context, message: state.errorMessage);

        if (state.trainingConsumptionModel?.completedLessonsIDs.contains(
              widget.lessonParams.lesson.id,
            ) ==
            false) {
          // if markAsRead request is failed then reset the isMarkedAsRead flag
          isMarkedAsRead = false;
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return const AspectRatio(aspectRatio: 16 / 9, child: BuildLoader());
        }

        final videoLessonUrl = state.videoLessonUrl;

        if (videoLessonUrl == null) return const _VideoNotFound();

        return ColoredBox(
          color: Colors.black,
          child: AppVideoPlayer(
            videoLessonUrl,
            allowFullScreen: false,
            disableBorderCurve: true,
            allowMuting: false,
            markAsCompleted: _markAsCompleted,
            customControls: BlocProvider.value(
              value: widget.lessonParams.trainingConsumptionBloc,
              child: PlayerCustomControls(widget.lessonParams),
            ),
          ),
        );
      },
    );
  }

  void _markAsCompleted() {
    final isNotInstructorLedTraining =
        widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel.type !=
            TrainingType.InstructorLed;

    //in instructor-led trainings we don't need mark as read feature
    if (isNotInstructorLedTraining && !isMarkedAsRead) {
      isMarkedAsRead = true;
      widget.lessonParams.trainingConsumptionBloc.add(
        MarkLessonAsCompletedEvent(lesson: widget.lessonParams.lesson),
      );
    }
  }
}

class _VideoNotFound extends StatelessWidget {
  const _VideoNotFound();

  @override
  Widget build(BuildContext context) {
    return const Align(
      child: Padding(padding: EdgeInsets.all(8.0), child: Text('Video Not Found')),
    );
  }
}
