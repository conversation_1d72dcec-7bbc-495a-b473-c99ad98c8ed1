import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/live_session_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/unified_navigation_mixin.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/live_session_cancelled_card.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_bottom_navigation.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/live_session_changes_info.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/cancelled_label.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';
import 'package:url_launcher/url_launcher.dart';

class LiveSessionPage extends StatelessWidget with UnifiedNavigationMixin {
  const LiveSessionPage({super.key, required this.params});

  final LiveSessionParams params;

  @override
  Widget build(BuildContext context) {
    final bloc = params.trainingConsumptionBloc;
    final meeting = params.meeting;
    final isMeetingCancelled = (meeting.meetingStatus == MeetingStatus.Cancelled);
    final isMeetingPassed = (meeting.meetingStatus == MeetingStatus.Passed);
    final cardNotes = meeting.cardNotes ?? [];
    final cardNote = cardNotes.isNotEmpty ? cardNotes.last : null;

    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      appBar: AppBar(
        leading: CloseButton(color: Colors.white, onPressed: () => router.pop()),
        title: Text(
          formatTime(meeting.startDate),
          style: context.textTheme.textSmall.semiBold.white,
        ),
        backgroundColor: AppColors.greenAccentPrimary,
      ),
      bottomNavigationBar: Builder(
        builder: (context) {
          // Find the relevant section(s) for this meeting
          final sections = bloc.state.trainingStructure?.sections ?? [];
          final currentSection = sections.firstWhere(
            (section) => meeting.sections?.contains(section.id) ?? false,
            orElse: () => sections.first,
          );

          // Build initial LessonParams (pointing to the current meeting)
          final completedIDs = bloc.state.trainingConsumptionModel?.completedLessonsIDs ?? {};
          final navigationParams = LessonParams(
            section: currentSection,
            lesson: currentSection.lessons.first,
            // Use first lesson to indicate meeting page
            completedLessonsInSection: completedIDs.length,
            isCompleted: false,
            trainingConsumptionBloc: bloc,
            lessonNavigationType: params.lessonNavigationType,
            meeting: meeting,
          );

          return LessonBottomNavigation(
            lessonParams: navigationParams,
            canNavigateNext: !isLastContent(navigationParams),
          );
        },
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(color: AppColors.accentLight, width: 0.5)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  formatTime(meeting.startDate),
                  style: context.textTheme.textMedium.semiBold,
                  maxLines: 2,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16.0),
              margin: const EdgeInsets.only(bottom: 16.0),
              width: MediaQuery.sizeOf(context).width,
              child: Column(
                children: [
                  const SizedBox(height: 24),

                  if (isMeetingCancelled) ...[const CancelledLabel(), const SizedBox(height: 8)],

                  Text(
                    formatTime(meeting.startDate),
                    style: context.textTheme.paragraphLarge.copyWith(
                      decoration: isMeetingCancelled ? TextDecoration.lineThrough : null,
                    ),
                  ),
                  Text(
                    LocaleKeys.at_time_gmt3.tr(args: [formatTime(meeting.startDate)]),
                    style: context.textTheme.paragraphLarge.copyWith(
                      decoration: isMeetingCancelled ? TextDecoration.lineThrough : null,
                    ),
                  ),

                  // Show meeting ID & password if not passed/cancelled
                  if (!isMeetingPassed && !isMeetingCancelled) ...[
                    const SizedBox(height: 8),
                    Text(
                      LocaleKeys.meeting_id.tr(args: [meeting.meetingId ?? '']),
                      style: context.textTheme.paragraphLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      LocaleKeys.meeting_password.tr(args: [meeting.zoomPassword ?? '']),
                      style: context.textTheme.paragraphLarge,
                    ),
                    const SizedBox(height: 24),
                    AppButton(
                      onTap: () async {
                        String url = meeting.zoomLink ?? '';
                        if (!url.startsWith(Constants.httpPrefix) &&
                            !url.startsWith(Constants.httpsPrefix)) {
                          url = Constants.httpPrefix + url;
                        }
                        final uri = Uri.tryParse(url);
                        if (uri != null) await launchUrl(uri);
                      },
                      buttonText: LocaleKeys.join_meeting.tr(),
                    ),
                  ],

                  if (isMeetingCancelled) LiveSessionCancelledCard(meeting: meeting),
                  if (meeting.meetingStatus == MeetingStatus.Rescheduled && cardNote != null)
                    LiveSessionChangesInfo(cardNote: cardNote),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
