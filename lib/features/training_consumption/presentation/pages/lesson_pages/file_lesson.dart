import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/file_tile.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_bottom_navigation.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_header.dart';
import 'package:national_skills_platform/router.dart';

class FileLesson extends StatefulWidget {
  const FileLesson({required this.lessonParams, super.key});

  final LessonParams lessonParams;

  @override
  State<FileLesson> createState() => _FileLessonState();
}

class _FileLessonState extends State<FileLesson> {
  @override
  void initState() {
    super.initState();
    markAsRead();
  }

  @override
  Widget build(BuildContext context) {
    final resources = widget.lessonParams.lesson.resources;

    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      appBar: AppBar(
        leading: CloseButton(color: Colors.white, onPressed: router.pop),
        title: Text(
          widget.lessonParams.lesson.title,
          style: context.textTheme.textSmall.semiBold.white,
        ),
        backgroundColor: AppColors.greenAccentPrimary,
      ),
      bottomNavigationBar: LessonBottomNavigation(lessonParams: widget.lessonParams),
      body: ListView(
        children: [
          LessonHeader(widget.lessonParams),
          const SizedBox(height: 8),
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border.symmetric(
                horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
              ),
            ),
            child: Column(
              children: [
                if (resources != null)
                  for (int i = 0; i < resources.length; i++)
                    FileTile(resource: resources[i], lessonParams: widget.lessonParams),
              ].divide(divider: const AppDivider()),
            ),
          ),
        ],
      ),
    );
  }

  void markAsRead() {
    final isNotInstructorLedTraining =
        widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel.type !=
            TrainingType.InstructorLed;

    //in instructor-led trainings we don't need mark as read feature
    if (isNotInstructorLedTraining && !widget.lessonParams.isCompleted) {
      widget.lessonParams.trainingConsumptionBloc.add(
        MarkLessonAsCompletedEvent(lesson: widget.lessonParams.lesson),
      );
    }
  }
}
