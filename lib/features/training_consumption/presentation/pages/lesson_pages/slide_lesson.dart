import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_loading_overlay.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_viewer_landscape.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_viewer_portrait.dart';

class SlideLesson extends StatefulWidget {
  const SlideLesson({required this.lessonParams, super.key});

  final LessonParams lessonParams;

  @override
  State<SlideLesson> createState() => _SlideLessonState();
}

class _SlideLessonState extends State<SlideLesson> {
  final ValueNotifier<int> _currentPageNotifier = ValueNotifier<int>(1);
  bool isPortrait = true;

  @override
  void initState() {
    super.initState();
    if (widget.lessonParams.trainingConsumptionBloc.state.slideFilePath == null) {
      widget.lessonParams.trainingConsumptionBloc.add(
        GetSlideFilePathEvent(widget.lessonParams.lesson),
      );
    }
  }

  @override
  void didUpdateWidget(SlideLesson oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (oldWidget.lessonParams.lesson.id != widget.lessonParams.lesson.id) {
      _currentPageNotifier.value = 1;
      widget.lessonParams.trainingConsumptionBloc.add(
        GetSlideFilePathEvent(widget.lessonParams.lesson),
      );
    }
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    widget.lessonParams.trainingConsumptionBloc.add(const CancelAllDownloadsEvent());

    _currentPageNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: widget.lessonParams.trainingConsumptionBloc,
      child: BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
        listenWhen: (prevState, state) => prevState.errorMessage != state.errorMessage,
        listener: (context, state) {
          if (state.errorMessage.isNotEmpty) showAppToast(context, message: state.errorMessage);
        },
        buildWhen: (prevState, state) =>
            prevState.isLoading != state.isLoading ||
            prevState.slideFilePath != state.slideFilePath,
        builder: (context, state) {
          return AppLoadingOverlay(
            isLoading: state.isLoading,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 500),
              transitionBuilder: (child, animation) {
                return RotationTransition(
                  turns: Tween<double>(
                    begin: isPortrait ? -0.25 : 0.25,
                    end: 0,
                  ).animate(CurvedAnimation(parent: animation, curve: Curves.easeInOut)),
                  child: FadeTransition(opacity: animation, child: child),
                );
              },
              child: Stack(
                children: [
                  //Portrait
                  Visibility(
                    visible: isPortrait,
                    maintainState: true,
                    child: SlideViewerPortrait(
                      lessonParams: widget.lessonParams,
                      currentPageNotifier: _currentPageNotifier,
                      screenOrientationToggle: screenOrientationToggle,
                    ),
                  ),

                  // Landscape
                  Visibility(
                    visible: !isPortrait,
                    maintainState: true,
                    child: SlideViewerLandscape(
                      lessonParams: widget.lessonParams,
                      currentPageNotifier: _currentPageNotifier,
                      screenOrientationToggle: screenOrientationToggle,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void screenOrientationToggle() {
    setState(() {
      isPortrait = !isPortrait;

      SystemChrome.setPreferredOrientations(
        isPortrait
            ? [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]
            : [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight],
      );
    });
  }
}
