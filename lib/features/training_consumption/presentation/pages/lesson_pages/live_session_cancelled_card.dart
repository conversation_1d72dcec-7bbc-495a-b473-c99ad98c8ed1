import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class LiveSessionCancelledCard extends StatelessWidget {
  const LiveSessionCancelledCard({super.key, required this.meeting});

  final Meeting meeting;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.uiBackgroundPrimary,
        border: Border.all(color: AppColors.greyLight),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Cancellation Reason', style: context.textTheme.textMedium.semiBold),
          const SizedBox(height: 8),
          Text(
            formatDateTime(meeting.cancellationDate),
            style: context.textTheme.textXSmall.medium.greyPrimary,
          ),
          const SizedBox(height: 12),
          Text(meeting.cancellationReason ?? '', style: context.textTheme.textSmall),
        ],
      ),
    );
  }
}
