import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_bottom_navigation.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_header.dart';
import 'package:national_skills_platform/router.dart';
import 'package:url_launcher/url_launcher.dart';

class ArticleLesson extends StatefulWidget {
  const ArticleLesson({required this.lessonParams, super.key});

  final LessonParams lessonParams;

  @override
  State<ArticleLesson> createState() => _ArticleLessonState();
}

class _ArticleLessonState extends State<ArticleLesson> {
  late final ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    final isNotInstructorLedTraining =
        widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel.type !=
            TrainingType.InstructorLed;

    //in instructor-led trainings we don't need mark as read feature
    if (isNotInstructorLedTraining && !widget.lessonParams.isCompleted) {
      scrollController.addListener(_onScroll);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Content is smaller than viewport, mark as read
        if (scrollController.position.maxScrollExtent == 0) _markAsRead();
      });
    }
  }

  @override
  void didUpdateWidget(ArticleLesson oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.lessonParams.lesson.id != widget.lessonParams.lesson.id) {
      scrollController.jumpTo(0);
    }
  }

  @override
  void dispose() {
    if (!widget.lessonParams.isCompleted) scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      appBar: AppBar(
        leading: CloseButton(color: Colors.white, onPressed: () => router.pop()),
        title: Text(
          widget.lessonParams.lesson.title,
          style: context.textTheme.textSmall.semiBold.white,
        ),
        backgroundColor: AppColors.greenAccentPrimary,
      ),
      bottomNavigationBar: LessonBottomNavigation(lessonParams: widget.lessonParams),
      body: ListView(
        controller: scrollController,
        children: [
          LessonHeader(widget.lessonParams),

          const SizedBox(height: 8),

          ColoredBox(
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    widget.lessonParams.lesson.title,
                    style: context.textTheme.h3.semiBold,
                  ),
                ),
                if (widget.lessonParams.lesson.text != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Html(
                      data: widget.lessonParams.lesson.text,
                      onLinkTap: (url, _, __) async {
                        if (url == null) return;
                        final uri = Uri.parse(url);
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri);
                        }
                      },
                      style: {
                        Constants.body: Style(
                          fontSize: FontSize(context.textTheme.paragraphLarge.fontSize ?? 0),
                        ),
                      },
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 8),
          //
        ],
      ),
    );
  }

  void _onScroll() {
    if (scrollController.position.atEdge) {
      final isBottom =
          scrollController.position.pixels == scrollController.position.maxScrollExtent;
      if (isBottom) _markAsRead();
    }
  }

  void _markAsRead() {
    widget.lessonParams.trainingConsumptionBloc.add(
      MarkLessonAsCompletedEvent(lesson: widget.lessonParams.lesson),
    );
  }
}
