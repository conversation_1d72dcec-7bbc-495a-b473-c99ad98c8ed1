import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/convert_bytes_to_mb.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:path_provider/path_provider.dart';

class FileTile extends StatefulWidget {
  const FileTile({super.key, required this.resource, required this.lessonParams});

  final Resource resource;
  final LessonParams lessonParams;

  @override
  State<FileTile> createState() => _FileTileState();
}

class _FileTileState extends State<FileTile> {
  bool isDownloading = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TrainingConsumptionBloc, TrainingConsumptionState>(
      bloc: widget.lessonParams.trainingConsumptionBloc,
      builder: (context, state) {
        isDownloading = state.downloadingFiles.contains(widget.resource.key);

        return FutureBuilder<bool>(
          future: _checkFileExists(widget.resource.originalFilename),
          builder: (context, snapshot) {
            return ListTile(
              leading: Image.asset(AssetsPath.fileImage, height: 32),
              title: Text(
                widget.resource.originalFilename ?? '',
                style: context.textTheme.textMedium.semiBold,
              ),
              subtitle: Text(
                '${convertBytesToMB(widget.resource.size ?? 0)} ${Constants.mb}',
                style: context.textTheme.textSmall.regular.greyPrimary,
              ),
              trailing: trailingWidget(snapshot),
              onTap: () => snapshot.data == true
                  ? widget.lessonParams.trainingConsumptionBloc.add(
                      OpenFileEvent(widget.resource.originalFilename),
                    )
                  : widget.lessonParams.trainingConsumptionBloc.add(
                      DownloadFileEvent(
                        fileKey: widget.resource.key ?? '',
                        fileName: widget.resource.originalFilename,
                      ),
                    ),
            );
          },
        );
      },
    );
  }

  Widget trailingWidget(AsyncSnapshot<bool> snapshot) {
    final showLoading = isDownloading || snapshot.connectionState == ConnectionState.waiting;

    return showLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: BuildLoader(
              loaderColor: AppColors.greenAccentPrimary,
              padding: EdgeInsets.zero,
              strokeWidth: 2.4,
            ),
          )
        : snapshot.data == true
            ? const Icon(Icons.check_circle_outline, color: AppColors.greenAccentPrimary)
            : Image.asset(AssetsPath.downloadIcon, height: 20);
  }

  Future<bool> _checkFileExists(String? fileName) async {
    if (fileName == null) return false;

    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.absolute.path}/$fileName';
    return File(filePath).existsSync();
  }
}
