import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

class SlideControlIcon extends StatelessWidget {
  const SlideControlIcon({required this.onTap, required this.icon, super.key});

  final VoidCallback onTap;
  final Widget icon;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 42,
        width: 42,
        padding: const EdgeInsets.all(11),
        decoration: BoxDecoration(
          color: AppColors.uiBackgroundPrimary,
          border: Border.all(color: AppColors.accentLight),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
        ),
        child: icon,
      ),
    );
  }
}
