import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_viewer_controls.dart';
import 'package:pdfrx/pdfrx.dart';

class SlideViewerLandscape extends StatefulWidget {
  const SlideViewerLandscape({
    required this.lessonParams,
    required this.screenOrientationToggle,
    required this.currentPageNotifier,
    super.key,
  });

  final LessonParams lessonParams;
  final VoidCallback screenOrientationToggle;
  final ValueNotifier<int> currentPageNotifier;

  @override
  State<SlideViewerLandscape> createState() => SlideViewerLandscapeState();
}

class SlideViewerLandscapeState extends State<SlideViewerLandscape> {
  bool isReady = false;
  bool isMarkedAsRead = false;
  PdfDocument? documentRef;
  final pageController = PageController();
  final transformationController = TransformationController();

  @override
  void initState() {
    super.initState();
    isMarkedAsRead = widget.lessonParams.isCompleted;
    pageController.addListener(pageListener);
  }

  void pageListener() {
    if (pageController.hasClients) {
      if (pageController.page == (documentRef?.pages.length ?? 0) - 1) markAsRead();
    }
  }

  @override
  void didUpdateWidget(covariant SlideViewerLandscape oldWidget) {
    if (context.read<TrainingConsumptionBloc>().state.slideFilePath != null &&
        pageController.hasClients) {
      if (widget.currentPageNotifier.value - 1 != pageController.page) {
        pageController.jumpToPage(widget.currentPageNotifier.value - 1);
      }
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    pageController
      ..removeListener(pageListener)
      ..dispose();
    transformationController.dispose();
    super.dispose();
  }

  bool showControls = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.greenAccentPrimary,
      body: GestureDetector(
        onTap: () {
          setState(() => showControls = !showControls);
        },
        behavior: HitTestBehavior.translucent,
        child: BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
          listenWhen: (prevState, state) =>
              prevState.trainingConsumptionModel?.completedLessonsIDs.length !=
              state.trainingConsumptionModel?.completedLessonsIDs.length,
          listener: (context, state) {
            if (state.trainingConsumptionModel?.completedLessonsIDs.contains(
                  widget.lessonParams.lesson.id,
                ) ==
                false) {
              // if markAsRead request is failed then reset the isMarkedAsRead flag
              isMarkedAsRead = false;
            }
          },
          builder: (context, state) {
            final isDirectionRTL = Directionality.of(context) == TextDirection.rtl;

            final slideDownloadProgress = state.slideDownloadProgress;
            if (slideDownloadProgress != null) {
              return BuildLoader(value: slideDownloadProgress);
            }

            final slideFilePath = state.slideFilePath;

            if (slideFilePath != null) {
              return Stack(
                children: [
                  Positioned.fill(
                    child: PdfDocumentViewBuilder.file(
                      slideFilePath,
                      builder: (context, document) {
                        const minScale = 1.0;
                        const doubleScale = 2.5;
                        Offset tapPosition = Offset.zero;

                        return GestureDetector(
                          onDoubleTapDown: (details) {
                            tapPosition = details.localPosition;
                          },
                          onDoubleTap: () {
                            final currentScale = transformationController.value.getMaxScaleOnAxis();
                            final newScale = currentScale == minScale ? doubleScale : minScale;
                            final scenePosition = transformationController.toScene(tapPosition);

                            final translation = Matrix4.identity()
                              ..translate(
                                -scenePosition.dx * (newScale - 1),
                                -scenePosition.dy * (newScale - 1),
                              )
                              ..scale(newScale);

                            transformationController.value = translation;
                          },
                          child: InteractiveViewer(
                            minScale: minScale,
                            maxScale: 5.0,
                            panAxis: PanAxis.aligned,
                            transformationController: transformationController,
                            child: PageView.builder(
                              itemCount: document?.pages.length ?? 0,
                              onPageChanged: (page) => widget.currentPageNotifier.value = page + 1,
                              controller: pageController,
                              itemBuilder: (context, index) {
                                documentRef = document;

                                return PdfPageView(document: document, pageNumber: index + 1);
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  if (showControls) ...[
                    //Dim effect
                    Positioned.fill(child: ColoredBox(color: Colors.black.withValues(alpha: 0.5))),

                    Positioned(
                      left: isDirectionRTL ? 0 : 16,
                      right: isDirectionRTL ? 16 : 0,
                      child: Row(
                        children: [
                          CloseButton(
                            color: AppColors.neutralWhite,
                            onPressed: widget.screenOrientationToggle,
                          ),
                          Expanded(
                            child: Text(
                              widget.lessonParams.lesson.title,
                              textAlign: TextAlign.center,
                              style: context.textTheme.textSmall.semiBold.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    if (documentRef != null)
                      Positioned.fill(
                        bottom: 16,
                        right: 18,
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: SlideViewerControls(
                            totalPages: documentRef?.pages.length.toString() ?? '',
                            currentPageNotifier: widget.currentPageNotifier,
                            screenOrientationToggle: widget.screenOrientationToggle,
                            goToPreviousPage: goToPreviousPage,
                            goToNextPage: goToNextPage,
                          ),
                        ),
                      ),
                  ],
                ],
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Future<void> goToPreviousPage() async {
    final pageNumber = pageController.page?.toInt() ?? 1;
    if (pageNumber == 0) return;

    await pageController.animateToPage(
      pageNumber - 1,
      curve: Curves.easeInOut,
      duration: const Duration(milliseconds: 300),
    );
  }

  Future<void> goToNextPage() async {
    final pageNumber = pageController.page?.toInt() ?? 1;
    if (pageNumber == documentRef?.pages.length) return;

    await pageController.animateToPage(
      pageNumber + 1,
      curve: Curves.easeInOut,
      duration: const Duration(milliseconds: 300),
    );
  }

  void markAsRead() {
    final isNotInstructorLedTraining =
        widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel.type !=
            TrainingType.InstructorLed;

    //in instructor-led trainings we don't need mark as read feature
    if (isNotInstructorLedTraining && !isMarkedAsRead) {
      isMarkedAsRead = true;
      widget.lessonParams.trainingConsumptionBloc.add(
        MarkLessonAsCompletedEvent(lesson: widget.lessonParams.lesson),
      );
    }
  }
}
