import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_control_icon.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SlideViewerControls extends StatelessWidget {
  const SlideViewerControls({
    required this.screenOrientationToggle,
    required this.totalPages,
    required this.currentPageNotifier,
    required this.goToPreviousPage,
    required this.goToNextPage,
    super.key,
  });

  final String totalPages;
  final ValueNotifier<int> currentPageNotifier;
  final VoidCallback screenOrientationToggle;
  final VoidCallback goToPreviousPage;
  final VoidCallback goToNextPage;

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.orientationOf(context);
    final isLandscapeMode = orientation == Orientation.landscape;
    final isArabic = Localizations.localeOf(context).languageCode == Constants.localeAR;

    return Stack(
      children: [
        /// Slide's page navigation controls
        Align(
          alignment: isLandscapeMode ? Alignment.bottomCenter : Alignment.bottomLeft,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SlideControlIcon(
                onTap: goToPreviousPage,
                icon: Transform(
                  alignment: Alignment.center,
                  transform: isArabic ? Matrix4.rotationY(pi) : Matrix4.identity(),
                  child: Image.asset(AssetsPath.backIcon, color: AppColors.neutralBlack),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: ValueListenableBuilder<int>(
                  valueListenable: currentPageNotifier,
                  builder: (context, currentPage, child) => Text(
                    LocaleKeys.trainingView_pagesCount.tr(
                      args: [currentPage.toString(), totalPages],
                    ),
                    style: context.textTheme.textSmall.medium.copyWith(
                      color: isLandscapeMode ? AppColors.neutralWhite : AppColors.greyPrimary,
                    ),
                  ),
                ),
              ),
              SlideControlIcon(
                icon: Transform(
                  alignment: Alignment.center,
                  transform: isArabic ? Matrix4.identity() : Matrix4.rotationY(pi),
                  child: Image.asset(AssetsPath.backIcon, color: AppColors.neutralBlack),
                ),
                onTap: goToNextPage,
              ),
            ],
          ),
        ),

        /// Bottom-right aligned orientation toggle button(s)
        Align(
          alignment: Alignment.bottomRight,
          child: isLandscapeMode
              ? GestureDetector(
                  onTap: screenOrientationToggle,
                  child: Padding(
                    padding: const EdgeInsets.all(9),
                    child: Image.asset(AssetsPath.closeFullScreenIcon, width: 24),
                  ),
                )
              : SlideControlIcon(
                  onTap: screenOrientationToggle,
                  icon: Image.asset(AssetsPath.codeIcon, color: AppColors.neutralBlack),
                ),
        ),
      ],
    );
  }
}
