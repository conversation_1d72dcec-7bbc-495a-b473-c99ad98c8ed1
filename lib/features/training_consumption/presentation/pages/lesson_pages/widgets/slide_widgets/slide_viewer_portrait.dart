import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/slide_completion_mixin.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_bottom_navigation.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_header.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_viewer_controls.dart';
import 'package:national_skills_platform/router.dart';
import 'package:pdfrx/pdfrx.dart';

class SlideViewerPortrait extends StatefulWidget {
  const SlideViewerPortrait({
    required this.lessonParams,
    required this.screenOrientationToggle,
    required this.currentPageNotifier,
    super.key,
  });

  final LessonParams lessonParams;
  final VoidCallback screenOrientationToggle;
  final ValueNotifier<int> currentPageNotifier;

  @override
  State<SlideViewerPortrait> createState() => _SlideViewerPortraitState();
}

class _SlideViewerPortraitState extends State<SlideViewerPortrait> with SlideCompletionMixin {
  final isTestState = Platform.environment.containsKey(Constants.flutterTest);
  final pageController = PageController();
  bool isMarkedAsRead = false;
  PdfDocument? documentRef;

  @override
  void initState() {
    super.initState();
    isMarkedAsRead = widget.lessonParams.isCompleted;
    pageController.addListener(pageListener);
  }

  @override
  void didUpdateWidget(SlideViewerPortrait oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reset page controller if lesson changed
    if (oldWidget.lessonParams.lesson.id != widget.lessonParams.lesson.id) {
      isMarkedAsRead = widget.lessonParams.isCompleted;

      documentRef = null;
      documentRef?.dispose();
      pageController.jumpToPage(0);
    }
    // Otherwise sync with current page notifier
    else if (context.read<TrainingConsumptionBloc>().state.slideFilePath != null &&
        pageController.hasClients) {
      if (widget.currentPageNotifier.value - 1 != pageController.page) {
        pageController.jumpToPage(widget.currentPageNotifier.value - 1);
      }
    }
  }

  void pageListener() {
    if (pageController.hasClients) {
      if (isLastSlide(documentRef, pageController.page)) markAsRead();
    }
  }

  @override
  void dispose() {
    documentRef?.dispose();
    pageController
      ..removeListener(pageListener)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      appBar: AppBar(
        leading: CloseButton(color: Colors.white, onPressed: router.pop),
        title: Text(
          widget.lessonParams.lesson.title,
          style: context.textTheme.textSmall.semiBold.white,
        ),
        backgroundColor: AppColors.greenAccentPrimary,
      ),
      bottomNavigationBar: BlocSelector<TrainingConsumptionBloc, TrainingConsumptionState, bool>(
        selector: (state) =>
            state.trainingConsumptionModel?.completedLessonsIDs.contains(
              widget.lessonParams.lesson.id,
            ) ??
            false,
        builder: (context, isCompletedInBloc) {
          return LessonBottomNavigation(
            lessonParams: widget.lessonParams,
            canNavigateNext: isMarkedAsRead || isCompletedInBloc,
          );
        },
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          LessonHeader(widget.lessonParams),
          const SizedBox(height: 8),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border.symmetric(
                  horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
                ),
              ),
              child: BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
                listenWhen: (prevState, state) =>
                    prevState.trainingConsumptionModel?.completedLessonsIDs.length !=
                    state.trainingConsumptionModel?.completedLessonsIDs.length,
                listener: (context, state) {
                  if (state.trainingConsumptionModel?.completedLessonsIDs.contains(
                        widget.lessonParams.lesson.id,
                      ) ==
                      false) {
                    // if markAsRead request is failed then reset the isMarkedAsRead flag
                    isMarkedAsRead = false;
                  }
                },
                buildWhen: (previous, current) =>
                    previous.slideFilePath != current.slideFilePath ||
                    previous.slideDownloadProgress != current.slideDownloadProgress,
                builder: (context, state) {
                  final slideDownloadProgress = state.slideDownloadProgress;
                  if (slideDownloadProgress != null) {
                    return BuildLoader(value: slideDownloadProgress);
                  }

                  final slideFilePath = state.slideFilePath;

                  // couldn't create golden test for the PdfDocumentViewBuilder.file thus only this part replaced with ColoredBox
                  if (isTestState) return const ColoredBox(color: Colors.grey);

                  if (slideFilePath != null) {
                    return PdfDocumentViewBuilder.file(
                      slideFilePath,
                      key: ValueKey(slideFilePath),
                      builder: (context, document) {
                        documentRef = document;

                        //if slide is only 1 page, mark as read immediately
                        if (documentRef?.pages.length == 1) markAsRead();

                        return Column(
                          children: [
                            Expanded(
                              child: PageView.builder(
                                itemCount: document?.pages.length,
                                onPageChanged: (page) =>
                                    widget.currentPageNotifier.value = page + 1,
                                controller: pageController,
                                itemBuilder: (context, page) {
                                  if (document == null) return const SizedBox.shrink();

                                  return ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Container(
                                      foregroundDecoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(color: AppColors.greyLight),
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: PdfPageView(document: document, pageNumber: page + 1),
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 16),
                            SlideViewerControls(
                              totalPages: documentRef?.pages.length.toString() ?? '',
                              currentPageNotifier: widget.currentPageNotifier,
                              screenOrientationToggle: widget.screenOrientationToggle,
                              goToPreviousPage: goToPreviousPage,
                              goToNextPage: goToNextPage,
                            ),
                          ],
                        );
                      },
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void markAsRead() {
    final isNotInstructorLedTraining =
        widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel.type !=
            TrainingType.InstructorLed;

    //in instructor-led trainings we don't need mark as read feature
    if (isNotInstructorLedTraining) {
      if (!isMarkedAsRead && !widget.lessonParams.isCompleted) {
        isMarkedAsRead = true;
        widget.lessonParams.trainingConsumptionBloc.add(
          MarkLessonAsCompletedEvent(lesson: widget.lessonParams.lesson),
        );
      }
    }
  }

  Future<void> goToPreviousPage() async {
    final pageNumber = pageController.page?.toInt() ?? 1;
    if (pageNumber == 0) return;

    await pageController.animateToPage(
      pageNumber - 1,
      curve: Curves.easeInOut,
      duration: const Duration(milliseconds: 300),
    );
  }

  Future<void> goToNextPage() async {
    final pageNumber = pageController.page?.toInt() ?? 1;
    if (pageNumber == documentRef?.pages.length) return;

    await pageController.animateToPage(
      pageNumber + 1,
      curve: Curves.easeInOut,
      duration: const Duration(milliseconds: 300),
    );
  }
}
