import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/environment/environment_configs.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:share_plus/share_plus.dart';

class ShareLessonButton extends StatelessWidget {
  const ShareLessonButton({required this.trainingID, required this.lessonID, super.key});

  final String trainingID;
  final String lessonID;

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.orientationOf(context);
    final barHeight = orientation == Orientation.portrait ? 16.0 : 24.0;
    final buttonPadding = orientation == Orientation.portrait ? 12.0 : 24.0;
    final lessonShareLink =
        '${EnvironmentConfigs.websiteUrl}${ApiConstants.userLearningsPath}/$trainingID?s=$lessonID';

    return Semantics(
      button: true,
      label: 'Share',
      child: GestureDetector(
        excludeFromSemantics: true,
        onTap: () => SharePlus.instance.share(ShareParams(text: lessonShareLink)),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: buttonPadding),
          child: Image.asset(AssetsPath.shareIcon, height: barHeight, color: Colors.white),
        ),
      ),
    );
  }
}
