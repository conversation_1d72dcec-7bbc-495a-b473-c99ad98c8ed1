import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/unified_navigation_mixin.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

/// A bottom navigation for normal lessons or sub-lessons in meetings.
class LessonBottomNavigation extends StatelessWidget with UnifiedNavigationMixin {
  const LessonBottomNavigation({
    required this.lessonParams,
    this.canNavigateNext = true,
    super.key,
  });

  final LessonParams lessonParams;
  final bool canNavigateNext;

  /// Returns `true` if the "Previous" button should be shown.
  bool _canShowPreviousButton() {
    final bloc = lessonParams.trainingConsumptionBloc;

    // If this is a meeting or sub-lesson in a meeting
    if (lessonParams.meeting != null) {
      // Check if we're on a meeting page (lesson is first lesson of section)
      final isOnMeetingPage = lessonParams.lesson.id == lessonParams.section.lessons.first.id;

      if (isOnMeetingPage) {
        // If on meeting page, only show previous if this isn't the first meeting
        final meetings = getMeetings(bloc);
        if (meetings == null || meetings.isEmpty) return false;
        return meetings.first.id != lessonParams.meeting!.id;
      }

      // Otherwise we're on a sub-lesson, show if there's previous content
      final result = getPreviousContent(bloc, lessonParams.meeting, lessonParams.lesson);
      return result.hasContent;
    }

    // Otherwise normal lesson
    final sections = getSections(bloc);
    final (previousLesson, _) = getPreviousLesson(sections, lessonParams);

    // Show Previous button if:
    // 1. We have a previous lesson OR
    // 2. We're on first lesson AND have a pre-test (regardless of pass status)
    final hasPreTest = bloc.state.trainingConsumptionModel?.preQualificationTest != null;
    return previousLesson != null || (isOnFirstLesson(bloc, lessonParams) && hasPreTest);
  }

  /// True if there is no previous content available
  bool isOnFirstLesson(TrainingConsumptionBloc bloc, LessonParams params) {
    final result = getPreviousContent(bloc, params.meeting, params.lesson);
    return !result.hasContent;
  }

  /// Called when user taps "Next".
  void _handleNextNavigation(BuildContext context) {
    if (!canNavigateNext) return;

    final bloc = lessonParams.trainingConsumptionBloc;

    if (isLastContent(lessonParams)) return router.pop();

    if (lessonParams.meeting != null) {
      final nextResult = getNextContent(bloc, lessonParams.meeting, lessonParams.lesson);

      if (nextResult.hasContent) {
        openContent(bloc, nextResult, LessonNavigationType.forward);
        return;
      }

      // If no next content => check if we can open post-test
      if (canNavigateToPostQualificationTest(lessonParams)) {
        bloc.add(const OpenPostQualificationTestEvent());
        return;
      }

      return router.pop();
    }

    // Otherwise, a normal lesson
    final sections = getSections(bloc);
    final (nextLesson, nextSection) = getNextLesson(sections, lessonParams);

    if (nextLesson != null && nextSection != null) {
      final completedIDs = bloc.state.trainingConsumptionModel?.completedLessonsIDs ?? {};
      final params = lessonParams.copyWith(
        lesson: nextLesson,
        section: nextSection,
        completedLessonsInSection: completedIDs.length,
        isCompleted: completedIDs.contains(nextLesson.id),
        lessonNavigationType: LessonNavigationType.forward,
      );
      bloc.add(OpenLessonEvent(params));
      return;
    }

    // If no next lesson => check if we can open post-test
    if (canNavigateToPostQualificationTest(lessonParams)) {
      bloc.add(const OpenPostQualificationTestEvent());
      return;
    }

    router.pop();
  }

  /// Called when user taps "Previous".
  void _handlePreviousNavigation(BuildContext context) {
    final bloc = lessonParams.trainingConsumptionBloc;

    if (isOnFirstLesson(bloc, lessonParams) &&
        bloc.state.trainingConsumptionModel?.preQualificationTest != null) {
      bloc.add(const OpenPreQualificationTestEvent());
      return;
    }

    // Otherwise find the previous content
    final result = getPreviousContent(bloc, lessonParams.meeting, lessonParams.lesson);

    if (!result.hasContent) {
      router.pop();
    } else {
      openContent(bloc, result, LessonNavigationType.backward);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == Constants.localeAR;
    final bottomPadding =
        (MediaQuery.paddingOf(context).bottom != 0) ? MediaQuery.paddingOf(context).bottom : 16.0;
    final lastContent = isLastContent(lessonParams);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: AppColors.accentLight, width: 0.5)),
      ),
      padding: EdgeInsets.only(bottom: bottomPadding, top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_canShowPreviousButton())
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: IntrinsicWidth(
                child: AppButton(
                  onTap: () => _handlePreviousNavigation(context),
                  buttonText: LocaleKeys.trainingView_previous.tr(),
                  height: 36,
                  width: 100,
                  padding: EdgeInsets.zero,
                  backgroundColor: Colors.white,
                  borderColor: AppColors.accentLight,
                  textStyle: context.textTheme.textSmall.accentGreenPrimary.semiBold,
                ),
              ),
            )
          else
            const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: IntrinsicWidth(
              child: AppButton(
                onTap: () => _handleNextNavigation(context),
                height: 36,
                width: 100,
                trailing: canNavigateNext && !lastContent
                    ? Transform(
                        alignment: Alignment.center,
                        transform: isArabic ? Matrix4.identity() : Matrix4.rotationY(pi),
                        child: Image.asset(
                          AssetsPath.backIcon,
                          color: Colors.white,
                          width: 20,
                          height: 20,
                        ),
                      )
                    : null,
                padding: EdgeInsets.zero,
                buttonText: lastContent
                    ? LocaleKeys.complete_lesson.tr()
                    : LocaleKeys.trainingView_next.tr(),
                textStyle: context.textTheme.textSmall
                    .copyWith(color: canNavigateNext ? Colors.white : AppColors.greySecondary)
                    .semiBold,
                backgroundColor:
                    canNavigateNext ? AppColors.greenAccentPrimary : AppColors.greyLight,
                borderColor: AppColors.greyLight,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
