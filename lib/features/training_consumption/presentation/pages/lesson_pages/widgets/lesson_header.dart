import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class LessonHeader extends StatelessWidget {
  const LessonHeader(this.lessonParams, {super.key});

  final LessonParams lessonParams;

  @override
  Widget build(BuildContext context) {
    var completedLessonsCount = lessonParams.completedLessonsInSection;
    final lessonAmount = lessonParams.section.lessons.length;

    return BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
      bloc: lessonParams.trainingConsumptionBloc,
      listenWhen: (previous, current) =>
          previous.trainingConsumptionModel != current.trainingConsumptionModel,
      listener: (context, state) {
        completedLessonsCount = state.trainingConsumptionModel?.completedLessonsIDs
                .intersection(lessonParams.section.lessons.map((l) => l.id).toSet())
                .length ??
            completedLessonsCount;
      },
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(color: AppColors.accentLight, width: 0.5)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                lessonParams.section.title,
                style: context.textTheme.textMedium.semiBold,
                maxLines: 2,
              ),
              const SizedBox(height: 4),
              Text(
                LocaleKeys.completedLessonsCount.tr(
                  args: ['$completedLessonsCount', '$lessonAmount'],
                ),
                style: context.textTheme.textSmall,
              ),
            ],
          ),
        );
      },
    );
  }
}
