import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class LiveSessionChangesInfo extends StatelessWidget {
  const LiveSessionChangesInfo({required this.cardNote, super.key});

  final CardNote cardNote;

  @override
  Widget build(BuildContext context) {
    final locale = context.locale.languageCode.toUpperCase();
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.uiBackgroundPrimary,
        border: Border.all(color: AppColors.greyLight),
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            cardNote.localizedContents[locale]?.title ?? '',
            style: context.textTheme.textMedium.semiBold,
          ),
          const SizedBox(height: 8),
          Text(
            formatDateTime(cardNote.createdDate),
            style: context.textTheme.textXSmall.medium.greyPrimary,
          ),
          const SizedBox(height: 12),
          Text(
            cardNote.localizedContents[locale]?.message ?? '',
            style: context.textTheme.textSmall,
          ),
        ],
      ),
    );
  }
}
