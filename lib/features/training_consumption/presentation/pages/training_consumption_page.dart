import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/training_consumption_view.dart';

class TrainingConsumptionPage extends StatelessWidget {
  const TrainingConsumptionPage(this.trainingDetailsModel, {super.key});

  final TrainingDetailsModel trainingDetailsModel;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance.get<TrainingConsumptionBloc>()
        ..add(LoadTrainingConsumptionPageEvent(trainingDetailsModel)),
      child: TrainingConsumptionView(trainingDetailsModel),
    );
  }
}
