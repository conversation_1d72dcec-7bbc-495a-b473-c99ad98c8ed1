import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/meeting_session_card.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_overview.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_section_card.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_structure_appbar.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_test_contents.dart';

class TrainingConsumptionView extends StatefulWidget {
  const TrainingConsumptionView(this.trainingDetailsModel, {super.key});

  final TrainingDetailsModel trainingDetailsModel;

  @override
  State<TrainingConsumptionView> createState() => _TrainingConsumptionViewState();
}

class _TrainingConsumptionViewState extends State<TrainingConsumptionView> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final trainingStructureModel = widget.trainingDetailsModel.trainingStructure;

    return LayoutBuilder(
      builder: (context, constraints) {
        return Scaffold(
          backgroundColor: AppColors.uiBackgroundPrimary,
          body: BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
            listener: (context, state) {
              if (state.errorMessage.isNotEmpty) {
                showAppToast(context, message: state.errorMessage);
              }

              if (state.trainingConsumptionModel != null && _scrollController.hasClients) {
                scrollToUncompletedSection(state.uncompletedSectionIndex);
              }
            },
            builder: (context, state) {
              if (state.isLoading) return const BuildLoader();

              if (state.trainingConsumptionModel != null) {
                return CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    TrainingStructureAppbar(
                      constraints: constraints,
                      trainingDetailsModel: widget.trainingDetailsModel,
                    ),

                    if (trainingStructureModel != null)
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (context, index) =>
                              Column(children: getStructureItems(trainingStructureModel, state)),
                          childCount: 1,
                        ),
                      ),
                    //
                  ],
                );
              }

              return const SizedBox.shrink();
            },
          ),
        );
      },
    );
  }

  List<Widget> getStructureItems(
    TrainingStructureModel trainingStructureModel,
    TrainingConsumptionState state,
  ) {
    final consumptionModel = state.trainingConsumptionModel;
    if (consumptionModel == null) return [];

    final isPretestPassed = consumptionModel.preQualificationTest == null ||
        (state.trainingConsumptionModel?.preTestPassed ?? false);
    final isContentDisabled = consumptionModel.preQualificationTest != null &&
        (consumptionModel.preQualificationTest?.mandatory == true && !isPretestPassed);
    final children = <Widget>[const TrainingOverview()];

    if (consumptionModel.preQualificationTest != null) {
      children.add(
        TrainingTestContents(
          isExpanded: !isPretestPassed,
          preQualificationTest: consumptionModel.preQualificationTest,
          trainingId: consumptionModel.trainingId,
          disabled: false,
        ),
      );
    }

    if (widget.trainingDetailsModel.type != TrainingType.InstructorLed) {
      for (var i = 0; i < trainingStructureModel.sections.length; i++) {
        children.add(
          TrainingSectionCard(
            section: trainingStructureModel.sections[i],
            completedLessonsIDs: consumptionModel.completedLessonsIDs,
            isExpanded: isPretestPassed && i == state.uncompletedSectionIndex,
            disabled: isContentDisabled,
          ),
        );
      }
    }

    final currentEnrolledStudyStream = widget.trainingDetailsModel.studyStreams.firstWhereOrNull(
      (element) =>
          element?.id ==
          widget.trainingDetailsModel.applicantDto?.studyStreams
              .firstWhereOrNull((element) => element?.status == Constants.ENROLLED)
              ?.streamId,
    );

    final meetings = currentEnrolledStudyStream?.liveSession?.meetings;

    if (meetings != null && meetings.isNotEmpty) {
      for (var i = 0; i < meetings.length; i++) {
        children.add(
          MeetingsSessionCard(
            trainingStructureModel: trainingStructureModel,
            meeting: meetings[i],
            completedLessonsIDs: consumptionModel.completedLessonsIDs,
            isExpanded: isPretestPassed && i == state.uncompletedSectionIndex,
            disabled: isContentDisabled,
          ),
        );
      }
    }

    if (consumptionModel.postQualificationTest != null) {
      children.add(
        TrainingTestContents(
          postQualificationTest: consumptionModel.postQualificationTest,
          isExpanded: isPostTestExpanded(consumptionModel),
          trainingId: consumptionModel.trainingId,
          disabled: isContentDisabled || isPostTestDisabled(meetings),
        ),
      );
    }

    children.add(const SizedBox(height: 32));

    return children;
  }

  bool isPostTestDisabled(List<Meeting>? meetings) {
    return meetings != null &&
        meetings.any(
          (element) =>
              element.meetingStatus != MeetingStatus.Passed &&
              element.meetingStatus != MeetingStatus.Cancelled,
        );
  }

  bool isPostTestExpanded(TrainingConsumptionModel consumptionModel) {
    final isPretestPassed =
        consumptionModel.preQualificationTest == null || (consumptionModel.preTestPassed ?? false);
    final postQualificationTest = consumptionModel.postQualificationTest;
    final isPostTestPassed = postQualificationTest?.resolutionDate != null &&
        (postQualificationTest?.finalScore ?? -1) >= (postQualificationTest?.minimumScore ?? 0);

    return isPretestPassed &&
        consumptionModel.passedSections == consumptionModel.allSections &&
        !isPostTestPassed;
  }

  void scrollToUncompletedSection(int uncompletedLessonIndexInList) {
    _scrollController.animateTo(
      uncompletedLessonIndexInList * 100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }
}
