import 'package:collection/collection.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/router.dart';

/// ---------------------- //
/// 1) Shared model types  //
/// ---------------------- //

/// ContentType helps us decide if it's a meeting, lesson, or post-test
enum ContentType { meeting, lesson, qualificationTest, none }

/// Sealed base class for any piece of content in the training
abstract class TrainingContent {
  const TrainingContent();
}

/// For meeting content
class Meeting<PERSON>ontent extends TrainingContent {
  const MeetingContent(this.meeting);

  final Meeting meeting;
}

/// For normal lesson
class LessonContent extends TrainingContent {
  const LessonContent(this.lesson);

  final Lesson lesson;
}

/// For a post-test
class PostQualificationTestContent extends TrainingContent {
  const PostQualificationTestContent();
}

/// No content
class NoContent extends TrainingContent {
  const NoContent();
}

/// A helper for returning the “type” of content + the content object + optional Section
class ContentNavigationResult {
  const ContentNavigationResult({required this.type, required this.content, this.section});

  final ContentType type;
  final TrainingContent content;
  final Section? section;

  bool get hasContent => content is! NoContent;
}

/// ------------------------------------------------------ //
/// 2) A single mixin that unifies all navigation logic    //
/// ------------------------------------------------------ //

mixin UnifiedNavigationMixin {
  /// ------------------------------------------------------------------------ //
  ///    A) Helper to retrieve sections or meetings from the bloc/state
  /// ------------------------------------------------------------------------ //

  /// Return all sections from the bloc state.
  List<Section> getSections(TrainingConsumptionBloc bloc) {
    return bloc.state.trainingStructure?.sections ?? [];
  }

  void navigateToFirstLesson(TrainingConsumptionBloc bloc) {
    final sections = bloc.state.trainingStructure?.sections ?? [];
    if (sections.isEmpty) {
      router.pop();
      return;
    }

    final firstSection = sections.first;
    if (firstSection.lessons.isEmpty) {
      router.pop();
      return;
    }

    final firstLesson = firstSection.lessons.first;
    final completedIDs = bloc.state.trainingConsumptionModel?.completedLessonsIDs ?? {};

    final params = LessonParams(
      section: firstSection,
      lesson: firstLesson,
      completedLessonsInSection: completedIDs.length,
      isCompleted: completedIDs.contains(firstLesson.id),
      trainingConsumptionBloc: bloc,
      lessonNavigationType: LessonNavigationType.forward,
    );

    bloc.add(OpenLessonEvent(params));
  }

  /// Check if we can navigate to post-qualification test after the
  /// *very last lesson* in the *very last section*.
  bool canNavigateToPostQualificationTest(LessonParams lessonParams) {
    final bloc = lessonParams.trainingConsumptionBloc;
    final state = bloc.state;

    final isLastLesson = lessonParams.section.lessons.last.id == lessonParams.lesson.id &&
        state.trainingStructure?.sections.last.id == lessonParams.section.id;

    final hasPostTest = (state.trainingConsumptionModel?.postQualificationTest != null);

    return isLastLesson && hasPostTest;
  }

  /// Get the *next* lesson in the same section or the first lesson in the next section.
  /// Returns (null, null) if none found.
  (Lesson?, Section?) getNextLesson(List<Section> sections, LessonParams lessonParams) {
    final currentSectionIndex = sections.indexWhere((s) => s.id == lessonParams.section.id);
    if (currentSectionIndex == -1) return (null, null);

    final currentSection = sections[currentSectionIndex];
    final currentLessonIndex = currentSection.lessons.indexWhere(
      (l) => l.id == lessonParams.lesson.id,
    );
    if (currentLessonIndex == -1) return (null, null);

    // Next lesson in the same section
    if (currentLessonIndex < currentSection.lessons.length - 1) {
      final nextLesson = currentSection.lessons[currentLessonIndex + 1];
      return (nextLesson, currentSection);
    }

    // Or first lesson in the next section
    if (currentSectionIndex < sections.length - 1) {
      final nextSection = sections[currentSectionIndex + 1];
      if (nextSection.lessons.isNotEmpty) {
        return (nextSection.lessons.first, nextSection);
      }
    }

    // None found
    return (null, null);
  }

  /// Get the *previous* lesson in the same section or the last lesson in the previous section.
  /// Returns (null, null) if none found.
  (Lesson?, Section?) getPreviousLesson(List<Section> sections, LessonParams lessonParams) {
    final currentSectionIndex = sections.indexWhere((s) => s.id == lessonParams.section.id);
    if (currentSectionIndex == -1) return (null, null);

    final currentSection = sections[currentSectionIndex];
    final currentLessonIndex = currentSection.lessons.indexWhere(
      (l) => l.id == lessonParams.lesson.id,
    );
    if (currentLessonIndex == -1) return (null, null);

    // Previous lesson in the same section
    if (currentLessonIndex > 0) {
      final previousLesson = currentSection.lessons[currentLessonIndex - 1];
      return (previousLesson, currentSection);
    }

    // Or last lesson in the previous section
    if (currentSectionIndex > 0) {
      final previousSection = sections[currentSectionIndex - 1];
      if (previousSection.lessons.isNotEmpty) {
        return (previousSection.lessons.last, previousSection);
      }
    }

    // None found
    return (null, null);
  }

  List<Meeting>? getMeetings(TrainingConsumptionBloc bloc) {
    final trainingDetailsModel = bloc.trainingDetailsModel;
    if (trainingDetailsModel.studyStreams.isEmpty) return null;

    final enrolledStream = trainingDetailsModel.applicantDto?.studyStreams.firstWhereOrNull(
      (stream) => stream?.status == Constants.ENROLLED,
    );
    if (enrolledStream == null) return null;

    final actualStream = trainingDetailsModel.studyStreams.firstWhereOrNull(
      (s) => s?.id == enrolledStream.streamId,
    );
    return actualStream?.liveSession?.meetings;
  }

  /// If a meeting references certain sections via [meeting.sections],
  /// return all lessons from those sections.
  List<Lesson> getMeetingLessons(Meeting meeting, List<Section> sections) {
    // If meeting.sections is empty, assume it's associated with all sections
    if (meeting.sections?.isEmpty ?? true) {
      return sections.expand((section) => section.lessons).toList();
    }

    // Otherwise use only the specified sections
    final matchingSections =
        sections.where((section) => meeting.sections?.contains(section.id) ?? false).toList();
    return matchingSections.expand((section) => section.lessons).toList();
  }

  /// ------------------------------------------------------------------------ //
  ///    B) Next and previous content logic, covering sub-lessons & normal
  /// ------------------------------------------------------------------------ //
  ContentNavigationResult getNextContent(
    TrainingConsumptionBloc bloc,
    Meeting? currentMeeting, [
    Lesson? currentLesson,
  ]) {
    final sections = getSections(bloc);

    // 1) If we are inside a meeting (sub-lesson):
    if (currentMeeting != null) {
      final meetingLessons = getMeetingLessons(currentMeeting, sections);

      if (currentLesson != null) {
        final i = meetingLessons.indexOf(currentLesson);
        if (i != -1 && i < meetingLessons.length - 1) {
          final nextLesson = meetingLessons[i + 1];
          final nextSection = sections.firstWhereOrNull((s) => s.lessons.contains(nextLesson));
          return ContentNavigationResult(
            type: ContentType.lesson,
            content: LessonContent(nextLesson),
            section: nextSection,
          );
        }
      }
      // Else: if the user is on the meeting page itself (no lesson),
      // we open the first sub-lesson (if any).
      if (currentLesson == null && meetingLessons.isNotEmpty) {
        final firstLesson = meetingLessons.first;
        final nextSection = sections.firstWhereOrNull((s) => s.lessons.contains(firstLesson));
        return ContentNavigationResult(
          type: ContentType.lesson,
          content: LessonContent(firstLesson),
          section: nextSection,
        );
      }

      // If no more sub-lessons, check next meeting
      final meetings = getMeetings(bloc);
      if (meetings != null && meetings.isNotEmpty) {
        final mIndex = meetings.indexOf(currentMeeting);
        if (mIndex != -1 && mIndex < meetings.length - 1) {
          final nextMeeting = meetings[mIndex + 1];
          final nextSection = sections.firstWhereOrNull(
            (s) => nextMeeting.sections?.contains(s.id) ?? false,
          );
          return ContentNavigationResult(
            type: ContentType.meeting,
            content: MeetingContent(nextMeeting),
            section: nextSection,
          );
        }
      }

      // If no more meetings, try normal lessons after these sections
      if (currentMeeting.sections?.isNotEmpty == true) {
        final lastMeetingSectionId = currentMeeting.sections!.last;
        final indexInSections = sections.indexWhere((s) => s.id == lastMeetingSectionId);
        if (indexInSections != -1 && indexInSections < sections.length - 1) {
          final nextSection = sections[indexInSections + 1];
          if (nextSection.lessons.isNotEmpty) {
            return ContentNavigationResult(
              type: ContentType.lesson,
              content: LessonContent(nextSection.lessons.first),
              section: nextSection,
            );
          }
        }
      }

      // If still none, check post-test
      final canOpenPostTest = isPostTestAvailable(bloc.state.trainingConsumptionModel);
      if (canOpenPostTest && !isPostTestDisabled(getMeetings(bloc))) {
        return const ContentNavigationResult(
          type: ContentType.qualificationTest,
          content: PostQualificationTestContent(),
        );
      }

      // Otherwise, no content
      return const ContentNavigationResult(type: ContentType.none, content: NoContent());
    }

    // 2) If this is a normal lesson (no meeting):
    final (lesson, section) = _getNextLesson(bloc, currentLesson);
    if (lesson != null && section != null) {
      return ContentNavigationResult(
        type: ContentType.lesson,
        content: LessonContent(lesson),
        section: section,
      );
    }

    // If no next lesson, check post-test
    final canOpenPost = isPostTestAvailable(bloc.state.trainingConsumptionModel);
    if (canOpenPost && !isPostTestDisabled(getMeetings(bloc))) {
      return const ContentNavigationResult(
        type: ContentType.qualificationTest,
        content: PostQualificationTestContent(),
      );
    }

    // Nothing left
    return const ContentNavigationResult(type: ContentType.none, content: NoContent());
  }

  ContentNavigationResult getPreviousContent(
    TrainingConsumptionBloc bloc,
    Meeting? currentMeeting, [
    Lesson? currentLesson,
  ]) {
    final sections = getSections(bloc);

    if (currentMeeting != null) {
      final meetingLessons = getMeetingLessons(currentMeeting, sections);

      // If there's a current sub-lesson, find the previous sub-lesson
      if (currentLesson != null) {
        final i = meetingLessons.indexOf(currentLesson);
        if (i > 0) {
          final prevLesson = meetingLessons[i - 1];
          final prevSection = sections.firstWhereOrNull((s) => s.lessons.contains(prevLesson));
          return ContentNavigationResult(
            type: ContentType.lesson,
            content: LessonContent(prevLesson),
            section: prevSection,
          );
        }

        final meetings = getMeetings(bloc);
        if (meetings != null && meetings.isNotEmpty) {
          final mIndex = meetings.indexOf(currentMeeting);
          if (mIndex > 0) {
            final prevMeeting = meetings[mIndex - 1];

            // Just go to the previous meeting page
            final prevSection = sections.firstWhereOrNull(
              (s) => prevMeeting.sections?.contains(s.id) ?? false,
            );
            return ContentNavigationResult(
              type: ContentType.meeting,
              content: MeetingContent(prevMeeting),
              section: prevSection ?? sections.first,
            );
          }
        }

        // If no previous meeting, return to current meeting page
        return ContentNavigationResult(
          type: ContentType.meeting,
          content: MeetingContent(currentMeeting),
          section:
              sections.firstWhereOrNull((s) => currentMeeting.sections?.contains(s.id) ?? false) ??
                  sections.first,
        );
      }

      // If on the meeting page, check previous meeting
      final meetings = getMeetings(bloc);
      if (meetings != null && meetings.isNotEmpty) {
        final mIndex = meetings.indexOf(currentMeeting);
        if (mIndex > 0) {
          final prevMeeting = meetings[mIndex - 1];

          // Just go to the previous meeting page
          final prevSection = sections.firstWhereOrNull(
            (s) => prevMeeting.sections?.contains(s.id) ?? false,
          );
          return ContentNavigationResult(
            type: ContentType.meeting,
            content: MeetingContent(prevMeeting),
            section: prevSection ?? sections.first,
          );
        }
      }
    }

    // 2) If it's a normal lesson
    final (lesson, section) = _getPreviousLesson(bloc, currentLesson);
    if (lesson != null && section != null) {
      return ContentNavigationResult(
        type: ContentType.lesson,
        content: LessonContent(lesson),
        section: section,
      );
    }

    return const ContentNavigationResult(type: ContentType.none, content: NoContent());
  }

  void openContent(
    TrainingConsumptionBloc bloc,
    ContentNavigationResult result,
    LessonNavigationType navigationType,
  ) {
    if (!result.hasContent) return;

    switch (result.content) {
      case MeetingContent(meeting: final meeting):
        bloc.add(
          OpenLiveSessionLessonEvent(meeting: meeting, lessonNavigationType: navigationType),
        );

      case LessonContent(lesson: final lesson):
        final completedIDs = bloc.state.trainingConsumptionModel?.completedLessonsIDs ?? {};
        final section = result.section;
        if (section == null) return;

        final meetings = getMeetings(bloc);
        final associatedMeeting = meetings?.firstWhereOrNull(
          (m) => getMeetingLessons(m, getSections(bloc)).contains(lesson),
        );

        if (associatedMeeting != null) {
          bloc.add(
            OpenLiveSessionLessonEvent(
              meeting: associatedMeeting,
              lessonNavigationType: navigationType,
              subLesson: lesson,
            ),
          );
        } else {
          final params = LessonParams(
            section: section,
            lesson: lesson,
            completedLessonsInSection: completedIDs.length,
            isCompleted: completedIDs.contains(lesson.id),
            trainingConsumptionBloc: bloc,
            lessonNavigationType: navigationType,
          );
          bloc.add(OpenLessonEvent(params));
        }

      case PostQualificationTestContent():
        bloc.add(const OpenPostQualificationTestEvent());

      case NoContent():
        break;
    }
  }

  /// Helper for normal (non-meeting) lessons: find the next lesson
  (Lesson?, Section?) _getNextLesson(TrainingConsumptionBloc bloc, Lesson? currentLesson) {
    if (currentLesson == null) return (null, null);
    final sections = getSections(bloc);

    // find the section that has the lesson
    final sectionIndex = sections.indexWhere((s) => s.lessons.contains(currentLesson));
    if (sectionIndex == -1) return (null, null);

    final currentSection = sections[sectionIndex];
    final lessonIndex = currentSection.lessons.indexOf(currentLesson);
    if (lessonIndex == -1) return (null, null);

    // next lesson in same section
    if (lessonIndex < currentSection.lessons.length - 1) {
      return (currentSection.lessons[lessonIndex + 1], currentSection);
    }

    // or first lesson in next section
    if (sectionIndex < sections.length - 1) {
      final nextSection = sections[sectionIndex + 1];
      if (nextSection.lessons.isNotEmpty) {
        return (nextSection.lessons.first, nextSection);
      }
    }
    return (null, null);
  }

  /// Helper for normal (non-meeting) lessons: find the previous lesson
  (Lesson?, Section?) _getPreviousLesson(TrainingConsumptionBloc bloc, Lesson? currentLesson) {
    if (currentLesson == null) return (null, null);
    final sections = getSections(bloc);

    final sectionIndex = sections.indexWhere((s) => s.lessons.contains(currentLesson));
    if (sectionIndex == -1) return (null, null);

    final currentSection = sections[sectionIndex];
    final lessonIndex = currentSection.lessons.indexOf(currentLesson);
    if (lessonIndex == -1) return (null, null);

    // previous lesson in same section
    if (lessonIndex > 0) {
      return (currentSection.lessons[lessonIndex - 1], currentSection);
    }

    // or last lesson in previous section
    if (sectionIndex > 0) {
      final prevSection = sections[sectionIndex - 1];
      if (prevSection.lessons.isNotEmpty) {
        return (prevSection.lessons.last, prevSection);
      }
    }
    return (null, null);
  }

  /// ------------------------------------------------------------------------ ///
  ///  C) Checking post-test availability, etc.
  /// ------------------------------------------------------------------------ ///
  bool isPostTestAvailable(TrainingConsumptionModel? model) {
    if (model == null) return false;

    final preTest = model.preQualificationTest;
    // If pre-test exists and is mandatory but not passed, post-test is not available
    if (preTest != null && preTest.mandatory == true && !(model.preTestPassed ?? false)) {
      return false;
    }

    return model.postQualificationTest != null;
  }

  bool isPostTestDisabled(List<Meeting>? meetings) {
    if (meetings == null || meetings.isEmpty) return false;
    // If there's any meeting that's not cancelled/passed => post test is disabled
    return meetings.any(
      (m) => m.meetingStatus != MeetingStatus.Passed && m.meetingStatus != MeetingStatus.Cancelled,
    );
  }

  /// If there's no "next" content (lesson or meeting)
  /// and no post-test exists => it's the last content
  bool isLastContent(LessonParams lessonParams) {
    final bloc = lessonParams.trainingConsumptionBloc;
    // 1) Check if there's next content (lesson, sub-lesson, or meeting)
    final nextResult = getNextContent(bloc, lessonParams.meeting, lessonParams.lesson);
    if (nextResult.hasContent) {
      return false;
    }

    // 2) Check if a post-test exists AND not disabled
    final model = bloc.state.trainingConsumptionModel;
    final hasPostTestAvailable =
        isPostTestAvailable(model) && !isPostTestDisabled(getMeetings(bloc));

    // If post-test exists, it's NOT the last content
    return !hasPostTestAvailable;
  }
}
