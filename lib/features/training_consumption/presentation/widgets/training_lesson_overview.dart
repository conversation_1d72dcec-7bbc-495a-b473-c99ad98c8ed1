import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class TrainingLessonOverview extends StatelessWidget {
  const TrainingLessonOverview({
    required this.lesson,
    required this.isCompleted,
    required this.onTap,
    super.key,
  });

  final Lesson lesson;
  final bool isCompleted;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Icon(
                size: 20,
                isCompleted ? Icons.check_circle_outline : Icons.circle_outlined,
                color: isCompleted ? AppColors.greenAccentPrimary : AppColors.neutralBlack,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lesson.title,
                    style: context.textTheme.textMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (lesson.lessonType?.name != null)
                    Text(
                      lesson.lessonType?.name ?? '',
                      style: context.textTheme.textSmall.greyPrimary,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
