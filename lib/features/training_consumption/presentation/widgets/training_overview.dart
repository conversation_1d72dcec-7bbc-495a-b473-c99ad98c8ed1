import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/completed_sections_progress_info.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class TrainingOverview extends StatelessWidget {
  const TrainingOverview({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TrainingConsumptionBloc, TrainingConsumptionState>(
      builder: (context, state) {
        final trainingConsumptionModel = state.trainingConsumptionModel;

        if (trainingConsumptionModel != null) {
          return Container(
            color: Colors.white,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LinearProgressIndicator(
                  minHeight: 4,
                  borderRadius: BorderRadius.circular(40),
                  value: trainingConsumptionModel.passedScore / 100,
                  color: AppColors.greenAccentPrimary,
                  backgroundColor: const Color(0xFFE6E6E7),
                ),

                const SizedBox(height: 8),

                CompletedSectionsProgressInfo(
                  passedSections: trainingConsumptionModel.passedSections,
                  allSections: trainingConsumptionModel.allSections,
                  trainingCompletedPercent: trainingConsumptionModel.passedScore,
                ),

                const SizedBox(height: 12),

                TextButton(
                  onPressed: () => router.pushNamed(
                    Routes.trainingDetailsPage.name,
                    extra: trainingConsumptionModel.trainingId,
                  ),
                  child: Text(
                    LocaleKeys.trainingOverview.tr(),
                    style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                  ),
                ),

                const SizedBox(height: 4),
                //
              ],
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
