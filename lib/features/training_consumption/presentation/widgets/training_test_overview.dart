import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/qualification_test/data/params/qualification_test_params.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class TrainingTestOverview extends StatelessWidget {
  const TrainingTestOverview(this.qualificationTestModel, {required this.trainingId, super.key});

  final QualificationTestModel qualificationTestModel;
  final String trainingId;

  @override
  Widget build(BuildContext context) {
    final isCompleted = isTestCompleted(qualificationTestModel);
    final trainingConsumptionBloc = context.read<TrainingConsumptionBloc>();

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      // Opening the qualification test page and refreshing it when leaving that page
      onTap: () => router
          .pushNamed(
            Routes.qualificationTestPage.name,
            extra: QualificationTestParams(
              qualificationTestModel: qualificationTestModel,
              trainingConsumptionBloc: trainingConsumptionBloc,
              trainingId: trainingId,
            ),
          )
          .then(
            (value) => trainingConsumptionBloc.add(const RefreshTrainingConsumptionPageEvent()),
          ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Icon(
                isCompleted ? Icons.check_circle_outline_rounded : Icons.circle_outlined,
                color: isCompleted ? AppColors.greenAccentPrimary : AppColors.neutralBlack,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(qualificationTestModel.title ?? '', style: context.textTheme.textMedium),
                  Text(LocaleKeys.test.tr(), style: context.textTheme.textSmall.greyPrimary),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool isTestCompleted(QualificationTestModel qualificationTestModel) {
    return qualificationTestModel.resolutionDate != null &&
        (qualificationTestModel.finalScore ?? -1) >= (qualificationTestModel.minimumScore ?? 0);
  }
}
