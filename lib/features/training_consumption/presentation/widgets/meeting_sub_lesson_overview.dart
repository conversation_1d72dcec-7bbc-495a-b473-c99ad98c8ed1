import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class MeetingSubLessonOverview extends StatelessWidget {
  const MeetingSubLessonOverview({
    required this.lesson,
    required this.isCompleted,
    required this.onTap,
    super.key,
  });

  final Lesson lesson;
  final bool isCompleted;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 20),
              Text(
                lesson.title,
                style: context.textTheme.textMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Text(lesson.lessonType?.name ?? '', style: context.textTheme.textSmall.greyPrimary),
            ],
          ),
        ],
      ),
    );
  }
}
