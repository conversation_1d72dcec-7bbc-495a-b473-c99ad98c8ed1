import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_expander_icon.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_lesson_overview.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingSectionCard extends StatefulWidget {
  const TrainingSectionCard({
    required this.completedLessonsIDs,
    required this.disabled,
    required this.section,
    this.isExpanded,
    super.key,
  });

  final Section section;
  final bool? isExpanded;
  final bool disabled;

  final Set<String> completedLessonsIDs;

  @override
  State<TrainingSectionCard> createState() => _TrainingSectionCardState();
}

class _TrainingSectionCardState extends State<TrainingSectionCard> {
  late final List<Lesson> lessons;
  late final TrainingConsumptionBloc trainingConsumptionBloc;

  int completedLessonsCount = 0;
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    if (!widget.disabled) {
      isExpanded = widget.isExpanded ?? false;
    }
    lessons = widget.section.lessons;
    // count of completed lessons in this section
    completedLessonsCount =
        widget.completedLessonsIDs.intersection(lessons.map((l) => l.id).toSet()).length;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    trainingConsumptionBloc = context.read<TrainingConsumptionBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: widget.disabled ? null : () => setState(() => isExpanded = !isExpanded),
            child: BlocConsumer<TrainingConsumptionBloc, TrainingConsumptionState>(
              bloc: trainingConsumptionBloc,
              listenWhen: (previous, current) =>
                  previous.trainingConsumptionModel != current.trainingConsumptionModel,
              listener: (context, state) {
                completedLessonsCount = state.trainingConsumptionModel?.completedLessonsIDs
                        .intersection(lessons.map((l) => l.id).toSet())
                        .length ??
                    completedLessonsCount;
              },
              builder: (context, state) {
                return Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.section.title,
                              style: context.textTheme.textMedium.semiBold.copyWith(
                                color: widget.disabled
                                    ? AppColors.greySecondary
                                    : AppColors.neutralBlack,
                              ),
                              maxLines: 2,
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              LocaleKeys.completedLessonsCount.tr(
                                args: ['$completedLessonsCount', '${lessons.length}'],
                              ),
                              style: context.textTheme.textSmall.copyWith(
                                color: widget.disabled
                                    ? AppColors.greySecondary
                                    : AppColors.neutralBlack,
                              ),
                            ),
                          ],
                        ),
                      ),
                      AppExpanderIcon(isExpanded: isExpanded, disabled: widget.disabled),
                    ],
                  ),
                );
              },
            ),
          ),

          if (isExpanded)
            for (int i = 0; i < lessons.length; i++)
              TrainingLessonOverview(
                onTap: () => trainingConsumptionBloc.add(
                  OpenLessonEvent(
                    LessonParams(
                      lesson: lessons[i],
                      section: widget.section,
                      completedLessonsInSection: completedLessonsCount,
                      isCompleted: widget.completedLessonsIDs.contains(lessons[i].id),
                      trainingConsumptionBloc: trainingConsumptionBloc,
                    ),
                  ),
                ),
                isCompleted: widget.completedLessonsIDs.contains(lessons[i].id),
                lesson: lessons[i],
              ),
          //
        ],
      ),
    );
  }
}
