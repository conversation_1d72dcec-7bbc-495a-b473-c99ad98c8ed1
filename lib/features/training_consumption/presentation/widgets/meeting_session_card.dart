import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_expander_icon.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/cancelled_label.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/meeting_sub_lesson_overview.dart';

class MeetingsSessionCard extends StatefulWidget {
  const MeetingsSessionCard({
    required this.completedLessonsIDs,
    required this.disabled,
    required this.meeting,
    required this.trainingStructureModel,
    this.isExpanded,
    super.key,
  });

  final Meeting meeting;
  final bool? isExpanded;
  final bool disabled;
  final TrainingStructureModel trainingStructureModel;

  final Set<String> completedLessonsIDs;

  @override
  State<MeetingsSessionCard> createState() => _MeetingsSessionCardState();
}

class _MeetingsSessionCardState extends State<MeetingsSessionCard> {
  late final List<Lesson> lessons;
  late final TrainingConsumptionBloc trainingConsumptionBloc;

  int completedLessonsCount = 0;
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();

    lessons = widget.trainingStructureModel.sections
        .where((element) => widget.meeting.sections?.contains(element.id) ?? false)
        .expand((section) => section.lessons)
        .toList();
    // count of completed lessons in this section
    completedLessonsCount =
        widget.completedLessonsIDs.intersection(lessons.map((l) => l.id).toSet()).length;
    if (!widget.disabled) {
      isExpanded = (widget.isExpanded ?? false) && lessons.isNotEmpty;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    trainingConsumptionBloc = context.read<TrainingConsumptionBloc>();
  }

  @override
  Widget build(BuildContext context) {
    final isMeetingCancelled = widget.meeting.meetingStatus == MeetingStatus.Cancelled;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        trainingConsumptionBloc.add(OpenLiveSessionLessonEvent(meeting: widget.meeting));
      },
      child: Container(
        color: Colors.white,
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.fromLTRB(16, 16, 10, 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BlocListener<TrainingConsumptionBloc, TrainingConsumptionState>(
              bloc: trainingConsumptionBloc,
              listenWhen: (previous, current) =>
                  previous.trainingConsumptionModel != current.trainingConsumptionModel,
              listener: listener,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Icon(size: 20, getMeetingStatusIcon(), color: getIconColor()),
                            ),

                            const SizedBox(width: 8),

                            Text(
                              formatTime(widget.meeting.startDate),
                              style: context.textTheme.textMedium.semiBold.copyWith(
                                color: widget.disabled
                                    ? AppColors.greySecondary
                                    : AppColors.neutralBlack,
                                decoration: isMeetingCancelled ? TextDecoration.lineThrough : null,
                                decorationColor: AppColors.neutralBlack,
                                decorationThickness: 2.0,
                              ),
                              maxLines: 2,
                              softWrap: true,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(width: 8),

                            if (isMeetingCancelled) const CancelledLabel(),

                            //
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${formatTimeRange(widget.meeting.startDate, widget.meeting.startDate.add(Duration(hours: widget.meeting.durationHours)))} GMT +3:00',
                          style: context.textTheme.textSmall.greyPrimary,
                        ),
                      ],
                    ),
                  ),

                  if (lessons.isNotEmpty)
                    GestureDetector(
                      onTap:
                          widget.disabled ? null : () => setState(() => isExpanded = !isExpanded),
                      child: AppExpanderIcon(isExpanded: isExpanded, disabled: widget.disabled),
                    ),

                  //
                ],
              ),
            ),
            if (isExpanded) ...[
              const SizedBox(height: 12),
              for (int i = 0; i < lessons.length; i++) ...[
                MeetingSubLessonOverview(
                  onTap: () {
                    trainingConsumptionBloc.add(
                      OpenLessonEvent(
                        LessonParams(
                          lesson: lessons[i],
                          section: widget.trainingStructureModel.sections.firstWhere(
                            (element) => element.lessons.contains(lessons[i]),
                          ),
                          completedLessonsInSection: completedLessonsCount,
                          isCompleted: widget.completedLessonsIDs.contains(lessons[i].id),
                          trainingConsumptionBloc: trainingConsumptionBloc,
                        ),
                      ),
                    );
                  },
                  isCompleted: widget.completedLessonsIDs.contains(lessons[i].id),
                  lesson: lessons[i],
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  void listener(BuildContext context, TrainingConsumptionState state) {
    completedLessonsCount = state.trainingConsumptionModel?.completedLessonsIDs
            .intersection(lessons.map((l) => l.id).toSet())
            .length ??
        completedLessonsCount;
  }

  IconData getMeetingStatusIcon() {
    switch (widget.meeting.meetingStatus) {
      case MeetingStatus.Cancelled:
        return Icons.cancel_outlined;
      case MeetingStatus.Passed:
        return Icons.check_circle_outline;
      case MeetingStatus.Rescheduled:
        return Icons.error_outline_rounded;
      case MeetingStatus.Live:
      case MeetingStatus.Upcoming:
        return Icons.circle_outlined;
    }
  }

  Color getIconColor() {
    switch (widget.meeting.meetingStatus) {
      case MeetingStatus.Cancelled:
        return AppColors.greySecondary;
      case MeetingStatus.Passed:
        return AppColors.greenAccentPrimary;
      case MeetingStatus.Rescheduled:
        return AppColors.orangeAccentPrimary;
      case MeetingStatus.Live:
      case MeetingStatus.Upcoming:
        return AppColors.greySecondary;
    }
  }
}
