import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

class TrainingStructureAppbar extends StatelessWidget {
  const TrainingStructureAppbar({
    required this.trainingDetailsModel,
    required this.constraints,
    super.key,
  });

  final TrainingDetailsModel trainingDetailsModel;
  final BoxConstraints constraints;

  @override
  Widget build(BuildContext context) {
    final textSpan = TextSpan(
      text: trainingDetailsModel.title,
      style: context.textTheme.textLarge.semiBold.white,
    );
    final tp = TextPainter(text: textSpan, textDirection: TextDirection.ltr)
      ..layout(maxWidth: constraints.maxWidth - 32);
    final titleLineCount = tp.computeLineMetrics().length;
    final double expandedHeight = titleLineCount == 1 ? kToolbarHeight : 140;

    return SliverAppBar(
      leading: const AppBackButton(iconColor: Colors.white),
      backgroundColor: AppColors.greenAccentDark,
      expandedHeight: expandedHeight,
      floating: true,
      pinned: true,
      snap: true,
      title: titleLineCount == 1
          ? Text(trainingDetailsModel.title, style: context.textTheme.textLarge.semiBold.white)
          : null,
      flexibleSpace: titleLineCount == 1
          ? null
          : FlexibleSpaceBar(
              background: Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    trainingDetailsModel.title,
                    style: context.textTheme.textLarge.semiBold.white,
                  ),
                ),
              ),
            ),
    );
  }
}
