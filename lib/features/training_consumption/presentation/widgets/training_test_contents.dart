import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_expander_icon.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/training_test_overview.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingTestContents extends StatefulWidget {
  const TrainingTestContents({
    required this.isExpanded,
    required this.trainingId,
    required this.disabled,
    this.preQualificationTest,
    this.postQualificationTest,
    super.key,
  });

  final PreQualificationTest? preQualificationTest;
  final PostQualificationTest? postQualificationTest;
  final bool isExpanded;
  final bool disabled;
  final String trainingId;

  @override
  State<TrainingTestContents> createState() => _TrainingTestContentsState();
}

class _TrainingTestContentsState extends State<TrainingTestContents> {
  bool isExpanded = false;

  @override
  void initState() {
    super.initState();
    if (!widget.disabled) {
      isExpanded = widget.isExpanded;
    }
  }

  @override
  Widget build(BuildContext context) {
    final qualificationTestModel = widget.preQualificationTest ?? widget.postQualificationTest;

    return Container(
      margin: const EdgeInsets.only(top: 8),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: widget.disabled ? null : () => setState(() => isExpanded = !isExpanded),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.preQualificationTest != null
                            ? LocaleKeys.trainingView_preQualificationTest.tr()
                            : LocaleKeys.trainingView_postEvaluationTest.tr(),
                        style: context.textTheme.textMedium.semiBold.copyWith(
                          color: widget.disabled ? AppColors.greySecondary : AppColors.neutralBlack,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (qualificationTestModel?.mandatory ?? false)
                        Text(
                          LocaleKeys.trainingView_mandatoryTest.tr(),
                          style: context.textTheme.textSmall.copyWith(
                            color:
                                widget.disabled ? AppColors.greySecondary : AppColors.neutralBlack,
                          ),
                        ),
                    ],
                  ),
                  AppExpanderIcon(isExpanded: isExpanded, disabled: widget.disabled),
                ],
              ),
            ),
          ),

          if (isExpanded && qualificationTestModel != null)
            TrainingTestOverview(qualificationTestModel, trainingId: widget.trainingId),

          //
        ],
      ),
    );
  }
}
