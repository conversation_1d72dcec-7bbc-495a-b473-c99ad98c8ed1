import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CancelledLabel extends StatelessWidget {
  const CancelledLabel({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      decoration: BoxDecoration(
        color: AppColors.statusWarningLight,
        border: Border.all(color: AppColors.statusWarning),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Text(LocaleKeys.cancelled.tr(), style: context.textTheme.textSmall.statusWarning),
    );
  }
}
