import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/data_sources/training_consumption_datasource.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

@injectable
class TrainingConsumptionRepository {
  const TrainingConsumptionRepository({required TrainingConsumptionDatasource dataSource})
      : _dataSource = dataSource;

  final TrainingConsumptionDatasource _dataSource;

  Future<TrainingConsumptionModel> getTrainingConsumptionDetails(String courseId) async {
    final trainingConsumptionModel = await _dataSource.getTrainingConsumptionDetails(courseId);

    return trainingConsumptionModel.copyWith(
      completedLessonsIDs: trainingConsumptionModel.progress
          .where((e) => e.isPassed ?? false)
          .map((e) => e.lessonId)
          .toSet(),
    );
  }

  Future<void> markLessonAsCompleted({
    required String trainingId,
    required String lessonId,
  }) =>
      _dataSource.markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId);

  Future<String> getVideoUrl(String videoKey) => _dataSource.getVideoUrl(videoKey);

  Future<void> downloadSlide(Resource resource, Function(double) onProgress) =>
      _dataSource.downloadSlide(resource, onProgress);

  Future<void> downloadFile(String fileKey, String? fileName) =>
      _dataSource.downloadFile(fileKey, fileName);
}
