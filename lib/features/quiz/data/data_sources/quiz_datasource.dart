import 'dart:async';

import 'package:dio/dio.dart';

import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';

@injectable
class QuizDatasource {
  const QuizDatasource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<QuizResult> submitAnswer(
    String trainingId,
    String lessonId,
    AnswerModel answerModel,
  ) async {
    final response = await _dio.put(
      ApiConstants.applicantsTrainings + trainingId + ApiConstants.quizAnswerSubmitPath + lessonId,
      data: [answerModel.toJson()],
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      return QuizResult.from<PERSON>son(response.data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
