// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuizResult {
  int? get totalScore;
  Map<String, QuestionAnswerPair?> get questionAnswerPairs;

  /// Create a copy of QuizResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizResultCopyWith<QuizResult> get copyWith =>
      _$QuizResultCopyWithImpl<QuizResult>(this as QuizResult, _$identity);

  /// Serializes this QuizResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizResult &&
            (identical(other.totalScore, totalScore) || other.totalScore == totalScore) &&
            const DeepCollectionEquality().equals(other.questionAnswerPairs, questionAnswerPairs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, totalScore, const DeepCollectionEquality().hash(questionAnswerPairs));

  @override
  String toString() {
    return 'QuizResult(totalScore: $totalScore, questionAnswerPairs: $questionAnswerPairs)';
  }
}

/// @nodoc
abstract mixin class $QuizResultCopyWith<$Res> {
  factory $QuizResultCopyWith(QuizResult value, $Res Function(QuizResult) _then) =
      _$QuizResultCopyWithImpl;
  @useResult
  $Res call({int? totalScore, Map<String, QuestionAnswerPair?> questionAnswerPairs});
}

/// @nodoc
class _$QuizResultCopyWithImpl<$Res> implements $QuizResultCopyWith<$Res> {
  _$QuizResultCopyWithImpl(this._self, this._then);

  final QuizResult _self;
  final $Res Function(QuizResult) _then;

  /// Create a copy of QuizResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalScore = freezed,
    Object? questionAnswerPairs = null,
  }) {
    return _then(_self.copyWith(
      totalScore: freezed == totalScore
          ? _self.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      questionAnswerPairs: null == questionAnswerPairs
          ? _self.questionAnswerPairs
          : questionAnswerPairs // ignore: cast_nullable_to_non_nullable
              as Map<String, QuestionAnswerPair?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _QuizResult implements QuizResult {
  const _QuizResult(
      {required this.totalScore,
      required final Map<String, QuestionAnswerPair?> questionAnswerPairs})
      : _questionAnswerPairs = questionAnswerPairs;
  factory _QuizResult.fromJson(Map<String, dynamic> json) => _$QuizResultFromJson(json);

  @override
  final int? totalScore;
  final Map<String, QuestionAnswerPair?> _questionAnswerPairs;
  @override
  Map<String, QuestionAnswerPair?> get questionAnswerPairs {
    if (_questionAnswerPairs is EqualUnmodifiableMapView) return _questionAnswerPairs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_questionAnswerPairs);
  }

  /// Create a copy of QuizResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizResultCopyWith<_QuizResult> get copyWith =>
      __$QuizResultCopyWithImpl<_QuizResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$QuizResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizResult &&
            (identical(other.totalScore, totalScore) || other.totalScore == totalScore) &&
            const DeepCollectionEquality()
                .equals(other._questionAnswerPairs, _questionAnswerPairs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, totalScore, const DeepCollectionEquality().hash(_questionAnswerPairs));

  @override
  String toString() {
    return 'QuizResult(totalScore: $totalScore, questionAnswerPairs: $questionAnswerPairs)';
  }
}

/// @nodoc
abstract mixin class _$QuizResultCopyWith<$Res> implements $QuizResultCopyWith<$Res> {
  factory _$QuizResultCopyWith(_QuizResult value, $Res Function(_QuizResult) _then) =
      __$QuizResultCopyWithImpl;
  @override
  @useResult
  $Res call({int? totalScore, Map<String, QuestionAnswerPair?> questionAnswerPairs});
}

/// @nodoc
class __$QuizResultCopyWithImpl<$Res> implements _$QuizResultCopyWith<$Res> {
  __$QuizResultCopyWithImpl(this._self, this._then);

  final _QuizResult _self;
  final $Res Function(_QuizResult) _then;

  /// Create a copy of QuizResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? totalScore = freezed,
    Object? questionAnswerPairs = null,
  }) {
    return _then(_QuizResult(
      totalScore: freezed == totalScore
          ? _self.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int?,
      questionAnswerPairs: null == questionAnswerPairs
          ? _self._questionAnswerPairs
          : questionAnswerPairs // ignore: cast_nullable_to_non_nullable
              as Map<String, QuestionAnswerPair?>,
    ));
  }
}

// dart format on
