import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

part 'quiz_result.freezed.dart';

part 'quiz_result.g.dart';

@freezed
abstract class QuizResult with _$QuizResult {
  const factory QuizResult({
    required int? totalScore,
    required Map<String, QuestionAnswerPair?> questionAnswerPairs,
  }) = _QuizResult;

  factory QuizResult.fromJson(Map<String, dynamic> json) => _$QuizResultFromJson(json);
}
