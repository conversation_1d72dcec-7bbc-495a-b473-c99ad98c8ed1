import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/quiz/data/data_sources/quiz_datasource.dart';
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';

@injectable
class QuizRepository {
  const QuizRepository({required QuizDatasource dataSource}) : _dataSource = dataSource;

  final QuizDatasource _dataSource;

  Future<QuizResult> submitAnswer(String trainingId, String lessonId, AnswerModel answerModel) =>
      _dataSource.submitAnswer(trainingId, lessonId, answerModel);
}
