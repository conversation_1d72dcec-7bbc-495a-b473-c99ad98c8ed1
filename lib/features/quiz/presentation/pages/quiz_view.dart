import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/qualification_test/domain/converters/model_converter.dart';
import 'package:national_skills_platform/features/quiz/presentation/bloc/quiz_bloc.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answer_tile.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/lesson_header.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class QuizView extends StatelessWidget {
  const QuizView({required this.lessonParams, super.key});

  final LessonParams lessonParams;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<QuizBloc>().state;
    final currentQuestion = state.quiz?.questions[state.currentQuestionIndex];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        LessonHeader(lessonParams),
        const SizedBox(height: 8),
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              color: Colors.white,
              border: Border.symmetric(
                horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
              ),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  Text(
                    LocaleKeys.trainingView_questionOutOf.tr(
                      args: [
                        (state.currentQuestionIndex + 1).toString(),
                        (state.quiz?.questions.length ?? 0).toString(),
                      ],
                    ),
                    style: context.textTheme.textSmall.greyPrimary,
                  ),

                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    minHeight: 4,
                    borderRadius: BorderRadius.circular(40),
                    value: (state.currentQuestionIndex + 1) / (state.quiz?.questions.length ?? 1),
                    color: AppColors.greenAccentPrimary,
                    backgroundColor: const Color(0xFFE6E6E7),
                  ),
                  const SizedBox(height: 16),

                  ///Question
                  Text(
                    currentQuestion?.question ?? '',
                    style: context.textTheme.textLarge.semiBold,
                  ),

                  const SizedBox(height: 16),

                  Text(
                    LocaleKeys.trainingBuilder_tests_questionDescription_choose_singular_answer
                        .tr(),
                    style: context.textTheme.textMedium.greyPrimary,
                  ),

                  ///Answers
                  for (int i = 0; i < (currentQuestion?.answers.length ?? 0); i++)
                    AnswerTile(
                      //generates A, B.. letters for options
                      answerOption: String.fromCharCode(65 + i),
                      answer: currentQuestion?.answers[i],
                      isSelected: state.selectedAnswers.containsKey(currentQuestion?.id) &&
                          state.selectedAnswers[currentQuestion?.id]?.answerIds.firstOrNull ==
                              currentQuestion?.answers[i].id,
                      onTap: () => context.read<QuizBloc>().add(
                            SelectAnswerEvent(
                              convertAnswerToQuestionAnswerPair(
                                currentQuestion?.answers[i],
                                currentQuestion?.id,
                                currentQuestion?.question,
                              ),
                            ),
                          ),
                    ),

                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
