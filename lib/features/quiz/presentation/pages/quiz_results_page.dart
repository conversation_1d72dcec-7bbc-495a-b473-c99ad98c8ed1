import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answer_result_title.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answers_of_question.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/results_page_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';

class QuizResultsPage extends StatelessWidget {
  const QuizResultsPage({required this.lessonParams, required this.quizResult, super.key});

  final QuizResult? quizResult;
  final LessonParams lessonParams;

  @override
  Widget build(BuildContext context) {
    final questions = lessonParams.lesson.quiz?.questions ?? [];
    final questionAnswerPairs = quizResult?.questionAnswerPairs;

    return ListView(
      children: [
        ResultsPageHeader(finalScore: quizResult?.totalScore, minPassingScore: 0),
        const SizedBox(height: 8),
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border.symmetric(
              horizontal: BorderSide(color: AppColors.accentLight, width: 0.5),
            ),
          ),
          child: Column(
            children: [
              if (questionAnswerPairs != null)

                ///All Questions
                for (int i = 0; i < questions.length; i++)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AnswerResultTitle(
                        isCorrect: questionAnswerPairs[questions[i]?.id]?.correct ?? false,
                      ),

                      ///Question number and question
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '${i + 1}. ${questions[i]?.question}',
                          style: context.textTheme.textLarge.semiBold,
                        ),
                      ),

                      const SizedBox(height: 16),

                      ///All answers of the question
                      AnswersOfQuiz(
                        questionIndex: i,
                        questions: questions,
                        questionAnswerPairs: questionAnswerPairs,
                      ),

                      const AppDivider(padding: EdgeInsets.only(top: 16)),
                    ],
                  ),
            ],
          ),
        ),
      ],
    );
  }
}
