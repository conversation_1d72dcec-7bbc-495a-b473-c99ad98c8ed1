import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/presentation/bloc/quiz_bloc.dart';
import 'package:national_skills_platform/features/quiz/presentation/pages/quiz_results_page.dart';
import 'package:national_skills_platform/features/quiz/presentation/pages/quiz_view.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/quiz_bottom_navigation.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/router.dart';

class QuizPage extends StatefulWidget {
  const QuizPage({required this.lessonParams, required this.quizResult, super.key});

  final LessonParams lessonParams;
  final QuizResult? quizResult;

  @override
  State<QuizPage> createState() => _QuizPageState();
}

class _QuizPageState extends State<QuizPage> {
  QuizResult? quizResult;
  late final QuizBloc quizBloc;

  @override
  void initState() {
    super.initState();
    quizBloc = GetIt.instance.get<QuizBloc>();
    _initQuiz();
  }

  void _initQuiz() {
    quizBloc.add(
      LoadQuizEvent(
        lesson: widget.lessonParams.lesson,
        quizResult: widget.quizResult,
        trainingDetailsModel: widget.lessonParams.trainingConsumptionBloc.trainingDetailsModel,
      ),
    );
  }

  @override
  void didUpdateWidget(QuizPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If we're navigating to a different lesson, reinitialize the quiz
    if (oldWidget.lessonParams.lesson.id != widget.lessonParams.lesson.id) {
      quizResult = null; // Reset result when changing lessons
      _initQuiz();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => quizBloc,
      child: Scaffold(
        backgroundColor: AppColors.uiBackgroundPrimary,
        body: BlocConsumer<QuizBloc, QuizState>(
          listener: (context, state) {
            if (state.errorMessage.isNotEmpty) {
              showAppToast(context, message: state.errorMessage);
            }
            if (state.currentQuestionIndex >= (state.quiz?.questions.length ?? 0)) {
              quizResult = state.quizResultsModel;
            }
          },
          builder: (context, state) {
            final quiz = state.quiz;
            if (quiz == null) return const BuildLoader();

            return AppLoadingOverlay(
              isLoading: state.submittingAnswer,
              child: Scaffold(
                appBar: AppBar(
                  leading: CloseButton(color: Colors.white, onPressed: () => router.pop()),
                  title: Text(
                    widget.lessonParams.lesson.title,
                    style: context.textTheme.textSmall.semiBold.white,
                  ),
                  backgroundColor: AppColors.greenAccentPrimary,
                ),
                bottomNavigationBar: QuizBottomNavigation(
                  quizResult: quizResult,
                  lessonParams: widget.lessonParams,
                ),
                body: quizResult?.totalScore != null ||
                        state.currentQuestionIndex >= quiz.questions.length
                    ? QuizResultsPage(lessonParams: widget.lessonParams, quizResult: quizResult)
                    : QuizView(lessonParams: widget.lessonParams),
              ),
            );
          },
        ),
      ),
    );
  }
}
