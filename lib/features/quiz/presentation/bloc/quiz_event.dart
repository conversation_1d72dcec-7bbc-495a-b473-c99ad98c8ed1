part of 'quiz_bloc.dart';

sealed class QuizEvent {
  const QuizEvent();
}

class LoadQuizEvent extends QuizEvent {
  final Lesson? lesson;
  final TrainingDetailsModel trainingDetailsModel;
  final QuizResult? quizResult;

  const LoadQuizEvent({
    required this.lesson,
    required this.trainingDetailsModel,
    required this.quizResult,
  });
}

class NavigateToPreviousQuestionEvent extends QuizEvent {
  const NavigateToPreviousQuestionEvent();
}

class SubmitAnswerEvent extends QuizEvent {
  const SubmitAnswerEvent();
}

class SelectAnswerEvent extends QuizEvent {
  final QuestionAnswerPair? answer;

  const SelectAnswerEvent(this.answer);
}
