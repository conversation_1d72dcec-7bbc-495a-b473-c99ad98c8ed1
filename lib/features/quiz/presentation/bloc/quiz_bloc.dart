import 'dart:async';
import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/domain/repositories/quiz_repository.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

part 'quiz_event.dart';

part 'quiz_bloc.freezed.dart';

part 'quiz_state.dart';

@injectable
class QuizBloc extends Bloc<QuizEvent, QuizState> {
  QuizBloc({required QuizRepository quizRepository, required Random random})
      : _quizRepository = quizRepository,
        _random = random,
        super(QuizState(selectedAnswers: {})) {
    on<LoadQuizEvent>(_loadQuizEvent);
    on<SelectAnswerEvent>(_selectAnswerEvent);
    on<SubmitAnswerEvent>(_submitAnswerEvent);
    on<NavigateToPreviousQuestionEvent>(_navigateToPreviousQuestionEvent);
  }

  final QuizRepository _quizRepository;
  final Random _random;
  late TrainingDetailsModel trainingDetailsModel;

  void _loadQuizEvent(LoadQuizEvent event, Emitter<QuizState> emit) {
    trainingDetailsModel = event.trainingDetailsModel;

    final randomize = event.lesson?.quiz?.randomized ?? false;
    final questions = randomize
        ? _getShuffledQuestions(
            allQuestions: event.lesson?.quiz?.questions ?? [],
            answered: event.quizResult?.questionAnswerPairs ?? {},
            random: _random,
          )
        : event.lesson?.quiz?.questions ?? [];

    emit(
      QuizState(
        selectedAnswers: Map.from(event.quizResult?.questionAnswerPairs ?? {}),
        lesson: event.lesson,
        quizResultsModel: event.quizResult,
        quiz: event.lesson?.quiz?.copyWith(questions: questions),
        currentQuestionIndex: event.quizResult?.questionAnswerPairs.length ?? 0,
      ),
    );
  }

  Future<void> _submitAnswerEvent(SubmitAnswerEvent event, Emitter<QuizState> emit) async {
    emit(state.copyWith(submittingAnswer: true, errorMessage: ''));
    final questionId = state.quiz?.questions[state.currentQuestionIndex]?.id;
    final lessonId = state.lesson?.id;

    if (questionId == null || lessonId == null) return emitError(emit);

    final answerID = state.selectedAnswers[questionId]?.answerIds.firstOrNull;

    if (answerID == null) return emitError(emit);

    final answerModel = AnswerModel(questionId: questionId, answerIds: [answerID]);

    await _quizRepository.submitAnswer(trainingDetailsModel.id, lessonId, answerModel).errorHandler(
          onSuccess: (quizResultsModel) async {
            emit(
              state.copyWith(
                submittingAnswer: false,
                currentQuestionIndex: state.currentQuestionIndex + 1,
                quizResultsModel: quizResultsModel,
                errorMessage: '',
              ),
            );
          },
          onError: (errorMsg) =>
              emit(state.copyWith(submittingAnswer: false, errorMessage: errorMsg)),
        );
  }

  void emitError(Emitter<QuizState> emit) {
    emit(
      state.copyWith(
        submittingAnswer: false,
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    );
  }

  void _navigateToPreviousQuestionEvent(
    NavigateToPreviousQuestionEvent event,
    Emitter<QuizState> emit,
  ) {
    emit(state.copyWith(currentQuestionIndex: state.currentQuestionIndex - 1, errorMessage: ''));
  }

  void _selectAnswerEvent(SelectAnswerEvent event, Emitter<QuizState> emit) {
    final answer = event.answer;
    if (answer == null) return;

    final currentQuestionID = state.quiz?.questions[state.currentQuestionIndex]?.id;
    if (currentQuestionID == null) return;

    emit(state.copyWith(updatingAnswerSelection: true, errorMessage: ''));
    //bloc state doesn't detect mutable collections changes, thus "updatingAnswerSelection" property added so that bloc could identify changes in state
    state.selectedAnswers[currentQuestionID] = answer;

    emit(state.copyWith(selectedAnswers: state.selectedAnswers, updatingAnswerSelection: false));
  }

  List<Question?> _shuffleQuestions(List<Question?> questions, Random random) {
    // Map each non-null question to a new question with shuffled answers
    final shuffledQuestions = questions.map((question) {
      if (question == null) return null;
      final shuffledAnswers = List<Answer>.from(question.answers)..shuffle(random);
      return question.copyWith(answers: shuffledAnswers);
    }).toList()
      // Shuffle the entire list of questions after answers are shuffled
      ..shuffle(random);

    return shuffledQuestions;
  }

  // Function to get shuffled questions based on the randomized flag and answered questions
  List<Question?> _getShuffledQuestions({
    required List<Question?> allQuestions,
    required Map<String?, QuestionAnswerPair?> answered,
    required Random random,
  }) {
    final answeredQuestionIds = answered.keys.toList();
    final answeredQuestions =
        allQuestions.where((question) => answeredQuestionIds.contains(question?.id)).toList();
    final nonAnsweredQuestions =
        allQuestions.where((question) => !answeredQuestionIds.contains(question?.id)).toList();

    return [...answeredQuestions, ..._shuffleQuestions(nonAnsweredQuestions, random)];
  }
}
