part of 'quiz_bloc.dart';

@Freezed(makeCollectionsUnmodifiable: false)
abstract class QuizState with _$QuizState {
  factory QuizState({
    required Map<String, QuestionAnswerPair?> selectedAnswers,
    Quiz? quiz,
    Lesson? lesson,
    @Default(0) int currentQuestionIndex,
    @Default(false) bool submittingAnswer,
    @Default(false) bool updatingAnswerSelection,
    @Default('') String errorMessage,
    QuizResult? quizResultsModel,
  }) = _QuizState;
}
