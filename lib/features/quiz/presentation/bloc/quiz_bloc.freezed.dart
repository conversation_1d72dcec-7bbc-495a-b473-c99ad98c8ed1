// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'quiz_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$QuizState {
  Map<String, QuestionAnswerPair?> get selectedAnswers;
  Quiz? get quiz;
  Lesson? get lesson;
  int get currentQuestionIndex;
  bool get submittingAnswer;
  bool get updatingAnswerSelection;
  String get errorMessage;
  QuizResult? get quizResultsModel;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $QuizStateCopyWith<QuizState> get copyWith =>
      _$QuizStateCopyWithImpl<QuizState>(this as QuizState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is QuizState &&
            const DeepCollectionEquality().equals(other.selectedAnswers, selectedAnswers) &&
            (identical(other.quiz, quiz) || other.quiz == quiz) &&
            (identical(other.lesson, lesson) || other.lesson == lesson) &&
            (identical(other.currentQuestionIndex, currentQuestionIndex) ||
                other.currentQuestionIndex == currentQuestionIndex) &&
            (identical(other.submittingAnswer, submittingAnswer) ||
                other.submittingAnswer == submittingAnswer) &&
            (identical(other.updatingAnswerSelection, updatingAnswerSelection) ||
                other.updatingAnswerSelection == updatingAnswerSelection) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.quizResultsModel, quizResultsModel) ||
                other.quizResultsModel == quizResultsModel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(selectedAnswers),
      quiz,
      lesson,
      currentQuestionIndex,
      submittingAnswer,
      updatingAnswerSelection,
      errorMessage,
      quizResultsModel);

  @override
  String toString() {
    return 'QuizState(selectedAnswers: $selectedAnswers, quiz: $quiz, lesson: $lesson, currentQuestionIndex: $currentQuestionIndex, submittingAnswer: $submittingAnswer, updatingAnswerSelection: $updatingAnswerSelection, errorMessage: $errorMessage, quizResultsModel: $quizResultsModel)';
  }
}

/// @nodoc
abstract mixin class $QuizStateCopyWith<$Res> {
  factory $QuizStateCopyWith(QuizState value, $Res Function(QuizState) _then) =
      _$QuizStateCopyWithImpl;
  @useResult
  $Res call(
      {Map<String, QuestionAnswerPair?> selectedAnswers,
      Quiz? quiz,
      Lesson? lesson,
      int currentQuestionIndex,
      bool submittingAnswer,
      bool updatingAnswerSelection,
      String errorMessage,
      QuizResult? quizResultsModel});

  $QuizCopyWith<$Res>? get quiz;
  $LessonCopyWith<$Res>? get lesson;
  $QuizResultCopyWith<$Res>? get quizResultsModel;
}

/// @nodoc
class _$QuizStateCopyWithImpl<$Res> implements $QuizStateCopyWith<$Res> {
  _$QuizStateCopyWithImpl(this._self, this._then);

  final QuizState _self;
  final $Res Function(QuizState) _then;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedAnswers = null,
    Object? quiz = freezed,
    Object? lesson = freezed,
    Object? currentQuestionIndex = null,
    Object? submittingAnswer = null,
    Object? updatingAnswerSelection = null,
    Object? errorMessage = null,
    Object? quizResultsModel = freezed,
  }) {
    return _then(_self.copyWith(
      selectedAnswers: null == selectedAnswers
          ? _self.selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, QuestionAnswerPair?>,
      quiz: freezed == quiz
          ? _self.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
      lesson: freezed == lesson
          ? _self.lesson
          : lesson // ignore: cast_nullable_to_non_nullable
              as Lesson?,
      currentQuestionIndex: null == currentQuestionIndex
          ? _self.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      submittingAnswer: null == submittingAnswer
          ? _self.submittingAnswer
          : submittingAnswer // ignore: cast_nullable_to_non_nullable
              as bool,
      updatingAnswerSelection: null == updatingAnswerSelection
          ? _self.updatingAnswerSelection
          : updatingAnswerSelection // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      quizResultsModel: freezed == quizResultsModel
          ? _self.quizResultsModel
          : quizResultsModel // ignore: cast_nullable_to_non_nullable
              as QuizResult?,
    ));
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizCopyWith<$Res>? get quiz {
    if (_self.quiz == null) {
      return null;
    }

    return $QuizCopyWith<$Res>(_self.quiz!, (value) {
      return _then(_self.copyWith(quiz: value));
    });
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LessonCopyWith<$Res>? get lesson {
    if (_self.lesson == null) {
      return null;
    }

    return $LessonCopyWith<$Res>(_self.lesson!, (value) {
      return _then(_self.copyWith(lesson: value));
    });
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizResultCopyWith<$Res>? get quizResultsModel {
    if (_self.quizResultsModel == null) {
      return null;
    }

    return $QuizResultCopyWith<$Res>(_self.quizResultsModel!, (value) {
      return _then(_self.copyWith(quizResultsModel: value));
    });
  }
}

/// @nodoc

class _QuizState implements QuizState {
  _QuizState(
      {required this.selectedAnswers,
      this.quiz,
      this.lesson,
      this.currentQuestionIndex = 0,
      this.submittingAnswer = false,
      this.updatingAnswerSelection = false,
      this.errorMessage = '',
      this.quizResultsModel});

  @override
  final Map<String, QuestionAnswerPair?> selectedAnswers;
  @override
  final Quiz? quiz;
  @override
  final Lesson? lesson;
  @override
  @JsonKey()
  final int currentQuestionIndex;
  @override
  @JsonKey()
  final bool submittingAnswer;
  @override
  @JsonKey()
  final bool updatingAnswerSelection;
  @override
  @JsonKey()
  final String errorMessage;
  @override
  final QuizResult? quizResultsModel;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$QuizStateCopyWith<_QuizState> get copyWith =>
      __$QuizStateCopyWithImpl<_QuizState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _QuizState &&
            const DeepCollectionEquality().equals(other.selectedAnswers, selectedAnswers) &&
            (identical(other.quiz, quiz) || other.quiz == quiz) &&
            (identical(other.lesson, lesson) || other.lesson == lesson) &&
            (identical(other.currentQuestionIndex, currentQuestionIndex) ||
                other.currentQuestionIndex == currentQuestionIndex) &&
            (identical(other.submittingAnswer, submittingAnswer) ||
                other.submittingAnswer == submittingAnswer) &&
            (identical(other.updatingAnswerSelection, updatingAnswerSelection) ||
                other.updatingAnswerSelection == updatingAnswerSelection) &&
            (identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage) &&
            (identical(other.quizResultsModel, quizResultsModel) ||
                other.quizResultsModel == quizResultsModel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(selectedAnswers),
      quiz,
      lesson,
      currentQuestionIndex,
      submittingAnswer,
      updatingAnswerSelection,
      errorMessage,
      quizResultsModel);

  @override
  String toString() {
    return 'QuizState(selectedAnswers: $selectedAnswers, quiz: $quiz, lesson: $lesson, currentQuestionIndex: $currentQuestionIndex, submittingAnswer: $submittingAnswer, updatingAnswerSelection: $updatingAnswerSelection, errorMessage: $errorMessage, quizResultsModel: $quizResultsModel)';
  }
}

/// @nodoc
abstract mixin class _$QuizStateCopyWith<$Res> implements $QuizStateCopyWith<$Res> {
  factory _$QuizStateCopyWith(_QuizState value, $Res Function(_QuizState) _then) =
      __$QuizStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {Map<String, QuestionAnswerPair?> selectedAnswers,
      Quiz? quiz,
      Lesson? lesson,
      int currentQuestionIndex,
      bool submittingAnswer,
      bool updatingAnswerSelection,
      String errorMessage,
      QuizResult? quizResultsModel});

  @override
  $QuizCopyWith<$Res>? get quiz;
  @override
  $LessonCopyWith<$Res>? get lesson;
  @override
  $QuizResultCopyWith<$Res>? get quizResultsModel;
}

/// @nodoc
class __$QuizStateCopyWithImpl<$Res> implements _$QuizStateCopyWith<$Res> {
  __$QuizStateCopyWithImpl(this._self, this._then);

  final _QuizState _self;
  final $Res Function(_QuizState) _then;

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedAnswers = null,
    Object? quiz = freezed,
    Object? lesson = freezed,
    Object? currentQuestionIndex = null,
    Object? submittingAnswer = null,
    Object? updatingAnswerSelection = null,
    Object? errorMessage = null,
    Object? quizResultsModel = freezed,
  }) {
    return _then(_QuizState(
      selectedAnswers: null == selectedAnswers
          ? _self.selectedAnswers
          : selectedAnswers // ignore: cast_nullable_to_non_nullable
              as Map<String, QuestionAnswerPair?>,
      quiz: freezed == quiz
          ? _self.quiz
          : quiz // ignore: cast_nullable_to_non_nullable
              as Quiz?,
      lesson: freezed == lesson
          ? _self.lesson
          : lesson // ignore: cast_nullable_to_non_nullable
              as Lesson?,
      currentQuestionIndex: null == currentQuestionIndex
          ? _self.currentQuestionIndex
          : currentQuestionIndex // ignore: cast_nullable_to_non_nullable
              as int,
      submittingAnswer: null == submittingAnswer
          ? _self.submittingAnswer
          : submittingAnswer // ignore: cast_nullable_to_non_nullable
              as bool,
      updatingAnswerSelection: null == updatingAnswerSelection
          ? _self.updatingAnswerSelection
          : updatingAnswerSelection // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: null == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      quizResultsModel: freezed == quizResultsModel
          ? _self.quizResultsModel
          : quizResultsModel // ignore: cast_nullable_to_non_nullable
              as QuizResult?,
    ));
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizCopyWith<$Res>? get quiz {
    if (_self.quiz == null) {
      return null;
    }

    return $QuizCopyWith<$Res>(_self.quiz!, (value) {
      return _then(_self.copyWith(quiz: value));
    });
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LessonCopyWith<$Res>? get lesson {
    if (_self.lesson == null) {
      return null;
    }

    return $LessonCopyWith<$Res>(_self.lesson!, (value) {
      return _then(_self.copyWith(lesson: value));
    });
  }

  /// Create a copy of QuizState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuizResultCopyWith<$Res>? get quizResultsModel {
    if (_self.quizResultsModel == null) {
      return null;
    }

    return $QuizResultCopyWith<$Res>(_self.quizResultsModel!, (value) {
      return _then(_self.copyWith(quizResultsModel: value));
    });
  }
}

// dart format on
