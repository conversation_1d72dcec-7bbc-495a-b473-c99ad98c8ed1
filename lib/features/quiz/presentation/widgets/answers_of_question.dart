import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

class AnswersOfQuiz extends StatelessWidget {
  const AnswersOfQuiz({
    required this.questions,
    required this.questionIndex,
    required this.questionAnswerPairs,
    super.key,
  });

  final int questionIndex;
  final List<Question?> questions;
  final Map<String, QuestionAnswerPair?> questionAnswerPairs;

  @override
  Widget build(BuildContext context) {
    final allAnswerOptions = questions[questionIndex]?.answers;
    final userSelectedAnswer =
        questionAnswerPairs[questions[questionIndex]?.id]?.answerIds.firstOrNull;

    return Column(
      children: [
        if (allAnswerOptions != null)
          for (int j = 0; j < allAnswerOptions.length; j++)
            Container(
              color:
                  //selected answer by user == each answer is compared
                  userSelectedAnswer == allAnswerOptions[j].id
                      ? AppColors.uiBackgroundPrimary
                      : null,
              child: ListTile(
                minLeadingWidth: 0,
                dense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                leading: Text(
                  String.fromCharCode(65 + j),
                  style: context.textTheme.textSmall.semiBold.greyPrimary,
                ),
                title: Text(
                  allAnswerOptions[j].answer ?? '',
                  style: context.textTheme.textSmall.medium,
                ),
                //if the user selected answer is the same as the correct answer OR if , then show the correct icon
                trailing: userSelectedAnswer == allAnswerOptions[j].id
                    ? (questionAnswerPairs[questions[questionIndex]?.id]?.correct ?? false)
                        ? const Icon(Icons.check_rounded, color: AppColors.statusSuccess)
                        : const Icon(Icons.close_rounded, color: AppColors.statusWarning)
                    : null,
              ),
            ),
      ],
    );
  }
}
