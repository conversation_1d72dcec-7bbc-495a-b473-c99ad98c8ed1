import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

class AnswerTile extends StatelessWidget {
  const AnswerTile({
    required this.answerOption,
    required this.answer,
    required this.onTap,
    this.isSelected = false,
    super.key,
  });

  final bool isSelected;
  final String answerOption;
  final Answer? answer;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.accentExtraLight : Colors.white,
        border: Border.all(color: isSelected ? AppColors.greenAccentPrimary : AppColors.greyLight),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Text(answerOption, style: context.textTheme.textLarge.semiBold.accentGreenPrimary),
        title: Text(answer?.answer ?? '', style: context.textTheme.textSmall.medium),
      ),
    );
  }
}
