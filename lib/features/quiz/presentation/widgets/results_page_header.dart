import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ResultsPageHeader extends StatelessWidget {
  const ResultsPageHeader({
    required this.finalScore,
    required this.minPassingScore,
    this.onRetakePressed,
    super.key,
  });

  final int? finalScore;
  final int minPassingScore;
  final VoidCallback? onRetakePressed;

  @override
  Widget build(BuildContext context) {
    final isTestFailed = (finalScore ?? 0) < minPassingScore;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: AppColors.accentLight, width: 0.5)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(AssetsPath.successIcon, height: 24),
              const SizedBox(width: 8),
              Text(
                LocaleKeys.questions_results_quizCompleted.tr(),
                style: context.textTheme.textLarge.semiBold,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.questions_results_yourScore.tr(),
            style: context.textTheme.textSmall.medium,
          ),
          Text(
            LocaleKeys.questions_results_score.tr(args: [(finalScore ?? 0).toString()]),
            style: context.textTheme.h3.semiBold.copyWith(
              color: isTestFailed ? AppColors.statusWarning : AppColors.statusSuccess,
            ),
          ),
          if (isTestFailed) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onRetakePressed,
              child: Text(
                LocaleKeys.questions_results_retakeTest.tr(),
                style: context.textTheme.textSmall.semiBold.statusSuccess,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
