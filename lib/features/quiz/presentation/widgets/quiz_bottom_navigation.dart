import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/presentation/bloc/quiz_bloc.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/unified_navigation_mixin.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class QuizBottomNavigation extends StatelessWidget with UnifiedNavigationMixin {
  const QuizBottomNavigation({required this.quizResult, required this.lessonParams, super.key});

  final QuizResult? quizResult;
  final LessonParams lessonParams;

  /// Called when the quiz is fully completed (e.g. user answers the last question or sees results).
  void _handleQuizCompletion(BuildContext context) {
    final bloc = lessonParams.trainingConsumptionBloc
      // Refresh training consumption to update quiz result
      ..add(const RefreshTrainingConsumptionPageEvent());

    // Get next content first
    final nextResult = getNextContent(bloc, lessonParams.meeting, lessonParams.lesson);

    // If there's no next content, check for post-test
    if (!nextResult.hasContent) {
      final model = bloc.state.trainingConsumptionModel;
      final hasPostTest = model?.postQualificationTest != null;
      final preTestPassed = model?.preQualificationTest == null || (model?.preTestPassed ?? false);
      final meetings = getMeetings(bloc);
      final postTestDisabled = meetings?.any(
            (m) =>
                m.meetingStatus != MeetingStatus.Passed &&
                m.meetingStatus != MeetingStatus.Cancelled,
          ) ??
          false;

      final canOpenPostTest = hasPostTest && preTestPassed && !postTestDisabled;
      if (canOpenPostTest) {
        bloc.add(const OpenPostQualificationTestEvent());
        return;
      }

      router.pop();
      return;
    }

    // Has next content - navigate to it
    openContent(bloc, nextResult, LessonNavigationType.forward);
  }

  /// Called when tapping "Previous" after the quiz is done (or if the user is on the first quiz question).
  void _handlePreviousNavigation() {
    final bloc = lessonParams.trainingConsumptionBloc;

    // Navigate to previous content
    final prevResult = getPreviousContent(bloc, lessonParams.meeting, lessonParams.lesson);
    if (prevResult.hasContent) {
      openContent(bloc, prevResult, LessonNavigationType.backward);
    } else {
      router.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bottomPadding =
        MediaQuery.paddingOf(context).bottom != 0 ? MediaQuery.paddingOf(context).bottom : 16.0;

    return BlocBuilder<QuizBloc, QuizState>(
      builder: (context, state) {
        final questions = state.quiz?.questions ?? [];
        final currentQuestionID = (state.currentQuestionIndex < questions.length)
            ? questions[state.currentQuestionIndex]?.id
            : '';
        final isAnswerSelected = state.selectedAnswers[currentQuestionID] != null;
        final isQuizCompleted = quizResult?.totalScore != null ||
            state.currentQuestionIndex >= state.quiz!.questions.length;

        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(top: BorderSide(color: AppColors.accentLight, width: 0.5)),
          ),
          padding: EdgeInsets.only(bottom: bottomPadding, top: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // If we’re not on the first question, show "goBack" button
              // If the quiz is completed, show the "Previous" button to navigate
              if (state.currentQuestionIndex > 0 &&
                  state.currentQuestionIndex < state.quiz!.questions.length)
                _NavButton(
                  onTap: () =>
                      context.read<QuizBloc>().add(const NavigateToPreviousQuestionEvent()),
                  buttonText: LocaleKeys.questions_goBack.tr(),
                )
              else if (isQuizCompleted)
                _NavButton(
                  onTap: _handlePreviousNavigation,
                  buttonText: LocaleKeys.trainingView_previous.tr(),
                )
              else
                const Spacer(),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: IntrinsicWidth(
                  child: AppButton(
                    onTap: () {
                      if (isAnswerSelected) {
                        context.read<QuizBloc>().add(const SubmitAnswerEvent());
                      } else if (isQuizCompleted) {
                        _handleQuizCompletion(context);
                      }
                    },
                    backgroundColor: (isAnswerSelected || isQuizCompleted)
                        ? AppColors.greenAccentPrimary
                        : AppColors.greenAccentSecondary,
                    height: 36,
                    width: 100,
                    padding: EdgeInsets.zero,
                    buttonText:
                        isQuizCompleted ? LocaleKeys.done.tr() : LocaleKeys.questions_confirm.tr(),
                    textStyle: context.textTheme.textSmall.white.semiBold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _NavButton extends StatelessWidget {
  const _NavButton({required this.buttonText, required this.onTap});

  final String buttonText;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: IntrinsicWidth(
        child: AppButton(
          onTap: onTap,
          buttonText: buttonText,
          height: 36,
          width: 100,
          padding: EdgeInsets.zero,
          backgroundColor: Colors.white,
          borderColor: AppColors.accentLight,
          textStyle: context.textTheme.textSmall.accentGreenPrimary.semiBold,
        ),
      ),
    );
  }
}
