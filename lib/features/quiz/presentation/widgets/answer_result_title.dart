import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class AnswerResultTitle extends StatelessWidget {
  const AnswerResultTitle({required this.isCorrect, super.key});

  final bool isCorrect;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8),
      child: Row(
        children: [
          if (isCorrect) ...[
            const Icon(Icons.check_rounded, color: AppColors.statusSuccess),
            const SizedBox(width: 10),
            Text(
              LocaleKeys.trainingBuilder_tests_correctAnswer.tr(),
              style: context.textTheme.textSmall.medium.statusSuccess,
            ),
          ] else ...[
            const Icon(Icons.close_rounded, color: AppColors.statusWarning),
            const SizedBox(width: 10),
            Text(
              LocaleKeys.trainingBuilder_tests_incorrectAnswer.tr(),
              style: context.textTheme.textSmall.medium.statusWarning,
            ),
          ],
        ],
      ),
    );
  }
}
