import 'dart:async';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';

@injectable
class SearchDataSource {
  const SearchDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<SearchModel> search(String query) async {
    final response = await _dio.get(
      ApiConstants.searchPath,
      queryParameters: {Constants.term: query, Constants.page: 0, Constants.size: 6},
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      return SearchModel.fromJson(response.data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
