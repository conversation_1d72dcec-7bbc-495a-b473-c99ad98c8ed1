import 'package:json_annotation/json_annotation.dart';

part 'search_model.g.dart';

@JsonSerializable()
class SearchModel {
  SearchModel({required this.trainings, required this.learningTracks});

  factory SearchModel.fromJson(Map<String, dynamic> json) => _$SearchModelFromJson(json);

  final ContentData trainings;
  final ContentData learningTracks;
}

@JsonSerializable()
class ContentData {
  ContentData({
    required this.content,
    required this.pageable,
    required this.totalElements,
    required this.totalPages,
    required this.last,
    required this.numberOfElements,
    required this.size,
    required this.number,
    required this.sort,
    required this.first,
    required this.empty,
  });

  factory ContentData.fromJson(Map<String, dynamic> json) => _$ContentDataFromJson(json);
  final List<Content> content;
  final Pageable pageable;
  final int totalElements;
  final int totalPages;
  final bool last;
  final int numberOfElements;
  final int size;
  final int number;
  final List<dynamic> sort;
  final bool first;
  final bool empty;
}

@JsonSerializable()
class Content {
  Content({
    required this.id,
    required this.title,
    this.profileImageUrl,
    required this.organizationName,
  });

  factory Content.fromJson(Map<String, dynamic> json) => _$ContentFromJson(json);

  final String id;
  final String title;
  final String? profileImageUrl;
  final String organizationName;
}

@JsonSerializable()
class Pageable {
  Pageable({
    required this.pageNumber,
    required this.pageSize,
    required this.sort,
    required this.offset,
    required this.paged,
    required this.unpaged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => _$PageableFromJson(json);

  final int pageNumber;
  final int pageSize;
  final List<dynamic> sort;
  final int offset;
  final bool paged;
  final bool unpaged;
}
