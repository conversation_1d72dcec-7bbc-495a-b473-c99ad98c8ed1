import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/search/data/data_sources/search_data_source.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';

@injectable
class SearchRepository {
  const SearchRepository({required SearchDataSource searchDataSource})
      : _searchDataSource = searchDataSource;

  final SearchDataSource _searchDataSource;

  Future<SearchModel> search(String query) => _searchDataSource.search(query);
}
