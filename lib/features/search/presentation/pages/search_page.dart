import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/presentation/bloc/search_bloc.dart';
import 'package:national_skills_platform/features/search/presentation/widgets/no_results_found.dart';
import 'package:national_skills_platform/features/search/presentation/widgets/search_result_preview.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({required this.courseType, super.key});

  final CourseType courseType;

  static const _previewItemsLimit = 6;

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  late SearchBloc searchBloc;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    searchBloc = GetIt.instance.get<SearchBloc>();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: Text(
          widget.courseType == CourseType.training
              ? LocaleKeys.search_for_training.tr()
              : LocaleKeys.search_for_ltrack.tr(),
          style: context.textTheme.textLarge.semiBold,
        ),
        bottom: const PreferredSize(preferredSize: Size.fromHeight(0), child: AppDivider()),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const SizedBox(height: 24),

            /// Search Field
            AppInputField(
              hintText: LocaleKeys.search.tr(),
              onChanged: search,
              onSubmit: search,
              borderColor: AppColors.greenAccentPrimary,
              onClear: () => searchBloc.add(const SearchReset()),
              prefixIcon: Padding(
                padding: const EdgeInsets.all(12),
                child: Image.asset(
                  AssetsPath.searchIcon,
                  color: AppColors.greenAccentPrimary,
                  width: 16,
                ),
              ),
            ),

            Expanded(
              child: BlocBuilder<SearchBloc, SearchState>(
                bloc: searchBloc,
                builder: (context, state) {
                  if (state is Searching) return const BuildLoader();

                  if (state is SearchResultsState) {
                    final searchResults = state.searchResults;

                    if (noResultsFound(searchResults)) {
                      return const NoResultsFound();
                    }

                    return Column(
                      children: [
                        const SizedBox(height: 16),

                        /// Results Count and "View All" Button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              getResultsCount(widget.courseType, searchResults),
                              style: context.textTheme.textSmall.medium.greyAdditional,
                            ),
                            TextButton(
                              onPressed: () => router.pushNamed(
                                Routes.viewAllPage.name,
                                extra: [widget.courseType, state],
                              ),
                              child: Text(
                                LocaleKeys.view_all.tr(),
                                style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                              ),
                            ),
                          ],
                        ),

                        /// Search Results list
                        Expanded(
                          child: ListView.builder(
                            itemCount: getItemCount(widget.courseType, searchResults),
                            itemBuilder: (BuildContext context, int index) {
                              if (widget.courseType == CourseType.training) {
                                return SearchResultPreview(
                                  searchResult: searchResults.trainings.content[index],
                                  routeName: Routes.trainingDetailsPage.name,
                                );
                              }

                              return SearchResultPreview(
                                routeName: Routes.learningTrackDetailsPage.name,
                                searchResult: searchResults.learningTracks.content[index],
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  }

                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void search(String input) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (input.length >= 3) {
        searchBloc.add(SearchEvent(query: input));
      }
    });
  }

  String getResultsCount(CourseType courseType, SearchModel searchEntity) {
    return LocaleKeys.results.plural(
      courseType == CourseType.training
          ? searchEntity.trainings.totalElements
          : searchEntity.learningTracks.totalElements,
    );
  }

  int getItemCount(CourseType courseType, SearchModel searchResults) {
    int totalElements = 0;

    if (courseType == CourseType.training) {
      totalElements = searchResults.trainings.numberOfElements;
    } else if (courseType == CourseType.learningTrack) {
      totalElements = searchResults.learningTracks.numberOfElements;
    }

    return totalElements > SearchPage._previewItemsLimit
        ? SearchPage._previewItemsLimit
        : totalElements;
  }

  bool noResultsFound(SearchModel searchResults) =>
      (widget.courseType == CourseType.training && searchResults.trainings.totalElements == 0) ||
      (widget.courseType == CourseType.learningTrack &&
          searchResults.learningTracks.totalElements == 0);
}
