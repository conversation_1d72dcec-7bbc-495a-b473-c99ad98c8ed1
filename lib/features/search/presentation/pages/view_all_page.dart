import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/learning_tracks_view.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/trainings_view.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_header_panel.dart';
import 'package:national_skills_platform/features/search/presentation/bloc/search_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ViewAllPage extends StatelessWidget {
  const ViewAllPage({required this.courseType, required this.searchResultsState, super.key});

  final CourseType courseType;
  final SearchResultsState searchResultsState;

  @override
  Widget build(BuildContext context) {
    ///CupertinoScaffold used above Scaffold to enable Segue modal sheet
    return CupertinoScaffold(
      body: Scaffold(
        appBar: AppBar(
          leading: const AppBackButton(),
          title: Text(LocaleKeys.search_results.tr(), style: context.textTheme.textLarge.semiBold),
          bottom: const PreferredSize(preferredSize: Size.fromHeight(0), child: AppDivider()),
        ),
        body: courseType == CourseType.training
            ? TrainingsView(searchKey: searchResultsState.searchKey)
            : LearningTracksView(searchKey: searchResultsState.searchKey),
      ),
    );
  }
}
