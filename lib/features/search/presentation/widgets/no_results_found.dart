import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class NoResultsFound extends StatelessWidget {
  const NoResultsFound({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          backgroundColor: AppColors.accentExtraLight,
          radius: 30,
          child: Image.asset(
            AssetsPath.searchIcon,
            width: 24,
            height: 24,
            color: AppColors.greenAccentPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          LocaleKeys.trainingsList_noTrainingsFound.tr(),
          style: context.textTheme.textSmall.semiBold,
        ),
        const SizedBox(height: 8),
        Text(LocaleKeys.expand_search.tr(), style: context.textTheme.textSmall.regular),
      ],
    );
  }
}
