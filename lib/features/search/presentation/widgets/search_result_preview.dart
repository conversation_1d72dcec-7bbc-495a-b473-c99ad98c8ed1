import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/router.dart';

class SearchResultPreview extends StatelessWidget {
  const SearchResultPreview({required this.searchResult, required this.routeName, super.key});

  final Content searchResult;
  final String routeName;

  @override
  Widget build(BuildContext context) {
    final profileImageUrl = searchResult.profileImageUrl ?? '';

    return ListTile(
      contentPadding: EdgeInsets.zero,
      onTap: () => router.pushNamed(routeName, extra: searchResult.id),
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(4.0),
        child: Platform.environment.containsKey(Constants.flutterTest) || profileImageUrl.isEmpty
            ? Container(width: 32, height: 32, color: Colors.grey)
            : CachedNetworkImage(
                imageUrl: profileImageUrl,
                placeholder: (context, url) => const ShimmerPlaceholder(height: 32),
                fit: BoxFit.cover,
                width: 32,
                height: 32,
              ),
      ),
      title: Text(searchResult.title, style: context.textTheme.textSmall.medium),
      subtitle: Text(
        searchResult.organizationName,
        style: context.textTheme.textXSmall.medium.accentGreenDark,
      ),
    );
  }
}
