part of 'search_bloc.dart';

sealed class SearchState extends Equatable {
  const SearchState();
}

final class SearchInitial extends SearchState {
  const SearchInitial();

  @override
  List<Object> get props => [];
}

final class Searching extends SearchState {
  const Searching();

  @override
  List<Object> get props => [];
}

final class SearchResultsState extends SearchState {
  const SearchResultsState(this.searchResults, this.searchKey);
  final SearchModel searchResults;
  final String searchKey;

  @override
  List<Object> get props => [searchResults, searchKey];
}

final class SearchError extends SearchState {
  const SearchError(this.msg);

  final String msg;

  @override
  List<Object> get props => [msg];
}
