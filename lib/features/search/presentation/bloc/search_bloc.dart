import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/domain/repositories/search_repository.dart';

part 'search_event.dart';

part 'search_state.dart';

@injectable
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  SearchBloc({required SearchRepository searchRepository})
      : _searchRepository = searchRepository,
        super(const SearchInitial()) {
    on<SearchEvent>(_searchEvent);
    on<SearchReset>(_searchReset);
  }

  final SearchRepository _searchRepository;

  Future<void> _searchEvent(SearchEvent event, Emitter<SearchState> emit) async {
    if (event.query.isNotEmpty) {
      emit(const Searching());

      await _searchRepository.search(event.query).errorHandler(
            onSuccess: (SearchModel searchResultsEntity) async {
              emit(SearchResultsState(searchResultsEntity, event.query));
            },
            onError: (errorMsg) => emit(SearchError(errorMsg)),
          );
    }
  }

  Future<void> _searchReset(SearchReset event, Emitter<SearchState> emit) async {
    emit(const SearchInitial());
  }
}
