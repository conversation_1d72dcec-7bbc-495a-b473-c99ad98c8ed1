import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/domain/repositories/training_details_repository.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/my_learnings/domain/repositories/my_learnings_repository.dart';
import 'package:national_skills_platform/features/training_consumption/domain/repositories/training_consumption_repository.dart';
import 'package:national_skills_platform/router.dart';

part 'my_learnings_event.dart';

part 'my_learnings_state.dart';

part 'my_learnings_bloc.freezed.dart';

@singleton
class MyLearningsBloc extends Bloc<MyLearningsEvent, MyLearningsState> {
  MyLearningsBloc({
    required this.myLearningsRepository,
    required this.trainingDetailsRepository,
    required this.trainingConsumptionRepository,
    required this.trainingsBloc,
  }) : super(MyLearningsState()) {
    on<GetAllMyLearnings>(_getAllMyLearnings);
    on<ExploreLearningsEvent>(_exploreLearningsEvent);
    on<EnrollTrainingEvent>(_enrollTraining);
    on<ContinueTrainingEvent>(_continueTraining);
  }

  final MyLearningsRepository myLearningsRepository;
  final TrainingDetailsRepository trainingDetailsRepository;
  final TrainingConsumptionRepository trainingConsumptionRepository;
  final TrainingsBloc trainingsBloc;

  Future<void> _getAllMyLearnings(GetAllMyLearnings event, Emitter<MyLearningsState> emit) async {
    emit(state.copyWith(isLoading: true, error: ''));

    await myLearningsRepository.getMyLearnings().errorHandler(
      onSuccess: ((MyLearningsModel mylearningsModel, List<ApplicantTraining>) myLearnings) async {
        final myLearningsModel = myLearnings.$1;
        final allRecentTrainings = myLearnings.$2;
        emit(
          state.copyWith(
            isLoading: false,
            myLearningsModel: myLearningsModel,
            allRecentTrainings: allRecentTrainings,
          ),
        );
      },
      onError: (error) {
        emit(state.copyWith(isLoading: false, error: error));
      },
    );
  }

  Future<void> _exploreLearningsEvent(
    ExploreLearningsEvent event,
    Emitter<MyLearningsState> emit,
  ) async {
    switch (event.type) {
      case ExploreCardType.selfPaced:
        trainingsBloc.add(
          const ApplyFilterToList(
            FilterModel(trainingTypeFilterModel: TrainingTypeFilterModel(selfPaced: true)),
          ),
        );
        router.go('${Routes.rootPage.path}${Routes.catalogPage.path}');
      case ExploreCardType.onlineTraining:
        trainingsBloc.add(
          const ApplyFilterToList(
            FilterModel(trainingTypeFilterModel: TrainingTypeFilterModel(online: true)),
          ),
        );
        router.go('${Routes.rootPage.path}${Routes.catalogPage.path}');
      case ExploreCardType.inPerson:
        trainingsBloc.add(
          const ApplyFilterToList(
            FilterModel(trainingTypeFilterModel: TrainingTypeFilterModel(inPerson: true)),
          ),
        );
        router.go('${Routes.rootPage.path}${Routes.catalogPage.path}');
      case ExploreCardType.learningTrack:
        //path: rootPage/catalogPage?tab=learningTrack
        router.go(
          '${Routes.rootPage.path}${Routes.catalogPage.path}?${RouterQueryConstants.tab}=${RouterQueryConstants.learningTrackTab}',
        );
    }
  }

  Future<void> _enrollTraining(EnrollTrainingEvent event, Emitter<MyLearningsState> emit) async {
    emit(state.copyWith(isEnrollmentInProgress: true, enrollmentError: ''));

    /// First load training details
    await trainingDetailsRepository.loadTrainingDetails(event.trainingId).errorHandler(
      onSuccess: (trainingDetails) async {
        /// Enroll based on training type
        if (event.trainingType == TrainingType.SelfPaced) {
          await trainingDetailsRepository.enrollTraining(trainingId: event.trainingId).errorHandler(
            onSuccess: (_) async {
              emit(state.copyWith(isEnrollmentInProgress: false));

              /// Refresh my learnings list
              add(const GetAllMyLearnings());
            },
            onError: (error) {
              return emit(
                state.copyWith(isEnrollmentInProgress: false, enrollmentError: error),
              );
            },
          );
        } else if (event.trainingType == TrainingType.InstructorLed) {
          final streamId = trainingDetails.studyStreams.firstOrNull?.id;
          if (streamId != null) {
            await trainingDetailsRepository
                .enrollStream(trainingId: event.trainingId, streamId: streamId)
                .errorHandler(
              onSuccess: (_) async {
                emit(state.copyWith(isEnrollmentInProgress: false));

                /// Refresh my learnings list
                add(const GetAllMyLearnings());
              },
              onError: (error) {
                return emit(
                  state.copyWith(isEnrollmentInProgress: false, enrollmentError: error),
                );
              },
            );
          }
        }
      },
      onError: (error) {
        emit(state.copyWith(isEnrollmentInProgress: false, enrollmentError: error));
      },
    );
  }

  Future<void> _continueTraining(
    ContinueTrainingEvent event,
    Emitter<MyLearningsState> emit,
  ) async {
    emit(state.copyWith(isContinueInProgress: true, continueError: ''));

    /// Load training details first
    await trainingDetailsRepository.loadTrainingDetails(event.trainingId).errorHandler(
      onSuccess: (trainingDetails) async {
        emit(state.copyWith(isContinueInProgress: false));

        /// Navigate to training consumption page
        router.pushNamed(Routes.trainingConsumptionPage.name, extra: trainingDetails);
      },
      onError: (error) {
        emit(state.copyWith(isContinueInProgress: false, continueError: error));
      },
    );
  }
}
