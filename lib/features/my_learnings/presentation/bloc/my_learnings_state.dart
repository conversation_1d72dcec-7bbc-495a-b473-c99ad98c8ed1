part of 'my_learnings_bloc.dart';

@freezed
abstract class MyLearningsState with _$MyLearningsState {
  factory MyLearningsState({
    @Default(
      MyLearningsModel(
        applicantSelfPacedTrainingList: [],
        applicantOnlineStudyTrainingList: [],
        applicantInPersonStudyTrainingList: [],
        applicantLearningTrackViewList: [],
      ),
    )
    MyLearningsModel myLearningsModel,
    @Default([]) List<ApplicantTraining> allRecentTrainings,
    @Default(false) bool isLoading,
    @Default(false) bool isEnrollmentInProgress,
    @Default(false) bool isContinueInProgress,
    @Default('') String error,
    @Default('') String enrollmentError,
    @Default('') String continueError,
  }) = _MyLearningsState;
}
