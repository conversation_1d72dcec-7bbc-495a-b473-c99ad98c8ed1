// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'my_learnings_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MyLearningsState {
  MyLearningsModel get myLearningsModel;
  List<ApplicantTraining> get allRecentTrainings;
  bool get isLoading;
  bool get isEnrollmentInProgress;
  bool get isContinueInProgress;
  String get error;
  String get enrollmentError;
  String get continueError;

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MyLearningsStateCopyWith<MyLearningsState> get copyWith =>
      _$MyLearningsStateCopyWithImpl<MyLearningsState>(this as MyLearningsState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MyLearningsState &&
            (identical(other.myLearningsModel, myLearningsModel) ||
                other.myLearningsModel == myLearningsModel) &&
            const DeepCollectionEquality().equals(other.allRecentTrainings, allRecentTrainings) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.isEnrollmentInProgress, isEnrollmentInProgress) ||
                other.isEnrollmentInProgress == isEnrollmentInProgress) &&
            (identical(other.isContinueInProgress, isContinueInProgress) ||
                other.isContinueInProgress == isContinueInProgress) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.enrollmentError, enrollmentError) ||
                other.enrollmentError == enrollmentError) &&
            (identical(other.continueError, continueError) ||
                other.continueError == continueError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      myLearningsModel,
      const DeepCollectionEquality().hash(allRecentTrainings),
      isLoading,
      isEnrollmentInProgress,
      isContinueInProgress,
      error,
      enrollmentError,
      continueError);

  @override
  String toString() {
    return 'MyLearningsState(myLearningsModel: $myLearningsModel, allRecentTrainings: $allRecentTrainings, isLoading: $isLoading, isEnrollmentInProgress: $isEnrollmentInProgress, isContinueInProgress: $isContinueInProgress, error: $error, enrollmentError: $enrollmentError, continueError: $continueError)';
  }
}

/// @nodoc
abstract mixin class $MyLearningsStateCopyWith<$Res> {
  factory $MyLearningsStateCopyWith(MyLearningsState value, $Res Function(MyLearningsState) _then) =
      _$MyLearningsStateCopyWithImpl;
  @useResult
  $Res call(
      {MyLearningsModel myLearningsModel,
      List<ApplicantTraining> allRecentTrainings,
      bool isLoading,
      bool isEnrollmentInProgress,
      bool isContinueInProgress,
      String error,
      String enrollmentError,
      String continueError});

  $MyLearningsModelCopyWith<$Res> get myLearningsModel;
}

/// @nodoc
class _$MyLearningsStateCopyWithImpl<$Res> implements $MyLearningsStateCopyWith<$Res> {
  _$MyLearningsStateCopyWithImpl(this._self, this._then);

  final MyLearningsState _self;
  final $Res Function(MyLearningsState) _then;

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? myLearningsModel = null,
    Object? allRecentTrainings = null,
    Object? isLoading = null,
    Object? isEnrollmentInProgress = null,
    Object? isContinueInProgress = null,
    Object? error = null,
    Object? enrollmentError = null,
    Object? continueError = null,
  }) {
    return _then(_self.copyWith(
      myLearningsModel: null == myLearningsModel
          ? _self.myLearningsModel
          : myLearningsModel // ignore: cast_nullable_to_non_nullable
              as MyLearningsModel,
      allRecentTrainings: null == allRecentTrainings
          ? _self.allRecentTrainings
          : allRecentTrainings // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnrollmentInProgress: null == isEnrollmentInProgress
          ? _self.isEnrollmentInProgress
          : isEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      isContinueInProgress: null == isContinueInProgress
          ? _self.isContinueInProgress
          : isContinueInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
      enrollmentError: null == enrollmentError
          ? _self.enrollmentError
          : enrollmentError // ignore: cast_nullable_to_non_nullable
              as String,
      continueError: null == continueError
          ? _self.continueError
          : continueError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MyLearningsModelCopyWith<$Res> get myLearningsModel {
    return $MyLearningsModelCopyWith<$Res>(_self.myLearningsModel, (value) {
      return _then(_self.copyWith(myLearningsModel: value));
    });
  }
}

/// @nodoc

class _MyLearningsState implements MyLearningsState {
  _MyLearningsState(
      {this.myLearningsModel = const MyLearningsModel(
          applicantSelfPacedTrainingList: [],
          applicantOnlineStudyTrainingList: [],
          applicantInPersonStudyTrainingList: [],
          applicantLearningTrackViewList: []),
      final List<ApplicantTraining> allRecentTrainings = const [],
      this.isLoading = false,
      this.isEnrollmentInProgress = false,
      this.isContinueInProgress = false,
      this.error = '',
      this.enrollmentError = '',
      this.continueError = ''})
      : _allRecentTrainings = allRecentTrainings;

  @override
  @JsonKey()
  final MyLearningsModel myLearningsModel;
  final List<ApplicantTraining> _allRecentTrainings;
  @override
  @JsonKey()
  List<ApplicantTraining> get allRecentTrainings {
    if (_allRecentTrainings is EqualUnmodifiableListView) return _allRecentTrainings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allRecentTrainings);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isEnrollmentInProgress;
  @override
  @JsonKey()
  final bool isContinueInProgress;
  @override
  @JsonKey()
  final String error;
  @override
  @JsonKey()
  final String enrollmentError;
  @override
  @JsonKey()
  final String continueError;

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MyLearningsStateCopyWith<_MyLearningsState> get copyWith =>
      __$MyLearningsStateCopyWithImpl<_MyLearningsState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MyLearningsState &&
            (identical(other.myLearningsModel, myLearningsModel) ||
                other.myLearningsModel == myLearningsModel) &&
            const DeepCollectionEquality().equals(other._allRecentTrainings, _allRecentTrainings) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.isEnrollmentInProgress, isEnrollmentInProgress) ||
                other.isEnrollmentInProgress == isEnrollmentInProgress) &&
            (identical(other.isContinueInProgress, isContinueInProgress) ||
                other.isContinueInProgress == isContinueInProgress) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.enrollmentError, enrollmentError) ||
                other.enrollmentError == enrollmentError) &&
            (identical(other.continueError, continueError) ||
                other.continueError == continueError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      myLearningsModel,
      const DeepCollectionEquality().hash(_allRecentTrainings),
      isLoading,
      isEnrollmentInProgress,
      isContinueInProgress,
      error,
      enrollmentError,
      continueError);

  @override
  String toString() {
    return 'MyLearningsState(myLearningsModel: $myLearningsModel, allRecentTrainings: $allRecentTrainings, isLoading: $isLoading, isEnrollmentInProgress: $isEnrollmentInProgress, isContinueInProgress: $isContinueInProgress, error: $error, enrollmentError: $enrollmentError, continueError: $continueError)';
  }
}

/// @nodoc
abstract mixin class _$MyLearningsStateCopyWith<$Res> implements $MyLearningsStateCopyWith<$Res> {
  factory _$MyLearningsStateCopyWith(
          _MyLearningsState value, $Res Function(_MyLearningsState) _then) =
      __$MyLearningsStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {MyLearningsModel myLearningsModel,
      List<ApplicantTraining> allRecentTrainings,
      bool isLoading,
      bool isEnrollmentInProgress,
      bool isContinueInProgress,
      String error,
      String enrollmentError,
      String continueError});

  @override
  $MyLearningsModelCopyWith<$Res> get myLearningsModel;
}

/// @nodoc
class __$MyLearningsStateCopyWithImpl<$Res> implements _$MyLearningsStateCopyWith<$Res> {
  __$MyLearningsStateCopyWithImpl(this._self, this._then);

  final _MyLearningsState _self;
  final $Res Function(_MyLearningsState) _then;

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? myLearningsModel = null,
    Object? allRecentTrainings = null,
    Object? isLoading = null,
    Object? isEnrollmentInProgress = null,
    Object? isContinueInProgress = null,
    Object? error = null,
    Object? enrollmentError = null,
    Object? continueError = null,
  }) {
    return _then(_MyLearningsState(
      myLearningsModel: null == myLearningsModel
          ? _self.myLearningsModel
          : myLearningsModel // ignore: cast_nullable_to_non_nullable
              as MyLearningsModel,
      allRecentTrainings: null == allRecentTrainings
          ? _self._allRecentTrainings
          : allRecentTrainings // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEnrollmentInProgress: null == isEnrollmentInProgress
          ? _self.isEnrollmentInProgress
          : isEnrollmentInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      isContinueInProgress: null == isContinueInProgress
          ? _self.isContinueInProgress
          : isContinueInProgress // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
      enrollmentError: null == enrollmentError
          ? _self.enrollmentError
          : enrollmentError // ignore: cast_nullable_to_non_nullable
              as String,
      continueError: null == continueError
          ? _self.continueError
          : continueError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }

  /// Create a copy of MyLearningsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MyLearningsModelCopyWith<$Res> get myLearningsModel {
    return $MyLearningsModelCopyWith<$Res>(_self.myLearningsModel, (value) {
      return _then(_self.copyWith(myLearningsModel: value));
    });
  }
}

// dart format on
