part of 'my_learnings_bloc.dart';

enum ExploreCardType { selfPaced, onlineTraining, inPerson, learningTrack }

sealed class MyLearningsEvent {
  const MyLearningsEvent();
}

class ExploreLearningsEvent extends MyLearningsEvent {
  final ExploreCardType type;

  const ExploreLearningsEvent(this.type);
}

class GetAllMyLearnings extends MyLearningsEvent {
  const GetAllMyLearnings();
}

class EnrollTrainingEvent extends MyLearningsEvent {
  final String trainingId;
  final TrainingType trainingType;

  const EnrollTrainingEvent({required this.trainingId, required this.trainingType});
}

class ContinueTrainingEvent extends MyLearningsEvent {
  final String trainingId;

  const ContinueTrainingEvent({required this.trainingId});
}
