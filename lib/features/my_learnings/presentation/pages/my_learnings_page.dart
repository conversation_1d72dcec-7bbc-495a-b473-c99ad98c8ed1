import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/pages/my_learnings_view.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/pages/not_signed_in_learnings_page.dart';

class MyLearningsPage extends StatelessWidget {
  const MyLearningsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: CupertinoScaffold(
        body: StreamBuilder<bool>(
          stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
          builder: (context, isLoggedIn) {
            if (isLoggedIn.data == true) return const MyLearningsView();

            return const NotSignedInLearningsPage();
          },
        ),
      ),
    );
  }
}
