import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/explore_card.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/learning_track/learning_track_my_learning.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/my_learning_header.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/training_card_my_learning.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_loading_overlay.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';

class MyLearningsView extends StatefulWidget {
  const MyLearningsView({super.key});

  @override
  State<MyLearningsView> createState() => _MyLearningsViewState();
}

class _MyLearningsViewState extends State<MyLearningsView> {
  late MyLearningsBloc myLearningsBloc;
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    myLearningsBloc = GetIt.instance.get<MyLearningsBloc>();

    ///This check is needed because GetEvent might already be triggered in HomePage
    if (!myLearningsBloc.state.isLoading || myLearningsBloc.state.error.isNotEmpty) {
      myLearningsBloc.add(const GetAllMyLearnings());
    }
  }

  /// Forces the RefreshIndicator to show and start the refresh with animation
  void showRefreshIndicatorAndRefresh() {
    _refreshIndicatorKey.currentState?.show();
  }

  Future<void> _handleRefresh() {
    myLearningsBloc.add(const GetAllMyLearnings());
    // Return a delayed future to give time for the refresh to complete
    return Future.delayed(const Duration(seconds: 2));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MyLearningsBloc, MyLearningsState>(
      bloc: myLearningsBloc,
      listener: (context, state) {
        if (state.error.isNotEmpty) {
          showAppToast(context, message: state.error);
        } else if (state.enrollmentError.isNotEmpty) {
          showAppToast(context, message: state.enrollmentError);
        } else if (state.continueError.isNotEmpty) {
          showAppToast(context, message: state.continueError);
        }
      },
      builder: (context, state) {
        return AppLoadingOverlay(
          isLoading: state.isEnrollmentInProgress || state.isContinueInProgress,
          child: Scaffold(
            backgroundColor: AppColors.neutralWhite,
            appBar: const NspAppBar(),
            body: state.isLoading
                ? const BuildLoader()
                : Column(
                    children: [
                      const MyLearningHeader(),
                      Expanded(
                        child: RefreshIndicator(
                          key: _refreshIndicatorKey,
                          onRefresh: _handleRefresh,
                          child: ListView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            children: [
                              if (state.myLearningsModel.applicantSelfPacedTrainingList.isNotEmpty)
                                TrainingCardMyLearning.selfPaced(
                                  state.myLearningsModel.applicantSelfPacedTrainingList,
                                  onRefresh: showRefreshIndicatorAndRefresh,
                                )
                              else
                                ExploreCard.selfPaced(
                                  onExploreTap: () => myLearningsBloc.add(
                                    const ExploreLearningsEvent(ExploreCardType.selfPaced),
                                  ),
                                ),

                              if (state
                                  .myLearningsModel.applicantOnlineStudyTrainingList.isNotEmpty)
                                TrainingCardMyLearning.onlineTraining(
                                  state.myLearningsModel.applicantOnlineStudyTrainingList,
                                  onRefresh: showRefreshIndicatorAndRefresh,
                                )
                              else
                                ExploreCard.onlineTraining(
                                  onExploreTap: () => myLearningsBloc.add(
                                    const ExploreLearningsEvent(ExploreCardType.onlineTraining),
                                  ),
                                ),

                              if (state
                                  .myLearningsModel.applicantInPersonStudyTrainingList.isNotEmpty)
                                TrainingCardMyLearning.inPerson(
                                  state.myLearningsModel.applicantInPersonStudyTrainingList,
                                  onRefresh: showRefreshIndicatorAndRefresh,
                                )
                              else
                                ExploreCard.inPerson(
                                  onExploreTap: () => myLearningsBloc.add(
                                    const ExploreLearningsEvent(ExploreCardType.inPerson),
                                  ),
                                ),

                              if (state.myLearningsModel.applicantLearningTrackViewList.isNotEmpty)
                                LearningTrackMyLearning(
                                  state.myLearningsModel.applicantLearningTrackViewList,
                                  onRefresh: showRefreshIndicatorAndRefresh,
                                )
                              else
                                ExploreCard.learningTrack(
                                  onExploreTap: () => myLearningsBloc.add(
                                    const ExploreLearningsEvent(ExploreCardType.learningTrack),
                                  ),
                                ),

                              const SizedBox(height: 40),
                              //
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }
}
