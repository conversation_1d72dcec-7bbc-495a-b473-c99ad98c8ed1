import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/profile_page/presentation/pages/non_logged_in_page.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:provider/provider.dart';

class MyLearningsPage extends StatelessWidget {
  const MyLearningsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const NspAppBar(),
      body: StreamBuilder<bool>(
        stream: context.read<GetIt>().get<AuthTokenProvider>().authStateStream,
        builder: (context, isLoggedIn) {
          if (isLoggedIn.data == true) {
            return Center(
              key: ValueKey(EasyLocalization.of(context)!.locale.toString()),
              child: Text(LocaleKeys.bottom_nav_bar_myLearnings.tr()),
            );
          }

          return const NonLoggedInPage();
        },
      ),
    );
  }
}
