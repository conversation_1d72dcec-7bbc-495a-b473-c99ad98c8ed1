import 'package:equatable/equatable.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

class MyLearningsFilterState extends Equatable {
  final ApplicantTrainingStatus selectedStatus;
  final List<ApplicantTraining>? filteredTrainings;
  final List<LearningTrackView>? filteredLearningTracks;
  final List<ApplicantTraining>? allTrainings;
  final List<LearningTrackView>? allLearningTracks;
  final Map<ApplicantTrainingStatus, int> statusCounts;

  const MyLearningsFilterState({
    this.selectedStatus = ApplicantTrainingStatus.ENROLLED,
    this.filteredTrainings,
    this.filteredLearningTracks,
    this.allTrainings,
    this.allLearningTracks,
    this.statusCounts = const {},
  });

  MyLearningsFilterState copyWith({
    ApplicantTrainingStatus? selectedStatus,
    List<ApplicantTraining>? filteredTrainings,
    List<LearningTrackView>? filteredLearningTracks,
    List<ApplicantTraining>? allTrainings,
    List<LearningTrackView>? allLearningTracks,
    Map<ApplicantTrainingStatus, int>? statusCounts,
  }) {
    return MyLearningsFilterState(
      selectedStatus: selectedStatus ?? this.selectedStatus,
      filteredTrainings: filteredTrainings ?? this.filteredTrainings,
      filteredLearningTracks: filteredLearningTracks ?? this.filteredLearningTracks,
      allTrainings: allTrainings ?? this.allTrainings,
      allLearningTracks: allLearningTracks ?? this.allLearningTracks,
      statusCounts: statusCounts ?? this.statusCounts,
    );
  }

  int getCountForStatus(ApplicantTrainingStatus status) => statusCounts[status] ?? 0;

  int get filteredLength {
    if (filteredTrainings != null) {
      return filteredTrainings?.length ?? 0;
    } else {
      return filteredLearningTracks?.length ?? 0;
    }
  }

  @override
  List<Object?> get props => [
        selectedStatus,
        filteredTrainings,
        filteredLearningTracks,
        allTrainings,
        allLearningTracks,
        statusCounts,
      ];
}
