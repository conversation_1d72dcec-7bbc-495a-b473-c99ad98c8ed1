import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_state.dart';

class MyLearningsFilterCubit extends Cubit<MyLearningsFilterState> {
  MyLearningsFilterCubit() : super(const MyLearningsFilterState());

  void initialize({List<ApplicantTraining>? trainings, List<LearningTrackView>? learningTracks}) {
    final Map<ApplicantTrainingStatus, int> counts = {};

    if (trainings != null) {
      for (final status in ApplicantTrainingStatus.values) {
        counts[status] =
            trainings.where((training) => training.applicantTrainingStatus == status).length;
      }
    } else if (learningTracks != null) {
      for (final status in ApplicantTrainingStatus.values) {
        counts[status] = learningTracks.where((track) => track.status == status).length;
      }
    }

    emit(
      state.copyWith(
        allTrainings: trainings,
        allLearningTracks: learningTracks,
        statusCounts: counts,
      ),
    );
    _filterContent(ApplicantTrainingStatus.ENROLLED);
  }

  int _compareByStatus<T>({
    required T a,
    required T b,
    required ApplicantTrainingStatus status,
    required DateTime Function(T) getEnrollmentDate,
    required DateTime? Function(T) getCompletionDate,
  }) {
    switch (status) {
      case ApplicantTrainingStatus.ENROLLED:
        return getEnrollmentDate(b).compareTo(getEnrollmentDate(a));
      case ApplicantTrainingStatus.NOMINATED:
        return getEnrollmentDate(b).compareTo(getEnrollmentDate(a));
      case ApplicantTrainingStatus.COMPLETED:
        final aDate = getCompletionDate(a);
        final bDate = getCompletionDate(b);
        if (aDate == null || bDate == null) {
          return 0;
        }
        return bDate.compareTo(aDate);
    }
  }

  void _filterContent(ApplicantTrainingStatus status) {
    final filteredTrainings =
        state.allTrainings?.where((training) => training.applicantTrainingStatus == status).toList()
          ?..sort(
            (a, b) => _compareByStatus(
              a: a,
              b: b,
              status: status,
              getEnrollmentDate: (training) => training.enrolledDate,
              getCompletionDate: (training) => training.completedDate,
            ),
          );

    final filteredLearningTracks =
        state.allLearningTracks?.where((track) => track.status == status).toList()
          ?..sort(
            (a, b) => _compareByStatus(
              a: a,
              b: b,
              status: status,
              getEnrollmentDate: (track) => track.enrolledDate,
              getCompletionDate: (track) => track.completedDate,
            ),
          );

    emit(
      state.copyWith(
        selectedStatus: status,
        filteredTrainings: filteredTrainings,
        filteredLearningTracks: filteredLearningTracks,
      ),
    );
  }

  void updateFilter(ApplicantTrainingStatus status) => _filterContent(status);
}
