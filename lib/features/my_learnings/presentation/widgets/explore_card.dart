import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ExploreCard extends StatelessWidget {
  const ExploreCard({
    required this.title,
    required this.iconPath,
    required this.description,
    required this.onExploreTap,
    super.key,
  });

  final String title;
  final String iconPath;
  final String description;
  final VoidCallback onExploreTap;

  factory ExploreCard.selfPaced({required VoidCallback onExploreTap}) => ExploreCard(
        title: LocaleKeys.self_paced.tr(),
        iconPath: AssetsPath.selfStudyBadge,
        description: LocaleKeys.self_paced_description.tr(),
        onExploreTap: onExploreTap,
      );

  factory ExploreCard.onlineTraining({required VoidCallback onExploreTap}) => ExploreCard(
        title: LocaleKeys.trainingDetails_online.tr(),
        iconPath: AssetsPath.onlineStudyBadge,
        description: LocaleKeys.online_training_description.tr(),
        onExploreTap: onExploreTap,
      );

  factory ExploreCard.inPerson({required VoidCallback onExploreTap}) => ExploreCard(
        title: LocaleKeys.in_person.tr(),
        iconPath: AssetsPath.inPersonStudyBadge,
        description: LocaleKeys.in_person_training_description.tr(),
        onExploreTap: onExploreTap,
      );

  factory ExploreCard.learningTrack({required VoidCallback onExploreTap}) => ExploreCard(
        title: LocaleKeys.learningTracks_title.tr(),
        iconPath: AssetsPath.learningTrackBadge,
        description: LocaleKeys.learning_track_description.tr(),
        onExploreTap: onExploreTap,
      );

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppColors.uiBackgroundPrimary,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.accentLight),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 8),
          Image.asset(width: 65, iconPath),
          const SizedBox(height: 16),
          Text(title, textAlign: TextAlign.center, style: context.textTheme.textLarge.semiBold),
          const SizedBox(height: 16),
          Text(description, style: context.textTheme.textSmall, textAlign: TextAlign.center),
          const SizedBox(height: 24),
          AppButton(onTap: onExploreTap, buttonText: LocaleKeys.explore.tr()),
        ],
      ),
    );
  }
}
