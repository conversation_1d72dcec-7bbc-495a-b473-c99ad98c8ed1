import 'dart:math';

import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/trainings_in_progress_card.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/view_all_card.dart';
import 'package:national_skills_platform/features/shared/page_view_height_adaptive.dart';
import 'package:national_skills_platform/router.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class TrainingsCardCarousel extends StatefulWidget {
  const TrainingsCardCarousel({
    required this.applicantTraining,
    required this.learningType,
    this.disableCarouselLimit = false,
    this.onRefresh,
    super.key,
  });

  final List<ApplicantTraining> applicantTraining;
  final bool disableCarouselLimit;
  final LearningType learningType;
  final VoidCallback? onRefresh;

  @override
  _TrainingsCardCarouselState createState() => _TrainingsCardCarouselState();
}

class _TrainingsCardCarouselState extends State<TrainingsCardCarousel> {
  final PageController _controller = PageController();
  final trainingsLimitOnCarousel = 7;

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final carouselLength = widget.disableCarouselLimit
        ? widget.applicantTraining.length
        : min(trainingsLimitOnCarousel + 1, widget.applicantTraining.length + 1);

    return Column(
      children: [
        PageViewHeightAdaptive(
          controller: _controller,
          children: [
            ...widget.applicantTraining.take(trainingsLimitOnCarousel).map(
                  (training) => TrainingsInProgressCard(
                    training,
                    onRefresh: widget.onRefresh,
                  ),
                ),
            if (!widget.disableCarouselLimit)
              ViewAllCard(
                onTap: () => router.pushNamed(
                  Routes.viewAllMyLearnings.name,
                  extra: [widget.learningType, widget.applicantTraining],
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        SmoothPageIndicator(
          controller: _controller,
          // +1 for view all
          count: carouselLength,
          effect: const WormEffect(
            dotWidth: 6,
            dotHeight: 6,
            spacing: 14,
            activeDotColor: AppColors.greenAccentPrimary,
            dotColor: AppColors.greyTertiary,
          ),
        ),
      ],
    );
  }
}
