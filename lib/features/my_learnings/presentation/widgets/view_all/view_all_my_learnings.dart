import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_cubit.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_state.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/learning_track/learning_track_my_learning.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/my_learnings_filter.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/trainings_in_progress_card.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_back_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_loading_overlay.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ViewAllMyLearnings extends StatelessWidget {
  const ViewAllMyLearnings({
    required this.learningType,
    this.myTrainings,
    this.myLearningTracks,
    super.key,
  });

  final List<ApplicantTraining>? myTrainings;
  final List<LearningTrackView>? myLearningTracks;
  final LearningType learningType;

  @override
  Widget build(BuildContext context) {
    return CupertinoScaffold(
      body: BlocProvider(
        create: (context) => MyLearningsFilterCubit()
          ..initialize(trainings: myTrainings, learningTracks: myLearningTracks),
        child: BlocConsumer<MyLearningsBloc, MyLearningsState>(
          bloc: GetIt.instance<MyLearningsBloc>(),
          listener: (context, state) {
            if (state.error.isNotEmpty) showAppToast(context, message: state.error);
            if (state.enrollmentError.isNotEmpty) {
              showAppToast(context, message: state.enrollmentError);
            }
            if (state.continueError.isNotEmpty) {
              showAppToast(context, message: state.continueError);
            }
          },
          builder: (context, state) {
            return AppLoadingOverlay(
              isLoading: state.isEnrollmentInProgress || state.isContinueInProgress,
              child: Scaffold(
                backgroundColor: AppColors.neutralWhite,
                appBar: AppBar(
                  leading: const AppBackButton(),
                  title: Text(
                    _getTitle(learningType),
                    style: context.textTheme.textLarge.semiBold,
                  ),
                  bottom: const PreferredSize(
                    preferredSize: Size.fromHeight(0),
                    child: AppDivider(),
                  ),
                ),
                body: BlocBuilder<MyLearningsFilterCubit, MyLearningsFilterState>(
                  builder: (context, state) {
                    return Column(
                      children: [
                        const SizedBox(height: 16),
                        MyLearningsFilter(learningType: learningType),
                        const SizedBox(height: 12),
                        Expanded(
                          child: ListView.separated(
                            padding: const EdgeInsets.only(bottom: 16),
                            itemCount: state.filteredLength,
                            separatorBuilder: (context, index) => const SizedBox(height: 16),
                            itemBuilder: (context, i) {
                              if (learningType == LearningType.LearningTrack &&
                                  state.filteredLearningTracks != null) {
                                return LearningTrackMyLearning(
                                  state.filteredLearningTracks!,
                                  showViewAll: false,
                                  learningTrackIndex: i,
                                );
                              }

                              if (state.filteredTrainings != null) {
                                return TrainingsInProgressCard(state.filteredTrainings![i]);
                              }

                              return const SizedBox.shrink();
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String _getTitle(LearningType learningType) {
    switch (learningType) {
      case LearningType.SelfPaced:
        return LocaleKeys.self_paced.tr();
      case LearningType.OnlineStudy:
        return LocaleKeys.trainingDetails_online.tr();
      case LearningType.InPersonStudy:
        return LocaleKeys.in_person.tr();
      case LearningType.LearningTrack:
        return LocaleKeys.learningTracks_title.tr();
    }
  }
}
