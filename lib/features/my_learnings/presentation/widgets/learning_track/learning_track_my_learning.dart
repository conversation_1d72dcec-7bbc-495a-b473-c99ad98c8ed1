import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/learning_track_details_bottom_sheet.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/completed_sections_progress_info.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/trainings_card_carousel.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class LearningTrackMyLearning extends StatelessWidget {
  const LearningTrackMyLearning(
    this.learningTracks, {
    this.learningTrackIndex = 0,
    this.showViewAll = true,
    this.onRefresh,
    super.key,
  });

  final List<LearningTrackView> learningTracks;
  final int learningTrackIndex;
  final bool showViewAll;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.uiBackgroundPrimary,
        border: Border.all(color: AppColors.accentLight, width: 0.5),
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),

          _LearningTrackCardHeader(learningTrack: learningTracks[learningTrackIndex]),

          const SizedBox(height: 12),

          if (learningTracks[learningTrackIndex].applicantTrainings.isNotEmpty) ...[
            TrainingsCardCarousel(
              applicantTraining: learningTracks[learningTrackIndex].applicantTrainings,
              disableCarouselLimit: true,
              learningType: LearningType.LearningTrack,
              onRefresh: onRefresh,
            ),
            const SizedBox(height: 16),
            if (showViewAll) ...[
              Align(
                alignment: Alignment.bottomLeft,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: TextButton(
                    onPressed: () async {
                      await router.pushNamed(
                        Routes.viewAllMyLearnings.name,
                        extra: [LearningType.LearningTrack, learningTracks],
                      );
                      // Refresh data when returning from View All page
                      onRefresh?.call();
                    },
                    child: Text(
                      LocaleKeys.view_all.tr(),
                      style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ],
          //
        ],
      ),
    );
  }
}

class _LearningTrackCardHeader extends StatelessWidget {
  const _LearningTrackCardHeader({required this.learningTrack});

  final LearningTrackView? learningTrack;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => unawaited(openLearningTrackDetailsBottomSheet(context, learningTrack)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.asset(AssetsPath.learningTrackBadge, height: 45),
                const SizedBox(width: 8),
                Text(LocaleKeys.learningTracks_title.tr(), style: context.textTheme.h4.semiBold),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: Text(
                learningTrack?.learningTrackTitle ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.textLarge.semiBold,
              ),
            ),
            const SizedBox(height: 8),
            CompletedSectionsProgressInfo(
              passedSections: learningTrack?.passedTrainings ?? 0,
              allSections: learningTrack?.allTrainings ?? 0,
              trainingCompletedPercent: learningTrack?.passedScore ?? 0,
            ),
          ],
        ),
      ),
    );
  }
}
