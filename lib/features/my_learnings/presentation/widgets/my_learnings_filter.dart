import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_cubit.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_state.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class MyLearningsFilter extends StatelessWidget {
  const MyLearningsFilter({required this.learningType, super.key});

  final LearningType learningType;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MyLearningsFilterCubit, MyLearningsFilterState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: InkWell(
            onTap: () => _showFilterOptions(context, context.read<MyLearningsFilterCubit>()),
            child: Row(
              children: [
                Text(
                  LocaleKeys.filterBy.tr(),
                  style: context.textTheme.textSmall.medium.greyAdditional,
                ),
                const SizedBox(width: 8),
                Text(
                  '${_getStatusText(state.selectedStatus)} (${state.statusCounts[state.selectedStatus] ?? 0})',
                  style: context.textTheme.textSmall.medium,
                ),
                const SizedBox(width: 4),
                const Icon(Icons.keyboard_arrow_down_rounded, color: AppColors.neutralBlack),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showFilterOptions(BuildContext context, MyLearningsFilterCubit cubit) {
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    showModalBottomSheet(
      context: context,
      useRootNavigator: useRootNavigator,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: SharedDecoration.borderTopLeftRight10,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: BlocBuilder<MyLearningsFilterCubit, MyLearningsFilterState>(
          bloc: cubit,
          builder: (context, state) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Align(
                child: Container(
                  width: 36,
                  height: 3,
                  margin: const EdgeInsets.only(top: 8, bottom: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2.5),
                    color: AppColors.greyTertiary,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: Text(
                  LocaleKeys.filterBy.tr(),
                  style: context.textTheme.textLarge.semiBold,
                ),
              ),
              ...ApplicantTrainingStatus.values
                  .map(
                    (status) => ListTile(
                      contentPadding: const EdgeInsets.symmetric(vertical: 8),
                      dense: true,
                      title: Text(
                        '${_getStatusText(status)} (${state.statusCounts[status] ?? 0})',
                        style: context.textTheme.textSmall.medium,
                      ),
                      trailing: AppCustomRadio(isSelected: state.selectedStatus == status),
                      onTap: () {
                        cubit.updateFilter(status);
                        Navigator.pop(context);
                      },
                    ),
                  )
                  .toList()
                  .divide(divider: const AppDivider()),
              const SizedBox(height: 34),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusText(ApplicantTrainingStatus status) {
    switch (status) {
      case ApplicantTrainingStatus.ENROLLED:
        return LocaleKeys.userLearnings_in_progress.tr();
      case ApplicantTrainingStatus.COMPLETED:
        return LocaleKeys.userLearnings_completed.tr();
      case ApplicantTrainingStatus.NOMINATED:
        return LocaleKeys.userLearnings_nominated.tr();
    }
  }
}
