import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class MoreMenuBottomSheet extends StatelessWidget {
  const MoreMenuBottomSheet({
    required this.onContinuePressed,
    required this.onViewTrainingDetails,
    this.onRefresh,
    super.key,
  });

  final VoidCallback onContinuePressed;
  final VoidCallback onViewTrainingDetails;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          const SizedBox(height: 10),

          Text(LocaleKeys.view.tr(), style: context.textTheme.textLarge.semiBold),

          const SizedBox(height: 20),

          TextButton(
            onPressed: onContinuePressed,
            child: Text(
              LocaleKeys.userLearnings_continue.tr(),
              style: context.textTheme.textSmall.medium,
            ),
          ),

          const AppDivider(padding: EdgeInsets.symmetric(vertical: 8)),

          const SizedBox(height: 10),

          TextButton(
            onPressed: onViewTrainingDetails,
            child: Text(
              LocaleKeys.viewTrainingDetails.tr(),
              style: context.textTheme.textSmall.medium,
            ),
          ),

          const SizedBox(height: 45),

          //
        ],
      ),
    );
  }
}
