import 'dart:async';

import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/presentation/pages/learning_track_details_page.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:platform/platform.dart';

Future<void> openLearningTrackDetailsBottomSheet(
  BuildContext context,
  LearningTrackView? lTrack, {
  Platform? platform,
  VoidCallback? onRefresh,
}) async {
  platform ??= const LocalPlatform();
  final useRootNavigator = !platform.environment.containsKey(Constants.flutterTest);

  if (lTrack != null && lTrack.id.isNotEmpty) {
    final result = await (platform.operatingSystem == Platform.iOS
        ? CupertinoScaffold.showCupertinoModalBottomSheet(
            context: context,
            enableDrag: false,
            useRootNavigator: useRootNavigator,
            backgroundColor: Colors.white,
            builder: (_) => LearningTrackDetailsPage(
              learningTrackID: lTrack.learningTrackId,
              isBottomSheetState: true,
            ),
          )
        : showCupertinoModalBottomSheet(
            context: context,
            enableDrag: false,
            backgroundColor: Colors.white,
            builder: (_) => LearningTrackDetailsPage(
              learningTrackID: lTrack.learningTrackId,
              isBottomSheetState: true,
            ),
          ));

    // Refresh data after bottom sheet is closed
    onRefresh?.call();
    return result;
  } else {
    showAppToast(context, message: 'Failed to open the card');
  }
}
