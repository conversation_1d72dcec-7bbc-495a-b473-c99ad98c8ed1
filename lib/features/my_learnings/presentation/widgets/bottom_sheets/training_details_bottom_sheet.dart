import 'dart:async';

import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/pages/training_details_page.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:platform/platform.dart';

Future<void> openTrainingDetailsBottomSheet(
  BuildContext context,
  ApplicantTraining? training, {
  Platform? platform,
  VoidCallback? onRefresh,
}) async {
  platform ??= const LocalPlatform();
  final useRootNavigator = !platform.environment.containsKey(Constants.flutterTest);

  if (training != null && training.id.isNotEmpty) {
    final result = await (platform.operatingSystem == Platform.iOS
        ? CupertinoScaffold.showCupertinoModalBottomSheet(
            context: context,
            enableDrag: false,
            useRootNavigator: useRootNavigator,
            backgroundColor: Colors.white,
            builder: (_) =>
                TrainingDetailsPage(trainingID: training.trainingId, isBottomSheetState: true),
          )
        : showCupertinoModalBottomSheet(
            context: context,
            enableDrag: false,
            backgroundColor: Colors.white,
            builder: (_) =>
                TrainingDetailsPage(trainingID: training.trainingId, isBottomSheetState: true),
          ));

    // Refresh data after bottom sheet is closed
    onRefresh?.call();
    return result;
  } else {
    showAppToast(context, message: 'Failed to open the card');
  }
}
