import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CompletedSectionsProgressInfo extends StatelessWidget {
  const CompletedSectionsProgressInfo({
    required this.passedSections,
    required this.allSections,
    required this.trainingCompletedPercent,
    this.shouldPercentageTextBeBlue = true,
    super.key,
  });

  final int passedSections;
  final int allSections;
  final int trainingCompletedPercent;
  final bool shouldPercentageTextBeBlue;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          LocaleKeys.trainingProgress_progressPercentage.tr(args: ['$trainingCompletedPercent']),
          style: shouldPercentageTextBeBlue
              ? context.textTheme.textSmall.semiBold.accentGreenPrimary
              : context.textTheme.textSmall,
        ),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.0),
          child: Icon(Icons.circle, size: 3, color: AppColors.greyTertiary),
        ),
        Text(
          LocaleKeys.trainingProgress_sectionsProgress.tr(
            args: ['$passedSections', '$allSections'],
          ),
          style: context.textTheme.textSmall,
        ),
      ],
    );
  }
}
