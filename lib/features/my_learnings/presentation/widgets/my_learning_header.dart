import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class MyLearningHeader extends StatelessWidget {
  const MyLearningHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      foregroundDecoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.greyLight, width: 0.5)),
      ),
      decoration: const BoxDecoration(color: Colors.white),
      padding: const EdgeInsets.fromLTRB(16, 16, 24, 16),
      child: Text(LocaleKeys.bottom_nav_bar_myLearnings.tr(), style: context.textTheme.h3.semiBold),
    );
  }
}
