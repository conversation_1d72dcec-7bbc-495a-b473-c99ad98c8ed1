import 'dart:io';

import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';

class MoreMenuIconButton extends StatelessWidget {
  const MoreMenuIconButton({required this.menuBottomSheet, super.key});

  final Widget menuBottomSheet;

  @override
  Widget build(BuildContext context) {
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    return GestureDetector(
      child: const Padding(
        padding: EdgeInsets.all(12.0),
        child: Icon(Icons.more_vert, size: 20, color: AppColors.neutralBlack),
      ),
      onTap: () => showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        useRootNavigator: useRootNavigator,
        shape: const RoundedRectangleBorder(
          borderRadius: SharedDecoration.borderTopLeftRight10,
        ),
        builder: (context) => menuBottomSheet,
      ),
    );
  }
}
