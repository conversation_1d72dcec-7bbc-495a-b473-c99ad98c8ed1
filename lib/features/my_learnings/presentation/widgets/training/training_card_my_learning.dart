import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/trainings_card_carousel.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class TrainingCardMyLearning extends StatelessWidget {
  const TrainingCardMyLearning({
    required this.title,
    required this.iconPath,
    required this.applicantTraining,
    required this.learningType,
    this.onRefresh,
    super.key,
  });

  factory TrainingCardMyLearning.selfPaced(
    List<ApplicantTraining> applicantTraining, {
    VoidCallback? onRefresh,
  }) =>
      TrainingCardMyLearning(
        title: LocaleKeys.self_paced.tr(),
        iconPath: AssetsPath.selfStudyBadge,
        applicantTraining: applicantTraining,
        learningType: LearningType.SelfPaced,
        onRefresh: onRefresh,
      );

  factory TrainingCardMyLearning.onlineTraining(
    List<ApplicantTraining> applicantTraining, {
    VoidCallback? onRefresh,
  }) =>
      TrainingCardMyLearning(
        title: LocaleKeys.trainingDetails_online.tr(),
        iconPath: AssetsPath.onlineStudyBadge,
        applicantTraining: applicantTraining,
        learningType: LearningType.OnlineStudy,
        onRefresh: onRefresh,
      );

  factory TrainingCardMyLearning.inPerson(
    List<ApplicantTraining> applicantTraining, {
    VoidCallback? onRefresh,
  }) =>
      TrainingCardMyLearning(
        title: LocaleKeys.in_person.tr(),
        iconPath: AssetsPath.inPersonStudyBadge,
        applicantTraining: applicantTraining,
        learningType: LearningType.InPersonStudy,
        onRefresh: onRefresh,
      );

  final String title;
  final String iconPath;
  final List<ApplicantTraining> applicantTraining;
  final LearningType learningType;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: AppColors.uiBackgroundPrimary,
        border: Border.all(color: AppColors.accentLight, width: 0.5),
      ),
      child: Column(
        children: [
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Image.asset(iconPath, height: 45),
                const SizedBox(width: 8),
                Text(title, style: context.textTheme.h4.semiBold),
              ],
            ),
          ),
          const SizedBox(height: 16),
          TrainingsCardCarousel(
            applicantTraining: applicantTraining,
            learningType: learningType,
            onRefresh: onRefresh,
          ),
          const SizedBox(height: 16),
          Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: TextButton(
                onPressed: () async {
                  await router.pushNamed(
                    Routes.viewAllMyLearnings.name,
                    extra: [learningType, applicantTraining],
                  );
                  // Refresh data when returning from View All page
                  onRefresh?.call();
                },
                child: Text(
                  LocaleKeys.view_all.tr(),
                  style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
