import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/menu_bottom_sheet.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/training_details_bottom_sheet.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/completed_sections_progress_info.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/more_menu_icon_button.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/training_completion_date.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/training/training_info.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class TrainingsInProgressCard extends StatelessWidget {
  const TrainingsInProgressCard(this.trainingViewDto, {this.onRefresh, super.key});

  final ApplicantTraining trainingViewDto;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    final training = trainingViewDto;

    return GestureDetector(
      onTap: () async {
        await openTrainingDetailsBottomSheet(
          context,
          training,
          onRefresh: onRefresh,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16),
        foregroundDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.greyLight),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: AppColors.neutralWhite,
          boxShadow: const [
            BoxShadow(color: Color(0x0D101828), offset: Offset(0, 1), blurRadius: 2),
          ],
        ),
        child: _TrainingsCardBody(training: training, onRefresh: onRefresh),
      ),
    );
  }
}

class _TrainingsCardBody extends StatelessWidget {
  const _TrainingsCardBody({required this.training, this.onRefresh});

  final ApplicantTraining training;
  final VoidCallback? onRefresh;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ///Training Image and Title
        Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(4.0),
              child: Platform.environment.containsKey(Constants.flutterTest) ||
                      training.profileImageUrl.isEmpty
                  ? Container(width: 48, height: 48, color: Colors.grey)
                  : CachedNetworkImage(
                      imageUrl: training.profileImageUrl,
                      placeholder: (context, url) => const ShimmerPlaceholder(height: 48),
                      fit: BoxFit.cover,
                      width: 48,
                      height: 48,
                    ),
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                training.trainingTitle,
                maxLines: 2,
                softWrap: false,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.textMedium.semiBold,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        ///Sections progress
        if (training.applicantTrainingStatus != ApplicantTrainingStatus.NOMINATED &&
            training.type != TrainingType.InstructorLed) ...[
          CompletedSectionsProgressInfo(
            passedSections: training.passedSections,
            allSections: training.allSections,
            trainingCompletedPercent: training.passedScore,
          ),
          const SizedBox(height: 8),

          ///show completion date only for completed trainings
          if (training.applicantTrainingStatus == ApplicantTrainingStatus.COMPLETED) ...[
            TrainingCompletionDate(completedDate: dateTimeFormat(training.completedDate)),
            const SizedBox(height: 12),
          ],

          ///show Progress Indicator only for in-progress trainings
          if (training.applicantTrainingStatus == ApplicantTrainingStatus.ENROLLED) ...[
            LinearProgressIndicator(
              borderRadius: BorderRadius.circular(40),
              value: training.passedScore / 100,
              minHeight: 6,
              color: AppColors.greenAccentPrimary,
              backgroundColor: const Color(0xFFE6E6E7),
            ),
            const SizedBox(height: 26),
          ],
        ],

        ///Estimated Time and Nominated Date for Self-paced
        ///Study Stream dates and Start date for In-Person/Online trainings
        TrainingInfo(training),

        /// Action button and more menu
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: AppButton(
                onTap: _getButtonAction,
                buttonText: _getButtonText(),
                borderColor: _getBorderColor(),
                backgroundColor: _getButtonColor(),
                textStyle: _getButtonTextStyle(context),
              ),
            ),
            const SizedBox(width: 8),
            MoreMenuIconButton(
              menuBottomSheet: MoreMenuBottomSheet(
                onContinuePressed: () {
                  GetIt.instance.get<MyLearningsBloc>().add(
                        ContinueTrainingEvent(trainingId: training.trainingId),
                      );
                  router.pop();
                },
                onViewTrainingDetails: () async {
                  await router.pushNamed(
                    Routes.trainingDetailsPage.name,
                    extra: training.trainingId,
                  );
                  // Refresh data when returning from details page
                  onRefresh?.call();
                },
                onRefresh: onRefresh,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _getButtonAction() {
    if (training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED) {
      GetIt.instance.get<MyLearningsBloc>().add(
            EnrollTrainingEvent(trainingId: training.trainingId, trainingType: training.type),
          );
    } else if (training.applicantTrainingStatus == ApplicantTrainingStatus.ENROLLED) {
      GetIt.instance.get<MyLearningsBloc>().add(
            ContinueTrainingEvent(trainingId: training.trainingId),
          );
    }
  }

  Color? _getBorderColor() {
    return training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED
        ? null
        : AppColors.accentLight;
  }

  Color _getButtonColor() {
    return training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED
        ? AppColors.greenAccentPrimary
        : AppColors.uiBackgroundSecondary;
  }

  TextStyle _getButtonTextStyle(BuildContext context) {
    return training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED
        ? context.textTheme.textSmall.semiBold.accentLight
        : context.textTheme.textSmall.semiBold.accentGreenPrimary;
  }

  String _getButtonText() {
    return training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED
        ? LocaleKeys.trainingDetails_enroll.tr()
        : training.applicantTrainingStatus == ApplicantTrainingStatus.ENROLLED
            ? LocaleKeys.userLearnings_continue.tr()
            : LocaleKeys.userLearnings_viewCertificate.tr();
  }
}
