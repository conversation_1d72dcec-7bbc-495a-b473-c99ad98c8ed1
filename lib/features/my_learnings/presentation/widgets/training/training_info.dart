import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingInfo extends StatelessWidget {
  const TrainingInfo(this.training, {super.key});

  final ApplicantTraining training;

  @override
  Widget build(BuildContext context) {
    if (training.type == TrainingType.SelfPaced &&
        training.applicantTrainingStatus == ApplicantTrainingStatus.NOMINATED) {
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.trainingDetails_estimatedTime.tr(),
                    style: context.textTheme.textSmall.greyPrimary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    LocaleKeys.userLearnings_nominated.tr(),
                    style: context.textTheme.textSmall.greyPrimary,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.trainingDetails_estimatedTimeValue.tr(
                      args: ["${training.trainingDurationMin} - ${training.trainingDurationMax}"],
                    ),
                    style: context.textTheme.textSmall.medium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    formatTime(training.enrolledDate),
                    style: context.textTheme.textSmall.medium,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      );
    }
    if (training.type == TrainingType.InstructorLed) {
      final applicantStudyStream = training.applicantStudyStreams.first;

      // Determine status text based on stream state
      String statusLabel = LocaleKeys.start.tr();
      if (applicantStudyStream.cancelled) {
        statusLabel = LocaleKeys.cancelled.tr();
      } else {
        // Check if study stream is ongoing (current date is between start and end dates)
        final now = DateTime.now();
        final hasStarted =
            applicantStudyStream.startDate != null && now.isAfter(applicantStudyStream.startDate!);
        final hasEnded =
            applicantStudyStream.endDate != null && now.isAfter(applicantStudyStream.endDate!);

        if (hasStarted && !hasEnded) {
          statusLabel = LocaleKeys.next_lesson.tr();
        }
      }

      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.study_stream.tr(),
                    style: context.textTheme.textSmall.greyPrimary,
                  ),
                  const SizedBox(height: 8),
                  Text(statusLabel, style: context.textTheme.textSmall.greyPrimary),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    formatDateRange(applicantStudyStream.startDate, applicantStudyStream.endDate),
                    style: context.textTheme.textSmall.medium.copyWith(
                      decoration: applicantStudyStream.cancelled
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    formatTime(training.enrolledDate),
                    style: context.textTheme.textSmall.medium,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}
