import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingCompletionDate extends StatelessWidget {
  const TrainingCompletionDate({this.completedDate, super.key});

  final String? completedDate;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(
          Icons.check_circle_outline_rounded,
          color: AppColors.greenAccentPrimary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          LocaleKeys.userLearnings_completedOn.tr(args: [completedDate ?? '']),
          style: context.textTheme.textSmall,
        ),
      ],
    );
  }
}
