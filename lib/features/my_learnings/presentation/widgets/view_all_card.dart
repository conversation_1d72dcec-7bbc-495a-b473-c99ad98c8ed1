import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ViewAllCard extends StatelessWidget {
  const ViewAllCard({super.key, required this.onTap});
  final VoidCallback onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 210,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16),
        foregroundDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.greyExtraLight),
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: AppColors.greyExtraLight,
        ),
        child: Center(
          child: Text(
            LocaleKeys.view_all.tr(),
            style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
          ),
        ),
      ),
    );
  }
}
