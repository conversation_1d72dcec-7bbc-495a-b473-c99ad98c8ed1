import 'dart:async';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

@injectable
class MyLearningsDataSource {
  const MyLearningsDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<MyLearningsModel> getMyLearnings() async {
    final response = await _dio.get(ApiConstants.currentCoursesAll);

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return MyLearningsModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
