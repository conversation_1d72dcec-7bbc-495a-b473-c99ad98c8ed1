// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_learnings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MyLearningsModel _$MyLearningsModelFromJson(Map<String, dynamic> json) => _MyLearningsModel(
      applicantSelfPacedTrainingList: (json['applicantSelfPacedTrainingList'] as List<dynamic>?)
              ?.map((e) => ApplicantTraining.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantOnlineStudyTrainingList: (json['applicantOnlineStudyTrainingList'] as List<dynamic>?)
              ?.map((e) => ApplicantTraining.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      applicantInPersonStudyTrainingList:
          (json['applicantInPersonStudyTrainingList'] as List<dynamic>?)
                  ?.map((e) => ApplicantTraining.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              [],
      applicantLearningTrackViewList: (json['applicantLearningTrackViewList'] as List<dynamic>?)
              ?.map((e) => LearningTrackView.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$MyLearningsModelToJson(_MyLearningsModel instance) => <String, dynamic>{
      'applicantSelfPacedTrainingList': instance.applicantSelfPacedTrainingList,
      'applicantOnlineStudyTrainingList': instance.applicantOnlineStudyTrainingList,
      'applicantInPersonStudyTrainingList': instance.applicantInPersonStudyTrainingList,
      'applicantLearningTrackViewList': instance.applicantLearningTrackViewList,
    };

_ApplicantTraining _$ApplicantTrainingFromJson(Map<String, dynamic> json) => _ApplicantTraining(
      id: json['id'] as String,
      trainingId: json['trainingId'] as String,
      trainingTitle: json['trainingTitle'] as String,
      trainingDurationMin: (json['trainingDurationMin'] as num).toInt(),
      trainingDurationMax: (json['trainingDurationMax'] as num).toInt(),
      profileImage: ProfileImage.fromJson(json['profileImage'] as Map<String, dynamic>),
      enrolledDate: DateTime.parse(json['enrolledDate'] as String),
      completedDate:
          json['completedDate'] == null ? null : DateTime.parse(json['completedDate'] as String),
      passedScore: (json['passedScore'] as num?)?.toInt() ?? 0,
      allSections: json['allSections'] == null ? 1 : _allSectionsFromJson(json['allSections']),
      passedSections: (json['passedSections'] as num?)?.toInt() ?? 0,
      rating: (json['rating'] as num).toInt(),
      applicantTrainingStatus:
          _stringToApplicantTrainingStatus(json['applicantTrainingStatus'] as String?),
      certificateId: (json['certificateId'] as num?)?.toInt(),
      preTrainingTestMandatory: json['preTrainingTestMandatory'] as bool? ?? false,
      profileImageUrl: json['profileImageUrl'] as String,
      preTestPassed: json['preTestPassed'] as bool?,
      type: stringToTrainingType(json['type'] as String?),
      applicantStudyStreams: (json['applicantStudyStreams'] as List<dynamic>?)
              ?.map((e) => ApplicantStudyStream.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$ApplicantTrainingToJson(_ApplicantTraining instance) => <String, dynamic>{
      'id': instance.id,
      'trainingId': instance.trainingId,
      'trainingTitle': instance.trainingTitle,
      'trainingDurationMin': instance.trainingDurationMin,
      'trainingDurationMax': instance.trainingDurationMax,
      'profileImage': instance.profileImage,
      'enrolledDate': instance.enrolledDate.toIso8601String(),
      'completedDate': instance.completedDate?.toIso8601String(),
      'passedScore': instance.passedScore,
      'allSections': instance.allSections,
      'passedSections': instance.passedSections,
      'rating': instance.rating,
      'applicantTrainingStatus':
          _$ApplicantTrainingStatusEnumMap[instance.applicantTrainingStatus]!,
      'certificateId': instance.certificateId,
      'preTrainingTestMandatory': instance.preTrainingTestMandatory,
      'profileImageUrl': instance.profileImageUrl,
      'preTestPassed': instance.preTestPassed,
      'type': _$TrainingTypeEnumMap[instance.type]!,
      'applicantStudyStreams': instance.applicantStudyStreams,
    };

const _$ApplicantTrainingStatusEnumMap = {
  ApplicantTrainingStatus.ENROLLED: 'ENROLLED',
  ApplicantTrainingStatus.NOMINATED: 'NOMINATED',
  ApplicantTrainingStatus.COMPLETED: 'COMPLETED',
};

const _$TrainingTypeEnumMap = {
  TrainingType.SelfPaced: 'SelfPaced',
  TrainingType.InstructorLed: 'InstructorLed',
  TrainingType.none: 'none',
};

_ProfileImage _$ProfileImageFromJson(Map<String, dynamic> json) => _ProfileImage(
      originalFilename: json['originalFilename'] as String,
      key: json['key'] as String,
      size: (json['size'] as num).toInt(),
    );

Map<String, dynamic> _$ProfileImageToJson(_ProfileImage instance) => <String, dynamic>{
      'originalFilename': instance.originalFilename,
      'key': instance.key,
      'size': instance.size,
    };

_LearningTrackView _$LearningTrackViewFromJson(Map<String, dynamic> json) => _LearningTrackView(
      id: json['id'] as String,
      learningTrackId: json['learningTrackId'] as String,
      learningTrackTitle: json['learningTrackTitle'] as String,
      status: _stringToApplicantTrainingStatus(json['status'] as String?),
      applicantTrainings: (json['applicantTrainings'] as List<dynamic>?)
              ?.map((e) => ApplicantTraining.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      passedScore: (json['passedScore'] as num).toInt(),
      allTrainings: _allSectionsFromJson(json['allTrainings']),
      passedTrainings: (json['passedTrainings'] as num).toInt(),
      completedDate:
          json['completedDate'] == null ? null : DateTime.parse(json['completedDate'] as String),
      enrolledDate: DateTime.parse(json['enrolledDate'] as String),
      certificateId: (json['certificateId'] as num).toInt(),
    );

Map<String, dynamic> _$LearningTrackViewToJson(_LearningTrackView instance) => <String, dynamic>{
      'id': instance.id,
      'learningTrackId': instance.learningTrackId,
      'learningTrackTitle': instance.learningTrackTitle,
      'status': _$ApplicantTrainingStatusEnumMap[instance.status]!,
      'applicantTrainings': instance.applicantTrainings,
      'passedScore': instance.passedScore,
      'allTrainings': instance.allTrainings,
      'passedTrainings': instance.passedTrainings,
      'completedDate': instance.completedDate?.toIso8601String(),
      'enrolledDate': instance.enrolledDate.toIso8601String(),
      'certificateId': instance.certificateId,
    };
