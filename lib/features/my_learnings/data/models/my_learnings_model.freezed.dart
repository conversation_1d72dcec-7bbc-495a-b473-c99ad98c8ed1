// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'my_learnings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MyLearningsModel {
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantSelfPacedTrainingList;
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantOnlineStudyTrainingList;
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantInPersonStudyTrainingList;
  @JsonKey(defaultValue: [])
  List<LearningTrackView> get applicantLearningTrackViewList;

  /// Create a copy of MyLearningsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MyLearningsModelCopyWith<MyLearningsModel> get copyWith =>
      _$MyLearningsModelCopyWithImpl<MyLearningsModel>(this as MyLearningsModel, _$identity);

  /// Serializes this MyLearningsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MyLearningsModel &&
            const DeepCollectionEquality()
                .equals(other.applicantSelfPacedTrainingList, applicantSelfPacedTrainingList) &&
            const DeepCollectionEquality()
                .equals(other.applicantOnlineStudyTrainingList, applicantOnlineStudyTrainingList) &&
            const DeepCollectionEquality().equals(
                other.applicantInPersonStudyTrainingList, applicantInPersonStudyTrainingList) &&
            const DeepCollectionEquality()
                .equals(other.applicantLearningTrackViewList, applicantLearningTrackViewList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(applicantSelfPacedTrainingList),
      const DeepCollectionEquality().hash(applicantOnlineStudyTrainingList),
      const DeepCollectionEquality().hash(applicantInPersonStudyTrainingList),
      const DeepCollectionEquality().hash(applicantLearningTrackViewList));

  @override
  String toString() {
    return 'MyLearningsModel(applicantSelfPacedTrainingList: $applicantSelfPacedTrainingList, applicantOnlineStudyTrainingList: $applicantOnlineStudyTrainingList, applicantInPersonStudyTrainingList: $applicantInPersonStudyTrainingList, applicantLearningTrackViewList: $applicantLearningTrackViewList)';
  }
}

/// @nodoc
abstract mixin class $MyLearningsModelCopyWith<$Res> {
  factory $MyLearningsModelCopyWith(MyLearningsModel value, $Res Function(MyLearningsModel) _then) =
      _$MyLearningsModelCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<ApplicantTraining> applicantSelfPacedTrainingList,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantOnlineStudyTrainingList,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantInPersonStudyTrainingList,
      @JsonKey(defaultValue: []) List<LearningTrackView> applicantLearningTrackViewList});
}

/// @nodoc
class _$MyLearningsModelCopyWithImpl<$Res> implements $MyLearningsModelCopyWith<$Res> {
  _$MyLearningsModelCopyWithImpl(this._self, this._then);

  final MyLearningsModel _self;
  final $Res Function(MyLearningsModel) _then;

  /// Create a copy of MyLearningsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? applicantSelfPacedTrainingList = null,
    Object? applicantOnlineStudyTrainingList = null,
    Object? applicantInPersonStudyTrainingList = null,
    Object? applicantLearningTrackViewList = null,
  }) {
    return _then(_self.copyWith(
      applicantSelfPacedTrainingList: null == applicantSelfPacedTrainingList
          ? _self.applicantSelfPacedTrainingList
          : applicantSelfPacedTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantOnlineStudyTrainingList: null == applicantOnlineStudyTrainingList
          ? _self.applicantOnlineStudyTrainingList
          : applicantOnlineStudyTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantInPersonStudyTrainingList: null == applicantInPersonStudyTrainingList
          ? _self.applicantInPersonStudyTrainingList
          : applicantInPersonStudyTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantLearningTrackViewList: null == applicantLearningTrackViewList
          ? _self.applicantLearningTrackViewList
          : applicantLearningTrackViewList // ignore: cast_nullable_to_non_nullable
              as List<LearningTrackView>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _MyLearningsModel implements MyLearningsModel {
  const _MyLearningsModel(
      {@JsonKey(defaultValue: [])
      required final List<ApplicantTraining> applicantSelfPacedTrainingList,
      @JsonKey(defaultValue: [])
      required final List<ApplicantTraining> applicantOnlineStudyTrainingList,
      @JsonKey(defaultValue: [])
      required final List<ApplicantTraining> applicantInPersonStudyTrainingList,
      @JsonKey(defaultValue: [])
      required final List<LearningTrackView> applicantLearningTrackViewList})
      : _applicantSelfPacedTrainingList = applicantSelfPacedTrainingList,
        _applicantOnlineStudyTrainingList = applicantOnlineStudyTrainingList,
        _applicantInPersonStudyTrainingList = applicantInPersonStudyTrainingList,
        _applicantLearningTrackViewList = applicantLearningTrackViewList;
  factory _MyLearningsModel.fromJson(Map<String, dynamic> json) => _$MyLearningsModelFromJson(json);

  final List<ApplicantTraining> _applicantSelfPacedTrainingList;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantSelfPacedTrainingList {
    if (_applicantSelfPacedTrainingList is EqualUnmodifiableListView)
      return _applicantSelfPacedTrainingList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantSelfPacedTrainingList);
  }

  final List<ApplicantTraining> _applicantOnlineStudyTrainingList;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantOnlineStudyTrainingList {
    if (_applicantOnlineStudyTrainingList is EqualUnmodifiableListView)
      return _applicantOnlineStudyTrainingList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantOnlineStudyTrainingList);
  }

  final List<ApplicantTraining> _applicantInPersonStudyTrainingList;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantInPersonStudyTrainingList {
    if (_applicantInPersonStudyTrainingList is EqualUnmodifiableListView)
      return _applicantInPersonStudyTrainingList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantInPersonStudyTrainingList);
  }

  final List<LearningTrackView> _applicantLearningTrackViewList;
  @override
  @JsonKey(defaultValue: [])
  List<LearningTrackView> get applicantLearningTrackViewList {
    if (_applicantLearningTrackViewList is EqualUnmodifiableListView)
      return _applicantLearningTrackViewList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantLearningTrackViewList);
  }

  /// Create a copy of MyLearningsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MyLearningsModelCopyWith<_MyLearningsModel> get copyWith =>
      __$MyLearningsModelCopyWithImpl<_MyLearningsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MyLearningsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MyLearningsModel &&
            const DeepCollectionEquality()
                .equals(other._applicantSelfPacedTrainingList, _applicantSelfPacedTrainingList) &&
            const DeepCollectionEquality().equals(
                other._applicantOnlineStudyTrainingList, _applicantOnlineStudyTrainingList) &&
            const DeepCollectionEquality().equals(
                other._applicantInPersonStudyTrainingList, _applicantInPersonStudyTrainingList) &&
            const DeepCollectionEquality()
                .equals(other._applicantLearningTrackViewList, _applicantLearningTrackViewList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_applicantSelfPacedTrainingList),
      const DeepCollectionEquality().hash(_applicantOnlineStudyTrainingList),
      const DeepCollectionEquality().hash(_applicantInPersonStudyTrainingList),
      const DeepCollectionEquality().hash(_applicantLearningTrackViewList));

  @override
  String toString() {
    return 'MyLearningsModel(applicantSelfPacedTrainingList: $applicantSelfPacedTrainingList, applicantOnlineStudyTrainingList: $applicantOnlineStudyTrainingList, applicantInPersonStudyTrainingList: $applicantInPersonStudyTrainingList, applicantLearningTrackViewList: $applicantLearningTrackViewList)';
  }
}

/// @nodoc
abstract mixin class _$MyLearningsModelCopyWith<$Res> implements $MyLearningsModelCopyWith<$Res> {
  factory _$MyLearningsModelCopyWith(
          _MyLearningsModel value, $Res Function(_MyLearningsModel) _then) =
      __$MyLearningsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<ApplicantTraining> applicantSelfPacedTrainingList,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantOnlineStudyTrainingList,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantInPersonStudyTrainingList,
      @JsonKey(defaultValue: []) List<LearningTrackView> applicantLearningTrackViewList});
}

/// @nodoc
class __$MyLearningsModelCopyWithImpl<$Res> implements _$MyLearningsModelCopyWith<$Res> {
  __$MyLearningsModelCopyWithImpl(this._self, this._then);

  final _MyLearningsModel _self;
  final $Res Function(_MyLearningsModel) _then;

  /// Create a copy of MyLearningsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? applicantSelfPacedTrainingList = null,
    Object? applicantOnlineStudyTrainingList = null,
    Object? applicantInPersonStudyTrainingList = null,
    Object? applicantLearningTrackViewList = null,
  }) {
    return _then(_MyLearningsModel(
      applicantSelfPacedTrainingList: null == applicantSelfPacedTrainingList
          ? _self._applicantSelfPacedTrainingList
          : applicantSelfPacedTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantOnlineStudyTrainingList: null == applicantOnlineStudyTrainingList
          ? _self._applicantOnlineStudyTrainingList
          : applicantOnlineStudyTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantInPersonStudyTrainingList: null == applicantInPersonStudyTrainingList
          ? _self._applicantInPersonStudyTrainingList
          : applicantInPersonStudyTrainingList // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      applicantLearningTrackViewList: null == applicantLearningTrackViewList
          ? _self._applicantLearningTrackViewList
          : applicantLearningTrackViewList // ignore: cast_nullable_to_non_nullable
              as List<LearningTrackView>,
    ));
  }
}

/// @nodoc
mixin _$ApplicantTraining {
  String get id;
  String get trainingId;
  String get trainingTitle;
  int get trainingDurationMin;
  int get trainingDurationMax;
  ProfileImage get profileImage;
  DateTime get enrolledDate;
  DateTime? get completedDate;
  @JsonKey(defaultValue: 0)
  int get passedScore;
  @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1)
  int get allSections;
  @JsonKey(defaultValue: 0)
  int get passedSections;
  int get rating;
  @JsonKey(fromJson: _stringToApplicantTrainingStatus)
  ApplicantTrainingStatus get applicantTrainingStatus;
  int? get certificateId;
  @JsonKey(defaultValue: false)
  bool get preTrainingTestMandatory;
  String get profileImageUrl;
  bool? get preTestPassed;
  @JsonKey(fromJson: stringToTrainingType)
  TrainingType get type;
  @JsonKey(defaultValue: [])
  List<ApplicantStudyStream> get applicantStudyStreams;
  @JsonKey(includeFromJson: false, includeToJson: false)
  LearningType? get learningType;

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApplicantTrainingCopyWith<ApplicantTraining> get copyWith =>
      _$ApplicantTrainingCopyWithImpl<ApplicantTraining>(this as ApplicantTraining, _$identity);

  /// Serializes this ApplicantTraining to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApplicantTraining &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.trainingTitle, trainingTitle) ||
                other.trainingTitle == trainingTitle) &&
            (identical(other.trainingDurationMin, trainingDurationMin) ||
                other.trainingDurationMin == trainingDurationMin) &&
            (identical(other.trainingDurationMax, trainingDurationMax) ||
                other.trainingDurationMax == trainingDurationMax) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allSections, allSections) || other.allSections == allSections) &&
            (identical(other.passedSections, passedSections) ||
                other.passedSections == passedSections) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.applicantTrainingStatus, applicantTrainingStatus) ||
                other.applicantTrainingStatus == applicantTrainingStatus) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId) &&
            (identical(other.preTrainingTestMandatory, preTrainingTestMandatory) ||
                other.preTrainingTestMandatory == preTrainingTestMandatory) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.preTestPassed, preTestPassed) ||
                other.preTestPassed == preTestPassed) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other.applicantStudyStreams, applicantStudyStreams) &&
            (identical(other.learningType, learningType) || other.learningType == learningType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        trainingId,
        trainingTitle,
        trainingDurationMin,
        trainingDurationMax,
        profileImage,
        enrolledDate,
        completedDate,
        passedScore,
        allSections,
        passedSections,
        rating,
        applicantTrainingStatus,
        certificateId,
        preTrainingTestMandatory,
        profileImageUrl,
        preTestPassed,
        type,
        const DeepCollectionEquality().hash(applicantStudyStreams),
        learningType
      ]);

  @override
  String toString() {
    return 'ApplicantTraining(id: $id, trainingId: $trainingId, trainingTitle: $trainingTitle, trainingDurationMin: $trainingDurationMin, trainingDurationMax: $trainingDurationMax, profileImage: $profileImage, enrolledDate: $enrolledDate, completedDate: $completedDate, passedScore: $passedScore, allSections: $allSections, passedSections: $passedSections, rating: $rating, applicantTrainingStatus: $applicantTrainingStatus, certificateId: $certificateId, preTrainingTestMandatory: $preTrainingTestMandatory, profileImageUrl: $profileImageUrl, preTestPassed: $preTestPassed, type: $type, applicantStudyStreams: $applicantStudyStreams, learningType: $learningType)';
  }
}

/// @nodoc
abstract mixin class $ApplicantTrainingCopyWith<$Res> {
  factory $ApplicantTrainingCopyWith(
          ApplicantTraining value, $Res Function(ApplicantTraining) _then) =
      _$ApplicantTrainingCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String trainingId,
      String trainingTitle,
      int trainingDurationMin,
      int trainingDurationMax,
      ProfileImage profileImage,
      DateTime enrolledDate,
      DateTime? completedDate,
      @JsonKey(defaultValue: 0) int passedScore,
      @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1) int allSections,
      @JsonKey(defaultValue: 0) int passedSections,
      int rating,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus)
      ApplicantTrainingStatus applicantTrainingStatus,
      int? certificateId,
      @JsonKey(defaultValue: false) bool preTrainingTestMandatory,
      String profileImageUrl,
      bool? preTestPassed,
      @JsonKey(fromJson: stringToTrainingType) TrainingType type,
      @JsonKey(defaultValue: []) List<ApplicantStudyStream> applicantStudyStreams,
      @JsonKey(includeFromJson: false, includeToJson: false) LearningType? learningType});

  $ProfileImageCopyWith<$Res> get profileImage;
}

/// @nodoc
class _$ApplicantTrainingCopyWithImpl<$Res> implements $ApplicantTrainingCopyWith<$Res> {
  _$ApplicantTrainingCopyWithImpl(this._self, this._then);

  final ApplicantTraining _self;
  final $Res Function(ApplicantTraining) _then;

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? trainingId = null,
    Object? trainingTitle = null,
    Object? trainingDurationMin = null,
    Object? trainingDurationMax = null,
    Object? profileImage = null,
    Object? enrolledDate = null,
    Object? completedDate = freezed,
    Object? passedScore = null,
    Object? allSections = null,
    Object? passedSections = null,
    Object? rating = null,
    Object? applicantTrainingStatus = null,
    Object? certificateId = freezed,
    Object? preTrainingTestMandatory = null,
    Object? profileImageUrl = null,
    Object? preTestPassed = freezed,
    Object? type = null,
    Object? applicantStudyStreams = null,
    Object? learningType = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      trainingTitle: null == trainingTitle
          ? _self.trainingTitle
          : trainingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      trainingDurationMin: null == trainingDurationMin
          ? _self.trainingDurationMin
          : trainingDurationMin // ignore: cast_nullable_to_non_nullable
              as int,
      trainingDurationMax: null == trainingDurationMax
          ? _self.trainingDurationMax
          : trainingDurationMax // ignore: cast_nullable_to_non_nullable
              as int,
      profileImage: null == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ProfileImage,
      enrolledDate: null == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allSections: null == allSections
          ? _self.allSections
          : allSections // ignore: cast_nullable_to_non_nullable
              as int,
      passedSections: null == passedSections
          ? _self.passedSections
          : passedSections // ignore: cast_nullable_to_non_nullable
              as int,
      rating: null == rating
          ? _self.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int,
      applicantTrainingStatus: null == applicantTrainingStatus
          ? _self.applicantTrainingStatus
          : applicantTrainingStatus // ignore: cast_nullable_to_non_nullable
              as ApplicantTrainingStatus,
      certificateId: freezed == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int?,
      preTrainingTestMandatory: null == preTrainingTestMandatory
          ? _self.preTrainingTestMandatory
          : preTrainingTestMandatory // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      preTestPassed: freezed == preTestPassed
          ? _self.preTestPassed
          : preTestPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as TrainingType,
      applicantStudyStreams: null == applicantStudyStreams
          ? _self.applicantStudyStreams
          : applicantStudyStreams // ignore: cast_nullable_to_non_nullable
              as List<ApplicantStudyStream>,
      learningType: freezed == learningType
          ? _self.learningType
          : learningType // ignore: cast_nullable_to_non_nullable
              as LearningType?,
    ));
  }

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileImageCopyWith<$Res> get profileImage {
    return $ProfileImageCopyWith<$Res>(_self.profileImage, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _ApplicantTraining implements ApplicantTraining {
  const _ApplicantTraining(
      {required this.id,
      required this.trainingId,
      required this.trainingTitle,
      required this.trainingDurationMin,
      required this.trainingDurationMax,
      required this.profileImage,
      required this.enrolledDate,
      this.completedDate,
      @JsonKey(defaultValue: 0) required this.passedScore,
      @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1) required this.allSections,
      @JsonKey(defaultValue: 0) required this.passedSections,
      required this.rating,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus) required this.applicantTrainingStatus,
      this.certificateId,
      @JsonKey(defaultValue: false) required this.preTrainingTestMandatory,
      required this.profileImageUrl,
      this.preTestPassed,
      @JsonKey(fromJson: stringToTrainingType) required this.type,
      @JsonKey(defaultValue: []) required final List<ApplicantStudyStream> applicantStudyStreams,
      @JsonKey(includeFromJson: false, includeToJson: false) this.learningType})
      : _applicantStudyStreams = applicantStudyStreams;
  factory _ApplicantTraining.fromJson(Map<String, dynamic> json) =>
      _$ApplicantTrainingFromJson(json);

  @override
  final String id;
  @override
  final String trainingId;
  @override
  final String trainingTitle;
  @override
  final int trainingDurationMin;
  @override
  final int trainingDurationMax;
  @override
  final ProfileImage profileImage;
  @override
  final DateTime enrolledDate;
  @override
  final DateTime? completedDate;
  @override
  @JsonKey(defaultValue: 0)
  final int passedScore;
  @override
  @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1)
  final int allSections;
  @override
  @JsonKey(defaultValue: 0)
  final int passedSections;
  @override
  final int rating;
  @override
  @JsonKey(fromJson: _stringToApplicantTrainingStatus)
  final ApplicantTrainingStatus applicantTrainingStatus;
  @override
  final int? certificateId;
  @override
  @JsonKey(defaultValue: false)
  final bool preTrainingTestMandatory;
  @override
  final String profileImageUrl;
  @override
  final bool? preTestPassed;
  @override
  @JsonKey(fromJson: stringToTrainingType)
  final TrainingType type;
  final List<ApplicantStudyStream> _applicantStudyStreams;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantStudyStream> get applicantStudyStreams {
    if (_applicantStudyStreams is EqualUnmodifiableListView) return _applicantStudyStreams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantStudyStreams);
  }

  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final LearningType? learningType;

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplicantTrainingCopyWith<_ApplicantTraining> get copyWith =>
      __$ApplicantTrainingCopyWithImpl<_ApplicantTraining>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApplicantTrainingToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplicantTraining &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.trainingId, trainingId) || other.trainingId == trainingId) &&
            (identical(other.trainingTitle, trainingTitle) ||
                other.trainingTitle == trainingTitle) &&
            (identical(other.trainingDurationMin, trainingDurationMin) ||
                other.trainingDurationMin == trainingDurationMin) &&
            (identical(other.trainingDurationMax, trainingDurationMax) ||
                other.trainingDurationMax == trainingDurationMax) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allSections, allSections) || other.allSections == allSections) &&
            (identical(other.passedSections, passedSections) ||
                other.passedSections == passedSections) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.applicantTrainingStatus, applicantTrainingStatus) ||
                other.applicantTrainingStatus == applicantTrainingStatus) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId) &&
            (identical(other.preTrainingTestMandatory, preTrainingTestMandatory) ||
                other.preTrainingTestMandatory == preTrainingTestMandatory) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.preTestPassed, preTestPassed) ||
                other.preTestPassed == preTestPassed) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other._applicantStudyStreams, _applicantStudyStreams) &&
            (identical(other.learningType, learningType) || other.learningType == learningType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        trainingId,
        trainingTitle,
        trainingDurationMin,
        trainingDurationMax,
        profileImage,
        enrolledDate,
        completedDate,
        passedScore,
        allSections,
        passedSections,
        rating,
        applicantTrainingStatus,
        certificateId,
        preTrainingTestMandatory,
        profileImageUrl,
        preTestPassed,
        type,
        const DeepCollectionEquality().hash(_applicantStudyStreams),
        learningType
      ]);

  @override
  String toString() {
    return 'ApplicantTraining(id: $id, trainingId: $trainingId, trainingTitle: $trainingTitle, trainingDurationMin: $trainingDurationMin, trainingDurationMax: $trainingDurationMax, profileImage: $profileImage, enrolledDate: $enrolledDate, completedDate: $completedDate, passedScore: $passedScore, allSections: $allSections, passedSections: $passedSections, rating: $rating, applicantTrainingStatus: $applicantTrainingStatus, certificateId: $certificateId, preTrainingTestMandatory: $preTrainingTestMandatory, profileImageUrl: $profileImageUrl, preTestPassed: $preTestPassed, type: $type, applicantStudyStreams: $applicantStudyStreams, learningType: $learningType)';
  }
}

/// @nodoc
abstract mixin class _$ApplicantTrainingCopyWith<$Res> implements $ApplicantTrainingCopyWith<$Res> {
  factory _$ApplicantTrainingCopyWith(
          _ApplicantTraining value, $Res Function(_ApplicantTraining) _then) =
      __$ApplicantTrainingCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String trainingId,
      String trainingTitle,
      int trainingDurationMin,
      int trainingDurationMax,
      ProfileImage profileImage,
      DateTime enrolledDate,
      DateTime? completedDate,
      @JsonKey(defaultValue: 0) int passedScore,
      @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1) int allSections,
      @JsonKey(defaultValue: 0) int passedSections,
      int rating,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus)
      ApplicantTrainingStatus applicantTrainingStatus,
      int? certificateId,
      @JsonKey(defaultValue: false) bool preTrainingTestMandatory,
      String profileImageUrl,
      bool? preTestPassed,
      @JsonKey(fromJson: stringToTrainingType) TrainingType type,
      @JsonKey(defaultValue: []) List<ApplicantStudyStream> applicantStudyStreams,
      @JsonKey(includeFromJson: false, includeToJson: false) LearningType? learningType});

  @override
  $ProfileImageCopyWith<$Res> get profileImage;
}

/// @nodoc
class __$ApplicantTrainingCopyWithImpl<$Res> implements _$ApplicantTrainingCopyWith<$Res> {
  __$ApplicantTrainingCopyWithImpl(this._self, this._then);

  final _ApplicantTraining _self;
  final $Res Function(_ApplicantTraining) _then;

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? trainingId = null,
    Object? trainingTitle = null,
    Object? trainingDurationMin = null,
    Object? trainingDurationMax = null,
    Object? profileImage = null,
    Object? enrolledDate = null,
    Object? completedDate = freezed,
    Object? passedScore = null,
    Object? allSections = null,
    Object? passedSections = null,
    Object? rating = null,
    Object? applicantTrainingStatus = null,
    Object? certificateId = freezed,
    Object? preTrainingTestMandatory = null,
    Object? profileImageUrl = null,
    Object? preTestPassed = freezed,
    Object? type = null,
    Object? applicantStudyStreams = null,
    Object? learningType = freezed,
  }) {
    return _then(_ApplicantTraining(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      trainingId: null == trainingId
          ? _self.trainingId
          : trainingId // ignore: cast_nullable_to_non_nullable
              as String,
      trainingTitle: null == trainingTitle
          ? _self.trainingTitle
          : trainingTitle // ignore: cast_nullable_to_non_nullable
              as String,
      trainingDurationMin: null == trainingDurationMin
          ? _self.trainingDurationMin
          : trainingDurationMin // ignore: cast_nullable_to_non_nullable
              as int,
      trainingDurationMax: null == trainingDurationMax
          ? _self.trainingDurationMax
          : trainingDurationMax // ignore: cast_nullable_to_non_nullable
              as int,
      profileImage: null == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ProfileImage,
      enrolledDate: null == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allSections: null == allSections
          ? _self.allSections
          : allSections // ignore: cast_nullable_to_non_nullable
              as int,
      passedSections: null == passedSections
          ? _self.passedSections
          : passedSections // ignore: cast_nullable_to_non_nullable
              as int,
      rating: null == rating
          ? _self.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int,
      applicantTrainingStatus: null == applicantTrainingStatus
          ? _self.applicantTrainingStatus
          : applicantTrainingStatus // ignore: cast_nullable_to_non_nullable
              as ApplicantTrainingStatus,
      certificateId: freezed == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int?,
      preTrainingTestMandatory: null == preTrainingTestMandatory
          ? _self.preTrainingTestMandatory
          : preTrainingTestMandatory // ignore: cast_nullable_to_non_nullable
              as bool,
      profileImageUrl: null == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      preTestPassed: freezed == preTestPassed
          ? _self.preTestPassed
          : preTestPassed // ignore: cast_nullable_to_non_nullable
              as bool?,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as TrainingType,
      applicantStudyStreams: null == applicantStudyStreams
          ? _self._applicantStudyStreams
          : applicantStudyStreams // ignore: cast_nullable_to_non_nullable
              as List<ApplicantStudyStream>,
      learningType: freezed == learningType
          ? _self.learningType
          : learningType // ignore: cast_nullable_to_non_nullable
              as LearningType?,
    ));
  }

  /// Create a copy of ApplicantTraining
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileImageCopyWith<$Res> get profileImage {
    return $ProfileImageCopyWith<$Res>(_self.profileImage, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }
}

/// @nodoc
mixin _$ProfileImage {
  String get originalFilename;
  String get key;
  int get size;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProfileImageCopyWith<ProfileImage> get copyWith =>
      _$ProfileImageCopyWithImpl<ProfileImage>(this as ProfileImage, _$identity);

  /// Serializes this ProfileImage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProfileImage &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'ProfileImage(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class $ProfileImageCopyWith<$Res> {
  factory $ProfileImageCopyWith(ProfileImage value, $Res Function(ProfileImage) _then) =
      _$ProfileImageCopyWithImpl;
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class _$ProfileImageCopyWithImpl<$Res> implements $ProfileImageCopyWith<$Res> {
  _$ProfileImageCopyWithImpl(this._self, this._then);

  final ProfileImage _self;
  final $Res Function(ProfileImage) _then;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_self.copyWith(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ProfileImage implements ProfileImage {
  const _ProfileImage({required this.originalFilename, required this.key, required this.size});
  factory _ProfileImage.fromJson(Map<String, dynamic> json) => _$ProfileImageFromJson(json);

  @override
  final String originalFilename;
  @override
  final String key;
  @override
  final int size;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProfileImageCopyWith<_ProfileImage> get copyWith =>
      __$ProfileImageCopyWithImpl<_ProfileImage>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ProfileImageToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProfileImage &&
            (identical(other.originalFilename, originalFilename) ||
                other.originalFilename == originalFilename) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.size, size) || other.size == size));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, originalFilename, key, size);

  @override
  String toString() {
    return 'ProfileImage(originalFilename: $originalFilename, key: $key, size: $size)';
  }
}

/// @nodoc
abstract mixin class _$ProfileImageCopyWith<$Res> implements $ProfileImageCopyWith<$Res> {
  factory _$ProfileImageCopyWith(_ProfileImage value, $Res Function(_ProfileImage) _then) =
      __$ProfileImageCopyWithImpl;
  @override
  @useResult
  $Res call({String originalFilename, String key, int size});
}

/// @nodoc
class __$ProfileImageCopyWithImpl<$Res> implements _$ProfileImageCopyWith<$Res> {
  __$ProfileImageCopyWithImpl(this._self, this._then);

  final _ProfileImage _self;
  final $Res Function(_ProfileImage) _then;

  /// Create a copy of ProfileImage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? originalFilename = null,
    Object? key = null,
    Object? size = null,
  }) {
    return _then(_ProfileImage(
      originalFilename: null == originalFilename
          ? _self.originalFilename
          : originalFilename // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _self.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      size: null == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$LearningTrackView {
  String get id;
  String get learningTrackId;
  String get learningTrackTitle;
  @JsonKey(fromJson: _stringToApplicantTrainingStatus)
  ApplicantTrainingStatus get status;
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantTrainings;
  int get passedScore;
  @JsonKey(fromJson: _allSectionsFromJson)
  int get allTrainings;
  int get passedTrainings;
  DateTime? get completedDate;
  DateTime get enrolledDate;
  int get certificateId;

  /// Create a copy of LearningTrackView
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LearningTrackViewCopyWith<LearningTrackView> get copyWith =>
      _$LearningTrackViewCopyWithImpl<LearningTrackView>(this as LearningTrackView, _$identity);

  /// Serializes this LearningTrackView to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LearningTrackView &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.learningTrackId, learningTrackId) ||
                other.learningTrackId == learningTrackId) &&
            (identical(other.learningTrackTitle, learningTrackTitle) ||
                other.learningTrackTitle == learningTrackTitle) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other.applicantTrainings, applicantTrainings) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allTrainings, allTrainings) || other.allTrainings == allTrainings) &&
            (identical(other.passedTrainings, passedTrainings) ||
                other.passedTrainings == passedTrainings) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      learningTrackId,
      learningTrackTitle,
      status,
      const DeepCollectionEquality().hash(applicantTrainings),
      passedScore,
      allTrainings,
      passedTrainings,
      completedDate,
      enrolledDate,
      certificateId);

  @override
  String toString() {
    return 'LearningTrackView(id: $id, learningTrackId: $learningTrackId, learningTrackTitle: $learningTrackTitle, status: $status, applicantTrainings: $applicantTrainings, passedScore: $passedScore, allTrainings: $allTrainings, passedTrainings: $passedTrainings, completedDate: $completedDate, enrolledDate: $enrolledDate, certificateId: $certificateId)';
  }
}

/// @nodoc
abstract mixin class $LearningTrackViewCopyWith<$Res> {
  factory $LearningTrackViewCopyWith(
          LearningTrackView value, $Res Function(LearningTrackView) _then) =
      _$LearningTrackViewCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String learningTrackId,
      String learningTrackTitle,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus) ApplicantTrainingStatus status,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantTrainings,
      int passedScore,
      @JsonKey(fromJson: _allSectionsFromJson) int allTrainings,
      int passedTrainings,
      DateTime? completedDate,
      DateTime enrolledDate,
      int certificateId});
}

/// @nodoc
class _$LearningTrackViewCopyWithImpl<$Res> implements $LearningTrackViewCopyWith<$Res> {
  _$LearningTrackViewCopyWithImpl(this._self, this._then);

  final LearningTrackView _self;
  final $Res Function(LearningTrackView) _then;

  /// Create a copy of LearningTrackView
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? learningTrackId = null,
    Object? learningTrackTitle = null,
    Object? status = null,
    Object? applicantTrainings = null,
    Object? passedScore = null,
    Object? allTrainings = null,
    Object? passedTrainings = null,
    Object? completedDate = freezed,
    Object? enrolledDate = null,
    Object? certificateId = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      learningTrackId: null == learningTrackId
          ? _self.learningTrackId
          : learningTrackId // ignore: cast_nullable_to_non_nullable
              as String,
      learningTrackTitle: null == learningTrackTitle
          ? _self.learningTrackTitle
          : learningTrackTitle // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApplicantTrainingStatus,
      applicantTrainings: null == applicantTrainings
          ? _self.applicantTrainings
          : applicantTrainings // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allTrainings: null == allTrainings
          ? _self.allTrainings
          : allTrainings // ignore: cast_nullable_to_non_nullable
              as int,
      passedTrainings: null == passedTrainings
          ? _self.passedTrainings
          : passedTrainings // ignore: cast_nullable_to_non_nullable
              as int,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      enrolledDate: null == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      certificateId: null == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LearningTrackView implements LearningTrackView {
  const _LearningTrackView(
      {required this.id,
      required this.learningTrackId,
      required this.learningTrackTitle,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus) required this.status,
      @JsonKey(defaultValue: []) required final List<ApplicantTraining> applicantTrainings,
      required this.passedScore,
      @JsonKey(fromJson: _allSectionsFromJson) required this.allTrainings,
      required this.passedTrainings,
      this.completedDate,
      required this.enrolledDate,
      required this.certificateId})
      : _applicantTrainings = applicantTrainings;
  factory _LearningTrackView.fromJson(Map<String, dynamic> json) =>
      _$LearningTrackViewFromJson(json);

  @override
  final String id;
  @override
  final String learningTrackId;
  @override
  final String learningTrackTitle;
  @override
  @JsonKey(fromJson: _stringToApplicantTrainingStatus)
  final ApplicantTrainingStatus status;
  final List<ApplicantTraining> _applicantTrainings;
  @override
  @JsonKey(defaultValue: [])
  List<ApplicantTraining> get applicantTrainings {
    if (_applicantTrainings is EqualUnmodifiableListView) return _applicantTrainings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_applicantTrainings);
  }

  @override
  final int passedScore;
  @override
  @JsonKey(fromJson: _allSectionsFromJson)
  final int allTrainings;
  @override
  final int passedTrainings;
  @override
  final DateTime? completedDate;
  @override
  final DateTime enrolledDate;
  @override
  final int certificateId;

  /// Create a copy of LearningTrackView
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LearningTrackViewCopyWith<_LearningTrackView> get copyWith =>
      __$LearningTrackViewCopyWithImpl<_LearningTrackView>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LearningTrackViewToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LearningTrackView &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.learningTrackId, learningTrackId) ||
                other.learningTrackId == learningTrackId) &&
            (identical(other.learningTrackTitle, learningTrackTitle) ||
                other.learningTrackTitle == learningTrackTitle) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._applicantTrainings, _applicantTrainings) &&
            (identical(other.passedScore, passedScore) || other.passedScore == passedScore) &&
            (identical(other.allTrainings, allTrainings) || other.allTrainings == allTrainings) &&
            (identical(other.passedTrainings, passedTrainings) ||
                other.passedTrainings == passedTrainings) &&
            (identical(other.completedDate, completedDate) ||
                other.completedDate == completedDate) &&
            (identical(other.enrolledDate, enrolledDate) || other.enrolledDate == enrolledDate) &&
            (identical(other.certificateId, certificateId) ||
                other.certificateId == certificateId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      learningTrackId,
      learningTrackTitle,
      status,
      const DeepCollectionEquality().hash(_applicantTrainings),
      passedScore,
      allTrainings,
      passedTrainings,
      completedDate,
      enrolledDate,
      certificateId);

  @override
  String toString() {
    return 'LearningTrackView(id: $id, learningTrackId: $learningTrackId, learningTrackTitle: $learningTrackTitle, status: $status, applicantTrainings: $applicantTrainings, passedScore: $passedScore, allTrainings: $allTrainings, passedTrainings: $passedTrainings, completedDate: $completedDate, enrolledDate: $enrolledDate, certificateId: $certificateId)';
  }
}

/// @nodoc
abstract mixin class _$LearningTrackViewCopyWith<$Res> implements $LearningTrackViewCopyWith<$Res> {
  factory _$LearningTrackViewCopyWith(
          _LearningTrackView value, $Res Function(_LearningTrackView) _then) =
      __$LearningTrackViewCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String learningTrackId,
      String learningTrackTitle,
      @JsonKey(fromJson: _stringToApplicantTrainingStatus) ApplicantTrainingStatus status,
      @JsonKey(defaultValue: []) List<ApplicantTraining> applicantTrainings,
      int passedScore,
      @JsonKey(fromJson: _allSectionsFromJson) int allTrainings,
      int passedTrainings,
      DateTime? completedDate,
      DateTime enrolledDate,
      int certificateId});
}

/// @nodoc
class __$LearningTrackViewCopyWithImpl<$Res> implements _$LearningTrackViewCopyWith<$Res> {
  __$LearningTrackViewCopyWithImpl(this._self, this._then);

  final _LearningTrackView _self;
  final $Res Function(_LearningTrackView) _then;

  /// Create a copy of LearningTrackView
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? learningTrackId = null,
    Object? learningTrackTitle = null,
    Object? status = null,
    Object? applicantTrainings = null,
    Object? passedScore = null,
    Object? allTrainings = null,
    Object? passedTrainings = null,
    Object? completedDate = freezed,
    Object? enrolledDate = null,
    Object? certificateId = null,
  }) {
    return _then(_LearningTrackView(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      learningTrackId: null == learningTrackId
          ? _self.learningTrackId
          : learningTrackId // ignore: cast_nullable_to_non_nullable
              as String,
      learningTrackTitle: null == learningTrackTitle
          ? _self.learningTrackTitle
          : learningTrackTitle // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as ApplicantTrainingStatus,
      applicantTrainings: null == applicantTrainings
          ? _self._applicantTrainings
          : applicantTrainings // ignore: cast_nullable_to_non_nullable
              as List<ApplicantTraining>,
      passedScore: null == passedScore
          ? _self.passedScore
          : passedScore // ignore: cast_nullable_to_non_nullable
              as int,
      allTrainings: null == allTrainings
          ? _self.allTrainings
          : allTrainings // ignore: cast_nullable_to_non_nullable
              as int,
      passedTrainings: null == passedTrainings
          ? _self.passedTrainings
          : passedTrainings // ignore: cast_nullable_to_non_nullable
              as int,
      completedDate: freezed == completedDate
          ? _self.completedDate
          : completedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      enrolledDate: null == enrolledDate
          ? _self.enrolledDate
          : enrolledDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      certificateId: null == certificateId
          ? _self.certificateId
          : certificateId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
