import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/converters/string_converters.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

part 'my_learnings_model.g.dart';

part 'my_learnings_model.freezed.dart';

enum ApplicantTrainingStatus { ENROLLED, NOMINATED, COMPLETED }

enum LearningType { SelfPaced, OnlineStudy, InPersonStudy, LearningTrack }

@freezed
abstract class MyLearningsModel with _$MyLearningsModel {
  const factory MyLearningsModel({
    @JsonKey(defaultValue: []) required List<ApplicantTraining> applicantSelfPacedTrainingList,
    @JsonKey(defaultValue: []) required List<ApplicantTraining> applicantOnlineStudyTrainingList,
    @<PERSON>sonKey(defaultValue: []) required List<ApplicantTraining> applicantInPersonStudyTrainingList,
    @JsonKey(defaultValue: []) required List<LearningTrackView> applicantLearningTrackViewList,
  }) = _MyLearningsModel;

  factory MyLearningsModel.fromJson(Map<String, dynamic> json) => _$MyLearningsModelFromJson(json);
}

@freezed
abstract class ApplicantTraining with _$ApplicantTraining {
  const factory ApplicantTraining({
    required String id,
    required String trainingId,
    required String trainingTitle,
    required int trainingDurationMin,
    required int trainingDurationMax,
    required ProfileImage profileImage,
    required DateTime enrolledDate,
    DateTime? completedDate,
    @JsonKey(defaultValue: 0) required int passedScore,
    @JsonKey(fromJson: _allSectionsFromJson, defaultValue: 1) required int allSections,
    @JsonKey(defaultValue: 0) required int passedSections,
    required int rating,
    @JsonKey(fromJson: _stringToApplicantTrainingStatus)
    required ApplicantTrainingStatus applicantTrainingStatus,
    int? certificateId,
    @JsonKey(defaultValue: false) required bool preTrainingTestMandatory,
    required String profileImageUrl,
    bool? preTestPassed,
    @JsonKey(fromJson: stringToTrainingType) required TrainingType type,
    @JsonKey(defaultValue: []) required List<ApplicantStudyStream> applicantStudyStreams,
    @JsonKey(includeFromJson: false, includeToJson: false) LearningType? learningType,
  }) = _ApplicantTraining;

  factory ApplicantTraining.fromJson(Map<String, dynamic> json) =>
      _$ApplicantTrainingFromJson(json);
}

@freezed
abstract class ProfileImage with _$ProfileImage {
  const factory ProfileImage({
    required String originalFilename,
    required String key,
    required int size,
  }) = _ProfileImage;

  factory ProfileImage.fromJson(Map<String, dynamic> json) => _$ProfileImageFromJson(json);
}

@freezed
abstract class LearningTrackView with _$LearningTrackView {
  const factory LearningTrackView({
    required String id,
    required String learningTrackId,
    required String learningTrackTitle,
    @JsonKey(fromJson: _stringToApplicantTrainingStatus) required ApplicantTrainingStatus status,
    @JsonKey(defaultValue: []) required List<ApplicantTraining> applicantTrainings,
    required int passedScore,
    @JsonKey(fromJson: _allSectionsFromJson) required int allTrainings,
    required int passedTrainings,
    DateTime? completedDate,
    required DateTime enrolledDate,
    required int certificateId,
  }) = _LearningTrackView;

  factory LearningTrackView.fromJson(Map<String, dynamic> json) =>
      _$LearningTrackViewFromJson(json);
}

int _allSectionsFromJson(Object? value) {
  if (value == null) return 1;
  if (value is int && value == 0) {
    return 1;
  } else if (value is double && value == 0.0) {
    return 1;
  }
  return value as int;
}

ApplicantTrainingStatus _stringToApplicantTrainingStatus(String? applicantTrainingStatus) {
  switch (applicantTrainingStatus) {
    case Constants.ENROLLED:
      return ApplicantTrainingStatus.ENROLLED;
    case Constants.NOMINATED:
      return ApplicantTrainingStatus.NOMINATED;
    case Constants.COMPLETED:
      return ApplicantTrainingStatus.COMPLETED;
    default:
      return ApplicantTrainingStatus.ENROLLED;
  }
}
