import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/my_learnings/data/data_sources/my_learning_datasource.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

@injectable
class MyLearningsRepository {
  const MyLearningsRepository({required MyLearningsDataSource dataSource})
      : _dataSource = dataSource;

  final MyLearningsDataSource _dataSource;

  Future<(MyLearningsModel, List<ApplicantTraining>)> getMyLearnings() async {
    final myLearningModel = await _dataSource.getMyLearnings();

    final allRecentTrainings = [
      ...myLearningModel.applicantSelfPacedTrainingList.map(
        (training) => training.copyWith(learningType: LearningType.SelfPaced),
      ),
      ...myLearningModel.applicantOnlineStudyTrainingList.map(
        (training) => training.copyWith(learningType: LearningType.OnlineStudy),
      ),
      ...myLearningModel.applicantInPersonStudyTrainingList.map(
        (training) => training.copyWith(learningType: LearningType.InPersonStudy),
      ),
    ];

    return (myLearningModel, allRecentTrainings);
  }
}
