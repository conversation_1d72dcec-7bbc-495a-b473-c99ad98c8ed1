import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/skill_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';

enum FilterKeywords {
  DAYS,
  WEEKS,
  MONTHS,
  AR,
  EN,
  SELF_PACED,
  ONLINE,
  IN_PERSON,
  BASIC,
  GENERAL,
  ADVANCED,
}

class FilterModelToLearningTracksRequestParamsConverter {
  String _getFilterKeyword(bool condition, FilterKeywords keyword) {
    return condition ? keyword.name : '';
  }

  LearningTracksRequestParams convert(FilterModel filterModel) {
    final LanguageFilterModel languageFilter = filterModel.languageFilterModel;
    final DurationFilterModel durationFilter = filterModel.durationFilterModel;
    final TrainingTypeFilterModel trainingTypeFilterModel = filterModel.trainingTypeFilterModel;
    final SectorFilterModel sectorFilterModel = filterModel.sectorFilterModel;
    final TrainingProviderFilterModel trainingProviderFilterModel =
        filterModel.trainingProviderFilterModel;
    final SkillFilterModel skillFilter = filterModel.skillFilterModel;
    final hasInstructorLed = trainingTypeFilterModel.online || trainingTypeFilterModel.inPerson;
    final hasSelfPaced = trainingTypeFilterModel.selfPaced;
    String selectedTrainingTypes = '';
    String selectedStudyStreamTypes = '';

    final selectedDurations = [
      _getFilterKeyword(durationFilter.days, FilterKeywords.DAYS),
      _getFilterKeyword(durationFilter.weeks, FilterKeywords.WEEKS),
      _getFilterKeyword(durationFilter.months, FilterKeywords.MONTHS),
    ].where((duration) => duration.isNotEmpty).join(',').toUpperCase();

    final selectedLanguages = [
      _getFilterKeyword(languageFilter.english, FilterKeywords.EN),
      _getFilterKeyword(languageFilter.arabic, FilterKeywords.AR),
    ].where((language) => language.isNotEmpty).join(',').toUpperCase();

    final selectedStreamTypes = [
      _getFilterKeyword(trainingTypeFilterModel.online, FilterKeywords.ONLINE),
      _getFilterKeyword(trainingTypeFilterModel.inPerson, FilterKeywords.IN_PERSON),
    ].where((type) => type.isNotEmpty).join(',').toUpperCase();

    final selectedSkills = [
      _getFilterKeyword(skillFilter.advanced, FilterKeywords.ADVANCED),
      _getFilterKeyword(skillFilter.basic, FilterKeywords.BASIC),
      _getFilterKeyword(skillFilter.general, FilterKeywords.GENERAL),
    ].where((skill) => skill.isNotEmpty).join(',').toUpperCase();

    if (selectedStreamTypes.isNotEmpty) {
      selectedStudyStreamTypes = selectedStreamTypes;
    }

    if (hasInstructorLed && hasSelfPaced) {
      selectedTrainingTypes = '${Constants.SELF_PACED},${Constants.INSTRUCTOR_LED}';
    } else if (hasInstructorLed) {
      selectedTrainingTypes = Constants.INSTRUCTOR_LED;
    } else if (hasSelfPaced) {
      selectedTrainingTypes = Constants.SELF_PACED;
    }

    final selectedSectors = sectorFilterModel.selectedSectorIds;
    final selectedProviders = trainingProviderFilterModel.selectedTrainingProviderIds;
    final selectedLocations = filterModel.locationFilterModel.selectedLocationIds;

    return LearningTracksRequestParams(
      page: 0,
      size: 10,
      duration: selectedDurations.isNotEmpty ? selectedDurations : null,
      languages: selectedLanguages.isNotEmpty ? selectedLanguages : null,
      types: selectedTrainingTypes.isNotEmpty ? selectedTrainingTypes : null,
      studyStreamTypes: selectedStudyStreamTypes.isNotEmpty ? selectedStudyStreamTypes : null,
      sectors: selectedSectors.isNotEmpty ? selectedSectors.join(',') : null,
      trainingProviders: selectedProviders.isNotEmpty ? selectedProviders.join(',') : null,
      skillLevels: selectedSkills.isNotEmpty ? selectedSkills : null,
      locations: selectedLocations.isNotEmpty ? selectedLocations.join(',') : null,
      filterModel: filterModel,
    );
  }
}

class FilterModelToTrainingsRequestParamsConverter {
  String _getFilterKeyword(bool condition, FilterKeywords keyword) {
    return condition ? keyword.name : '';
  }

  TrainingsRequestParams convert(FilterModel filterModel) {
    final LanguageFilterModel languageFilter = filterModel.languageFilterModel;
    final DurationFilterModel durationFilter = filterModel.durationFilterModel;
    final TrainingTypeFilterModel trainingTypeFilterModel = filterModel.trainingTypeFilterModel;
    final SectorFilterModel sectorFilterModel = filterModel.sectorFilterModel;
    final TrainingProviderFilterModel trainingProviderFilterModel =
        filterModel.trainingProviderFilterModel;
    final SkillFilterModel skillFilter = filterModel.skillFilterModel;
    final hasInstructorLed = trainingTypeFilterModel.online || trainingTypeFilterModel.inPerson;
    final hasSelfPaced = trainingTypeFilterModel.selfPaced;
    String selectedTrainingTypes = '';
    String selectedStudyStreamTypes = '';

    final selectedDurations = [
      _getFilterKeyword(durationFilter.days, FilterKeywords.DAYS),
      _getFilterKeyword(durationFilter.weeks, FilterKeywords.WEEKS),
      _getFilterKeyword(durationFilter.months, FilterKeywords.MONTHS),
    ].where((duration) => duration.isNotEmpty).join(',').toUpperCase();

    final selectedLanguages = [
      _getFilterKeyword(languageFilter.english, FilterKeywords.EN),
      _getFilterKeyword(languageFilter.arabic, FilterKeywords.AR),
    ].where((language) => language.isNotEmpty).join(',').toUpperCase();

    final selectedStreamTypes = [
      _getFilterKeyword(trainingTypeFilterModel.online, FilterKeywords.ONLINE),
      _getFilterKeyword(trainingTypeFilterModel.inPerson, FilterKeywords.IN_PERSON),
    ].where((type) => type.isNotEmpty).join(',').toUpperCase();

    final selectedSkillLevels = [
      _getFilterKeyword(skillFilter.advanced, FilterKeywords.ADVANCED),
      _getFilterKeyword(skillFilter.basic, FilterKeywords.BASIC),
      _getFilterKeyword(skillFilter.general, FilterKeywords.GENERAL),
    ].where((skillLevel) => skillLevel.isNotEmpty).join(',').toUpperCase();

    if (selectedStreamTypes.isNotEmpty) {
      selectedStudyStreamTypes = selectedStreamTypes;
    }

    if (hasInstructorLed && hasSelfPaced) {
      selectedTrainingTypes = '${Constants.SELF_PACED},${Constants.INSTRUCTOR_LED}';
    } else if (hasInstructorLed) {
      selectedTrainingTypes = Constants.INSTRUCTOR_LED;
    } else if (hasSelfPaced) {
      selectedTrainingTypes = Constants.SELF_PACED;
    }

    final selectedSectors = sectorFilterModel.selectedSectorIds;
    final selectedProviders = trainingProviderFilterModel.selectedTrainingProviderIds;
    final selectedLocations = filterModel.locationFilterModel.selectedLocationIds;

    return TrainingsRequestParams(
      page: 0,
      size: 10,
      duration: selectedDurations.isNotEmpty ? selectedDurations : null,
      languages: selectedLanguages.isNotEmpty ? selectedLanguages : null,
      types: selectedTrainingTypes.isNotEmpty ? selectedTrainingTypes : null,
      studyStreamTypes: selectedStudyStreamTypes.isNotEmpty ? selectedStudyStreamTypes : null,
      sectors: selectedSectors.isNotEmpty ? selectedSectors.join(',') : null,
      trainingProviders: selectedProviders.isNotEmpty ? selectedProviders.join(',') : null,
      skillLevels: selectedSkillLevels.isNotEmpty ? selectedSkillLevels : null,
      locations: selectedLocations.isNotEmpty ? selectedLocations.join(',') : null,
      filterModel: filterModel,
    );
  }
}
