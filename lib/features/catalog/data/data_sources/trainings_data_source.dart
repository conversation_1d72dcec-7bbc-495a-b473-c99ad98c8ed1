import 'dart:async';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';

@injectable
class TrainingsDataSource {
  const TrainingsDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<TrainingsModel> getTrainingsList(TrainingsRequestParams requestPrams) async {
    final response = await _dio.get(
      ApiConstants.trainingCataloguePath,
      queryParameters: requestPrams.getParams(),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return TrainingsModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
