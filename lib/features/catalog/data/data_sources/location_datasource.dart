import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';

@injectable
class LocationDataSource {
  LocationDataSource(this._dio);

  final Dio _dio;

  Future<List<LocationModel>> getLocations(String locale) async {
    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      ApiConstants.cities,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data as List<dynamic>;
      final locations = data.map((json) => LocationModel.fromJson(json)).toList()

        // Sort by city name
        ..sort((a, b) => a.cityName.compareTo(b.cityName));

      return locations;
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
