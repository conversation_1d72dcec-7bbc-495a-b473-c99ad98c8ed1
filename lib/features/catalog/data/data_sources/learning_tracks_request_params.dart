import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

class LearningTracksRequestParams {
  const LearningTracksRequestParams({
    required this.page,
    required this.size,
    this.sortBy = SortOptionsEnum.enrolledCount,
    this.skillLevels,
    this.sectors,
    this.domains = const [],
    this.trainingProviders,
    this.forEmployer = false,
    this.title = '',
    this.duration,
    this.level,
    this.types,
    this.languages,
    this.locations,
    this.studyStreamTypes,
    this.filterModel = const FilterModel(),
  });

  const LearningTracksRequestParams.fallback() : this(page: 0, size: 10);
  final int page;
  final int size;
  final SortOptionsEnum sortBy;
  final String? skillLevels;
  final String? sectors;
  final List<String> domains;
  final String? trainingProviders;
  final String title;
  final String? duration;
  final String? level;
  final String? languages;
  final String? types;
  final String? locations;
  final bool? forEmployer;
  final String? studyStreamTypes;
  final FilterModel filterModel;

  // Helper to combine parameters
  Map<String, String> getParams() {
    return {
      'page': '$page',
      'size': '$size',
      'sort': sortBy.name,
      if (skillLevels != null) 'skillLevels': '$skillLevels',
      if (sectors != null) 'sectors': '$sectors',
      if (domains.isNotEmpty)
        for (int i = 0; i < domains.length; i++) 'domains': domains[i],
      if (trainingProviders != null) 'trainingProviders': '$trainingProviders',
      if (title.isNotEmpty) 'title': title,
      if (duration != null) 'durations': '$duration',
      if (level != null) 'levels': '$level',
      if (types != null) 'types': '$types',
      if (languages != null) 'languages': '$languages',
      if (locations != null) 'locations': '$locations',
      if (studyStreamTypes != null) 'studyStreamTypes': '$studyStreamTypes',
      'forEmployer': '$forEmployer',
    };
  }

  LearningTracksRequestParams copyWith({
    int? page,
    int? size,
    SortOptionsEnum? sortBy,
    String? skillLevels,
    String? sectors,
    List<String>? domains,
    String? trainingProviders,
    String? title,
    String? duration,
    String? level,
    String? types,
    String? languages,
    String? locations,
    bool? forEmployer,
    String? studyStreamTypes,
    FilterModel? filterModel,
  }) {
    return LearningTracksRequestParams(
      page: page ?? this.page,
      size: size ?? this.size,
      sortBy: sortBy ?? this.sortBy,
      skillLevels: skillLevels ?? this.skillLevels,
      types: types ?? this.types,
      sectors: sectors ?? this.sectors,
      domains: domains ?? this.domains,
      trainingProviders: trainingProviders ?? this.trainingProviders,
      title: title ?? this.title,
      duration: duration ?? this.duration,
      level: level ?? this.level,
      languages: languages ?? this.languages,
      locations: locations ?? this.locations,
      forEmployer: forEmployer ?? this.forEmployer,
      studyStreamTypes: studyStreamTypes ?? this.studyStreamTypes,
      filterModel: filterModel ?? this.filterModel,
    );
  }
}
