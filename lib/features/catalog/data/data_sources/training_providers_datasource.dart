import 'dart:async';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';

@injectable
class TrainingProvidersDataSource {
  const TrainingProvidersDataSource(this._dio);

  final Dio _dio;

  Future<List<TrainingProviderModel>> getTrainingProviders(String locale) async {
    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      ApiConstants.organizations,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data as List<dynamic>;
      return data.map((json) => TrainingProviderModel.fromJson(json)).toList();
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
