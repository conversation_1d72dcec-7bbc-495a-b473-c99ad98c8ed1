import 'dart:async';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';

@injectable
class LearningTracksDataSource {
  const LearningTracksDataSource({required Dio dio}) : _dio = dio;
  final Dio _dio;

  Future<LearningTracksModel> getLearningTracks(LearningTracksRequestParams requestPrams) async {
    final response = await _dio.get(
      ApiConstants.learningTracksCataloguePath,
      queryParameters: requestPrams.getParams(),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return LearningTracksModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
