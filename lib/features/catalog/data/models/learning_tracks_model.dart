import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'learning_tracks_model.g.dart';

@JsonSerializable()
class LearningTracksModel extends Equatable {
  const LearningTracksModel({
    required this.learningTracksList,
    required this.page,
    required this.size,
    required this.totalPages,
    required this.totalElements,
    this.numberOfSuggestions = 0,
  });

  factory LearningTracksModel.fromJson(Map<String, dynamic> json) =>
      _$LearningTracksModelFromJson(json);
  @J<PERSON><PERSON><PERSON>(name: 'content')
  final List<LearningTracksContentModel> learningTracksList;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'page', includeToJson: false)
  final int? page;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'size', includeToJson: false)
  final int? size;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalRecords', includeToJson: false, defaultValue: 0)
  final int totalElements;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'totalPages', includeToJson: false)
  final int? totalPages;
  @J<PERSON><PERSON><PERSON>(includeFromJson: false, includeToJson: false)
  final int numberOfSuggestions; //number of returned elements from "suggestions" api

  LearningTracksModel copyWith({
    List<LearningTracksContentModel>? learningTracksList,
    int? page,
    int? size,
    int? totalElements,
    int? totalPages,
    int? numberOfSuggestions,
  }) =>
      LearningTracksModel(
        learningTracksList: learningTracksList ?? this.learningTracksList,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
        totalPages: totalPages ?? this.totalPages,
        numberOfSuggestions: numberOfSuggestions ?? this.numberOfSuggestions,
      );

  @override
  List<Object?> get props => [learningTracksList, page, size, totalPages, totalElements];
}

@JsonSerializable()
class LearningTracksContentModel extends Equatable {
  const LearningTracksContentModel({
    required this.createdDate,
    required this.description,
    required this.domainId,
    required this.forNominationOnly,
    required this.id,
    required this.languageCode,
    required this.level,
    required this.profileImage,
    required this.profileImageUrl,
    required this.sectorId,
    required this.status,
    required this.title,
    required this.trainingCount,
    required this.trainingProviderName,
    required this.organizationName,
    this.trainingIds = const [],
  });

  factory LearningTracksContentModel.fromJson(Map<String, dynamic> json) =>
      _$LearningTracksContentModelFromJson(json);
  @JsonKey(name: 'id')
  final String id;
  @JsonKey(name: 'title')
  final String? title;
  @JsonKey(name: 'trainingCount')
  final int? trainingCount;
  @JsonKey(name: 'status')
  final String? status;
  @JsonKey(name: 'createdDate')
  final DateTime? createdDate;
  @JsonKey(name: 'profileImage')
  final ProfileImage? profileImage;
  @JsonKey(name: 'profileImageUrl')
  final String? profileImageUrl;
  @JsonKey(name: 'sectorId')
  final String? sectorId;
  @JsonKey(name: 'domainId')
  final String? domainId;
  @JsonKey(name: 'languageCode')
  final String? languageCode;
  @JsonKey(name: 'trainingIds')
  final List<String>? trainingIds;
  @JsonKey(name: 'description')
  final String? description;
  @JsonKey(name: 'level')
  final String? level;
  @JsonKey(name: 'forNominationOnly')
  final bool? forNominationOnly;
  @JsonKey(name: 'trainingProviderName')
  final String? trainingProviderName;
  @JsonKey(defaultValue: '')
  final String organizationName;

  @override
  List<Object?> get props => [
        createdDate,
        description,
        domainId,
        forNominationOnly,
        id,
        languageCode,
        level,
        profileImage,
        profileImageUrl,
        sectorId,
        status,
        title,
        trainingCount,
        trainingIds,
        trainingProviderName,
        organizationName,
      ];
}

@JsonSerializable()
class ProfileImage {
  const ProfileImage({required this.key, required this.originalFilename, required this.size});

  factory ProfileImage.fromJson(Map<String, dynamic> json) => _$ProfileImageFromJson(json);
  @JsonKey(name: 'originalFilename')
  final String? originalFilename;
  @JsonKey(name: 'key')
  final String? key;
  @JsonKey(name: 'size')
  final int? size;
}
