import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/core/shared/converters/string_converters.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

part 'trainings_model.freezed.dart';

part 'trainings_model.g.dart';

@freezed
abstract class TrainingsModel with _$TrainingsModel {
  const factory TrainingsModel({
    @JsonKey(name: 'content') required List<TrainingContentModel> trainingsList,
    @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0)
    required int totalElements,
    @JsonKey(includeToJson: false) int? page,
    @JsonKey(includeToJson: false) int? size,
    @JsonKey(includeToJson: false) int? totalPages,
    @JsonKey(includeFromJson: false, includeToJson: false)
    @Default(0)
    int numberOfSuggestions, //number of returned elements from "suggestions" api
  }) = _TrainingsModel;

  factory TrainingsModel.fromJson(Map<String, dynamic> json) => _$TrainingsModelFromJson(json);
}

@freezed
abstract class TrainingContentModel with _$TrainingContentModel {
  const factory TrainingContentModel({
    required String id,
    String? title,
    @JsonKey(fromJson: fromJsonToDuration) String? duration,
    ImageModel? profileImage,
    String? profileImageUrl,
    @JsonKey(fromJson: languageCodeToText) String? language,
    @JsonKey(fromJson: levelEnumToText) String? level,
    String? status,
    @JsonKey(defaultValue: []) required List<String> skills,
    required DateTime? createdDate,
    required DateTime? lastModifiedDate,
    @JsonKey(defaultValue: 0) required int enrolledCount,
    @JsonKey(defaultValue: '') required String organizationName,
    @JsonKey(defaultValue: '') String? avatarUrl,
    @JsonKey(fromJson: stringToTrainingType) TrainingType? trainingType,
    @JsonKey(defaultValue: false) required bool hidden,
    @JsonKey(defaultValue: false) required bool hasFutureOnline,
    @JsonKey(defaultValue: false) required bool hasFutureInPerson,
    @JsonKey(defaultValue: []) required List<String>? cities,
    DateTime? nearestOpenForEnrollmentStreamDate,
  }) = _TrainingContentModel;

  factory TrainingContentModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingContentModelFromJson(json);
}
