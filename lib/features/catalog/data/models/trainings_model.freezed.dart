// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'trainings_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingsModel {
  @JsonKey(name: 'content')
  List<TrainingContentModel> get trainingsList;
  @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0)
  int get totalElements;
  @JsonKey(includeToJson: false)
  int? get page;
  @JsonKey(includeToJson: false)
  int? get size;
  @JsonKey(includeToJson: false)
  int? get totalPages;
  @JsonKey(includeFromJson: false, includeToJson: false)
  int get numberOfSuggestions;

  /// Create a copy of TrainingsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingsModelCopyWith<TrainingsModel> get copyWith =>
      _$TrainingsModelCopyWithImpl<TrainingsModel>(this as TrainingsModel, _$identity);

  /// Serializes this TrainingsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingsModel &&
            const DeepCollectionEquality().equals(other.trainingsList, trainingsList) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.totalPages, totalPages) || other.totalPages == totalPages) &&
            (identical(other.numberOfSuggestions, numberOfSuggestions) ||
                other.numberOfSuggestions == numberOfSuggestions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(trainingsList),
      totalElements, page, size, totalPages, numberOfSuggestions);

  @override
  String toString() {
    return 'TrainingsModel(trainingsList: $trainingsList, totalElements: $totalElements, page: $page, size: $size, totalPages: $totalPages, numberOfSuggestions: $numberOfSuggestions)';
  }
}

/// @nodoc
abstract mixin class $TrainingsModelCopyWith<$Res> {
  factory $TrainingsModelCopyWith(TrainingsModel value, $Res Function(TrainingsModel) _then) =
      _$TrainingsModelCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'content') List<TrainingContentModel> trainingsList,
      @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0) int totalElements,
      @JsonKey(includeToJson: false) int? page,
      @JsonKey(includeToJson: false) int? size,
      @JsonKey(includeToJson: false) int? totalPages,
      @JsonKey(includeFromJson: false, includeToJson: false) int numberOfSuggestions});
}

/// @nodoc
class _$TrainingsModelCopyWithImpl<$Res> implements $TrainingsModelCopyWith<$Res> {
  _$TrainingsModelCopyWithImpl(this._self, this._then);

  final TrainingsModel _self;
  final $Res Function(TrainingsModel) _then;

  /// Create a copy of TrainingsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trainingsList = null,
    Object? totalElements = null,
    Object? page = freezed,
    Object? size = freezed,
    Object? totalPages = freezed,
    Object? numberOfSuggestions = null,
  }) {
    return _then(_self.copyWith(
      trainingsList: null == trainingsList
          ? _self.trainingsList
          : trainingsList // ignore: cast_nullable_to_non_nullable
              as List<TrainingContentModel>,
      totalElements: null == totalElements
          ? _self.totalElements
          : totalElements // ignore: cast_nullable_to_non_nullable
              as int,
      page: freezed == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPages: freezed == totalPages
          ? _self.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      numberOfSuggestions: null == numberOfSuggestions
          ? _self.numberOfSuggestions
          : numberOfSuggestions // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingsModel implements TrainingsModel {
  const _TrainingsModel(
      {@JsonKey(name: 'content') required final List<TrainingContentModel> trainingsList,
      @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0)
      required this.totalElements,
      @JsonKey(includeToJson: false) this.page,
      @JsonKey(includeToJson: false) this.size,
      @JsonKey(includeToJson: false) this.totalPages,
      @JsonKey(includeFromJson: false, includeToJson: false) this.numberOfSuggestions = 0})
      : _trainingsList = trainingsList;
  factory _TrainingsModel.fromJson(Map<String, dynamic> json) => _$TrainingsModelFromJson(json);

  final List<TrainingContentModel> _trainingsList;
  @override
  @JsonKey(name: 'content')
  List<TrainingContentModel> get trainingsList {
    if (_trainingsList is EqualUnmodifiableListView) return _trainingsList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trainingsList);
  }

  @override
  @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0)
  final int totalElements;
  @override
  @JsonKey(includeToJson: false)
  final int? page;
  @override
  @JsonKey(includeToJson: false)
  final int? size;
  @override
  @JsonKey(includeToJson: false)
  final int? totalPages;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final int numberOfSuggestions;

  /// Create a copy of TrainingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingsModelCopyWith<_TrainingsModel> get copyWith =>
      __$TrainingsModelCopyWithImpl<_TrainingsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingsModel &&
            const DeepCollectionEquality().equals(other._trainingsList, _trainingsList) &&
            (identical(other.totalElements, totalElements) ||
                other.totalElements == totalElements) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.totalPages, totalPages) || other.totalPages == totalPages) &&
            (identical(other.numberOfSuggestions, numberOfSuggestions) ||
                other.numberOfSuggestions == numberOfSuggestions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(_trainingsList),
      totalElements, page, size, totalPages, numberOfSuggestions);

  @override
  String toString() {
    return 'TrainingsModel(trainingsList: $trainingsList, totalElements: $totalElements, page: $page, size: $size, totalPages: $totalPages, numberOfSuggestions: $numberOfSuggestions)';
  }
}

/// @nodoc
abstract mixin class _$TrainingsModelCopyWith<$Res> implements $TrainingsModelCopyWith<$Res> {
  factory _$TrainingsModelCopyWith(_TrainingsModel value, $Res Function(_TrainingsModel) _then) =
      __$TrainingsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'content') List<TrainingContentModel> trainingsList,
      @JsonKey(name: 'totalRecords', includeToJson: false, defaultValue: 0) int totalElements,
      @JsonKey(includeToJson: false) int? page,
      @JsonKey(includeToJson: false) int? size,
      @JsonKey(includeToJson: false) int? totalPages,
      @JsonKey(includeFromJson: false, includeToJson: false) int numberOfSuggestions});
}

/// @nodoc
class __$TrainingsModelCopyWithImpl<$Res> implements _$TrainingsModelCopyWith<$Res> {
  __$TrainingsModelCopyWithImpl(this._self, this._then);

  final _TrainingsModel _self;
  final $Res Function(_TrainingsModel) _then;

  /// Create a copy of TrainingsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? trainingsList = null,
    Object? totalElements = null,
    Object? page = freezed,
    Object? size = freezed,
    Object? totalPages = freezed,
    Object? numberOfSuggestions = null,
  }) {
    return _then(_TrainingsModel(
      trainingsList: null == trainingsList
          ? _self._trainingsList
          : trainingsList // ignore: cast_nullable_to_non_nullable
              as List<TrainingContentModel>,
      totalElements: null == totalElements
          ? _self.totalElements
          : totalElements // ignore: cast_nullable_to_non_nullable
              as int,
      page: freezed == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      size: freezed == size
          ? _self.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      totalPages: freezed == totalPages
          ? _self.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int?,
      numberOfSuggestions: null == numberOfSuggestions
          ? _self.numberOfSuggestions
          : numberOfSuggestions // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$TrainingContentModel {
  String get id;
  String? get title;
  @JsonKey(fromJson: fromJsonToDuration)
  String? get duration;
  ImageModel? get profileImage;
  String? get profileImageUrl;
  @JsonKey(fromJson: languageCodeToText)
  String? get language;
  @JsonKey(fromJson: levelEnumToText)
  String? get level;
  String? get status;
  @JsonKey(defaultValue: [])
  List<String> get skills;
  DateTime? get createdDate;
  DateTime? get lastModifiedDate;
  @JsonKey(defaultValue: 0)
  int get enrolledCount;
  @JsonKey(defaultValue: '')
  String get organizationName;
  @JsonKey(defaultValue: '')
  String? get avatarUrl;
  @JsonKey(fromJson: stringToTrainingType)
  TrainingType? get trainingType;
  @JsonKey(defaultValue: false)
  bool get hidden;
  @JsonKey(defaultValue: false)
  bool get hasFutureOnline;
  @JsonKey(defaultValue: false)
  bool get hasFutureInPerson;
  @JsonKey(defaultValue: [])
  List<String>? get cities;
  DateTime? get nearestOpenForEnrollmentStreamDate;

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingContentModelCopyWith<TrainingContentModel> get copyWith =>
      _$TrainingContentModelCopyWithImpl<TrainingContentModel>(
          this as TrainingContentModel, _$identity);

  /// Serializes this TrainingContentModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingContentModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.duration, duration) || other.duration == duration) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.language, language) || other.language == language) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other.skills, skills) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.enrolledCount, enrolledCount) ||
                other.enrolledCount == enrolledCount) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl) &&
            (identical(other.trainingType, trainingType) || other.trainingType == trainingType) &&
            (identical(other.hidden, hidden) || other.hidden == hidden) &&
            (identical(other.hasFutureOnline, hasFutureOnline) ||
                other.hasFutureOnline == hasFutureOnline) &&
            (identical(other.hasFutureInPerson, hasFutureInPerson) ||
                other.hasFutureInPerson == hasFutureInPerson) &&
            const DeepCollectionEquality().equals(other.cities, cities) &&
            (identical(
                    other.nearestOpenForEnrollmentStreamDate, nearestOpenForEnrollmentStreamDate) ||
                other.nearestOpenForEnrollmentStreamDate == nearestOpenForEnrollmentStreamDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        duration,
        profileImage,
        profileImageUrl,
        language,
        level,
        status,
        const DeepCollectionEquality().hash(skills),
        createdDate,
        lastModifiedDate,
        enrolledCount,
        organizationName,
        avatarUrl,
        trainingType,
        hidden,
        hasFutureOnline,
        hasFutureInPerson,
        const DeepCollectionEquality().hash(cities),
        nearestOpenForEnrollmentStreamDate
      ]);

  @override
  String toString() {
    return 'TrainingContentModel(id: $id, title: $title, duration: $duration, profileImage: $profileImage, profileImageUrl: $profileImageUrl, language: $language, level: $level, status: $status, skills: $skills, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate, enrolledCount: $enrolledCount, organizationName: $organizationName, avatarUrl: $avatarUrl, trainingType: $trainingType, hidden: $hidden, hasFutureOnline: $hasFutureOnline, hasFutureInPerson: $hasFutureInPerson, cities: $cities, nearestOpenForEnrollmentStreamDate: $nearestOpenForEnrollmentStreamDate)';
  }
}

/// @nodoc
abstract mixin class $TrainingContentModelCopyWith<$Res> {
  factory $TrainingContentModelCopyWith(
          TrainingContentModel value, $Res Function(TrainingContentModel) _then) =
      _$TrainingContentModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? title,
      @JsonKey(fromJson: fromJsonToDuration) String? duration,
      ImageModel? profileImage,
      String? profileImageUrl,
      @JsonKey(fromJson: languageCodeToText) String? language,
      @JsonKey(fromJson: levelEnumToText) String? level,
      String? status,
      @JsonKey(defaultValue: []) List<String> skills,
      DateTime? createdDate,
      DateTime? lastModifiedDate,
      @JsonKey(defaultValue: 0) int enrolledCount,
      @JsonKey(defaultValue: '') String organizationName,
      @JsonKey(defaultValue: '') String? avatarUrl,
      @JsonKey(fromJson: stringToTrainingType) TrainingType? trainingType,
      @JsonKey(defaultValue: false) bool hidden,
      @JsonKey(defaultValue: false) bool hasFutureOnline,
      @JsonKey(defaultValue: false) bool hasFutureInPerson,
      @JsonKey(defaultValue: []) List<String>? cities,
      DateTime? nearestOpenForEnrollmentStreamDate});

  $ImageModelCopyWith<$Res>? get profileImage;
}

/// @nodoc
class _$TrainingContentModelCopyWithImpl<$Res> implements $TrainingContentModelCopyWith<$Res> {
  _$TrainingContentModelCopyWithImpl(this._self, this._then);

  final TrainingContentModel _self;
  final $Res Function(TrainingContentModel) _then;

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? duration = freezed,
    Object? profileImage = freezed,
    Object? profileImageUrl = freezed,
    Object? language = freezed,
    Object? level = freezed,
    Object? status = freezed,
    Object? skills = null,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
    Object? enrolledCount = null,
    Object? organizationName = null,
    Object? avatarUrl = freezed,
    Object? trainingType = freezed,
    Object? hidden = null,
    Object? hasFutureOnline = null,
    Object? hasFutureInPerson = null,
    Object? cities = freezed,
    Object? nearestOpenForEnrollmentStreamDate = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: freezed == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      skills: null == skills
          ? _self.skills
          : skills // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      enrolledCount: null == enrolledCount
          ? _self.enrolledCount
          : enrolledCount // ignore: cast_nullable_to_non_nullable
              as int,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      avatarUrl: freezed == avatarUrl
          ? _self.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingType: freezed == trainingType
          ? _self.trainingType
          : trainingType // ignore: cast_nullable_to_non_nullable
              as TrainingType?,
      hidden: null == hidden
          ? _self.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureOnline: null == hasFutureOnline
          ? _self.hasFutureOnline
          : hasFutureOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureInPerson: null == hasFutureInPerson
          ? _self.hasFutureInPerson
          : hasFutureInPerson // ignore: cast_nullable_to_non_nullable
              as bool,
      cities: freezed == cities
          ? _self.cities
          : cities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nearestOpenForEnrollmentStreamDate: freezed == nearestOpenForEnrollmentStreamDate
          ? _self.nearestOpenForEnrollmentStreamDate
          : nearestOpenForEnrollmentStreamDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingContentModel implements TrainingContentModel {
  const _TrainingContentModel(
      {required this.id,
      this.title,
      @JsonKey(fromJson: fromJsonToDuration) this.duration,
      this.profileImage,
      this.profileImageUrl,
      @JsonKey(fromJson: languageCodeToText) this.language,
      @JsonKey(fromJson: levelEnumToText) this.level,
      this.status,
      @JsonKey(defaultValue: []) required final List<String> skills,
      required this.createdDate,
      required this.lastModifiedDate,
      @JsonKey(defaultValue: 0) required this.enrolledCount,
      @JsonKey(defaultValue: '') required this.organizationName,
      @JsonKey(defaultValue: '') this.avatarUrl,
      @JsonKey(fromJson: stringToTrainingType) this.trainingType,
      @JsonKey(defaultValue: false) required this.hidden,
      @JsonKey(defaultValue: false) required this.hasFutureOnline,
      @JsonKey(defaultValue: false) required this.hasFutureInPerson,
      @JsonKey(defaultValue: []) required final List<String>? cities,
      this.nearestOpenForEnrollmentStreamDate})
      : _skills = skills,
        _cities = cities;
  factory _TrainingContentModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingContentModelFromJson(json);

  @override
  final String id;
  @override
  final String? title;
  @override
  @JsonKey(fromJson: fromJsonToDuration)
  final String? duration;
  @override
  final ImageModel? profileImage;
  @override
  final String? profileImageUrl;
  @override
  @JsonKey(fromJson: languageCodeToText)
  final String? language;
  @override
  @JsonKey(fromJson: levelEnumToText)
  final String? level;
  @override
  final String? status;
  final List<String> _skills;
  @override
  @JsonKey(defaultValue: [])
  List<String> get skills {
    if (_skills is EqualUnmodifiableListView) return _skills;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_skills);
  }

  @override
  final DateTime? createdDate;
  @override
  final DateTime? lastModifiedDate;
  @override
  @JsonKey(defaultValue: 0)
  final int enrolledCount;
  @override
  @JsonKey(defaultValue: '')
  final String organizationName;
  @override
  @JsonKey(defaultValue: '')
  final String? avatarUrl;
  @override
  @JsonKey(fromJson: stringToTrainingType)
  final TrainingType? trainingType;
  @override
  @JsonKey(defaultValue: false)
  final bool hidden;
  @override
  @JsonKey(defaultValue: false)
  final bool hasFutureOnline;
  @override
  @JsonKey(defaultValue: false)
  final bool hasFutureInPerson;
  final List<String>? _cities;
  @override
  @JsonKey(defaultValue: [])
  List<String>? get cities {
    final value = _cities;
    if (value == null) return null;
    if (_cities is EqualUnmodifiableListView) return _cities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final DateTime? nearestOpenForEnrollmentStreamDate;

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingContentModelCopyWith<_TrainingContentModel> get copyWith =>
      __$TrainingContentModelCopyWithImpl<_TrainingContentModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingContentModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingContentModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.duration, duration) || other.duration == duration) &&
            (identical(other.profileImage, profileImage) || other.profileImage == profileImage) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.language, language) || other.language == language) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality().equals(other._skills, _skills) &&
            (identical(other.createdDate, createdDate) || other.createdDate == createdDate) &&
            (identical(other.lastModifiedDate, lastModifiedDate) ||
                other.lastModifiedDate == lastModifiedDate) &&
            (identical(other.enrolledCount, enrolledCount) ||
                other.enrolledCount == enrolledCount) &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl) &&
            (identical(other.trainingType, trainingType) || other.trainingType == trainingType) &&
            (identical(other.hidden, hidden) || other.hidden == hidden) &&
            (identical(other.hasFutureOnline, hasFutureOnline) ||
                other.hasFutureOnline == hasFutureOnline) &&
            (identical(other.hasFutureInPerson, hasFutureInPerson) ||
                other.hasFutureInPerson == hasFutureInPerson) &&
            const DeepCollectionEquality().equals(other._cities, _cities) &&
            (identical(
                    other.nearestOpenForEnrollmentStreamDate, nearestOpenForEnrollmentStreamDate) ||
                other.nearestOpenForEnrollmentStreamDate == nearestOpenForEnrollmentStreamDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        duration,
        profileImage,
        profileImageUrl,
        language,
        level,
        status,
        const DeepCollectionEquality().hash(_skills),
        createdDate,
        lastModifiedDate,
        enrolledCount,
        organizationName,
        avatarUrl,
        trainingType,
        hidden,
        hasFutureOnline,
        hasFutureInPerson,
        const DeepCollectionEquality().hash(_cities),
        nearestOpenForEnrollmentStreamDate
      ]);

  @override
  String toString() {
    return 'TrainingContentModel(id: $id, title: $title, duration: $duration, profileImage: $profileImage, profileImageUrl: $profileImageUrl, language: $language, level: $level, status: $status, skills: $skills, createdDate: $createdDate, lastModifiedDate: $lastModifiedDate, enrolledCount: $enrolledCount, organizationName: $organizationName, avatarUrl: $avatarUrl, trainingType: $trainingType, hidden: $hidden, hasFutureOnline: $hasFutureOnline, hasFutureInPerson: $hasFutureInPerson, cities: $cities, nearestOpenForEnrollmentStreamDate: $nearestOpenForEnrollmentStreamDate)';
  }
}

/// @nodoc
abstract mixin class _$TrainingContentModelCopyWith<$Res>
    implements $TrainingContentModelCopyWith<$Res> {
  factory _$TrainingContentModelCopyWith(
          _TrainingContentModel value, $Res Function(_TrainingContentModel) _then) =
      __$TrainingContentModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? title,
      @JsonKey(fromJson: fromJsonToDuration) String? duration,
      ImageModel? profileImage,
      String? profileImageUrl,
      @JsonKey(fromJson: languageCodeToText) String? language,
      @JsonKey(fromJson: levelEnumToText) String? level,
      String? status,
      @JsonKey(defaultValue: []) List<String> skills,
      DateTime? createdDate,
      DateTime? lastModifiedDate,
      @JsonKey(defaultValue: 0) int enrolledCount,
      @JsonKey(defaultValue: '') String organizationName,
      @JsonKey(defaultValue: '') String? avatarUrl,
      @JsonKey(fromJson: stringToTrainingType) TrainingType? trainingType,
      @JsonKey(defaultValue: false) bool hidden,
      @JsonKey(defaultValue: false) bool hasFutureOnline,
      @JsonKey(defaultValue: false) bool hasFutureInPerson,
      @JsonKey(defaultValue: []) List<String>? cities,
      DateTime? nearestOpenForEnrollmentStreamDate});

  @override
  $ImageModelCopyWith<$Res>? get profileImage;
}

/// @nodoc
class __$TrainingContentModelCopyWithImpl<$Res> implements _$TrainingContentModelCopyWith<$Res> {
  __$TrainingContentModelCopyWithImpl(this._self, this._then);

  final _TrainingContentModel _self;
  final $Res Function(_TrainingContentModel) _then;

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = freezed,
    Object? duration = freezed,
    Object? profileImage = freezed,
    Object? profileImageUrl = freezed,
    Object? language = freezed,
    Object? level = freezed,
    Object? status = freezed,
    Object? skills = null,
    Object? createdDate = freezed,
    Object? lastModifiedDate = freezed,
    Object? enrolledCount = null,
    Object? organizationName = null,
    Object? avatarUrl = freezed,
    Object? trainingType = freezed,
    Object? hidden = null,
    Object? hasFutureOnline = null,
    Object? hasFutureInPerson = null,
    Object? cities = freezed,
    Object? nearestOpenForEnrollmentStreamDate = freezed,
  }) {
    return _then(_TrainingContentModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _self.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as ImageModel?,
      profileImageUrl: freezed == profileImageUrl
          ? _self.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      language: freezed == language
          ? _self.language
          : language // ignore: cast_nullable_to_non_nullable
              as String?,
      level: freezed == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      skills: null == skills
          ? _self._skills
          : skills // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdDate: freezed == createdDate
          ? _self.createdDate
          : createdDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastModifiedDate: freezed == lastModifiedDate
          ? _self.lastModifiedDate
          : lastModifiedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      enrolledCount: null == enrolledCount
          ? _self.enrolledCount
          : enrolledCount // ignore: cast_nullable_to_non_nullable
              as int,
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      avatarUrl: freezed == avatarUrl
          ? _self.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      trainingType: freezed == trainingType
          ? _self.trainingType
          : trainingType // ignore: cast_nullable_to_non_nullable
              as TrainingType?,
      hidden: null == hidden
          ? _self.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureOnline: null == hasFutureOnline
          ? _self.hasFutureOnline
          : hasFutureOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      hasFutureInPerson: null == hasFutureInPerson
          ? _self.hasFutureInPerson
          : hasFutureInPerson // ignore: cast_nullable_to_non_nullable
              as bool,
      cities: freezed == cities
          ? _self._cities
          : cities // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nearestOpenForEnrollmentStreamDate: freezed == nearestOpenForEnrollmentStreamDate
          ? _self.nearestOpenForEnrollmentStreamDate
          : nearestOpenForEnrollmentStreamDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }

  /// Create a copy of TrainingContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ImageModelCopyWith<$Res>? get profileImage {
    if (_self.profileImage == null) {
      return null;
    }

    return $ImageModelCopyWith<$Res>(_self.profileImage!, (value) {
      return _then(_self.copyWith(profileImage: value));
    });
  }
}

// dart format on
