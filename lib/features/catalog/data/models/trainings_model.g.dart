// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trainings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TrainingsModel _$TrainingsModelFromJson(Map<String, dynamic> json) => _TrainingsModel(
      trainingsList: (json['content'] as List<dynamic>)
          .map((e) => TrainingContentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalElements: (json['totalRecords'] as num?)?.toInt() ?? 0,
      page: (json['page'] as num?)?.toInt(),
      size: (json['size'] as num?)?.toInt(),
      totalPages: (json['totalPages'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TrainingsModelToJson(_TrainingsModel instance) => <String, dynamic>{
      'content': instance.trainingsList,
    };

_TrainingContentModel _$TrainingContentModelFromJson(Map<String, dynamic> json) =>
    _TrainingContentModel(
      id: json['id'] as String,
      title: json['title'] as String?,
      duration: fromJsonToDuration(json['duration'] as Object),
      profileImage: json['profileImage'] == null
          ? null
          : ImageModel.fromJson(json['profileImage'] as Map<String, dynamic>),
      profileImageUrl: json['profileImageUrl'] as String?,
      language: languageCodeToText(json['language'] as String),
      level: levelEnumToText(json['level'] as String),
      status: json['status'] as String?,
      skills: (json['skills'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      createdDate:
          json['createdDate'] == null ? null : DateTime.parse(json['createdDate'] as String),
      lastModifiedDate: json['lastModifiedDate'] == null
          ? null
          : DateTime.parse(json['lastModifiedDate'] as String),
      enrolledCount: (json['enrolledCount'] as num?)?.toInt() ?? 0,
      organizationName: json['organizationName'] as String? ?? '',
      avatarUrl: json['avatarUrl'] as String? ?? '',
      trainingType: stringToTrainingType(json['trainingType'] as String?),
      hidden: json['hidden'] as bool? ?? false,
      hasFutureOnline: json['hasFutureOnline'] as bool? ?? false,
      hasFutureInPerson: json['hasFutureInPerson'] as bool? ?? false,
      cities: (json['cities'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
      nearestOpenForEnrollmentStreamDate: json['nearestOpenForEnrollmentStreamDate'] == null
          ? null
          : DateTime.parse(json['nearestOpenForEnrollmentStreamDate'] as String),
    );

Map<String, dynamic> _$TrainingContentModelToJson(_TrainingContentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'duration': instance.duration,
      'profileImage': instance.profileImage,
      'profileImageUrl': instance.profileImageUrl,
      'language': instance.language,
      'level': instance.level,
      'status': instance.status,
      'skills': instance.skills,
      'createdDate': instance.createdDate?.toIso8601String(),
      'lastModifiedDate': instance.lastModifiedDate?.toIso8601String(),
      'enrolledCount': instance.enrolledCount,
      'organizationName': instance.organizationName,
      'avatarUrl': instance.avatarUrl,
      'trainingType': _$TrainingTypeEnumMap[instance.trainingType],
      'hidden': instance.hidden,
      'hasFutureOnline': instance.hasFutureOnline,
      'hasFutureInPerson': instance.hasFutureInPerson,
      'cities': instance.cities,
      'nearestOpenForEnrollmentStreamDate':
          instance.nearestOpenForEnrollmentStreamDate?.toIso8601String(),
    };

const _$TrainingTypeEnumMap = {
  TrainingType.SelfPaced: 'SelfPaced',
  TrainingType.InstructorLed: 'InstructorLed',
  TrainingType.none: 'none',
};
