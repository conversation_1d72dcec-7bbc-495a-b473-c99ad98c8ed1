// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_tracks_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LearningTracksModel _$LearningTracksModelFromJson(Map<String, dynamic> json) => LearningTracksModel(
      learningTracksList: (json['content'] as List<dynamic>)
          .map((e) => LearningTracksContentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num?)?.toInt(),
      size: (json['size'] as num?)?.toInt(),
      totalPages: (json['totalPages'] as num?)?.toInt(),
      totalElements: (json['totalRecords'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$LearningTracksModelToJson(LearningTracksModel instance) => <String, dynamic>{
      'content': instance.learningTracksList,
    };

LearningTracksContentModel _$LearningTracksContentModelFromJson(Map<String, dynamic> json) =>
    LearningTracksContentModel(
      createdDate:
          json['createdDate'] == null ? null : DateTime.parse(json['createdDate'] as String),
      description: json['description'] as String?,
      domainId: json['domainId'] as String?,
      forNominationOnly: json['forNominationOnly'] as bool?,
      id: json['id'] as String,
      languageCode: json['languageCode'] as String?,
      level: json['level'] as String?,
      profileImage: json['profileImage'] == null
          ? null
          : ProfileImage.fromJson(json['profileImage'] as Map<String, dynamic>),
      profileImageUrl: json['profileImageUrl'] as String?,
      sectorId: json['sectorId'] as String?,
      status: json['status'] as String?,
      title: json['title'] as String?,
      trainingCount: (json['trainingCount'] as num?)?.toInt(),
      trainingProviderName: json['trainingProviderName'] as String?,
      organizationName: json['organizationName'] as String? ?? '',
      trainingIds:
          (json['trainingIds'] as List<dynamic>?)?.map((e) => e as String).toList() ?? const [],
    );

Map<String, dynamic> _$LearningTracksContentModelToJson(LearningTracksContentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'trainingCount': instance.trainingCount,
      'status': instance.status,
      'createdDate': instance.createdDate?.toIso8601String(),
      'profileImage': instance.profileImage,
      'profileImageUrl': instance.profileImageUrl,
      'sectorId': instance.sectorId,
      'domainId': instance.domainId,
      'languageCode': instance.languageCode,
      'trainingIds': instance.trainingIds,
      'description': instance.description,
      'level': instance.level,
      'forNominationOnly': instance.forNominationOnly,
      'trainingProviderName': instance.trainingProviderName,
      'organizationName': instance.organizationName,
    };

ProfileImage _$ProfileImageFromJson(Map<String, dynamic> json) => ProfileImage(
      key: json['key'] as String?,
      originalFilename: json['originalFilename'] as String?,
      size: (json['size'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProfileImageToJson(ProfileImage instance) => <String, dynamic>{
      'originalFilename': instance.originalFilename,
      'key': instance.key,
      'size': instance.size,
    };
