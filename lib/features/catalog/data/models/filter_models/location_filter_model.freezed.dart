// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationFilterModel {
  List<String> get selectedLocationIds;

  /// Create a copy of LocationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationFilterModelCopyWith<LocationFilterModel> get copyWith =>
      _$LocationFilterModelCopyWithImpl<LocationFilterModel>(
          this as LocationFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocationFilterModel &&
            const DeepCollectionEquality().equals(other.selectedLocationIds, selectedLocationIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(selectedLocationIds));

  @override
  String toString() {
    return 'LocationFilterModel(selectedLocationIds: $selectedLocationIds)';
  }
}

/// @nodoc
abstract mixin class $LocationFilterModelCopyWith<$Res> {
  factory $LocationFilterModelCopyWith(
          LocationFilterModel value, $Res Function(LocationFilterModel) _then) =
      _$LocationFilterModelCopyWithImpl;
  @useResult
  $Res call({List<String> selectedLocationIds});
}

/// @nodoc
class _$LocationFilterModelCopyWithImpl<$Res> implements $LocationFilterModelCopyWith<$Res> {
  _$LocationFilterModelCopyWithImpl(this._self, this._then);

  final LocationFilterModel _self;
  final $Res Function(LocationFilterModel) _then;

  /// Create a copy of LocationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedLocationIds = null,
  }) {
    return _then(_self.copyWith(
      selectedLocationIds: null == selectedLocationIds
          ? _self.selectedLocationIds
          : selectedLocationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _LocationFilterModel extends LocationFilterModel {
  const _LocationFilterModel({final List<String> selectedLocationIds = const []})
      : _selectedLocationIds = selectedLocationIds,
        super._();

  final List<String> _selectedLocationIds;
  @override
  @JsonKey()
  List<String> get selectedLocationIds {
    if (_selectedLocationIds is EqualUnmodifiableListView) return _selectedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedLocationIds);
  }

  /// Create a copy of LocationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationFilterModelCopyWith<_LocationFilterModel> get copyWith =>
      __$LocationFilterModelCopyWithImpl<_LocationFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationFilterModel &&
            const DeepCollectionEquality()
                .equals(other._selectedLocationIds, _selectedLocationIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_selectedLocationIds));

  @override
  String toString() {
    return 'LocationFilterModel(selectedLocationIds: $selectedLocationIds)';
  }
}

/// @nodoc
abstract mixin class _$LocationFilterModelCopyWith<$Res>
    implements $LocationFilterModelCopyWith<$Res> {
  factory _$LocationFilterModelCopyWith(
          _LocationFilterModel value, $Res Function(_LocationFilterModel) _then) =
      __$LocationFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({List<String> selectedLocationIds});
}

/// @nodoc
class __$LocationFilterModelCopyWithImpl<$Res> implements _$LocationFilterModelCopyWith<$Res> {
  __$LocationFilterModelCopyWithImpl(this._self, this._then);

  final _LocationFilterModel _self;
  final $Res Function(_LocationFilterModel) _then;

  /// Create a copy of LocationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedLocationIds = null,
  }) {
    return _then(_LocationFilterModel(
      selectedLocationIds: null == selectedLocationIds
          ? _self._selectedLocationIds
          : selectedLocationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
