// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_provider_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingProviderModel {
  String get organizationName;
  String get id;
  String? get commercialRegistration;
  DateTime? get commercialRegistrationEndDate;

  /// Create a copy of TrainingProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingProviderModelCopyWith<TrainingProviderModel> get copyWith =>
      _$TrainingProviderModelCopyWithImpl<TrainingProviderModel>(
          this as TrainingProviderModel, _$identity);

  /// Serializes this TrainingProviderModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingProviderModel &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.commercialRegistration, commercialRegistration) ||
                other.commercialRegistration == commercialRegistration) &&
            (identical(other.commercialRegistrationEndDate, commercialRegistrationEndDate) ||
                other.commercialRegistrationEndDate == commercialRegistrationEndDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, organizationName, id, commercialRegistration, commercialRegistrationEndDate);

  @override
  String toString() {
    return 'TrainingProviderModel(organizationName: $organizationName, id: $id, commercialRegistration: $commercialRegistration, commercialRegistrationEndDate: $commercialRegistrationEndDate)';
  }
}

/// @nodoc
abstract mixin class $TrainingProviderModelCopyWith<$Res> {
  factory $TrainingProviderModelCopyWith(
          TrainingProviderModel value, $Res Function(TrainingProviderModel) _then) =
      _$TrainingProviderModelCopyWithImpl;
  @useResult
  $Res call(
      {String organizationName,
      String id,
      String? commercialRegistration,
      DateTime? commercialRegistrationEndDate});
}

/// @nodoc
class _$TrainingProviderModelCopyWithImpl<$Res> implements $TrainingProviderModelCopyWith<$Res> {
  _$TrainingProviderModelCopyWithImpl(this._self, this._then);

  final TrainingProviderModel _self;
  final $Res Function(TrainingProviderModel) _then;

  /// Create a copy of TrainingProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? organizationName = null,
    Object? id = null,
    Object? commercialRegistration = freezed,
    Object? commercialRegistrationEndDate = freezed,
  }) {
    return _then(_self.copyWith(
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      commercialRegistration: freezed == commercialRegistration
          ? _self.commercialRegistration
          : commercialRegistration // ignore: cast_nullable_to_non_nullable
              as String?,
      commercialRegistrationEndDate: freezed == commercialRegistrationEndDate
          ? _self.commercialRegistrationEndDate
          : commercialRegistrationEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _TrainingProviderModel implements TrainingProviderModel {
  const _TrainingProviderModel(
      {required this.organizationName,
      required this.id,
      this.commercialRegistration,
      this.commercialRegistrationEndDate});
  factory _TrainingProviderModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingProviderModelFromJson(json);

  @override
  final String organizationName;
  @override
  final String id;
  @override
  final String? commercialRegistration;
  @override
  final DateTime? commercialRegistrationEndDate;

  /// Create a copy of TrainingProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingProviderModelCopyWith<_TrainingProviderModel> get copyWith =>
      __$TrainingProviderModelCopyWithImpl<_TrainingProviderModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TrainingProviderModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingProviderModel &&
            (identical(other.organizationName, organizationName) ||
                other.organizationName == organizationName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.commercialRegistration, commercialRegistration) ||
                other.commercialRegistration == commercialRegistration) &&
            (identical(other.commercialRegistrationEndDate, commercialRegistrationEndDate) ||
                other.commercialRegistrationEndDate == commercialRegistrationEndDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, organizationName, id, commercialRegistration, commercialRegistrationEndDate);

  @override
  String toString() {
    return 'TrainingProviderModel(organizationName: $organizationName, id: $id, commercialRegistration: $commercialRegistration, commercialRegistrationEndDate: $commercialRegistrationEndDate)';
  }
}

/// @nodoc
abstract mixin class _$TrainingProviderModelCopyWith<$Res>
    implements $TrainingProviderModelCopyWith<$Res> {
  factory _$TrainingProviderModelCopyWith(
          _TrainingProviderModel value, $Res Function(_TrainingProviderModel) _then) =
      __$TrainingProviderModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String organizationName,
      String id,
      String? commercialRegistration,
      DateTime? commercialRegistrationEndDate});
}

/// @nodoc
class __$TrainingProviderModelCopyWithImpl<$Res> implements _$TrainingProviderModelCopyWith<$Res> {
  __$TrainingProviderModelCopyWithImpl(this._self, this._then);

  final _TrainingProviderModel _self;
  final $Res Function(_TrainingProviderModel) _then;

  /// Create a copy of TrainingProviderModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? organizationName = null,
    Object? id = null,
    Object? commercialRegistration = freezed,
    Object? commercialRegistrationEndDate = freezed,
  }) {
    return _then(_TrainingProviderModel(
      organizationName: null == organizationName
          ? _self.organizationName
          : organizationName // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      commercialRegistration: freezed == commercialRegistration
          ? _self.commercialRegistration
          : commercialRegistration // ignore: cast_nullable_to_non_nullable
              as String?,
      commercialRegistrationEndDate: freezed == commercialRegistrationEndDate
          ? _self.commercialRegistrationEndDate
          : commercialRegistrationEndDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

// dart format on
