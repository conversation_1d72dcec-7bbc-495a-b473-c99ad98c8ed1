import 'package:freezed_annotation/freezed_annotation.dart';

part 'training_provider_filter_model.freezed.dart';

@freezed
abstract class TrainingProviderFilterModel with _$TrainingProviderFilterModel {
  const factory TrainingProviderFilterModel({
    @Default([]) List<String> selectedTrainingProviderIds,
  }) = _TrainingProviderFilterModel;

  const TrainingProviderFilterModel._();

  int get countSelected => selectedTrainingProviderIds.length;
}
