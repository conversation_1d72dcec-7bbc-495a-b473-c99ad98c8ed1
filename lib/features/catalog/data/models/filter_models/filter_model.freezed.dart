// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FilterModel {
//defaults to empty fields
  DurationFilterModel get durationFilterModel;
  LanguageFilterModel get languageFilterModel;
  TrainingTypeFilterModel get trainingTypeFilterModel;
  SectorFilterModel get sectorFilterModel;
  TrainingProviderFilterModel get trainingProviderFilterModel;
  SkillFilterModel get skillFilterModel;
  LocationFilterModel get locationFilterModel;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FilterModelCopyWith<FilterModel> get copyWith =>
      _$FilterModelCopyWithImpl<FilterModel>(this as FilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FilterModel &&
            (identical(other.durationFilterModel, durationFilterModel) ||
                other.durationFilterModel == durationFilterModel) &&
            (identical(other.languageFilterModel, languageFilterModel) ||
                other.languageFilterModel == languageFilterModel) &&
            (identical(other.trainingTypeFilterModel, trainingTypeFilterModel) ||
                other.trainingTypeFilterModel == trainingTypeFilterModel) &&
            (identical(other.sectorFilterModel, sectorFilterModel) ||
                other.sectorFilterModel == sectorFilterModel) &&
            (identical(other.trainingProviderFilterModel, trainingProviderFilterModel) ||
                other.trainingProviderFilterModel == trainingProviderFilterModel) &&
            (identical(other.skillFilterModel, skillFilterModel) ||
                other.skillFilterModel == skillFilterModel) &&
            (identical(other.locationFilterModel, locationFilterModel) ||
                other.locationFilterModel == locationFilterModel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      durationFilterModel,
      languageFilterModel,
      trainingTypeFilterModel,
      sectorFilterModel,
      trainingProviderFilterModel,
      skillFilterModel,
      locationFilterModel);

  @override
  String toString() {
    return 'FilterModel(durationFilterModel: $durationFilterModel, languageFilterModel: $languageFilterModel, trainingTypeFilterModel: $trainingTypeFilterModel, sectorFilterModel: $sectorFilterModel, trainingProviderFilterModel: $trainingProviderFilterModel, skillFilterModel: $skillFilterModel, locationFilterModel: $locationFilterModel)';
  }
}

/// @nodoc
abstract mixin class $FilterModelCopyWith<$Res> {
  factory $FilterModelCopyWith(FilterModel value, $Res Function(FilterModel) _then) =
      _$FilterModelCopyWithImpl;
  @useResult
  $Res call(
      {DurationFilterModel durationFilterModel,
      LanguageFilterModel languageFilterModel,
      TrainingTypeFilterModel trainingTypeFilterModel,
      SectorFilterModel sectorFilterModel,
      TrainingProviderFilterModel trainingProviderFilterModel,
      SkillFilterModel skillFilterModel,
      LocationFilterModel locationFilterModel});

  $DurationFilterModelCopyWith<$Res> get durationFilterModel;
  $LanguageFilterModelCopyWith<$Res> get languageFilterModel;
  $TrainingTypeFilterModelCopyWith<$Res> get trainingTypeFilterModel;
  $SectorFilterModelCopyWith<$Res> get sectorFilterModel;
  $TrainingProviderFilterModelCopyWith<$Res> get trainingProviderFilterModel;
  $SkillFilterModelCopyWith<$Res> get skillFilterModel;
  $LocationFilterModelCopyWith<$Res> get locationFilterModel;
}

/// @nodoc
class _$FilterModelCopyWithImpl<$Res> implements $FilterModelCopyWith<$Res> {
  _$FilterModelCopyWithImpl(this._self, this._then);

  final FilterModel _self;
  final $Res Function(FilterModel) _then;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? durationFilterModel = null,
    Object? languageFilterModel = null,
    Object? trainingTypeFilterModel = null,
    Object? sectorFilterModel = null,
    Object? trainingProviderFilterModel = null,
    Object? skillFilterModel = null,
    Object? locationFilterModel = null,
  }) {
    return _then(_self.copyWith(
      durationFilterModel: null == durationFilterModel
          ? _self.durationFilterModel
          : durationFilterModel // ignore: cast_nullable_to_non_nullable
              as DurationFilterModel,
      languageFilterModel: null == languageFilterModel
          ? _self.languageFilterModel
          : languageFilterModel // ignore: cast_nullable_to_non_nullable
              as LanguageFilterModel,
      trainingTypeFilterModel: null == trainingTypeFilterModel
          ? _self.trainingTypeFilterModel
          : trainingTypeFilterModel // ignore: cast_nullable_to_non_nullable
              as TrainingTypeFilterModel,
      sectorFilterModel: null == sectorFilterModel
          ? _self.sectorFilterModel
          : sectorFilterModel // ignore: cast_nullable_to_non_nullable
              as SectorFilterModel,
      trainingProviderFilterModel: null == trainingProviderFilterModel
          ? _self.trainingProviderFilterModel
          : trainingProviderFilterModel // ignore: cast_nullable_to_non_nullable
              as TrainingProviderFilterModel,
      skillFilterModel: null == skillFilterModel
          ? _self.skillFilterModel
          : skillFilterModel // ignore: cast_nullable_to_non_nullable
              as SkillFilterModel,
      locationFilterModel: null == locationFilterModel
          ? _self.locationFilterModel
          : locationFilterModel // ignore: cast_nullable_to_non_nullable
              as LocationFilterModel,
    ));
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DurationFilterModelCopyWith<$Res> get durationFilterModel {
    return $DurationFilterModelCopyWith<$Res>(_self.durationFilterModel, (value) {
      return _then(_self.copyWith(durationFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguageFilterModelCopyWith<$Res> get languageFilterModel {
    return $LanguageFilterModelCopyWith<$Res>(_self.languageFilterModel, (value) {
      return _then(_self.copyWith(languageFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingTypeFilterModelCopyWith<$Res> get trainingTypeFilterModel {
    return $TrainingTypeFilterModelCopyWith<$Res>(_self.trainingTypeFilterModel, (value) {
      return _then(_self.copyWith(trainingTypeFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorFilterModelCopyWith<$Res> get sectorFilterModel {
    return $SectorFilterModelCopyWith<$Res>(_self.sectorFilterModel, (value) {
      return _then(_self.copyWith(sectorFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingProviderFilterModelCopyWith<$Res> get trainingProviderFilterModel {
    return $TrainingProviderFilterModelCopyWith<$Res>(_self.trainingProviderFilterModel, (value) {
      return _then(_self.copyWith(trainingProviderFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SkillFilterModelCopyWith<$Res> get skillFilterModel {
    return $SkillFilterModelCopyWith<$Res>(_self.skillFilterModel, (value) {
      return _then(_self.copyWith(skillFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationFilterModelCopyWith<$Res> get locationFilterModel {
    return $LocationFilterModelCopyWith<$Res>(_self.locationFilterModel, (value) {
      return _then(_self.copyWith(locationFilterModel: value));
    });
  }
}

/// @nodoc

class _FilterModel extends FilterModel {
  const _FilterModel(
      {this.durationFilterModel = const DurationFilterModel(),
      this.languageFilterModel = const LanguageFilterModel(),
      this.trainingTypeFilterModel = const TrainingTypeFilterModel(),
      this.sectorFilterModel = const SectorFilterModel(),
      this.trainingProviderFilterModel = const TrainingProviderFilterModel(),
      this.skillFilterModel = const SkillFilterModel(),
      this.locationFilterModel = const LocationFilterModel()})
      : super._();

//defaults to empty fields
  @override
  @JsonKey()
  final DurationFilterModel durationFilterModel;
  @override
  @JsonKey()
  final LanguageFilterModel languageFilterModel;
  @override
  @JsonKey()
  final TrainingTypeFilterModel trainingTypeFilterModel;
  @override
  @JsonKey()
  final SectorFilterModel sectorFilterModel;
  @override
  @JsonKey()
  final TrainingProviderFilterModel trainingProviderFilterModel;
  @override
  @JsonKey()
  final SkillFilterModel skillFilterModel;
  @override
  @JsonKey()
  final LocationFilterModel locationFilterModel;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FilterModelCopyWith<_FilterModel> get copyWith =>
      __$FilterModelCopyWithImpl<_FilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FilterModel &&
            (identical(other.durationFilterModel, durationFilterModel) ||
                other.durationFilterModel == durationFilterModel) &&
            (identical(other.languageFilterModel, languageFilterModel) ||
                other.languageFilterModel == languageFilterModel) &&
            (identical(other.trainingTypeFilterModel, trainingTypeFilterModel) ||
                other.trainingTypeFilterModel == trainingTypeFilterModel) &&
            (identical(other.sectorFilterModel, sectorFilterModel) ||
                other.sectorFilterModel == sectorFilterModel) &&
            (identical(other.trainingProviderFilterModel, trainingProviderFilterModel) ||
                other.trainingProviderFilterModel == trainingProviderFilterModel) &&
            (identical(other.skillFilterModel, skillFilterModel) ||
                other.skillFilterModel == skillFilterModel) &&
            (identical(other.locationFilterModel, locationFilterModel) ||
                other.locationFilterModel == locationFilterModel));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      durationFilterModel,
      languageFilterModel,
      trainingTypeFilterModel,
      sectorFilterModel,
      trainingProviderFilterModel,
      skillFilterModel,
      locationFilterModel);

  @override
  String toString() {
    return 'FilterModel(durationFilterModel: $durationFilterModel, languageFilterModel: $languageFilterModel, trainingTypeFilterModel: $trainingTypeFilterModel, sectorFilterModel: $sectorFilterModel, trainingProviderFilterModel: $trainingProviderFilterModel, skillFilterModel: $skillFilterModel, locationFilterModel: $locationFilterModel)';
  }
}

/// @nodoc
abstract mixin class _$FilterModelCopyWith<$Res> implements $FilterModelCopyWith<$Res> {
  factory _$FilterModelCopyWith(_FilterModel value, $Res Function(_FilterModel) _then) =
      __$FilterModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {DurationFilterModel durationFilterModel,
      LanguageFilterModel languageFilterModel,
      TrainingTypeFilterModel trainingTypeFilterModel,
      SectorFilterModel sectorFilterModel,
      TrainingProviderFilterModel trainingProviderFilterModel,
      SkillFilterModel skillFilterModel,
      LocationFilterModel locationFilterModel});

  @override
  $DurationFilterModelCopyWith<$Res> get durationFilterModel;
  @override
  $LanguageFilterModelCopyWith<$Res> get languageFilterModel;
  @override
  $TrainingTypeFilterModelCopyWith<$Res> get trainingTypeFilterModel;
  @override
  $SectorFilterModelCopyWith<$Res> get sectorFilterModel;
  @override
  $TrainingProviderFilterModelCopyWith<$Res> get trainingProviderFilterModel;
  @override
  $SkillFilterModelCopyWith<$Res> get skillFilterModel;
  @override
  $LocationFilterModelCopyWith<$Res> get locationFilterModel;
}

/// @nodoc
class __$FilterModelCopyWithImpl<$Res> implements _$FilterModelCopyWith<$Res> {
  __$FilterModelCopyWithImpl(this._self, this._then);

  final _FilterModel _self;
  final $Res Function(_FilterModel) _then;

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? durationFilterModel = null,
    Object? languageFilterModel = null,
    Object? trainingTypeFilterModel = null,
    Object? sectorFilterModel = null,
    Object? trainingProviderFilterModel = null,
    Object? skillFilterModel = null,
    Object? locationFilterModel = null,
  }) {
    return _then(_FilterModel(
      durationFilterModel: null == durationFilterModel
          ? _self.durationFilterModel
          : durationFilterModel // ignore: cast_nullable_to_non_nullable
              as DurationFilterModel,
      languageFilterModel: null == languageFilterModel
          ? _self.languageFilterModel
          : languageFilterModel // ignore: cast_nullable_to_non_nullable
              as LanguageFilterModel,
      trainingTypeFilterModel: null == trainingTypeFilterModel
          ? _self.trainingTypeFilterModel
          : trainingTypeFilterModel // ignore: cast_nullable_to_non_nullable
              as TrainingTypeFilterModel,
      sectorFilterModel: null == sectorFilterModel
          ? _self.sectorFilterModel
          : sectorFilterModel // ignore: cast_nullable_to_non_nullable
              as SectorFilterModel,
      trainingProviderFilterModel: null == trainingProviderFilterModel
          ? _self.trainingProviderFilterModel
          : trainingProviderFilterModel // ignore: cast_nullable_to_non_nullable
              as TrainingProviderFilterModel,
      skillFilterModel: null == skillFilterModel
          ? _self.skillFilterModel
          : skillFilterModel // ignore: cast_nullable_to_non_nullable
              as SkillFilterModel,
      locationFilterModel: null == locationFilterModel
          ? _self.locationFilterModel
          : locationFilterModel // ignore: cast_nullable_to_non_nullable
              as LocationFilterModel,
    ));
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DurationFilterModelCopyWith<$Res> get durationFilterModel {
    return $DurationFilterModelCopyWith<$Res>(_self.durationFilterModel, (value) {
      return _then(_self.copyWith(durationFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguageFilterModelCopyWith<$Res> get languageFilterModel {
    return $LanguageFilterModelCopyWith<$Res>(_self.languageFilterModel, (value) {
      return _then(_self.copyWith(languageFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingTypeFilterModelCopyWith<$Res> get trainingTypeFilterModel {
    return $TrainingTypeFilterModelCopyWith<$Res>(_self.trainingTypeFilterModel, (value) {
      return _then(_self.copyWith(trainingTypeFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SectorFilterModelCopyWith<$Res> get sectorFilterModel {
    return $SectorFilterModelCopyWith<$Res>(_self.sectorFilterModel, (value) {
      return _then(_self.copyWith(sectorFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TrainingProviderFilterModelCopyWith<$Res> get trainingProviderFilterModel {
    return $TrainingProviderFilterModelCopyWith<$Res>(_self.trainingProviderFilterModel, (value) {
      return _then(_self.copyWith(trainingProviderFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SkillFilterModelCopyWith<$Res> get skillFilterModel {
    return $SkillFilterModelCopyWith<$Res>(_self.skillFilterModel, (value) {
      return _then(_self.copyWith(skillFilterModel: value));
    });
  }

  /// Create a copy of FilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LocationFilterModelCopyWith<$Res> get locationFilterModel {
    return $LocationFilterModelCopyWith<$Res>(_self.locationFilterModel, (value) {
      return _then(_self.copyWith(locationFilterModel: value));
    });
  }
}

// dart format on
