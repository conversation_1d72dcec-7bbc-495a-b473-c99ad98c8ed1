import 'package:freezed_annotation/freezed_annotation.dart';

part 'language_filter_model.freezed.dart';

@freezed
abstract class LanguageFilterModel with _$LanguageFilterModel {
  const factory LanguageFilterModel({@Default(false) bool arabic, @Default(false) bool english}) =
      _LanguageFilterModel;

  const LanguageFilterModel._();

  int get countSelected {
    int count = 0;
    if (arabic) count++;
    if (english) count++;

    return count;
  }
}
