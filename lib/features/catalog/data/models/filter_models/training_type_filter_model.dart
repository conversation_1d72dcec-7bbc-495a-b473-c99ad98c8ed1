import 'package:freezed_annotation/freezed_annotation.dart';

part 'training_type_filter_model.freezed.dart';

@freezed
abstract class TrainingTypeFilterModel with _$TrainingTypeFilterModel {
  const factory TrainingTypeFilterModel({
    @Default(false) bool online,
    @Default(false) bool inPerson,
    @Default(false) bool selfPaced,
  }) = _TrainingTypeFilterModel;

  const TrainingTypeFilterModel._();

  int get countSelected {
    int count = 0;
    if (online) count++;
    if (inPerson) count++;
    if (selfPaced) count++;

    return count;
  }
}
