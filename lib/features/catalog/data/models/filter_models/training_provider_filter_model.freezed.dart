// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_provider_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingProviderFilterModel {
  List<String> get selectedTrainingProviderIds;

  /// Create a copy of TrainingProviderFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingProviderFilterModelCopyWith<TrainingProviderFilterModel> get copyWith =>
      _$TrainingProviderFilterModelCopyWithImpl<TrainingProviderFilterModel>(
          this as TrainingProviderFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingProviderFilterModel &&
            const DeepCollectionEquality()
                .equals(other.selectedTrainingProviderIds, selectedTrainingProviderIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(selectedTrainingProviderIds));

  @override
  String toString() {
    return 'TrainingProviderFilterModel(selectedTrainingProviderIds: $selectedTrainingProviderIds)';
  }
}

/// @nodoc
abstract mixin class $TrainingProviderFilterModelCopyWith<$Res> {
  factory $TrainingProviderFilterModelCopyWith(
          TrainingProviderFilterModel value, $Res Function(TrainingProviderFilterModel) _then) =
      _$TrainingProviderFilterModelCopyWithImpl;
  @useResult
  $Res call({List<String> selectedTrainingProviderIds});
}

/// @nodoc
class _$TrainingProviderFilterModelCopyWithImpl<$Res>
    implements $TrainingProviderFilterModelCopyWith<$Res> {
  _$TrainingProviderFilterModelCopyWithImpl(this._self, this._then);

  final TrainingProviderFilterModel _self;
  final $Res Function(TrainingProviderFilterModel) _then;

  /// Create a copy of TrainingProviderFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTrainingProviderIds = null,
  }) {
    return _then(_self.copyWith(
      selectedTrainingProviderIds: null == selectedTrainingProviderIds
          ? _self.selectedTrainingProviderIds
          : selectedTrainingProviderIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _TrainingProviderFilterModel extends TrainingProviderFilterModel {
  const _TrainingProviderFilterModel({final List<String> selectedTrainingProviderIds = const []})
      : _selectedTrainingProviderIds = selectedTrainingProviderIds,
        super._();

  final List<String> _selectedTrainingProviderIds;
  @override
  @JsonKey()
  List<String> get selectedTrainingProviderIds {
    if (_selectedTrainingProviderIds is EqualUnmodifiableListView)
      return _selectedTrainingProviderIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTrainingProviderIds);
  }

  /// Create a copy of TrainingProviderFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingProviderFilterModelCopyWith<_TrainingProviderFilterModel> get copyWith =>
      __$TrainingProviderFilterModelCopyWithImpl<_TrainingProviderFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingProviderFilterModel &&
            const DeepCollectionEquality()
                .equals(other._selectedTrainingProviderIds, _selectedTrainingProviderIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_selectedTrainingProviderIds));

  @override
  String toString() {
    return 'TrainingProviderFilterModel(selectedTrainingProviderIds: $selectedTrainingProviderIds)';
  }
}

/// @nodoc
abstract mixin class _$TrainingProviderFilterModelCopyWith<$Res>
    implements $TrainingProviderFilterModelCopyWith<$Res> {
  factory _$TrainingProviderFilterModelCopyWith(
          _TrainingProviderFilterModel value, $Res Function(_TrainingProviderFilterModel) _then) =
      __$TrainingProviderFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({List<String> selectedTrainingProviderIds});
}

/// @nodoc
class __$TrainingProviderFilterModelCopyWithImpl<$Res>
    implements _$TrainingProviderFilterModelCopyWith<$Res> {
  __$TrainingProviderFilterModelCopyWithImpl(this._self, this._then);

  final _TrainingProviderFilterModel _self;
  final $Res Function(_TrainingProviderFilterModel) _then;

  /// Create a copy of TrainingProviderFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedTrainingProviderIds = null,
  }) {
    return _then(_TrainingProviderFilterModel(
      selectedTrainingProviderIds: null == selectedTrainingProviderIds
          ? _self._selectedTrainingProviderIds
          : selectedTrainingProviderIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
