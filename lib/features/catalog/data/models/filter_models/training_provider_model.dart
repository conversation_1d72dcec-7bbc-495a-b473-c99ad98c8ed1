import 'package:freezed_annotation/freezed_annotation.dart';

part 'training_provider_model.freezed.dart';
part 'training_provider_model.g.dart';

@freezed
abstract class TrainingProviderModel with _$TrainingProviderModel {
  const factory TrainingProviderModel({
    required String organizationName,
    required String id,
    String? commercialRegistration,
    DateTime? commercialRegistrationEndDate,
  }) = _TrainingProviderModel;

  factory TrainingProviderModel.fromJson(Map<String, dynamic> json) =>
      _$TrainingProviderModelFromJson(json);
}
