// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'language_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LanguageFilterModel {
  bool get arabic;
  bool get english;

  /// Create a copy of LanguageFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LanguageFilterModelCopyWith<LanguageFilterModel> get copyWith =>
      _$LanguageFilterModelCopyWithImpl<LanguageFilterModel>(
          this as LanguageFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LanguageFilterModel &&
            (identical(other.arabic, arabic) || other.arabic == arabic) &&
            (identical(other.english, english) || other.english == english));
  }

  @override
  int get hashCode => Object.hash(runtimeType, arabic, english);

  @override
  String toString() {
    return 'LanguageFilterModel(arabic: $arabic, english: $english)';
  }
}

/// @nodoc
abstract mixin class $LanguageFilterModelCopyWith<$Res> {
  factory $LanguageFilterModelCopyWith(
          LanguageFilterModel value, $Res Function(LanguageFilterModel) _then) =
      _$LanguageFilterModelCopyWithImpl;
  @useResult
  $Res call({bool arabic, bool english});
}

/// @nodoc
class _$LanguageFilterModelCopyWithImpl<$Res> implements $LanguageFilterModelCopyWith<$Res> {
  _$LanguageFilterModelCopyWithImpl(this._self, this._then);

  final LanguageFilterModel _self;
  final $Res Function(LanguageFilterModel) _then;

  /// Create a copy of LanguageFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? arabic = null,
    Object? english = null,
  }) {
    return _then(_self.copyWith(
      arabic: null == arabic
          ? _self.arabic
          : arabic // ignore: cast_nullable_to_non_nullable
              as bool,
      english: null == english
          ? _self.english
          : english // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _LanguageFilterModel extends LanguageFilterModel {
  const _LanguageFilterModel({this.arabic = false, this.english = false}) : super._();

  @override
  @JsonKey()
  final bool arabic;
  @override
  @JsonKey()
  final bool english;

  /// Create a copy of LanguageFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LanguageFilterModelCopyWith<_LanguageFilterModel> get copyWith =>
      __$LanguageFilterModelCopyWithImpl<_LanguageFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LanguageFilterModel &&
            (identical(other.arabic, arabic) || other.arabic == arabic) &&
            (identical(other.english, english) || other.english == english));
  }

  @override
  int get hashCode => Object.hash(runtimeType, arabic, english);

  @override
  String toString() {
    return 'LanguageFilterModel(arabic: $arabic, english: $english)';
  }
}

/// @nodoc
abstract mixin class _$LanguageFilterModelCopyWith<$Res>
    implements $LanguageFilterModelCopyWith<$Res> {
  factory _$LanguageFilterModelCopyWith(
          _LanguageFilterModel value, $Res Function(_LanguageFilterModel) _then) =
      __$LanguageFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({bool arabic, bool english});
}

/// @nodoc
class __$LanguageFilterModelCopyWithImpl<$Res> implements _$LanguageFilterModelCopyWith<$Res> {
  __$LanguageFilterModelCopyWithImpl(this._self, this._then);

  final _LanguageFilterModel _self;
  final $Res Function(_LanguageFilterModel) _then;

  /// Create a copy of LanguageFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? arabic = null,
    Object? english = null,
  }) {
    return _then(_LanguageFilterModel(
      arabic: null == arabic
          ? _self.arabic
          : arabic // ignore: cast_nullable_to_non_nullable
              as bool,
      english: null == english
          ? _self.english
          : english // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
