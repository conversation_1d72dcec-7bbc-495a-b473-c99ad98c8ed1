import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/skill_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';

part 'filter_model.freezed.dart';

@freezed
abstract class FilterModel with _$FilterModel {
  const factory FilterModel({
    //defaults to empty fields
    @Default(DurationFilterModel()) DurationFilterModel durationFilterModel,
    @Default(LanguageFilterModel()) LanguageFilterModel languageFilterModel,
    @Default(TrainingTypeFilterModel()) TrainingTypeFilterModel trainingTypeFilterModel,
    @Default(SectorFilterModel()) SectorFilterModel sectorFilterModel,
    @Default(TrainingProviderFilterModel()) TrainingProviderFilterModel trainingProviderFilterModel,
    @Default(SkillFilterModel()) SkillFilterModel skillFilterModel,
    @Default(LocationFilterModel()) LocationFilterModel locationFilterModel,
  }) = _FilterModel;

  const FilterModel._();

  bool get noFiltersSelected =>
      durationFilterModel.countSelected == 0 &&
      languageFilterModel.countSelected == 0 &&
      trainingTypeFilterModel.countSelected == 0 &&
      sectorFilterModel.countSelected == 0 &&
      trainingProviderFilterModel.countSelected == 0 &&
      skillFilterModel.countSelected == 0 &&
      trainingTypeFilterModel.countSelected == 0 &&
      locationFilterModel.countSelected == 0;
}
