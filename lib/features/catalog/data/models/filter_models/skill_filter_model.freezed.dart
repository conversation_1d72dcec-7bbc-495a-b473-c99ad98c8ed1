// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'skill_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SkillFilterModel {
  bool get advanced;
  bool get basic;
  bool get general;

  /// Create a copy of SkillFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SkillFilterModelCopyWith<SkillFilterModel> get copyWith =>
      _$SkillFilterModelCopyWithImpl<SkillFilterModel>(this as SkillFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SkillFilterModel &&
            (identical(other.advanced, advanced) || other.advanced == advanced) &&
            (identical(other.basic, basic) || other.basic == basic) &&
            (identical(other.general, general) || other.general == general));
  }

  @override
  int get hashCode => Object.hash(runtimeType, advanced, basic, general);

  @override
  String toString() {
    return 'SkillFilterModel(advanced: $advanced, basic: $basic, general: $general)';
  }
}

/// @nodoc
abstract mixin class $SkillFilterModelCopyWith<$Res> {
  factory $SkillFilterModelCopyWith(SkillFilterModel value, $Res Function(SkillFilterModel) _then) =
      _$SkillFilterModelCopyWithImpl;
  @useResult
  $Res call({bool advanced, bool basic, bool general});
}

/// @nodoc
class _$SkillFilterModelCopyWithImpl<$Res> implements $SkillFilterModelCopyWith<$Res> {
  _$SkillFilterModelCopyWithImpl(this._self, this._then);

  final SkillFilterModel _self;
  final $Res Function(SkillFilterModel) _then;

  /// Create a copy of SkillFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? advanced = null,
    Object? basic = null,
    Object? general = null,
  }) {
    return _then(_self.copyWith(
      advanced: null == advanced
          ? _self.advanced
          : advanced // ignore: cast_nullable_to_non_nullable
              as bool,
      basic: null == basic
          ? _self.basic
          : basic // ignore: cast_nullable_to_non_nullable
              as bool,
      general: null == general
          ? _self.general
          : general // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _SkillFilterModel extends SkillFilterModel {
  const _SkillFilterModel({this.advanced = false, this.basic = false, this.general = false})
      : super._();

  @override
  @JsonKey()
  final bool advanced;
  @override
  @JsonKey()
  final bool basic;
  @override
  @JsonKey()
  final bool general;

  /// Create a copy of SkillFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SkillFilterModelCopyWith<_SkillFilterModel> get copyWith =>
      __$SkillFilterModelCopyWithImpl<_SkillFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SkillFilterModel &&
            (identical(other.advanced, advanced) || other.advanced == advanced) &&
            (identical(other.basic, basic) || other.basic == basic) &&
            (identical(other.general, general) || other.general == general));
  }

  @override
  int get hashCode => Object.hash(runtimeType, advanced, basic, general);

  @override
  String toString() {
    return 'SkillFilterModel(advanced: $advanced, basic: $basic, general: $general)';
  }
}

/// @nodoc
abstract mixin class _$SkillFilterModelCopyWith<$Res> implements $SkillFilterModelCopyWith<$Res> {
  factory _$SkillFilterModelCopyWith(
          _SkillFilterModel value, $Res Function(_SkillFilterModel) _then) =
      __$SkillFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({bool advanced, bool basic, bool general});
}

/// @nodoc
class __$SkillFilterModelCopyWithImpl<$Res> implements _$SkillFilterModelCopyWith<$Res> {
  __$SkillFilterModelCopyWithImpl(this._self, this._then);

  final _SkillFilterModel _self;
  final $Res Function(_SkillFilterModel) _then;

  /// Create a copy of SkillFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? advanced = null,
    Object? basic = null,
    Object? general = null,
  }) {
    return _then(_SkillFilterModel(
      advanced: null == advanced
          ? _self.advanced
          : advanced // ignore: cast_nullable_to_non_nullable
              as bool,
      basic: null == basic
          ? _self.basic
          : basic // ignore: cast_nullable_to_non_nullable
              as bool,
      general: null == general
          ? _self.general
          : general // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
