// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationModel {
  String get id;
  String get cityPlaceId;
  String get cityName;
  String get regionPlaceId;
  String get regionName;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationModelCopyWith<LocationModel> get copyWith =>
      _$LocationModelCopyWithImpl<LocationModel>(this as LocationModel, _$identity);

  /// Serializes this LocationModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocationModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityPlaceId, cityPlaceId) || other.cityPlaceId == cityPlaceId) &&
            (identical(other.cityName, cityName) || other.cityName == cityName) &&
            (identical(other.regionPlaceId, regionPlaceId) ||
                other.regionPlaceId == regionPlaceId) &&
            (identical(other.regionName, regionName) || other.regionName == regionName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, cityPlaceId, cityName, regionPlaceId, regionName);

  @override
  String toString() {
    return 'LocationModel(id: $id, cityPlaceId: $cityPlaceId, cityName: $cityName, regionPlaceId: $regionPlaceId, regionName: $regionName)';
  }
}

/// @nodoc
abstract mixin class $LocationModelCopyWith<$Res> {
  factory $LocationModelCopyWith(LocationModel value, $Res Function(LocationModel) _then) =
      _$LocationModelCopyWithImpl;
  @useResult
  $Res call(
      {String id, String cityPlaceId, String cityName, String regionPlaceId, String regionName});
}

/// @nodoc
class _$LocationModelCopyWithImpl<$Res> implements $LocationModelCopyWith<$Res> {
  _$LocationModelCopyWithImpl(this._self, this._then);

  final LocationModel _self;
  final $Res Function(LocationModel) _then;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? cityPlaceId = null,
    Object? cityName = null,
    Object? regionPlaceId = null,
    Object? regionName = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityPlaceId: null == cityPlaceId
          ? _self.cityPlaceId
          : cityPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      regionPlaceId: null == regionPlaceId
          ? _self.regionPlaceId
          : regionPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      regionName: null == regionName
          ? _self.regionName
          : regionName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LocationModel implements LocationModel {
  const _LocationModel(
      {required this.id,
      required this.cityPlaceId,
      required this.cityName,
      required this.regionPlaceId,
      required this.regionName});
  factory _LocationModel.fromJson(Map<String, dynamic> json) => _$LocationModelFromJson(json);

  @override
  final String id;
  @override
  final String cityPlaceId;
  @override
  final String cityName;
  @override
  final String regionPlaceId;
  @override
  final String regionName;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationModelCopyWith<_LocationModel> get copyWith =>
      __$LocationModelCopyWithImpl<_LocationModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LocationModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.cityPlaceId, cityPlaceId) || other.cityPlaceId == cityPlaceId) &&
            (identical(other.cityName, cityName) || other.cityName == cityName) &&
            (identical(other.regionPlaceId, regionPlaceId) ||
                other.regionPlaceId == regionPlaceId) &&
            (identical(other.regionName, regionName) || other.regionName == regionName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, cityPlaceId, cityName, regionPlaceId, regionName);

  @override
  String toString() {
    return 'LocationModel(id: $id, cityPlaceId: $cityPlaceId, cityName: $cityName, regionPlaceId: $regionPlaceId, regionName: $regionName)';
  }
}

/// @nodoc
abstract mixin class _$LocationModelCopyWith<$Res> implements $LocationModelCopyWith<$Res> {
  factory _$LocationModelCopyWith(_LocationModel value, $Res Function(_LocationModel) _then) =
      __$LocationModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id, String cityPlaceId, String cityName, String regionPlaceId, String regionName});
}

/// @nodoc
class __$LocationModelCopyWithImpl<$Res> implements _$LocationModelCopyWith<$Res> {
  __$LocationModelCopyWithImpl(this._self, this._then);

  final _LocationModel _self;
  final $Res Function(_LocationModel) _then;

  /// Create a copy of LocationModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? cityPlaceId = null,
    Object? cityName = null,
    Object? regionPlaceId = null,
    Object? regionName = null,
  }) {
    return _then(_LocationModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      cityPlaceId: null == cityPlaceId
          ? _self.cityPlaceId
          : cityPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      cityName: null == cityName
          ? _self.cityName
          : cityName // ignore: cast_nullable_to_non_nullable
              as String,
      regionPlaceId: null == regionPlaceId
          ? _self.regionPlaceId
          : regionPlaceId // ignore: cast_nullable_to_non_nullable
              as String,
      regionName: null == regionName
          ? _self.regionName
          : regionName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
