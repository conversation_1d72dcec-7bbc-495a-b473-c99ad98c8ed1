import 'package:freezed_annotation/freezed_annotation.dart';

part 'duration_filter_model.freezed.dart';

@freezed
abstract class DurationFilterModel with _$DurationFilterModel {
  const factory DurationFilterModel({
    @Default(false) bool allDurations,
    @Default(false) bool days,
    @Default(false) bool weeks,
    @Default(false) bool months,
  }) = _DurationFilterModel;

  const DurationFilterModel._();

  //at this moment it's not compatible with freezed thus ignored
  //ignore: prefer_constructors_over_static_methods
  static DurationFilterModel all() =>
      const DurationFilterModel(allDurations: true, days: true, weeks: true, months: true);

  bool get isAllOptionTrue => days && weeks && months;

  int get countSelected {
    if (allDurations) return 4;

    int count = 0;
    if (days) count++;
    if (weeks) count++;
    if (months) count++;

    return count;
  }
}
