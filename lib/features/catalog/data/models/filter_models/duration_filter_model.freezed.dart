// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'duration_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DurationFilterModel {
  bool get allDurations;
  bool get days;
  bool get weeks;
  bool get months;

  /// Create a copy of DurationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DurationFilterModelCopyWith<DurationFilterModel> get copyWith =>
      _$DurationFilterModelCopyWithImpl<DurationFilterModel>(
          this as DurationFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DurationFilterModel &&
            (identical(other.allDurations, allDurations) || other.allDurations == allDurations) &&
            (identical(other.days, days) || other.days == days) &&
            (identical(other.weeks, weeks) || other.weeks == weeks) &&
            (identical(other.months, months) || other.months == months));
  }

  @override
  int get hashCode => Object.hash(runtimeType, allDurations, days, weeks, months);

  @override
  String toString() {
    return 'DurationFilterModel(allDurations: $allDurations, days: $days, weeks: $weeks, months: $months)';
  }
}

/// @nodoc
abstract mixin class $DurationFilterModelCopyWith<$Res> {
  factory $DurationFilterModelCopyWith(
          DurationFilterModel value, $Res Function(DurationFilterModel) _then) =
      _$DurationFilterModelCopyWithImpl;
  @useResult
  $Res call({bool allDurations, bool days, bool weeks, bool months});
}

/// @nodoc
class _$DurationFilterModelCopyWithImpl<$Res> implements $DurationFilterModelCopyWith<$Res> {
  _$DurationFilterModelCopyWithImpl(this._self, this._then);

  final DurationFilterModel _self;
  final $Res Function(DurationFilterModel) _then;

  /// Create a copy of DurationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allDurations = null,
    Object? days = null,
    Object? weeks = null,
    Object? months = null,
  }) {
    return _then(_self.copyWith(
      allDurations: null == allDurations
          ? _self.allDurations
          : allDurations // ignore: cast_nullable_to_non_nullable
              as bool,
      days: null == days
          ? _self.days
          : days // ignore: cast_nullable_to_non_nullable
              as bool,
      weeks: null == weeks
          ? _self.weeks
          : weeks // ignore: cast_nullable_to_non_nullable
              as bool,
      months: null == months
          ? _self.months
          : months // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _DurationFilterModel extends DurationFilterModel {
  const _DurationFilterModel(
      {this.allDurations = false, this.days = false, this.weeks = false, this.months = false})
      : super._();

  @override
  @JsonKey()
  final bool allDurations;
  @override
  @JsonKey()
  final bool days;
  @override
  @JsonKey()
  final bool weeks;
  @override
  @JsonKey()
  final bool months;

  /// Create a copy of DurationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DurationFilterModelCopyWith<_DurationFilterModel> get copyWith =>
      __$DurationFilterModelCopyWithImpl<_DurationFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DurationFilterModel &&
            (identical(other.allDurations, allDurations) || other.allDurations == allDurations) &&
            (identical(other.days, days) || other.days == days) &&
            (identical(other.weeks, weeks) || other.weeks == weeks) &&
            (identical(other.months, months) || other.months == months));
  }

  @override
  int get hashCode => Object.hash(runtimeType, allDurations, days, weeks, months);

  @override
  String toString() {
    return 'DurationFilterModel(allDurations: $allDurations, days: $days, weeks: $weeks, months: $months)';
  }
}

/// @nodoc
abstract mixin class _$DurationFilterModelCopyWith<$Res>
    implements $DurationFilterModelCopyWith<$Res> {
  factory _$DurationFilterModelCopyWith(
          _DurationFilterModel value, $Res Function(_DurationFilterModel) _then) =
      __$DurationFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({bool allDurations, bool days, bool weeks, bool months});
}

/// @nodoc
class __$DurationFilterModelCopyWithImpl<$Res> implements _$DurationFilterModelCopyWith<$Res> {
  __$DurationFilterModelCopyWithImpl(this._self, this._then);

  final _DurationFilterModel _self;
  final $Res Function(_DurationFilterModel) _then;

  /// Create a copy of DurationFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? allDurations = null,
    Object? days = null,
    Object? weeks = null,
    Object? months = null,
  }) {
    return _then(_DurationFilterModel(
      allDurations: null == allDurations
          ? _self.allDurations
          : allDurations // ignore: cast_nullable_to_non_nullable
              as bool,
      days: null == days
          ? _self.days
          : days // ignore: cast_nullable_to_non_nullable
              as bool,
      weeks: null == weeks
          ? _self.weeks
          : weeks // ignore: cast_nullable_to_non_nullable
              as bool,
      months: null == months
          ? _self.months
          : months // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
