// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_type_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingTypeFilterModel {
  bool get online;
  bool get inPerson;
  bool get selfPaced;

  /// Create a copy of TrainingTypeFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingTypeFilterModelCopyWith<TrainingTypeFilterModel> get copyWith =>
      _$TrainingTypeFilterModelCopyWithImpl<TrainingTypeFilterModel>(
          this as TrainingTypeFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingTypeFilterModel &&
            (identical(other.online, online) || other.online == online) &&
            (identical(other.inPerson, inPerson) || other.inPerson == inPerson) &&
            (identical(other.selfPaced, selfPaced) || other.selfPaced == selfPaced));
  }

  @override
  int get hashCode => Object.hash(runtimeType, online, inPerson, selfPaced);

  @override
  String toString() {
    return 'TrainingTypeFilterModel(online: $online, inPerson: $inPerson, selfPaced: $selfPaced)';
  }
}

/// @nodoc
abstract mixin class $TrainingTypeFilterModelCopyWith<$Res> {
  factory $TrainingTypeFilterModelCopyWith(
          TrainingTypeFilterModel value, $Res Function(TrainingTypeFilterModel) _then) =
      _$TrainingTypeFilterModelCopyWithImpl;
  @useResult
  $Res call({bool online, bool inPerson, bool selfPaced});
}

/// @nodoc
class _$TrainingTypeFilterModelCopyWithImpl<$Res>
    implements $TrainingTypeFilterModelCopyWith<$Res> {
  _$TrainingTypeFilterModelCopyWithImpl(this._self, this._then);

  final TrainingTypeFilterModel _self;
  final $Res Function(TrainingTypeFilterModel) _then;

  /// Create a copy of TrainingTypeFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? online = null,
    Object? inPerson = null,
    Object? selfPaced = null,
  }) {
    return _then(_self.copyWith(
      online: null == online
          ? _self.online
          : online // ignore: cast_nullable_to_non_nullable
              as bool,
      inPerson: null == inPerson
          ? _self.inPerson
          : inPerson // ignore: cast_nullable_to_non_nullable
              as bool,
      selfPaced: null == selfPaced
          ? _self.selfPaced
          : selfPaced // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _TrainingTypeFilterModel extends TrainingTypeFilterModel {
  const _TrainingTypeFilterModel(
      {this.online = false, this.inPerson = false, this.selfPaced = false})
      : super._();

  @override
  @JsonKey()
  final bool online;
  @override
  @JsonKey()
  final bool inPerson;
  @override
  @JsonKey()
  final bool selfPaced;

  /// Create a copy of TrainingTypeFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingTypeFilterModelCopyWith<_TrainingTypeFilterModel> get copyWith =>
      __$TrainingTypeFilterModelCopyWithImpl<_TrainingTypeFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingTypeFilterModel &&
            (identical(other.online, online) || other.online == online) &&
            (identical(other.inPerson, inPerson) || other.inPerson == inPerson) &&
            (identical(other.selfPaced, selfPaced) || other.selfPaced == selfPaced));
  }

  @override
  int get hashCode => Object.hash(runtimeType, online, inPerson, selfPaced);

  @override
  String toString() {
    return 'TrainingTypeFilterModel(online: $online, inPerson: $inPerson, selfPaced: $selfPaced)';
  }
}

/// @nodoc
abstract mixin class _$TrainingTypeFilterModelCopyWith<$Res>
    implements $TrainingTypeFilterModelCopyWith<$Res> {
  factory _$TrainingTypeFilterModelCopyWith(
          _TrainingTypeFilterModel value, $Res Function(_TrainingTypeFilterModel) _then) =
      __$TrainingTypeFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({bool online, bool inPerson, bool selfPaced});
}

/// @nodoc
class __$TrainingTypeFilterModelCopyWithImpl<$Res>
    implements _$TrainingTypeFilterModelCopyWith<$Res> {
  __$TrainingTypeFilterModelCopyWithImpl(this._self, this._then);

  final _TrainingTypeFilterModel _self;
  final $Res Function(_TrainingTypeFilterModel) _then;

  /// Create a copy of TrainingTypeFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? online = null,
    Object? inPerson = null,
    Object? selfPaced = null,
  }) {
    return _then(_TrainingTypeFilterModel(
      online: null == online
          ? _self.online
          : online // ignore: cast_nullable_to_non_nullable
              as bool,
      inPerson: null == inPerson
          ? _self.inPerson
          : inPerson // ignore: cast_nullable_to_non_nullable
              as bool,
      selfPaced: null == selfPaced
          ? _self.selfPaced
          : selfPaced // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
