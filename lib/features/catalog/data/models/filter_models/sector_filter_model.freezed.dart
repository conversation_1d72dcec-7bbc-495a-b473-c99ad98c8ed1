// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sector_filter_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SectorFilterModel {
  List<String> get selectedSectorIds;

  /// Create a copy of SectorFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SectorFilterModelCopyWith<SectorFilterModel> get copyWith =>
      _$SectorFilterModelCopyWithImpl<SectorFilterModel>(this as SectorFilterModel, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SectorFilterModel &&
            const DeepCollectionEquality().equals(other.selectedSectorIds, selectedSectorIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(selectedSectorIds));

  @override
  String toString() {
    return 'SectorFilterModel(selectedSectorIds: $selectedSectorIds)';
  }
}

/// @nodoc
abstract mixin class $SectorFilterModelCopyWith<$Res> {
  factory $SectorFilterModelCopyWith(
          SectorFilterModel value, $Res Function(SectorFilterModel) _then) =
      _$SectorFilterModelCopyWithImpl;
  @useResult
  $Res call({List<String> selectedSectorIds});
}

/// @nodoc
class _$SectorFilterModelCopyWithImpl<$Res> implements $SectorFilterModelCopyWith<$Res> {
  _$SectorFilterModelCopyWithImpl(this._self, this._then);

  final SectorFilterModel _self;
  final $Res Function(SectorFilterModel) _then;

  /// Create a copy of SectorFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedSectorIds = null,
  }) {
    return _then(_self.copyWith(
      selectedSectorIds: null == selectedSectorIds
          ? _self.selectedSectorIds
          : selectedSectorIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _SectorFilterModel extends SectorFilterModel {
  const _SectorFilterModel({final List<String> selectedSectorIds = const []})
      : _selectedSectorIds = selectedSectorIds,
        super._();

  final List<String> _selectedSectorIds;
  @override
  @JsonKey()
  List<String> get selectedSectorIds {
    if (_selectedSectorIds is EqualUnmodifiableListView) return _selectedSectorIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedSectorIds);
  }

  /// Create a copy of SectorFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SectorFilterModelCopyWith<_SectorFilterModel> get copyWith =>
      __$SectorFilterModelCopyWithImpl<_SectorFilterModel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SectorFilterModel &&
            const DeepCollectionEquality().equals(other._selectedSectorIds, _selectedSectorIds));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_selectedSectorIds));

  @override
  String toString() {
    return 'SectorFilterModel(selectedSectorIds: $selectedSectorIds)';
  }
}

/// @nodoc
abstract mixin class _$SectorFilterModelCopyWith<$Res> implements $SectorFilterModelCopyWith<$Res> {
  factory _$SectorFilterModelCopyWith(
          _SectorFilterModel value, $Res Function(_SectorFilterModel) _then) =
      __$SectorFilterModelCopyWithImpl;
  @override
  @useResult
  $Res call({List<String> selectedSectorIds});
}

/// @nodoc
class __$SectorFilterModelCopyWithImpl<$Res> implements _$SectorFilterModelCopyWith<$Res> {
  __$SectorFilterModelCopyWithImpl(this._self, this._then);

  final _SectorFilterModel _self;
  final $Res Function(_SectorFilterModel) _then;

  /// Create a copy of SectorFilterModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedSectorIds = null,
  }) {
    return _then(_SectorFilterModel(
      selectedSectorIds: null == selectedSectorIds
          ? _self._selectedSectorIds
          : selectedSectorIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

// dart format on
