import 'package:freezed_annotation/freezed_annotation.dart';

part 'skill_filter_model.freezed.dart';

@freezed
abstract class SkillFilterModel with _$SkillFilterModel {
  const factory SkillFilterModel({
    @Default(false) bool advanced,
    @Default(false) bool basic,
    @Default(false) bool general,
  }) = _SkillFilterModel;

  const SkillFilterModel._();

  int get countSelected {
    int count = 0;
    if (advanced) count++;
    if (basic) count++;
    if (general) count++;

    return count;
  }
}
