import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/training_providers_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';

@singleton
class TrainingProvidersRepository {
  TrainingProvidersRepository({required TrainingProvidersDataSource dataSource})
      : _dataSource = dataSource;

  final TrainingProvidersDataSource _dataSource;
  List<TrainingProviderModel>? _cachedTrainingProviders;

  Future<List<TrainingProviderModel>> getTrainingProviders(String locale) async {
    if (_cachedTrainingProviders != null) {
      return _cachedTrainingProviders!;
    }

    final trainingProviders = await _dataSource.getTrainingProviders(locale);
    _cachedTrainingProviders = trainingProviders;
    return trainingProviders;
  }
}
