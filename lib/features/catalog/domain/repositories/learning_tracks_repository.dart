import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_data_source.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';

@injectable
class LearningTracksRepository {
  const LearningTracksRepository({required LearningTracksDataSource dataSource})
      : _dataSource = dataSource;

  final LearningTracksDataSource _dataSource;

  Future<LearningTracksModel> getLearningTracks(LearningTracksRequestParams requestPrams) {
    return _dataSource.getLearningTracks(requestPrams);
  }
}
