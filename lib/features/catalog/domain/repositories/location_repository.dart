import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/location_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';

@injectable
class LocationRepository {
  LocationRepository({required LocationDataSource locationDataSource})
      : _locationDataSource = locationDataSource;

  final LocationDataSource _locationDataSource;

  Future<List<LocationModel>> getLocations(String locale) {
    return _locationDataSource.getLocations(locale);
  }
}
