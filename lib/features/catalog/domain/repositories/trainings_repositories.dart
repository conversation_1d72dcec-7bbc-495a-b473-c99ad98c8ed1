import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_data_source.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';

@injectable
class TrainingsRepository {
  const TrainingsRepository({required TrainingsDataSource dataSource}) : _dataSource = dataSource;

  final TrainingsDataSource _dataSource;

  Future<TrainingsModel> getTrainings(TrainingsRequestParams requestPrams) =>
      _dataSource.getTrainingsList(requestPrams);
}
