import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/text_language_utils.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/training_providers_repository.dart';

@injectable
class GetSortedTrainingProvidersUseCase {
  final TrainingProvidersRepository _repository;

  const GetSortedTrainingProvidersUseCase(this._repository);

  Future<List<TrainingProviderModel>> call(String locale) async {
    final providers = await _repository.getTrainingProviders(locale);
    return _sortProviders(providers, locale);
  }

  List<TrainingProviderModel> _sortProviders(List<TrainingProviderModel> providers, String locale) {
    return List<TrainingProviderModel>.from(providers)
      ..sort((a, b) {
        final isArabicA = isArabicText(a.organizationName);
        final isArabicB = isArabicText(b.organizationName);

        if (locale.startsWith(Constants.localeAR)) {
          if (isArabicA != isArabicB) return isArabicA ? -1 : 1;
        } else {
          if (isArabicA != isArabicB) return isArabicA ? 1 : -1;
        }

        return a.organizationName.compareTo(b.organizationName);
      });
  }
}
