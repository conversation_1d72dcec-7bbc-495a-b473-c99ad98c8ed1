import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

String getTotalResult(int numOfResults) {
  if (numOfResults == 1) {
    return LocaleKeys.totalResults1.tr();
  } else if (numOfResults == 2) {
    return LocaleKeys.totalResults2.tr();
  } else if (numOfResults > 2 && numOfResults < 11) {
    return LocaleKeys.totalResults2to11.tr();
  } else {
    return LocaleKeys.totalResults.tr();
  }
}

String getItemsSelectedTranslation(int numOfResults) {
  if (numOfResults == 1) {
    return LocaleKeys.itemsSelected1.tr();
  } else if (numOfResults == 2) {
    return LocaleKeys.itemsSelected2.tr();
  } else if (numOfResults > 2 && numOfResults < 11) {
    return LocaleKeys.itemsSelected3to10.tr();
  } else {
    return LocaleKeys.itemsSelected11.tr();
  }
}

String getTotalResultsForSearchKeyText(
  BuildContext context,
  int? totalElements,
  String? searchKey,
) {
  if (totalElements == null) return '';

  if (searchKey != null && searchKey.isNotEmpty) {
    return plural(
      LocaleKeys.searchResultCount,
      totalElements,
      args: context.locale == const Locale(Constants.localeAR)
          ? [searchKey.trim(), '$totalElements']
          : ['$totalElements', '"$searchKey"'],
    );
  }

  return context.locale == const Locale(Constants.localeAR)
      ? '${getTotalResult(totalElements)} $totalElements'
      : '$totalElements ${LocaleKeys.totalResults.tr()}';
}
