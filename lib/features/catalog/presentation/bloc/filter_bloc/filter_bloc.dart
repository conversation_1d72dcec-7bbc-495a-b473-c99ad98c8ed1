import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/skill_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart'
    as learning_tracks_bloc;
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart'
    hide ApplyFilterToLtList;
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart'
    as trainings_bloc;
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';

part 'filter_event.dart';
part 'filter_state.dart';

class FilterBloc extends Bloc<FilterEvent, FilterState> {
  FilterBloc({this.learningTracksBloc, this.trainingsBloc})
      : assert(
          (learningTracksBloc == null) != (trainingsBloc == null),
          'Only one bloc should be provided, either LearningTracksBloc or TrainingsBloc',
        ),
        super(const FilterUpdated(FilterModel())) {
    on<InitFilterPage>(_initFilterPage);
    on<SetLanguage>(_setLanguage);
    on<SetSkill>(_setSkill);
    on<SetTrainingType>(_setTrainingType);
    on<SetDuration>(_setDuration);
    on<SetSectors>(_setSectors);
    on<SetTrainingProviders>(_setTrainingProviders);
    on<SetLocations>(_setLocations);
    on<ResetAllFilter>(_resetAllFilter);
    on<ApplyFilters>(_applyFilters);
    on<DontApplyFilters>(_filtersNotApplied);

    // Initialize with current filter from trainings bloc if available
    if (trainingsBloc != null && trainingsBloc?.state is TrainingsLoaded) {
      final currentFilter = (trainingsBloc!.state as TrainingsLoaded).appliedFilter;
      add(InitFilterPage(currentFilter));
    }
  }

  final LearningTracksBloc? learningTracksBloc;
  final TrainingsBloc? trainingsBloc;

  Future<void> _initFilterPage(InitFilterPage event, Emitter<FilterState> emit) async {
    emit(FilterUpdated(event.filterModel));
  }

  Future<void> _setLanguage(SetLanguage event, Emitter<FilterState> emit) async {
    emit(
      FilterUpdated(_filterUpdatedState.filterModel.copyWith(languageFilterModel: event.language)),
    );
  }

  Future<void> _setSkill(SetSkill event, Emitter<FilterState> emit) async {
    emit(
      FilterUpdated(
        _filterUpdatedState.filterModel.copyWith(skillFilterModel: event.skillFilterModel),
      ),
    );
  }

  Future<void> _setTrainingType(SetTrainingType event, Emitter<FilterState> emit) async {
    emit(
      FilterUpdated(
        _filterUpdatedState.filterModel.copyWith(
          trainingTypeFilterModel: event.trainingTypeFilterModel,
        ),
      ),
    );
  }

  Future<void> _setDuration(SetDuration event, Emitter<FilterState> emit) async {
    final isAllOptionTrue = event.durationFilterModel.isAllOptionTrue;
    final durationFilterModel = event.durationFilterModel.copyWith(allDurations: isAllOptionTrue);

    emit(
      FilterUpdated(
        _filterUpdatedState.filterModel.copyWith(durationFilterModel: durationFilterModel),
      ),
    );
  }

  Future<void> _setSectors(SetSectors event, Emitter<FilterState> emit) async {
    final sectorId = event.sectorId;
    if (sectorId != null) {
      final currentIds = List<String>.from(
        _filterUpdatedState.filterModel.sectorFilterModel.selectedSectorIds,
      );

      final isSelected = currentIds.contains(sectorId);
      if (isSelected) {
        currentIds.remove(sectorId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              sectorFilterModel: SectorFilterModel(selectedSectorIds: currentIds),
            ),
          ),
        );
      } else {
        currentIds.add(sectorId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              sectorFilterModel: SectorFilterModel(selectedSectorIds: currentIds),
            ),
          ),
        );
      }
    } else {
      /// Reset sector filter when sectorId is null
      emit(
        FilterUpdated(
          _filterUpdatedState.filterModel.copyWith(
            sectorFilterModel: const SectorFilterModel(),
          ),
        ),
      );
    }
  }

  Future<void> _setLocations(SetLocations event, Emitter<FilterState> emit) async {
    final locationId = event.locationId;
    if (locationId != null) {
      final currentIds = List<String>.from(
        _filterUpdatedState.filterModel.locationFilterModel.selectedLocationIds,
      );

      final isSelected = currentIds.contains(locationId);
      if (isSelected) {
        currentIds.remove(locationId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              locationFilterModel: LocationFilterModel(selectedLocationIds: currentIds),
            ),
          ),
        );
      } else {
        currentIds.add(locationId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              locationFilterModel: LocationFilterModel(selectedLocationIds: currentIds),
            ),
          ),
        );
      }
    } else {
      /// Reset location filter when locationId is null
      emit(
        FilterUpdated(
          _filterUpdatedState.filterModel.copyWith(
            locationFilterModel: const LocationFilterModel(),
          ),
        ),
      );
    }
  }

  Future<void> _setTrainingProviders(SetTrainingProviders event, Emitter<FilterState> emit) async {
    final trainingProviderId = event.trainingProviderId;
    if (trainingProviderId != null) {
      final currentIds = List<String>.from(
        _filterUpdatedState.filterModel.trainingProviderFilterModel.selectedTrainingProviderIds,
      );

      final isSelected = currentIds.contains(trainingProviderId);
      if (isSelected) {
        currentIds.remove(trainingProviderId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              trainingProviderFilterModel: event.trainingProviderFilterModel.copyWith(
                selectedTrainingProviderIds: currentIds,
              ),
            ),
          ),
        );
      } else {
        currentIds.add(trainingProviderId);
        emit(
          FilterUpdated(
            _filterUpdatedState.filterModel.copyWith(
              trainingProviderFilterModel: event.trainingProviderFilterModel.copyWith(
                selectedTrainingProviderIds: currentIds,
              ),
            ),
          ),
        );
      }
    } else {
      emit(
        FilterUpdated(
          _filterUpdatedState.filterModel.copyWith(
            trainingProviderFilterModel: const TrainingProviderFilterModel(),
          ),
        ),
      );
    }
  }

  Future<void> _resetAllFilter(ResetAllFilter event, Emitter<FilterState> emit) async {
    if (!_filterUpdatedState.filterModel.noFiltersSelected) {
      emit(const FilterUpdated(FilterModel(), reset: true));

      add(const ApplyFilters());
    }
  }

  Future<void> _applyFilters(ApplyFilters event, Emitter<FilterState> emit) async {
    if (learningTracksBloc != null) {
      learningTracksBloc!.add(
        learning_tracks_bloc.ApplyFilterToLtList(_filterUpdatedState.filterModel),
      );
    } else if (trainingsBloc != null) {
      trainingsBloc!.add(trainings_bloc.ApplyFilterToList(_filterUpdatedState.filterModel));
    }
    emit(FiltersApplied(_filterUpdatedState.filterModel));
  }

  Future<void> _filtersNotApplied(DontApplyFilters event, Emitter<FilterState> emit) async {
    emit(const FiltersNotApplied());
  }

  FilterUpdated get _filterUpdatedState => (state is FilterUpdated)
      ? (state as FilterUpdated)
      : throw Exception('state should be filterUpdatedState');
}
