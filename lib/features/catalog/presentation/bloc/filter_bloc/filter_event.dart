part of 'filter_bloc.dart';

abstract class FilterEvent {
  const FilterEvent();
}

class InitFilterPage extends FilterEvent {
  const InitFilterPage(this.filterModel);

  final FilterModel filterModel;
}

class SetSkill extends FilterEvent {
  const SetSkill(this.skillFilterModel);

  final SkillFilterModel skillFilterModel;
}

class SetTrainingType extends FilterEvent {
  const SetTrainingType(this.trainingTypeFilterModel);

  final TrainingTypeFilterModel trainingTypeFilterModel;
}

class SetLanguage extends FilterEvent {
  const SetLanguage(this.language);

  final LanguageFilterModel language;
}

class SetDuration extends FilterEvent {
  const SetDuration(this.durationFilterModel);

  final DurationFilterModel durationFilterModel;
}

class SetSectors extends FilterEvent {
  const SetSectors({this.sectorFilterModel = const SectorFilterModel(), this.sectorId});

  final SectorFilterModel sectorFilterModel;
  final String? sectorId;
}

class SetTrainingProviders extends FilterEvent {
  const SetTrainingProviders({
    this.trainingProviderFilterModel = const TrainingProviderFilterModel(),
    this.trainingProviderId,
  });

  final TrainingProviderFilterModel trainingProviderFilterModel;
  final String? trainingProviderId;
}

class ResetAllFilter extends FilterEvent {
  const ResetAllFilter();
}

class ApplyFilters extends FilterEvent {
  const ApplyFilters();
}

class DontApplyFilters extends FilterEvent {
  const DontApplyFilters();
}

class SetLocations extends FilterEvent {
  const SetLocations({this.locationId});

  final String? locationId;
}
