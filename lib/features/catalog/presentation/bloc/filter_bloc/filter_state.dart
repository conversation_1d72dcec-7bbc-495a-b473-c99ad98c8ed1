part of 'filter_bloc.dart';

abstract class FilterState extends Equatable {
  const FilterState();
}

class FilterUpdated extends FilterState {
  const FilterUpdated(this.filterModel, {this.reset = false, this.limitWarning = false});

  final FilterModel filterModel;
  final bool reset;
  final bool limitWarning;

  @override
  List<Object> get props => [filterModel, reset, limitWarning];
}

class FiltersApplied extends FilterState {
  const FiltersApplied(this.filterModel);

  final FilterModel filterModel;

  @override
  List<Object> get props => [filterModel];
}

class FiltersNotApplied extends FilterState {
  const FiltersNotApplied();

  @override
  List<Object> get props => [];
}
