part of 'sector_filter_bloc.dart';

abstract class SectorFilterEvent {
  const SectorFilterEvent();
}

class LoadSectors extends SectorFilterEvent {
  const LoadSectors({required this.locale});

  final String locale;
}

class LoadActiveSectors extends SectorFilterEvent {
  const LoadActiveSectors({
    required this.locale,
    this.sort,
    this.shown,
  });

  final String locale;
  final List<String>? sort;
  final bool? shown;
}
