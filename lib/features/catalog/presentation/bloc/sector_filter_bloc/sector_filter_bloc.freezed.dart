// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sector_filter_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SectorFilterState {
  ActiveSectorResponse? get activeSectorResponse;
  List<String> get selectedSectorIds;
  bool get isLoadingActiveSectors;
  String get activeError;

  /// Create a copy of SectorFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SectorFilterStateCopyWith<SectorFilterState> get copyWith =>
      _$SectorFilterStateCopyWithImpl<SectorFilterState>(this as SectorFilterState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SectorFilterState &&
            (identical(other.activeSectorResponse, activeSectorResponse) ||
                other.activeSectorResponse == activeSectorResponse) &&
            const DeepCollectionEquality().equals(other.selectedSectorIds, selectedSectorIds) &&
            (identical(other.isLoadingActiveSectors, isLoadingActiveSectors) ||
                other.isLoadingActiveSectors == isLoadingActiveSectors) &&
            (identical(other.activeError, activeError) || other.activeError == activeError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, activeSectorResponse,
      const DeepCollectionEquality().hash(selectedSectorIds), isLoadingActiveSectors, activeError);

  @override
  String toString() {
    return 'SectorFilterState(activeSectorResponse: $activeSectorResponse, selectedSectorIds: $selectedSectorIds, isLoadingActiveSectors: $isLoadingActiveSectors, activeError: $activeError)';
  }
}

/// @nodoc
abstract mixin class $SectorFilterStateCopyWith<$Res> {
  factory $SectorFilterStateCopyWith(
          SectorFilterState value, $Res Function(SectorFilterState) _then) =
      _$SectorFilterStateCopyWithImpl;
  @useResult
  $Res call(
      {ActiveSectorResponse? activeSectorResponse,
      List<String> selectedSectorIds,
      bool isLoadingActiveSectors,
      String activeError});
}

/// @nodoc
class _$SectorFilterStateCopyWithImpl<$Res> implements $SectorFilterStateCopyWith<$Res> {
  _$SectorFilterStateCopyWithImpl(this._self, this._then);

  final SectorFilterState _self;
  final $Res Function(SectorFilterState) _then;

  /// Create a copy of SectorFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeSectorResponse = freezed,
    Object? selectedSectorIds = null,
    Object? isLoadingActiveSectors = null,
    Object? activeError = null,
  }) {
    return _then(_self.copyWith(
      activeSectorResponse: freezed == activeSectorResponse
          ? _self.activeSectorResponse
          : activeSectorResponse // ignore: cast_nullable_to_non_nullable
              as ActiveSectorResponse?,
      selectedSectorIds: null == selectedSectorIds
          ? _self.selectedSectorIds
          : selectedSectorIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoadingActiveSectors: null == isLoadingActiveSectors
          ? _self.isLoadingActiveSectors
          : isLoadingActiveSectors // ignore: cast_nullable_to_non_nullable
              as bool,
      activeError: null == activeError
          ? _self.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _SectorFilterState implements SectorFilterState {
  const _SectorFilterState(
      {this.activeSectorResponse,
      final List<String> selectedSectorIds = const [],
      this.isLoadingActiveSectors = false,
      this.activeError = ''})
      : _selectedSectorIds = selectedSectorIds;

  @override
  final ActiveSectorResponse? activeSectorResponse;
  final List<String> _selectedSectorIds;
  @override
  @JsonKey()
  List<String> get selectedSectorIds {
    if (_selectedSectorIds is EqualUnmodifiableListView) return _selectedSectorIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedSectorIds);
  }

  @override
  @JsonKey()
  final bool isLoadingActiveSectors;
  @override
  @JsonKey()
  final String activeError;

  /// Create a copy of SectorFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SectorFilterStateCopyWith<_SectorFilterState> get copyWith =>
      __$SectorFilterStateCopyWithImpl<_SectorFilterState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SectorFilterState &&
            (identical(other.activeSectorResponse, activeSectorResponse) ||
                other.activeSectorResponse == activeSectorResponse) &&
            const DeepCollectionEquality().equals(other._selectedSectorIds, _selectedSectorIds) &&
            (identical(other.isLoadingActiveSectors, isLoadingActiveSectors) ||
                other.isLoadingActiveSectors == isLoadingActiveSectors) &&
            (identical(other.activeError, activeError) || other.activeError == activeError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, activeSectorResponse,
      const DeepCollectionEquality().hash(_selectedSectorIds), isLoadingActiveSectors, activeError);

  @override
  String toString() {
    return 'SectorFilterState(activeSectorResponse: $activeSectorResponse, selectedSectorIds: $selectedSectorIds, isLoadingActiveSectors: $isLoadingActiveSectors, activeError: $activeError)';
  }
}

/// @nodoc
abstract mixin class _$SectorFilterStateCopyWith<$Res> implements $SectorFilterStateCopyWith<$Res> {
  factory _$SectorFilterStateCopyWith(
          _SectorFilterState value, $Res Function(_SectorFilterState) _then) =
      __$SectorFilterStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ActiveSectorResponse? activeSectorResponse,
      List<String> selectedSectorIds,
      bool isLoadingActiveSectors,
      String activeError});
}

/// @nodoc
class __$SectorFilterStateCopyWithImpl<$Res> implements _$SectorFilterStateCopyWith<$Res> {
  __$SectorFilterStateCopyWithImpl(this._self, this._then);

  final _SectorFilterState _self;
  final $Res Function(_SectorFilterState) _then;

  /// Create a copy of SectorFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activeSectorResponse = freezed,
    Object? selectedSectorIds = null,
    Object? isLoadingActiveSectors = null,
    Object? activeError = null,
  }) {
    return _then(_SectorFilterState(
      activeSectorResponse: freezed == activeSectorResponse
          ? _self.activeSectorResponse
          : activeSectorResponse // ignore: cast_nullable_to_non_nullable
              as ActiveSectorResponse?,
      selectedSectorIds: null == selectedSectorIds
          ? _self._selectedSectorIds
          : selectedSectorIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoadingActiveSectors: null == isLoadingActiveSectors
          ? _self.isLoadingActiveSectors
          : isLoadingActiveSectors // ignore: cast_nullable_to_non_nullable
              as bool,
      activeError: null == activeError
          ? _self.activeError
          : activeError // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
