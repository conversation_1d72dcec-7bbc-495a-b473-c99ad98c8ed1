import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart';

part 'sector_filter_bloc.freezed.dart';
part 'sector_filter_event.dart';
part 'sector_filter_state.dart';

class SectorFilterBloc extends Bloc<SectorFilterEvent, SectorFilterState> {
  SectorFilterBloc({required SectorRepository sectorRepository, required FilterBloc filterBloc})
      : _sectorRepository = sectorRepository,
        _filterBloc = filterBloc,
        super(const SectorFilterState()) {
    on<LoadActiveSectors>(_loadActiveSectors);
  }

  final SectorRepository _sectorRepository;
  final FilterBloc _filterBloc;

  Future<void> _loadActiveSectors(LoadActiveSectors event, Emitter<SectorFilterState> emit) async {
    final selectedSectorIds = _filterBloc.state is FilterUpdated
        ? ((_filterBloc.state as FilterUpdated).filterModel.sectorFilterModel.selectedSectorIds)
        : <String>[];

    emit(
      state.copyWith(
        isLoadingActiveSectors: true,
        selectedSectorIds: selectedSectorIds,
        activeError: '',
      ),
    );

    try {
      final activeSectorResponse = await _sectorRepository.getActiveSectors(
        locale: event.locale,
        sort: event.sort,
        shown: event.shown,
      );

      // Filter out inactive sectors
      final activeSectors = activeSectorResponse.content
          .where((sector) => sector.status == Constants.ACTIVE)
          .toList();

      // Create a new response with only active sectors
      final filteredResponse = ActiveSectorResponse(
        content: activeSectors,
        page: activeSectorResponse.page,
        size: activeSectorResponse.size,
        totalRecords: activeSectors.length, // Update total records to match filtered count
        totalPages: activeSectorResponse.totalPages,
      );

      emit(
        state.copyWith(
          activeSectorResponse: filteredResponse,
          isLoadingActiveSectors: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          activeError: e.toString(),
          isLoadingActiveSectors: false,
        ),
      );
    }
  }
}
