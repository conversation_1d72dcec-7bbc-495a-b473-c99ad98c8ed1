part of 'learning_tracks_bloc.dart';

abstract class LearningTracksState extends Equatable {
  const LearningTracksState();
}

class LearningTracksInitial extends LearningTracksState {
  @override
  List<Object> get props => [];
}

class LearningTracksLoading extends LearningTracksState {
  const LearningTracksLoading();

  @override
  List<Object?> get props => [];
}

class LearningTracksLoaded extends LearningTracksState {
  const LearningTracksLoaded(
    this.learningTracksModel, {
    required this.currentPage,
    required this.isLastPage,
    required this.sortedBy,
    required this.appliedFilter,
  });

  final LearningTracksModel learningTracksModel;
  final int currentPage;
  final bool isLastPage;
  final SortOptionsEnum sortedBy;
  final FilterModel appliedFilter;

  @override
  List<Object?> get props => [learningTracksModel, currentPage, sortedBy];
}

class LearningTracksError extends LearningTracksState {
  const LearningTracksError(this.errorMsg);
  final String errorMsg;

  @override
  List<Object?> get props => [errorMsg];
}
