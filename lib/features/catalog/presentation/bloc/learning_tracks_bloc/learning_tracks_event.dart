part of 'learning_tracks_bloc.dart';

abstract class LearningTracksEvent {
  const LearningTracksEvent();
}

class GetLearningTracksEvent extends LearningTracksEvent {
  const GetLearningTracksEvent({
    this.requestParams = const LearningTracksRequestParams.fallback(),
    this.searchKey,
  });

  final LearningTracksRequestParams requestParams;
  final String? searchKey;
}

class FetchMoreEvent extends LearningTracksEvent {
  const FetchMoreEvent();
}

class ApplyFilterToLtList extends LearningTracksEvent {
  const ApplyFilterToLtList(this.filterModel);

  final FilterModel filterModel;
}

class ApplySortToList extends LearningTracksEvent {
  const ApplySortToList(this.sortOption);

  final SortOptionsEnum sortOption;
}

class RefreshLearningTracksEvent extends LearningTracksEvent {
  const RefreshLearningTracksEvent();
}
