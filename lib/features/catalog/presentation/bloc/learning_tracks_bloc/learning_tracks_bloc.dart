import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/catalog/data/converter/filters_converter.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/learning_tracks_repository.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

part 'learning_tracks_event.dart';

part 'learning_tracks_state.dart';

@lazySingleton
class LearningTracksBloc extends Bloc<LearningTracksEvent, LearningTracksState> {
  LearningTracksBloc({required LearningTracksRepository learningTracksRepository})
      : _learningTracksRepository = learningTracksRepository,
        super(LearningTracksInitial()) {
    on<GetLearningTracksEvent>(_getLearningTracks);
    on<FetchMoreEvent>(_fetchMore);
    on<ApplyFilterToLtList>(_applyFilterToList);
    on<ApplySortToList>(_applySortToList);
    on<RefreshLearningTracksEvent>(_refreshLearningTracks);
  }

  final LearningTracksRepository _learningTracksRepository;
  LearningTracksRequestParams _currentRequestParams = const LearningTracksRequestParams.fallback();

  Future<void> _getLearningTracks(
    GetLearningTracksEvent event,
    Emitter<LearningTracksState> emit,
  ) async {
    emit(const LearningTracksLoading());

    //stored to reuse it in fetchMore event
    _currentRequestParams = event.requestParams.copyWith(title: event.searchKey);

    await _learningTracksRepository.getLearningTracks(_currentRequestParams).errorHandler(
          onSuccess: (learningTracksModel) async {
            final isLastPage = _isLastPage(learningTracksModel);
            emit(
              LearningTracksLoaded(
                learningTracksModel,
                currentPage: 0,
                isLastPage: isLastPage,
                sortedBy: _currentRequestParams.sortBy,
                appliedFilter: _currentRequestParams.filterModel,
              ),
            );
          },
          onError: (errorMsg) => emit(LearningTracksError(errorMsg)),
        );
  }

  Future<void> _fetchMore(FetchMoreEvent event, Emitter<LearningTracksState> emit) async {
    if (state is LearningTracksLoaded) {
      final currentState = state as LearningTracksLoaded;
      if (_isLastPage(currentState.learningTracksModel)) return;

      _currentRequestParams = _currentRequestParams.copyWith(page: currentState.currentPage + 1);

      await _learningTracksRepository.getLearningTracks(_currentRequestParams).errorHandler(
            onSuccess: (learningTracksModel) async {
              final newLearningTracksList = [
                ...currentState.learningTracksModel.learningTracksList,
                ...learningTracksModel.learningTracksList,
              ];

              emit(
                LearningTracksLoaded(
                  learningTracksModel.copyWith(learningTracksList: newLearningTracksList),
                  currentPage: currentState.currentPage + 1,
                  isLastPage: _isLastPage(learningTracksModel),
                  sortedBy: _currentRequestParams.sortBy,
                  appliedFilter: _currentRequestParams.filterModel,
                ),
              );
            },
            onError: (errorMsg) => emit(LearningTracksError(errorMsg)),
          );
    }
  }

  Future<void> _refreshLearningTracks(
    RefreshLearningTracksEvent event,
    Emitter<LearningTracksState> emit,
  ) async {
    // Preserve current filters and sorting while refreshing data
    final refreshParams = _currentRequestParams.copyWith(page: 0);

    await _learningTracksRepository.getLearningTracks(refreshParams).errorHandler(
          onSuccess: (learningTracksModel) async {
            final isLastPage = _isLastPage(learningTracksModel);
            emit(
              LearningTracksLoaded(
                learningTracksModel,
                currentPage: 0,
                isLastPage: isLastPage,
                sortedBy: _currentRequestParams.sortBy,
                appliedFilter: _currentRequestParams.filterModel,
              ),
            );
          },
          onError: (errorMsg) => emit(LearningTracksError(errorMsg)),
        );
  }

  bool _isLastPage(LearningTracksModel learningTracksModel) {
    return (learningTracksModel.page ?? 0) >= ((learningTracksModel.totalPages ?? 0) - 1);
  }

  Future<void> _applyFilterToList(
    ApplyFilterToLtList event,
    Emitter<LearningTracksState> emit,
  ) async {
    add(
      GetLearningTracksEvent(
        requestParams: FilterModelToLearningTracksRequestParamsConverter()
            .convert(event.filterModel)
            //adding previously applied sort
            .copyWith(
              sortBy: _currentRequestParams.sortBy,
              title: _currentRequestParams.title,
              page: 0,
            ),
      ),
    );
  }

  Future<void> _applySortToList(ApplySortToList event, Emitter<LearningTracksState> emit) async {
    /// if sort is already applied then no need to call api
    if (event.sortOption == _currentRequestParams.sortBy) return;

    add(
      GetLearningTracksEvent(
        requestParams: _currentRequestParams.copyWith(sortBy: event.sortOption, page: 0),
      ),
    );
  }
}
