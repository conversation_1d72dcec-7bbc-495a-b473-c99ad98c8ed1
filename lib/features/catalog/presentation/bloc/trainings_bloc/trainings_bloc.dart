import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/catalog/data/converter/filters_converter.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/trainings_repositories.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

part 'trainings_event.dart';

part 'trainings_state.dart';

@singleton
class TrainingsBloc extends Bloc<TrainingsEvent, TrainingsState> {
  TrainingsBloc({required TrainingsRepository trainingsRepository})
      : _trainingsRepository = trainingsRepository,
        super(const TrainingsInitial()) {
    on<GetTrainingsListEvent>(_getTrainings);
    on<FetchMoreEvent>(_fetchMore);
    on<ApplyFilterToList>(_applyFilterToList);
    on<ApplySortToList>(_applySortToList);
    on<RefreshTrainingsEvent>(_refreshTrainings);
  }

  final TrainingsRepository _trainingsRepository;
  TrainingsRequestParams _currentRequestParams = const TrainingsRequestParams.fallback();

  Future<void> _getTrainings(GetTrainingsListEvent event, Emitter<TrainingsState> emit) async {
    emit(const TrainingsLoading());

    //stored to reuse it in fetchMore event
    _currentRequestParams = event.requestParams.copyWith(title: event.searchKey);

    await _trainingsRepository.getTrainings(_currentRequestParams).errorHandler(
          onSuccess: (trainingsModel) async {
            final isLastPage = _isLastPage(trainingsModel);
            emit(
              TrainingsLoaded(
                trainingsModel,
                currentPage: 0,
                isLastPage: isLastPage,
                sortedBy: _currentRequestParams.sortBy,
                appliedFilter: _currentRequestParams.filterModel,
              ),
            );
          },
          onError: (errorMsg) => emit(TrainingsError(errorMsg)),
        );
  }

  Future<void> _fetchMore(FetchMoreEvent event, Emitter<TrainingsState> emit) async {
    if (state is TrainingsLoaded) {
      final currentState = state as TrainingsLoaded;
      if (_isLastPage(currentState.trainingsModel)) return;

      _currentRequestParams = _currentRequestParams.copyWith(page: currentState.currentPage + 1);

      await _trainingsRepository.getTrainings(_currentRequestParams).errorHandler(
            onSuccess: (trainingsModel) async {
              final newTrainingsList = [
                ...currentState.trainingsModel.trainingsList,
                ...trainingsModel.trainingsList,
              ];

              emit(
                TrainingsLoaded(
                  trainingsModel.copyWith(trainingsList: newTrainingsList),
                  currentPage: currentState.currentPage + 1,
                  isLastPage: _isLastPage(trainingsModel),
                  sortedBy: _currentRequestParams.sortBy,
                  appliedFilter: _currentRequestParams.filterModel,
                ),
              );
            },
            onError: (errorMsg) => emit(TrainingsError(errorMsg)),
          );
    }
  }

  Future<void> _refreshTrainings(RefreshTrainingsEvent event, Emitter<TrainingsState> emit) async {
    // Preserve current filters and sorting while refreshing data
    final refreshParams = _currentRequestParams.copyWith(page: 0);

    await _trainingsRepository.getTrainings(refreshParams).errorHandler(
          onSuccess: (trainingsModel) async {
            final isLastPage = _isLastPage(trainingsModel);
            emit(
              TrainingsLoaded(
                trainingsModel,
                currentPage: 0,
                isLastPage: isLastPage,
                sortedBy: _currentRequestParams.sortBy,
                appliedFilter: _currentRequestParams.filterModel,
              ),
            );
          },
          onError: (errorMsg) => emit(TrainingsError(errorMsg)),
        );
  }

  bool _isLastPage(TrainingsModel trainingsModel) {
    return (trainingsModel.page ?? 0) >= ((trainingsModel.totalPages ?? 0) - 1);
  }

  Future<void> _applyFilterToList(ApplyFilterToList event, Emitter<TrainingsState> emit) async {
    add(
      GetTrainingsListEvent(
        requestParams: FilterModelToTrainingsRequestParamsConverter()
            .convert(event.filterModel)
            //adding previously applied sort
            .copyWith(
              sortBy: _currentRequestParams.sortBy,
              title: _currentRequestParams.title,
              page: 0,
            ),
      ),
    );
  }

  Future<void> _applySortToList(ApplySortToList event, Emitter<TrainingsState> emit) async {
    /// if sort is already applied then no need to call api
    if (event.sortOption == _currentRequestParams.sortBy) return;

    add(
      GetTrainingsListEvent(
        requestParams: _currentRequestParams.copyWith(sortBy: event.sortOption, page: 0),
      ),
    );
  }
}
