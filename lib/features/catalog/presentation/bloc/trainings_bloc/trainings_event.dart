part of 'trainings_bloc.dart';

abstract class TrainingsEvent {
  const TrainingsEvent();
}

class GetTrainingsListEvent extends TrainingsEvent {
  const GetTrainingsListEvent({
    this.requestParams = const TrainingsRequestParams.fallback(),
    this.searchKey,
  });

  final TrainingsRequestParams requestParams;
  final String? searchKey;
}

class FetchMoreEvent extends TrainingsEvent {
  const FetchMoreEvent();
}

class ApplyFilterToList extends TrainingsEvent {
  const ApplyFilterToList(this.filterModel);

  final FilterModel filterModel;
}

class ApplySortToList extends TrainingsEvent {
  const ApplySortToList(this.sortOption);

  final SortOptionsEnum sortOption;
}

class RefreshTrainingsEvent extends TrainingsEvent {
  const RefreshTrainingsEvent();
}
