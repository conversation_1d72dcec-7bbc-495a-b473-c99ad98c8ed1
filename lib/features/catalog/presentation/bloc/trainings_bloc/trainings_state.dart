part of 'trainings_bloc.dart';

abstract class TrainingsState extends Equatable {
  const TrainingsState();
}

class TrainingsInitial extends TrainingsState {
  const TrainingsInitial();

  @override
  List<Object?> get props => [];
}

class TrainingsLoading extends TrainingsState {
  const TrainingsLoading();

  @override
  List<Object?> get props => [];
}

class TrainingsLoaded extends TrainingsState {
  const TrainingsLoaded(
    this.trainingsModel, {
    required this.currentPage,
    required this.isLastPage,
    required this.sortedBy,
    this.appliedFilter = const FilterModel(),
  });

  final TrainingsModel trainingsModel;
  final int currentPage;
  final bool isLastPage;
  final SortOptionsEnum sortedBy;
  final FilterModel appliedFilter;

  @override
  List<Object?> get props => [trainingsModel, currentPage, sortedBy, appliedFilter];
}

class TrainingsError extends TrainingsState {
  const TrainingsError(this.message);
  final String message;

  @override
  List<Object?> get props => [message];
}
