import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';

part 'location_filter_state.freezed.dart';

@freezed
abstract class LocationFilterState with _$LocationFilterState {
  const factory LocationFilterState({
    @Default([]) List<LocationModel> locations,
    @Default([]) List<String> selectedLocationIds,
    @Default(true) bool isLoading,
    @Default('') String error,
  }) = _LocationFilterState;
}
