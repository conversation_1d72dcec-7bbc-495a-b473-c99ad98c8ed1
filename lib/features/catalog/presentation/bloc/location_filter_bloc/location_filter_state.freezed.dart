// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationFilterState {
  List<LocationModel> get locations;
  List<String> get selectedLocationIds;
  bool get isLoading;
  String get error;

  /// Create a copy of LocationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LocationFilterStateCopyWith<LocationFilterState> get copyWith =>
      _$LocationFilterStateCopyWithImpl<LocationFilterState>(
          this as LocationFilterState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LocationFilterState &&
            const DeepCollectionEquality().equals(other.locations, locations) &&
            const DeepCollectionEquality().equals(other.selectedLocationIds, selectedLocationIds) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(locations),
      const DeepCollectionEquality().hash(selectedLocationIds), isLoading, error);

  @override
  String toString() {
    return 'LocationFilterState(locations: $locations, selectedLocationIds: $selectedLocationIds, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class $LocationFilterStateCopyWith<$Res> {
  factory $LocationFilterStateCopyWith(
          LocationFilterState value, $Res Function(LocationFilterState) _then) =
      _$LocationFilterStateCopyWithImpl;
  @useResult
  $Res call(
      {List<LocationModel> locations,
      List<String> selectedLocationIds,
      bool isLoading,
      String error});
}

/// @nodoc
class _$LocationFilterStateCopyWithImpl<$Res> implements $LocationFilterStateCopyWith<$Res> {
  _$LocationFilterStateCopyWithImpl(this._self, this._then);

  final LocationFilterState _self;
  final $Res Function(LocationFilterState) _then;

  /// Create a copy of LocationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? locations = null,
    Object? selectedLocationIds = null,
    Object? isLoading = null,
    Object? error = null,
  }) {
    return _then(_self.copyWith(
      locations: null == locations
          ? _self.locations
          : locations // ignore: cast_nullable_to_non_nullable
              as List<LocationModel>,
      selectedLocationIds: null == selectedLocationIds
          ? _self.selectedLocationIds
          : selectedLocationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _LocationFilterState implements LocationFilterState {
  const _LocationFilterState(
      {final List<LocationModel> locations = const [],
      final List<String> selectedLocationIds = const [],
      this.isLoading = true,
      this.error = ''})
      : _locations = locations,
        _selectedLocationIds = selectedLocationIds;

  final List<LocationModel> _locations;
  @override
  @JsonKey()
  List<LocationModel> get locations {
    if (_locations is EqualUnmodifiableListView) return _locations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_locations);
  }

  final List<String> _selectedLocationIds;
  @override
  @JsonKey()
  List<String> get selectedLocationIds {
    if (_selectedLocationIds is EqualUnmodifiableListView) return _selectedLocationIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedLocationIds);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String error;

  /// Create a copy of LocationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LocationFilterStateCopyWith<_LocationFilterState> get copyWith =>
      __$LocationFilterStateCopyWithImpl<_LocationFilterState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LocationFilterState &&
            const DeepCollectionEquality().equals(other._locations, _locations) &&
            const DeepCollectionEquality()
                .equals(other._selectedLocationIds, _selectedLocationIds) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, const DeepCollectionEquality().hash(_locations),
      const DeepCollectionEquality().hash(_selectedLocationIds), isLoading, error);

  @override
  String toString() {
    return 'LocationFilterState(locations: $locations, selectedLocationIds: $selectedLocationIds, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$LocationFilterStateCopyWith<$Res>
    implements $LocationFilterStateCopyWith<$Res> {
  factory _$LocationFilterStateCopyWith(
          _LocationFilterState value, $Res Function(_LocationFilterState) _then) =
      __$LocationFilterStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<LocationModel> locations,
      List<String> selectedLocationIds,
      bool isLoading,
      String error});
}

/// @nodoc
class __$LocationFilterStateCopyWithImpl<$Res> implements _$LocationFilterStateCopyWith<$Res> {
  __$LocationFilterStateCopyWithImpl(this._self, this._then);

  final _LocationFilterState _self;
  final $Res Function(_LocationFilterState) _then;

  /// Create a copy of LocationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? locations = null,
    Object? selectedLocationIds = null,
    Object? isLoading = null,
    Object? error = null,
  }) {
    return _then(_LocationFilterState(
      locations: null == locations
          ? _self._locations
          : locations // ignore: cast_nullable_to_non_nullable
              as List<LocationModel>,
      selectedLocationIds: null == selectedLocationIds
          ? _self._selectedLocationIds
          : selectedLocationIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
