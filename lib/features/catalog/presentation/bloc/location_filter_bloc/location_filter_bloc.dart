import 'package:bloc/bloc.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/location_repository.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_state.dart';

class LocationFilterBloc extends Bloc<LocationFilterEvent, LocationFilterState> {
  LocationFilterBloc({
    required LocationRepository locationRepository,
    required FilterBloc filterBloc,
  })  : _locationRepository = locationRepository,
        _filterBloc = filterBloc,
        super(const LocationFilterState()) {
    on<LoadLocations>(_loadLocations);
  }

  final LocationRepository _locationRepository;
  final FilterBloc _filterBloc;

  Future<void> _loadLocations(
    LoadLocations event,
    Emitter<LocationFilterState> emit,
  ) async {
    final selectedLocationIds = _filterBloc.state is FilterUpdated
        ? (_filterBloc.state as FilterUpdated).filterModel.locationFilterModel.selectedLocationIds
        : <String>[];

    emit(
      state.copyWith(isLoading: true, error: '', selectedLocationIds: selectedLocationIds),
    );

    await _locationRepository.getLocations(event.locale).errorHandler(
      onSuccess: (locations) async {
        emit(state.copyWith(locations: locations, isLoading: false));
      },
      onError: (error) {
        emit(state.copyWith(error: error, isLoading: false));
      },
    );
  }
}
