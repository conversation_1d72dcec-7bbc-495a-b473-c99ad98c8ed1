// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'organization_filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$OrganizationFilterState {
  List<OrganizationModel> get organizations => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String get error => throw _privateConstructorUsedError;

  /// Create a copy of OrganizationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrganizationFilterStateCopyWith<OrganizationFilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrganizationFilterStateCopyWith<$Res> {
  factory $OrganizationFilterStateCopyWith(
    OrganizationFilterState value,
    $Res Function(OrganizationFilterState) then,
  ) = _$OrganizationFilterStateCopyWithImpl<$Res, OrganizationFilterState>;
  @useResult
  $Res call({List<OrganizationModel> organizations, bool isLoading, String error});
}

/// @nodoc
class _$OrganizationFilterStateCopyWithImpl<$Res, $Val extends OrganizationFilterState>
    implements $OrganizationFilterStateCopyWith<$Res> {
  _$OrganizationFilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrganizationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? organizations = null, Object? isLoading = null, Object? error = null}) {
    return _then(
      _value.copyWith(
        organizations: null == organizations
            ? _value.organizations
            : organizations // ignore: cast_nullable_to_non_nullable
                as List<OrganizationModel>,
        isLoading: null == isLoading
            ? _value.isLoading
            : isLoading // ignore: cast_nullable_to_non_nullable
                as bool,
        error: null == error
            ? _value.error
            : error // ignore: cast_nullable_to_non_nullable
                as String,
      ) as $Val,
    );
  }
}

/// @nodoc
abstract class _$$OrganizationFilterStateImplCopyWith<$Res>
    implements $OrganizationFilterStateCopyWith<$Res> {
  factory _$$OrganizationFilterStateImplCopyWith(
    _$OrganizationFilterStateImpl value,
    $Res Function(_$OrganizationFilterStateImpl) then,
  ) = __$$OrganizationFilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<OrganizationModel> organizations, bool isLoading, String error});
}

/// @nodoc
class __$$OrganizationFilterStateImplCopyWithImpl<$Res>
    extends _$OrganizationFilterStateCopyWithImpl<$Res, _$OrganizationFilterStateImpl>
    implements _$$OrganizationFilterStateImplCopyWith<$Res> {
  __$$OrganizationFilterStateImplCopyWithImpl(
    _$OrganizationFilterStateImpl _value,
    $Res Function(_$OrganizationFilterStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of OrganizationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? organizations = null, Object? isLoading = null, Object? error = null}) {
    return _then(
      _$OrganizationFilterStateImpl(
        organizations: null == organizations
            ? _value._organizations
            : organizations // ignore: cast_nullable_to_non_nullable
                as List<OrganizationModel>,
        isLoading: null == isLoading
            ? _value.isLoading
            : isLoading // ignore: cast_nullable_to_non_nullable
                as bool,
        error: null == error
            ? _value.error
            : error // ignore: cast_nullable_to_non_nullable
                as String,
      ),
    );
  }
}

/// @nodoc

class _$OrganizationFilterStateImpl implements _OrganizationFilterState {
  const _$OrganizationFilterStateImpl({
    final List<OrganizationModel> organizations = const [],
    this.isLoading = true,
    this.error = '',
  }) : _organizations = organizations;

  final List<OrganizationModel> _organizations;
  @override
  @JsonKey()
  List<OrganizationModel> get organizations {
    if (_organizations is EqualUnmodifiableListView) return _organizations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_organizations);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String error;

  @override
  String toString() {
    return 'OrganizationFilterState(organizations: $organizations, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrganizationFilterStateImpl &&
            const DeepCollectionEquality().equals(other._organizations, _organizations) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
        runtimeType,
        const DeepCollectionEquality().hash(_organizations),
        isLoading,
        error,
      );

  /// Create a copy of OrganizationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrganizationFilterStateImplCopyWith<_$OrganizationFilterStateImpl> get copyWith =>
      __$$OrganizationFilterStateImplCopyWithImpl<_$OrganizationFilterStateImpl>(this, _$identity);
}

abstract class _OrganizationFilterState implements OrganizationFilterState {
  const factory _OrganizationFilterState({
    final List<OrganizationModel> organizations,
    final bool isLoading,
    final String error,
  }) = _$OrganizationFilterStateImpl;

  @override
  List<OrganizationModel> get organizations;
  @override
  bool get isLoading;
  @override
  String get error;

  /// Create a copy of OrganizationFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrganizationFilterStateImplCopyWith<_$OrganizationFilterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
