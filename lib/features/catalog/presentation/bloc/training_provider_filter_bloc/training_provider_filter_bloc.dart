import 'package:bloc/bloc.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_state.dart';

class TrainingProviderFilterBloc
    extends Bloc<TrainingProviderFilterEvent, TrainingProviderFilterState> {
  TrainingProviderFilterBloc({
    required GetSortedTrainingProvidersUseCase getSortedTrainingProvidersUseCase,
    required FilterBloc filterBloc,
  })  : _getSortedTrainingProvidersUseCase = getSortedTrainingProvidersUseCase,
        _filterBloc = filterBloc,
        super(const TrainingProviderFilterState()) {
    on<LoadTrainingProviders>(_loadTrainingProviders);
  }

  final GetSortedTrainingProvidersUseCase _getSortedTrainingProvidersUseCase;
  final FilterBloc _filterBloc;

  Future<void> _loadTrainingProviders(
    LoadTrainingProviders event,
    Emitter<TrainingProviderFilterState> emit,
  ) async {
    final selectedTrainingProviderIds = _filterBloc.state is FilterUpdated
        ? (_filterBloc.state as FilterUpdated)
            .filterModel
            .trainingProviderFilterModel
            .selectedTrainingProviderIds
        : <String>[];

    emit(
      state.copyWith(
        isLoading: true,
        error: '',
        selectedTrainingProviderIds: selectedTrainingProviderIds,
      ),
    );

    await _getSortedTrainingProvidersUseCase(event.locale).errorHandler(
      onSuccess: (trainingProviders) async {
        emit(state.copyWith(trainingProviders: trainingProviders, isLoading: false));
      },
      onError: (error) {
        emit(state.copyWith(error: error, isLoading: false));
      },
    );
  }
}
