import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';

part 'training_provider_filter_state.freezed.dart';

@freezed
abstract class TrainingProviderFilterState with _$TrainingProviderFilterState {
  const factory TrainingProviderFilterState({
    @Default([]) List<TrainingProviderModel> trainingProviders,
    @Default([]) List<String> selectedTrainingProviderIds,
    @Default(true) bool isLoading,
    @Default('') String error,
  }) = _TrainingProviderFilterState;
}
