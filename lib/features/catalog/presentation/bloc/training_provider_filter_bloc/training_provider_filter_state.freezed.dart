// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'training_provider_filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TrainingProviderFilterState {
  List<TrainingProviderModel> get trainingProviders;
  List<String> get selectedTrainingProviderIds;
  bool get isLoading;
  String get error;

  /// Create a copy of TrainingProviderFilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TrainingProviderFilterStateCopyWith<TrainingProviderFilterState> get copyWith =>
      _$TrainingProviderFilterStateCopyWithImpl<TrainingProviderFilterState>(
          this as TrainingProviderFilterState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TrainingProviderFilterState &&
            const DeepCollectionEquality().equals(other.trainingProviders, trainingProviders) &&
            const DeepCollectionEquality()
                .equals(other.selectedTrainingProviderIds, selectedTrainingProviderIds) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(trainingProviders),
      const DeepCollectionEquality().hash(selectedTrainingProviderIds),
      isLoading,
      error);

  @override
  String toString() {
    return 'TrainingProviderFilterState(trainingProviders: $trainingProviders, selectedTrainingProviderIds: $selectedTrainingProviderIds, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class $TrainingProviderFilterStateCopyWith<$Res> {
  factory $TrainingProviderFilterStateCopyWith(
          TrainingProviderFilterState value, $Res Function(TrainingProviderFilterState) _then) =
      _$TrainingProviderFilterStateCopyWithImpl;
  @useResult
  $Res call(
      {List<TrainingProviderModel> trainingProviders,
      List<String> selectedTrainingProviderIds,
      bool isLoading,
      String error});
}

/// @nodoc
class _$TrainingProviderFilterStateCopyWithImpl<$Res>
    implements $TrainingProviderFilterStateCopyWith<$Res> {
  _$TrainingProviderFilterStateCopyWithImpl(this._self, this._then);

  final TrainingProviderFilterState _self;
  final $Res Function(TrainingProviderFilterState) _then;

  /// Create a copy of TrainingProviderFilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? trainingProviders = null,
    Object? selectedTrainingProviderIds = null,
    Object? isLoading = null,
    Object? error = null,
  }) {
    return _then(_self.copyWith(
      trainingProviders: null == trainingProviders
          ? _self.trainingProviders
          : trainingProviders // ignore: cast_nullable_to_non_nullable
              as List<TrainingProviderModel>,
      selectedTrainingProviderIds: null == selectedTrainingProviderIds
          ? _self.selectedTrainingProviderIds
          : selectedTrainingProviderIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _TrainingProviderFilterState implements TrainingProviderFilterState {
  const _TrainingProviderFilterState(
      {final List<TrainingProviderModel> trainingProviders = const [],
      final List<String> selectedTrainingProviderIds = const [],
      this.isLoading = true,
      this.error = ''})
      : _trainingProviders = trainingProviders,
        _selectedTrainingProviderIds = selectedTrainingProviderIds;

  final List<TrainingProviderModel> _trainingProviders;
  @override
  @JsonKey()
  List<TrainingProviderModel> get trainingProviders {
    if (_trainingProviders is EqualUnmodifiableListView) return _trainingProviders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_trainingProviders);
  }

  final List<String> _selectedTrainingProviderIds;
  @override
  @JsonKey()
  List<String> get selectedTrainingProviderIds {
    if (_selectedTrainingProviderIds is EqualUnmodifiableListView)
      return _selectedTrainingProviderIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedTrainingProviderIds);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String error;

  /// Create a copy of TrainingProviderFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TrainingProviderFilterStateCopyWith<_TrainingProviderFilterState> get copyWith =>
      __$TrainingProviderFilterStateCopyWithImpl<_TrainingProviderFilterState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TrainingProviderFilterState &&
            const DeepCollectionEquality().equals(other._trainingProviders, _trainingProviders) &&
            const DeepCollectionEquality()
                .equals(other._selectedTrainingProviderIds, _selectedTrainingProviderIds) &&
            (identical(other.isLoading, isLoading) || other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_trainingProviders),
      const DeepCollectionEquality().hash(_selectedTrainingProviderIds),
      isLoading,
      error);

  @override
  String toString() {
    return 'TrainingProviderFilterState(trainingProviders: $trainingProviders, selectedTrainingProviderIds: $selectedTrainingProviderIds, isLoading: $isLoading, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$TrainingProviderFilterStateCopyWith<$Res>
    implements $TrainingProviderFilterStateCopyWith<$Res> {
  factory _$TrainingProviderFilterStateCopyWith(
          _TrainingProviderFilterState value, $Res Function(_TrainingProviderFilterState) _then) =
      __$TrainingProviderFilterStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<TrainingProviderModel> trainingProviders,
      List<String> selectedTrainingProviderIds,
      bool isLoading,
      String error});
}

/// @nodoc
class __$TrainingProviderFilterStateCopyWithImpl<$Res>
    implements _$TrainingProviderFilterStateCopyWith<$Res> {
  __$TrainingProviderFilterStateCopyWithImpl(this._self, this._then);

  final _TrainingProviderFilterState _self;
  final $Res Function(_TrainingProviderFilterState) _then;

  /// Create a copy of TrainingProviderFilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? trainingProviders = null,
    Object? selectedTrainingProviderIds = null,
    Object? isLoading = null,
    Object? error = null,
  }) {
    return _then(_TrainingProviderFilterState(
      trainingProviders: null == trainingProviders
          ? _self._trainingProviders
          : trainingProviders // ignore: cast_nullable_to_non_nullable
              as List<TrainingProviderModel>,
      selectedTrainingProviderIds: null == selectedTrainingProviderIds
          ? _self._selectedTrainingProviderIds
          : selectedTrainingProviderIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: null == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
