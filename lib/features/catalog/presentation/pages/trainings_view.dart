import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/no_filtered_results.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/sort_filter_buttons_container.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/trainings_card.dart';
import 'package:national_skills_platform/features/catalog/utils/count_related_translations_util.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_header_panel.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/router.dart';

class TrainingsView extends StatefulWidget {
  const TrainingsView({this.searchKey, super.key});

  final String? searchKey;

  @override
  State<TrainingsView> createState() => _TrainingsViewState();
}

class _TrainingsViewState extends State<TrainingsView> {
  late TrainingsBloc trainingsBloc;
  late FilterBloc filterBloc;
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  List<TrainingContentModel> trainings = [];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    trainingsBloc = GetIt.instance.get<TrainingsBloc>();
    if (trainingsBloc.state is TrainingsInitial || trainingsBloc.state is TrainingsError) {
      trainingsBloc.add(GetTrainingsListEvent(searchKey: widget.searchKey));
    }
    filterBloc = FilterBloc(trainingsBloc: trainingsBloc);
  }

  @override
  void didUpdateWidget(covariant TrainingsView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchKey != oldWidget.searchKey) {
      trainingsBloc.add(GetTrainingsListEvent(searchKey: widget.searchKey));
    }
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_scrollListener)
      ..dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      trainingsBloc.add(const FetchMoreEvent());
    }
  }

  /// Forces the RefreshIndicator to show and start the refresh with animation
  void showRefreshIndicatorAndRefresh() {
    _refreshIndicatorKey.currentState?.show();
  }

  Future<void> _handleRefresh() {
    trainingsBloc.add(const RefreshTrainingsEvent());
    // Return a delayed future to give time for the refresh to complete
    return Future.delayed(const Duration(seconds: 2));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      body: BlocConsumer<TrainingsBloc, TrainingsState>(
        bloc: trainingsBloc,
        listener: (context, state) {
          if (state is TrainingsError) showAppToast(context, message: state.message);
        },
        builder: (context, state) {
          if (state is TrainingsError) return const SizedBox.shrink();

          if (state is TrainingsLoading) return const BuildLoader();

          if (state is TrainingsLoaded) {
            trainings = state.trainingsModel.trainingsList;
            final trainingsModel = state.trainingsModel;

            if (trainings.isEmpty) {
              return const NoFilteredResults();
            }

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getTotalResultsForSearchKeyText(
                          context,
                          state.trainingsModel.totalElements,
                          widget.searchKey,
                        ),
                        style: context.textTheme.textSmall.medium.copyWith(
                          color: widget.searchKey != null ? AppColors.additionalGrey : null,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => router.pushNamed(
                          Routes.searchPage.path,
                          extra: CourseType.training,
                        ),
                        child: Image.asset(
                          width: 24,
                          AssetsPath.searchIcon,
                          color: AppColors.greenAccentPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: RefreshIndicator(
                      key: _refreshIndicatorKey,
                      onRefresh: _handleRefresh,
                      child: ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemCount: trainings.length + (state.isLastPage ? 0 : 1),
                        itemBuilder: (context, index) {
                          if (_shouldShowLoader(index, trainingsModel, state.isLastPage)) {
                            return const BuildLoader();
                          }

                          return TrainingCard(
                            trainingModel: trainings[index],
                            onBackFromDetailsPage: () {
                              showRefreshIndicatorAndRefresh();
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return const Center(child: Text('Unknown state'));
        },
      ),
      floatingActionButton: BlocSelector<TrainingsBloc, TrainingsState, String>(
        bloc: trainingsBloc,
        selector: (state) {
          if (state is TrainingsLoaded) {
            return state.sortedBy.name;
          }
          return '';
        },
        builder: (context, sortedBy) {
          return SortFilterButtonsContainer(
            filterBloc: filterBloc,
            trainingsBloc: trainingsBloc,
            isSortApplied: sortedBy.isNotEmpty,
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  bool _shouldShowLoader(int index, TrainingsModel trainingsModel, bool isLastPage) {
    return index == trainingsModel.trainingsList.length && !isLastPage;
  }
}
