import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/no_filtered_results.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/learning_track_card.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/sort_filter_buttons_container.dart';
import 'package:national_skills_platform/features/catalog/utils/count_related_translations_util.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/router.dart';

class LearningTracksView extends StatefulWidget {
  const LearningTracksView({this.searchKey, super.key});

  final String? searchKey;

  @override
  State<LearningTracksView> createState() => _LearningTracksViewState();
}

class _LearningTracksViewState extends State<LearningTracksView> {
  late LearningTracksBloc learningTracksBloc;
  late FilterBloc filterBloc;
  final _scrollController = ScrollController();
  final _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  List<LearningTracksContentModel> _learningTracks = <LearningTracksContentModel>[];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    learningTracksBloc = GetIt.instance.get<LearningTracksBloc>()
      ..add(GetLearningTracksEvent(searchKey: widget.searchKey));
    filterBloc = FilterBloc(learningTracksBloc: learningTracksBloc);
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      learningTracksBloc.add(const FetchMoreEvent());
    }
  }

  /// Forces the RefreshIndicator to show and start the refresh with animation
  void showRefreshIndicator() {
    _refreshIndicatorKey.currentState?.show();
  }

  Future<void> _handleRefresh() {
    learningTracksBloc.add(const RefreshLearningTracksEvent());
    // Return a delayed future to give time for the refresh to complete
    return Future.delayed(const Duration(seconds: 2));
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_scrollListener)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.uiBackgroundPrimary,
      body: BlocConsumer<LearningTracksBloc, LearningTracksState>(
        bloc: learningTracksBloc,
        listener: (context, state) {
          if (state is LearningTracksError) showAppToast(context, message: state.errorMsg);
        },
        builder: (context, state) {
          if (state is LearningTracksError) return const SizedBox.shrink();

          if (state is LearningTracksLoading) return const BuildLoader();

          if (state is LearningTracksLoaded) {
            _learningTracks = state.learningTracksModel.learningTracksList;
            final learningTracksModel = state.learningTracksModel;

            if (_learningTracks.isEmpty) {
              return const NoFilteredResults();
            }

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        getTotalResultsForSearchKeyText(
                          context,
                          state.learningTracksModel.totalElements,
                          widget.searchKey,
                        ),
                        style: context.textTheme.textSmall.medium.copyWith(
                          color: widget.searchKey != null ? AppColors.additionalGrey : null,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => router.pushNamed(
                          Routes.searchPage.path,
                          extra: CourseType.learningTrack,
                        ),
                        child: Image.asset(
                          width: 24,
                          AssetsPath.searchIcon,
                          color: AppColors.greenAccentPrimary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Expanded(
                    child: RefreshIndicator(
                      key: _refreshIndicatorKey,
                      onRefresh: _handleRefresh,
                      child: ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemCount: _learningTracks.length + (state.isLastPage ? 0 : 1),
                        itemBuilder: (context, index) {
                          if (_shouldShowLoader(index, learningTracksModel, state.isLastPage)) {
                            return const BuildLoader();
                          }

                          return LearningTrackCard(
                            learningTrack: _learningTracks[index],
                            onBackFromDetailsPage: () {
                              showRefreshIndicator();
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return const Center(child: Text('Unknown state'));
        },
      ),
      floatingActionButton: BlocSelector<LearningTracksBloc, LearningTracksState, String>(
        bloc: learningTracksBloc,
        selector: (state) {
          if (state is LearningTracksLoaded) {
            return state.sortedBy.name;
          }
          return '';
        },
        builder: (context, sortedBy) {
          return SortFilterButtonsContainer(
            filterBloc: filterBloc,
            learningTracksBloc: learningTracksBloc,
            isSortApplied: sortedBy.isNotEmpty,
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  bool _shouldShowLoader(int index, LearningTracksModel learningTracksModel, bool isLastPage) {
    return index == learningTracksModel.learningTracksList.length && !isLastPage;
  }
}
