import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/learning_tracks_view.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/trainings_view.dart';
import 'package:national_skills_platform/features/shared/keep_alive_widget.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class CatalogPage extends StatefulWidget {
  const CatalogPage({super.key, this.initialTab = 0});

  final int initialTab;

  @override
  State<CatalogPage> createState() => _CatalogPageState();
}

class _CatalogPageState extends State<CatalogPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: widget.initialTab);
    _pageController = PageController(initialPage: widget.initialTab);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: CupertinoScaffold(
        body: Builder(
          builder: (context) {
            return Scaffold(
              backgroundColor: AppColors.uiBackgroundPrimary,
              appBar: const NspAppBar(),
              body: Column(
                children: [
                  const SizedBox(height: 24),
                  AppTabBar(
                    key: ValueKey(EasyLocalization.of(context)!.locale.toString()),
                    tabController: _tabController,
                    onTabTapped: onTabTapped,
                    tabList: [LocaleKeys.trainings.tr(), LocaleKeys.header_learningTracks.tr()],
                  ),
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      onPageChanged: (page) => _tabController.index = page,
                      children: const [
                        KeepAliveWidget(child: TrainingsView()),
                        KeepAliveWidget(child: LearningTracksView()),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void onTabTapped(int tab) {
    _pageController.jumpToPage(tab);
  }
}
