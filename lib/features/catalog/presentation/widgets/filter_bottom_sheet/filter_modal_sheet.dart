import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_catagory_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/duration_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/language_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/location_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/sector_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/skill_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/training_provider_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_pages/training_type_filter.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/list_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class FilterModalSheet extends StatefulWidget {
  const FilterModalSheet({super.key});

  @override
  State<FilterModalSheet> createState() => _FilterModalSheetState();
}

class _FilterModalSheetState extends State<FilterModalSheet> {
  late final FilterBloc filterBloc;
  late final LearningTracksBloc learningTracksBloc;
  late final TrainingsBloc trainingsBloc;

  @override
  void initState() {
    super.initState();
    filterBloc = context.read<FilterBloc>();
    FilterModel? filterModel;

    if (context.read<LearningTracksBloc?>() != null) {
      final state = context.read<LearningTracksBloc>().state;
      if (state is LearningTracksLoaded) {
        filterModel = state.appliedFilter;
      }
    } else if (context.read<TrainingsBloc?>() != null) {
      final state = context.read<TrainingsBloc>().state;
      if (state is TrainingsLoaded) {
        filterModel = state.appliedFilter;
      }
    }

    filterBloc.add(InitFilterPage(filterModel ?? const FilterModel()));
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: BlocConsumer<FilterBloc, FilterState>(
        bloc: filterBloc,
        listener: (context, state) {
          if (state is FiltersApplied || state is FiltersNotApplied) {
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          if (state is FilterUpdated) {
            return ClipRRect(
              borderRadius: SharedDecoration.borderTopLeftRight10,
              child: Navigator(
                onGenerateRoute: (settings) => CupertinoPageRoute(
                  builder: (context) => ColoredBox(
                    color: Colors.white,
                    child: Column(
                      children: [
                        ModalHeader(
                          icon: Icons.clear,
                          title: LocaleKeys.employerApplicationsList_filters.tr(),
                          onReset: () => context.read<FilterBloc>().add(const ResetAllFilter()),
                          onIconTap: () => context.read<FilterBloc>().add(const DontApplyFilters()),
                        ),

                        const AppDivider(),

                        Expanded(
                          child: ListView(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            children: [
                              ///Sector filter
                              FilterCategoryTile(
                                title: LocaleKeys.sectors.tr(),
                                navigateTo: const SectorFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.sectorFilterModel.countSelected,
                              ),

                              ///Training Type filter
                              FilterCategoryTile(
                                title: LocaleKeys.type.tr(),
                                navigateTo: const TrainingTypeFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.trainingTypeFilterModel.countSelected,
                              ),

                              ///Skill filter
                              FilterCategoryTile(
                                title: LocaleKeys.skills_level.tr(),
                                navigateTo: const SkillFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.skillFilterModel.countSelected,
                              ),

                              ///Training Providers filter
                              FilterCategoryTile(
                                title: LocaleKeys.header_trainingProvider.tr(),
                                navigateTo: const TrainingProviderFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.trainingProviderFilterModel.countSelected,
                              ),

                              ///Duration filter
                              FilterCategoryTile(
                                title: LocaleKeys.duration.tr(),
                                navigateTo: const DurationFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.durationFilterModel.countSelected,
                              ),

                              ///Language filter
                              FilterCategoryTile(
                                title: LocaleKeys.language.tr(),
                                navigateTo: const LanguageFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.languageFilterModel.countSelected,
                              ),

                              ///Location filter
                              FilterCategoryTile(
                                title: LocaleKeys.location.tr(),
                                navigateTo: const LocationFilter(),
                                selectedCountInitialValue:
                                    state.filterModel.locationFilterModel.countSelected,
                              ),
                            ].divide(divider: const AppDivider(), bottom: true),
                          ),
                        ),

                        const AppDivider(),

                        AppButton(
                          onTap: () => context.read<FilterBloc>().add(const ApplyFilters()),
                          buttonText: LocaleKeys.filterModal_Show.tr(),
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        SizedBox(height: MediaQuery.paddingOf(context).bottom),
                        //
                      ],
                    ),
                  ),
                ),
              ),
            );
          }

          return const BuildLoader();
        },
      ),
    );
  }
}
