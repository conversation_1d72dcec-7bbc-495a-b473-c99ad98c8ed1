import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/utils/count_related_translations_util.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class FilterCategoryTile extends StatefulWidget {
  const FilterCategoryTile({
    required this.title,
    required this.navigateTo,
    this.selectedCountInitialValue = 0,
    super.key,
  });

  final String title;
  final Widget navigateTo;
  final int selectedCountInitialValue;

  @override
  State<FilterCategoryTile> createState() => _FilterCategoryTileState();
}

class _FilterCategoryTileState extends State<FilterCategoryTile> {
  late int _selectedCount;

  @override
  void initState() {
    super.initState();
    _selectedCount = widget.selectedCountInitialValue;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FilterBloc, FilterState>(
      builder: (context, state) {
        if (state is FilterUpdated) {
          _selectedCount = _getSelectedCountFromState(state.filterModel);
        }

        return ListTile(
          contentPadding: EdgeInsets.zero,
          onTap: () async {
            await Navigator.of(context)
                .push(CupertinoPageRoute(builder: (context) => widget.navigateTo))
                .then((selectedCount) {
              if (selectedCount != null && selectedCount is int) {
                setState(() => _selectedCount = selectedCount);
              }
            });
          },
          title: Text(widget.title, style: context.textTheme.textSmall.semiBold),
          subtitle: Text(
            selectedItemsText(_selectedCount),
            style: context.textTheme.textXSmall.greySecondary,
          ),
          trailing: const Icon(
            Icons.arrow_forward_ios_rounded,
            color: AppColors.neutralBlack,
            size: 16,
          ),
        );
      },
    );
  }

  int _getSelectedCountFromState(FilterModel model) {
    if (widget.title == LocaleKeys.duration.tr()) {
      return model.durationFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.language.tr()) {
      return model.languageFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.type.tr()) {
      return model.trainingTypeFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.skills_level.tr()) {
      return model.skillFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.sectors.tr()) {
      return model.sectorFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.header_trainingProvider.tr()) {
      return model.trainingProviderFilterModel.countSelected;
    } else if (widget.title == LocaleKeys.location.tr()) {
      return model.locationFilterModel.countSelected;
    }
    return _selectedCount;
  }

  String selectedItemsText(int? totalElements) {
    if (totalElements == null) return '';

    return context.locale == const Locale(Constants.localeAR)
        ? '${getItemsSelectedTranslation(totalElements)} $totalElements'
        : '$totalElements ${getItemsSelectedTranslation(totalElements)}';
  }
}
