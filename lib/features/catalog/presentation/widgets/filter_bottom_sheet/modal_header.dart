import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ModalHeader extends StatelessWidget {
  const ModalHeader({
    required this.onIconTap,
    required this.title,
    required this.onReset,
    required this.icon,
    super.key,
  });

  final VoidCallback onIconTap;
  final VoidCallback onReset;
  final String title;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              key: ValueKey(icon),
              padding: EdgeInsets.zero,
              onPressed: onIconTap,
              icon: Icon(icon, size: 24, color: AppColors.greenAccentPrimary),
            ),
            Text(title, style: context.textTheme.textLarge.w600),
            TextButton(
              onPressed: onReset,
              style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 16)),
              child: Text(
                LocaleKeys.filterModal_Reset.tr(),
                style: context.textTheme.textLarge.accentGreenPrimary.w600,
              ),
            ),
          ],
        ),
        const AppDivider(),
      ],
    );
  }
}
