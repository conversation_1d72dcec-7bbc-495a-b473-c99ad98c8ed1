import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ApplySubFilterButton extends StatelessWidget {
  const ApplySubFilterButton({super.key, required this.saveSelectedFilters});

  final Function(BuildContext, FilterUpdated) saveSelectedFilters;

  @override
  Widget build(BuildContext context) {
    return AppButton(
      onTap: () {
        final filterState = context.read<FilterBloc>().state;
        if (filterState is FilterUpdated) {
          saveSelectedFilters(context, filterState);
        }
      },
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(vertical: 12),
      buttonText: LocaleKeys.trainingDetails_apply.tr(),
    );
  }
}
