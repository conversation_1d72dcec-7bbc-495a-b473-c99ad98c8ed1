import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class DurationFilter extends StatefulWidget {
  const DurationFilter({super.key});

  @override
  State<DurationFilter> createState() => _DurationFilterState();
}

class _DurationFilterState extends State<DurationFilter> {
  @override
  Widget build(BuildContext context) {
    final filterState = context.watch<FilterBloc>().state;

    if (filterState is FilterUpdated) {
      final durationFilterModel = filterState.filterModel.durationFilterModel;

      return ColoredBox(
        color: Colors.white,
        child: Column(
          children: [
            ModalHeader(
              icon: Icons.keyboard_backspace_outlined,
              onIconTap: () => _saveSelectedFilters(context, filterState),
              title: LocaleKeys.duration.tr(),
              onReset: () =>
                  context.read<FilterBloc>().add(const SetDuration(DurationFilterModel())),
            ),

            Expanded(
              child: ListView(
                children: [
                  FilterOptionTile(
                    initialValue: durationFilterModel.allDurations,
                    title: LocaleKeys.durations_ALL.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetDuration(
                              isSelected ? DurationFilterModel.all() : const DurationFilterModel(),
                            ),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: durationFilterModel.days,
                    title: LocaleKeys.durations_DAYS.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetDuration(durationFilterModel.copyWith(days: isSelected)),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: durationFilterModel.weeks,
                    title: LocaleKeys.durations_WEEKS.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetDuration(durationFilterModel.copyWith(weeks: isSelected)),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: durationFilterModel.months,
                    title: LocaleKeys.durations_MONTHS.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetDuration(durationFilterModel.copyWith(months: isSelected)),
                          );
                    },
                  ),

                  //
                ].divide(divider: const AppDivider(), bottom: true),
              ),
            ),

            const AppDivider(),
            ApplySubFilterButton(saveSelectedFilters: _saveSelectedFilters),

            SizedBox(height: MediaQuery.paddingOf(context).bottom),
            //
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  void _saveSelectedFilters(BuildContext context, FilterUpdated filterState) {
    return Navigator.pop(context, filterState.filterModel.durationFilterModel.countSelected);
  }
}
