import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/sector_filter_bloc/sector_filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SectorFilter extends StatefulWidget {
  const SectorFilter({super.key});

  @override
  State<SectorFilter> createState() => _SectorFilterState();
}

class _SectorFilterState extends State<SectorFilter> {
  late final SectorFilterBloc _sectorFilterBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _sectorFilterBloc = SectorFilterBloc(
      filterBloc: context.read<FilterBloc>(),
      sectorRepository: GetIt.instance<SectorRepository>(),
    )..add(
        LoadActiveSectors(
          locale: context.locale.languageCode,
        ),
      );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FilterBloc, FilterState>(
      listener: (context, state) {
        if (state is FilterUpdated && state.limitWarning) {
          showAppToast(context, message: LocaleKeys.max6SectorsSelection.tr());
        }
      },
      child: BlocConsumer<SectorFilterBloc, SectorFilterState>(
        bloc: _sectorFilterBloc,
        listener: (context, state) {
          if (state.activeError.isNotEmpty) showAppToast(context, message: state.activeError);
        },
        builder: (context, state) {
          if (state.isLoadingActiveSectors) return const BuildLoader();
          if (state.activeError.isNotEmpty) {
            return Center(
              child: Text(state.activeError),
            );
          }

          // Check if active sectors are available
          final hasActiveSectors =
              state.activeSectorResponse != null && state.activeSectorResponse!.content.isNotEmpty;

          return ColoredBox(
            color: Colors.white,
            child: Column(
              children: [
                ModalHeader(
                  icon: Icons.keyboard_backspace_outlined,
                  onIconTap: () {
                    final filterState = context.read<FilterBloc>().state;
                    if (filterState is FilterUpdated) {
                      Navigator.pop(
                        context,
                        filterState.filterModel.sectorFilterModel.countSelected,
                      );
                    }
                  },
                  title: LocaleKeys.sectors.tr(),
                  onReset: () => context.read<FilterBloc>().add(const SetSectors()),
                ),
                const AppDivider(),
                Expanded(
                  child: hasActiveSectors
                      ? ListView.separated(
                          itemCount: state.activeSectorResponse!.content.length,
                          separatorBuilder: (_, __) => const AppDivider(),
                          itemBuilder: (context, index) {
                            final activeSector = state.activeSectorResponse!.content[index];
                            final filterState = context.watch<FilterBloc>().state;
                            if (filterState is! FilterUpdated) return const SizedBox.shrink();

                            final isSelected = filterState
                                .filterModel.sectorFilterModel.selectedSectorIds
                                .contains(activeSector.id);

                            return FilterOptionTile(
                              title: activeSector.title,
                              initialValue: isSelected,
                              onTap: (_) => context
                                  .read<FilterBloc>()
                                  .add(SetSectors(sectorId: activeSector.id)),
                            );
                          },
                        )
                      : _noDataView(),
                ),
                const AppDivider(),
                ApplySubFilterButton(
                  saveSelectedFilters: (context, _) {
                    final filterState = context.read<FilterBloc>().state;
                    if (filterState is FilterUpdated) {
                      Navigator.pop(
                        context,
                        filterState.filterModel.sectorFilterModel.countSelected,
                      );
                    }
                  },
                ),
                SizedBox(height: MediaQuery.paddingOf(context).bottom),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _noDataView() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Text(
          'No sectors available',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
