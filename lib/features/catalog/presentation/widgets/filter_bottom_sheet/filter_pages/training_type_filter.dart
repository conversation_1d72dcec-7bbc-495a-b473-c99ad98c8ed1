import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingTypeFilter extends StatefulWidget {
  const TrainingTypeFilter({super.key});

  @override
  State<TrainingTypeFilter> createState() => _TrainingTypeFilterState();
}

class _TrainingTypeFilterState extends State<TrainingTypeFilter> {
  @override
  Widget build(BuildContext context) {
    final filterState = context.watch<FilterBloc>().state;

    if (filterState is FilterUpdated) {
      final trainingTypeFilterModel = filterState.filterModel.trainingTypeFilterModel;

      return ColoredBox(
        color: Colors.white,
        child: Column(
          children: [
            ModalHeader(
              icon: Icons.keyboard_backspace_outlined,
              onIconTap: () => _saveSelectedFilters(context, filterState),
              title: LocaleKeys.type.tr(),
              onReset: () => context.read<FilterBloc>().add(
                    const SetTrainingType(TrainingTypeFilterModel()),
                  ),
            ),

            Expanded(
              child: ListView(
                children: [
                  FilterOptionTile(
                    initialValue: trainingTypeFilterModel.selfPaced,
                    title: LocaleKeys.selfPaced.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetTrainingType(
                              trainingTypeFilterModel.copyWith(selfPaced: isSelected),
                            ),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: trainingTypeFilterModel.online,
                    title: LocaleKeys.trainingDetails_online.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetTrainingType(trainingTypeFilterModel.copyWith(online: isSelected)),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: trainingTypeFilterModel.inPerson,
                    title: LocaleKeys.in_person.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetTrainingType(trainingTypeFilterModel.copyWith(inPerson: isSelected)),
                          );
                    },
                  ),

                  //
                ].divide(
                  divider: const AppDivider(padding: EdgeInsets.symmetric(horizontal: 16)),
                  bottom: true,
                ),
              ),
            ),

            const AppDivider(),
            ApplySubFilterButton(saveSelectedFilters: _saveSelectedFilters),
            SizedBox(height: MediaQuery.paddingOf(context).bottom),
            //
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  void _saveSelectedFilters(BuildContext context, FilterUpdated filterState) {
    return Navigator.pop(context, filterState.filterModel.trainingTypeFilterModel.countSelected);
  }
}
