import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/skill_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SkillFilter extends StatelessWidget {
  const SkillFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FilterBloc, FilterState>(
      builder: (context, filterState) {
        if (filterState is FilterUpdated) {
          final skillFilterModel = filterState.filterModel.skillFilterModel;

          return ColoredBox(
            color: Colors.white,
            child: Column(
              children: [
                ModalHeader(
                  icon: Icons.keyboard_backspace_outlined,
                  onIconTap: () => _saveSelectedFilters(context, filterState),
                  title: LocaleKeys.skills_level.tr(),
                  onReset: () => context.read<FilterBloc>().add(const SetSkill(SkillFilterModel())),
                ),

                Expanded(
                  child: ListView(
                    children: [
                      FilterOptionTile(
                        initialValue: skillFilterModel.general,
                        title: LocaleKeys.GENERAL.tr(),
                        onTap: (isSelected) {
                          context.read<FilterBloc>().add(
                                SetSkill(skillFilterModel.copyWith(general: isSelected)),
                              );
                        },
                      ),

                      FilterOptionTile(
                        initialValue: skillFilterModel.basic,
                        title: LocaleKeys.BASIC.tr(),
                        onTap: (isSelected) {
                          context.read<FilterBloc>().add(
                                SetSkill(skillFilterModel.copyWith(basic: isSelected)),
                              );
                        },
                      ),

                      FilterOptionTile(
                        initialValue: skillFilterModel.advanced,
                        title: LocaleKeys.ADVANCED.tr(),
                        onTap: (isSelected) {
                          context.read<FilterBloc>().add(
                                SetSkill(skillFilterModel.copyWith(advanced: isSelected)),
                              );
                        },
                      ),

                      //
                    ].divide(
                      divider: const AppDivider(padding: EdgeInsets.symmetric(horizontal: 16)),
                      bottom: true,
                    ),
                  ),
                ),

                const AppDivider(),
                ApplySubFilterButton(saveSelectedFilters: _saveSelectedFilters),
                SizedBox(height: MediaQuery.paddingOf(context).bottom),
                //
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  void _saveSelectedFilters(BuildContext context, FilterUpdated filterState) {
    return Navigator.pop(context, filterState.filterModel.skillFilterModel.countSelected);
  }
}
