import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class LanguageFilter extends StatefulWidget {
  const LanguageFilter({super.key});

  @override
  State<LanguageFilter> createState() => _LanguageFilterState();
}

class _LanguageFilterState extends State<LanguageFilter> {
  bool? isArabicSelected;
  bool? isEnglishSelected;

  @override
  Widget build(BuildContext context) {
    final filterState = context.watch<FilterBloc>().state;

    if (filterState is FilterUpdated) {
      final languageFilterModel = filterState.filterModel.languageFilterModel;

      return ColoredBox(
        color: Colors.white,
        child: Column(
          children: [
            ModalHeader(
              icon: Icons.keyboard_backspace_outlined,
              onIconTap: () => _saveSelectedFilters(context, filterState),
              title: LocaleKeys.language.tr(),
              onReset: () =>
                  context.read<FilterBloc>().add(const SetLanguage(LanguageFilterModel())),
            ),

            Expanded(
              child: ListView(
                children: [
                  FilterOptionTile(
                    initialValue: languageFilterModel.arabic,
                    title: LocaleKeys.filterModal_arabic.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetLanguage(languageFilterModel.copyWith(arabic: isSelected)),
                          );
                    },
                  ),

                  FilterOptionTile(
                    initialValue: languageFilterModel.english,
                    title: LocaleKeys.filterModal_english.tr(),
                    onTap: (isSelected) {
                      context.read<FilterBloc>().add(
                            SetLanguage(languageFilterModel.copyWith(english: isSelected)),
                          );
                    },
                  ),

                  //
                ].divide(
                  divider: const AppDivider(padding: EdgeInsets.symmetric(horizontal: 16)),
                  bottom: true,
                ),
              ),
            ),

            const AppDivider(),
            ApplySubFilterButton(saveSelectedFilters: _saveSelectedFilters),

            SizedBox(height: MediaQuery.paddingOf(context).bottom),
            //
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  void _saveSelectedFilters(BuildContext context, FilterUpdated filterState) {
    return Navigator.pop(context, filterState.filterModel.languageFilterModel.countSelected);
  }
}
