import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/location_repository.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_state.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class LocationFilter extends StatefulWidget {
  const LocationFilter({super.key});

  @override
  State<LocationFilter> createState() => _LocationFilterState();
}

class _LocationFilterState extends State<LocationFilter> {
  late final LocationFilterBloc _locationFilterBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _locationFilterBloc = LocationFilterBloc(
      locationRepository: GetIt.instance<LocationRepository>(),
      filterBloc: context.read<FilterBloc>(),
    )..add(LoadLocations(locale: context.locale.languageCode));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FilterBloc, FilterState>(
      listener: (context, state) {
        if (state is FilterUpdated && state.limitWarning) {
          showAppToast(context, message: LocaleKeys.max6LocationSelection.tr());
        }
      },
      child: BlocConsumer<LocationFilterBloc, LocationFilterState>(
        bloc: _locationFilterBloc,
        listener: (context, state) {
          if (state.error.isNotEmpty) showAppToast(context, message: state.error);
        },
        builder: (context, state) {
          return ColoredBox(
            color: Colors.white,
            child: Column(
              children: [
                ModalHeader(
                  icon: Icons.keyboard_backspace_outlined,
                  onIconTap: () {
                    final filterState = context.read<FilterBloc>().state;
                    if (filterState is FilterUpdated) {
                      Navigator.pop(
                        context,
                        filterState.filterModel.locationFilterModel.countSelected,
                      );
                    }
                  },
                  title: LocaleKeys.location.tr(),
                  onReset: () => context.read<FilterBloc>().add(const SetLocations()),
                ),
                const AppDivider(),
                if (state.isLoading)
                  const Expanded(child: BuildLoader())
                else if (state.error.isNotEmpty)
                  const SizedBox.shrink()
                else ...[
                  Expanded(
                    child: ListView.separated(
                      itemCount: state.locations.length,
                      separatorBuilder: (_, __) => const AppDivider(),
                      itemBuilder: (context, index) {
                        final location = state.locations[index];
                        final filterState = context.watch<FilterBloc>().state;
                        if (filterState is! FilterUpdated) return const SizedBox.shrink();

                        final isSelected = filterState
                            .filterModel.locationFilterModel.selectedLocationIds
                            .contains(location.id);

                        return FilterOptionTile(
                          title: location.cityName,
                          initialValue: isSelected,
                          onTap: (_) => context.read<FilterBloc>().add(
                                SetLocations(locationId: location.id),
                              ),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}
