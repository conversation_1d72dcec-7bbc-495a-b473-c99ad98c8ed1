import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_state.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/apply_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/modal_header.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingProviderFilter extends StatefulWidget {
  const TrainingProviderFilter({super.key});

  @override
  State<TrainingProviderFilter> createState() => _TrainingProviderFilterState();
}

class _TrainingProviderFilterState extends State<TrainingProviderFilter> {
  late final TrainingProviderFilterBloc _trainingProviderFilterBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _trainingProviderFilterBloc = TrainingProviderFilterBloc(
      filterBloc: context.read<FilterBloc>(),
      getSortedTrainingProvidersUseCase: GetIt.instance<GetSortedTrainingProvidersUseCase>(),
    )..add(LoadTrainingProviders(locale: context.locale.languageCode));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<FilterBloc, FilterState>(
      listener: (context, state) {
        if (state is FilterUpdated && state.limitWarning) {
          showAppToast(context, message: LocaleKeys.max6TPSelection.tr());
        }
      },
      child: BlocConsumer<TrainingProviderFilterBloc, TrainingProviderFilterState>(
        bloc: _trainingProviderFilterBloc,
        listener: (context, state) {
          if (state.error.isNotEmpty) showAppToast(context, message: state.error);
        },
        builder: (context, state) {
          return ColoredBox(
            color: Colors.white,
            child: Column(
              children: [
                ModalHeader(
                  icon: Icons.keyboard_backspace_outlined,
                  onIconTap: () {
                    final filterState = context.read<FilterBloc>().state;
                    if (filterState is FilterUpdated) {
                      Navigator.pop(
                        context,
                        filterState.filterModel.trainingProviderFilterModel.countSelected,
                      );
                    }
                  },
                  title: LocaleKeys.header_trainingProviders.tr(),
                  onReset: () => context.read<FilterBloc>().add(const SetTrainingProviders()),
                ),
                const AppDivider(),
                if (state.isLoading)
                  const Expanded(child: BuildLoader())
                else if (state.error.isNotEmpty)
                  const SizedBox.shrink()
                else ...[
                  Expanded(
                    child: ListView.separated(
                      itemCount: state.trainingProviders.length,
                      separatorBuilder: (_, __) => const AppDivider(),
                      itemBuilder: (context, index) {
                        final trainingProvider = state.trainingProviders[index];
                        final filterState = context.watch<FilterBloc>().state;
                        if (filterState is! FilterUpdated) return const SizedBox.shrink();

                        final isSelected = filterState
                            .filterModel.trainingProviderFilterModel.selectedTrainingProviderIds
                            .contains(trainingProvider.id);

                        return FilterOptionTile(
                          title: trainingProvider.organizationName,
                          initialValue: isSelected,
                          onTap: (_) => context.read<FilterBloc>().add(
                                SetTrainingProviders(trainingProviderId: trainingProvider.id),
                              ),
                        );
                      },
                    ),
                  ),
                  const AppDivider(),
                  ApplySubFilterButton(
                    saveSelectedFilters: (context, _) {
                      final filterState = context.read<FilterBloc>().state;
                      if (filterState is FilterUpdated) {
                        Navigator.pop(
                          context,
                          filterState.filterModel.trainingProviderFilterModel.countSelected,
                        );
                      }
                    },
                  ),
                ],
                SizedBox(height: MediaQuery.paddingOf(context).bottom),
              ],
            ),
          );
        },
      ),
    );
  }
}
