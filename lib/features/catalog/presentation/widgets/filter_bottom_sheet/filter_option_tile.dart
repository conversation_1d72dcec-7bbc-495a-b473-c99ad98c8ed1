import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

class FilterOptionTile extends StatelessWidget {
  const FilterOptionTile({
    required this.initialValue,
    required this.title,
    required this.onTap,
    super.key,
  });

  final String title;
  final bool initialValue;
  final Function(bool val) onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () => onTap(!initialValue),
      leading: IgnorePointer(child: AppCheckBox(initialValue: initialValue)),
      title: Text(title, style: context.textTheme.textSmall),
    );
  }
}
