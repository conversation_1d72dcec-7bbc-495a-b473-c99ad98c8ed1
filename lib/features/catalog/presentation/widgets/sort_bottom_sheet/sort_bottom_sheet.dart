import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart'
    as learning_tracks_bloc;
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart'
    hide ApplySortToList;
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

enum SortOptionsEnum { enrolledCount, createdDate }

class SortBottomSheet extends StatefulWidget {
  const SortBottomSheet({this.learningTracksBloc, this.trainingsBloc, super.key})
      : assert(
          (learningTracksBloc == null) != (trainingsBloc == null),
          'Only one bloc should be provided, either LearningTracksBloc or TrainingsBloc',
        );

  final LearningTracksBloc? learningTracksBloc;
  final TrainingsBloc? trainingsBloc;

  @override
  State<SortBottomSheet> createState() => _SortBottomSheetState();
}

class _SortBottomSheetState extends State<SortBottomSheet> {
  int _selectedOption = 0;

  @override
  void initState() {
    super.initState();
    if (widget.learningTracksBloc != null) {
      final state = widget.learningTracksBloc?.state;
      if (state is learning_tracks_bloc.LearningTracksLoaded) {
        _selectedOption = _options.indexWhere((element) => element.$2 == state.sortedBy);
      }
    } else if (widget.trainingsBloc != null) {
      final state = widget.trainingsBloc?.state;
      if (state is TrainingsLoaded) {
        _selectedOption = _options.indexWhere((element) => element.$2 == state.sortedBy);
      }
    }
  }

  final _options = [
    (LocaleKeys.mostPopular.tr(), SortOptionsEnum.enrolledCount),
    // LocaleKeys.filterModal_HighestRated.tr()
    (LocaleKeys.recentlyAdded.tr(), SortOptionsEnum.createdDate),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: SharedDecoration.borderTopLeftRight10,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Align(
            child: Container(
              width: 36,
              height: 3,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2.5),
                color: AppColors.greyTertiary,
              ),
            ),
          ),

          const SizedBox(height: 6),

          Text(LocaleKeys.filterModal_Sort.tr(), style: context.textTheme.textLarge.semiBold),

          const SizedBox(height: 8),
          ..._options
              .map(
                (option) => ListTile(
                  contentPadding: const EdgeInsets.symmetric(vertical: 8),
                  dense: true,
                  title: Text(option.$1, style: context.textTheme.textSmall.medium),
                  trailing: AppCustomRadio(isSelected: _options.indexOf(option) == _selectedOption),
                  onTap: () => onChanged(option),
                ),
              )
              .toList()
              .divide(divider: const AppDivider()),

          const SizedBox(height: 34),
          //
        ],
      ),
    );
  }

  void onChanged((String, SortOptionsEnum) option) {
    _selectedOption = _options.indexOf(option);
    if (widget.learningTracksBloc != null) {
      widget.learningTracksBloc?.add(learning_tracks_bloc.ApplySortToList(option.$2));
    } else {
      widget.trainingsBloc?.add(ApplySortToList(option.$2));
    }
    Navigator.pop(context);
  }
}
