import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class LearningTrackCard extends StatelessWidget {
  const LearningTrackCard({
    required this.learningTrack,
    this.onBackFromDetailsPage,
    super.key,
  });

  final LearningTracksContentModel learningTrack;
  final VoidCallback? onBackFromDetailsPage;

  @override
  Widget build(BuildContext context) {
    final profileImageUrl = learningTrack.profileImageUrl;

    return GestureDetector(
      excludeFromSemantics: true,
      onTap: () async {
        if (learningTrack.id.isNotEmpty) {
          await router.pushNamed(Routes.learningTrackDetailsPage.name, extra: learningTrack.id);
          onBackFromDetailsPage?.call();
        } else {
          showAppToast(context, message: 'Failed to open the card');
        }
      },
      child: Container(
        height: 126,
        width: MediaQuery.sizeOf(context).width,
        margin: const EdgeInsets.only(bottom: 12),
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.accentLight),
          borderRadius: BorderRadius.circular(8),
        ),
        foregroundDecoration: BoxDecoration(
          border: Border.all(color: AppColors.accentLight),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (profileImageUrl != null)

              ///This line is added to fix golden tests, because it gets stuck on infinite loading
              if (profileImageUrl.isEmpty)
                Container(width: 126, height: 126, color: Colors.grey)
              else
                CachedNetworkImage(
                  imageUrl: profileImageUrl,
                  width: 126,
                  height: 126,
                  placeholder: (context, url) => const ShimmerPlaceholder(height: 126),
                  errorWidget: (ctx, _, __) => const SizedBox.shrink(),
                  fit: BoxFit.cover,
                ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          LocaleKeys.header_learningTrack.tr(),
                          style: context.textTheme.textXSmall.orangeAccentPrimary.medium,
                        ),
                        //     Wrap(
                        //       children: [
                        //         Text(
                        //           '4.0',
                        //           style:
                        //               context.textTheme.smallButton.copyWith(fontWeight: FontWeight.w500),
                        //         ),
                        //         const SizedBox(width: 1),
                        //         const Icon(
                        //           Icons.star,
                        //           color: AppColors.orangeAccentPrimary,
                        //           size: 16,
                        //         ),
                        //       ],
                        //     ),
                      ],
                    ),
                    Text(
                      learningTrack.title ?? '',
                      style: context.textTheme.textSmall.semiBold,
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Text(
                      learningTrack.organizationName,
                      style: context.textTheme.textXSmall.greyPrimary.medium,
                      softWrap: true,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
