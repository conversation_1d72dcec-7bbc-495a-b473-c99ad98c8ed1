import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/filter_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/sort_button.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

class SortFilterButtonsContainer extends StatelessWidget {
  const SortFilterButtonsContainer({
    required this.filterBloc,
    required this.isSortApplied,
    this.learningTracksBloc,
    this.trainingsBloc,
    super.key,
  }) : assert(
          (learningTracksBloc == null) != (trainingsBloc == null),
          'Only one bloc should be provided, either LearningTracksBloc or TrainingsBloc',
        );

  final FilterBloc filterBloc;
  final bool isSortApplied;
  final LearningTracksBloc? learningTracksBloc;
  final TrainingsBloc? trainingsBloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: filterBloc),
        if (learningTracksBloc != null)
          BlocProvider<LearningTracksBloc>.value(value: learningTracksBloc!),
        if (trainingsBloc != null) BlocProvider.value(value: trainingsBloc!),
      ],
      child: Container(
        height: 50, // Clickable area
        alignment: Alignment.center, // Centers the content
        child: IntrinsicWidth(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: AppColors.greenAccentDark,
              borderRadius: BorderRadius.circular(4),
            ),
            height: 42, // Visual size
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ///Sort
                SortButton(
                  sortBottomSheet: SortBottomSheet(
                    learningTracksBloc: learningTracksBloc,
                    trainingsBloc: trainingsBloc,
                  ),
                  isSortApplied: isSortApplied,
                ),

                const SizedBox(height: 16, child: VerticalDivider()),

                ///Filter
                const FilterButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
