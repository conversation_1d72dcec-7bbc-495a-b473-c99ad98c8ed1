import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/training_type_string.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/training_card_bottom_info_panel.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/router.dart';

class TrainingCard extends StatelessWidget {
  const TrainingCard({
    required this.trainingModel,
    this.axisHorizontal = true,
    this.clickable = true,
    this.onBackFromDetailsPage,
    super.key,
  });

  final TrainingContentModel trainingModel;
  final bool axisHorizontal;
  final bool clickable;
  final void Function()? onBackFromDetailsPage;

  @override
  Widget build(BuildContext context) {
    final profileImageUrl = trainingModel.profileImageUrl;
    const sizeInHorizontalView = 126.0;
    const sizeInVerticalView = 174.0;

    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = MediaQuery.sizeOf(context).width;
        final textWidth = axisHorizontal ? null : cardWidth * 0.55;

        return GestureDetector(
          excludeFromSemantics: true,
          onTap: () async {
            if (clickable) {
              if (trainingModel.id.isNotEmpty) {
                await router.pushNamed(Routes.trainingDetailsPage.name, extra: trainingModel.id);
                onBackFromDetailsPage?.call();
              } else {
                showAppToast(context, message: 'Failed to open the card');
              }
            }
          },
          child: Container(
            height: axisHorizontal ? sizeInHorizontalView : constraints.minHeight,
            margin: const EdgeInsets.only(bottom: 12),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: AppColors.accentLight),
              borderRadius: BorderRadius.circular(8),
            ),
            foregroundDecoration: BoxDecoration(
              border: Border.all(color: AppColors.accentLight),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Flex(
              mainAxisSize: axisHorizontal ? MainAxisSize.max : MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              direction: axisHorizontal ? Axis.horizontal : Axis.vertical,
              children: [
                if (profileImageUrl != null)

                  ///This line is added to fix golden tests, because it gets stuck on infinite loading
                  if (profileImageUrl.isEmpty)
                    Container(
                      width: axisHorizontal ? sizeInHorizontalView : null,
                      height: axisHorizontal ? sizeInHorizontalView : sizeInVerticalView,
                      color: Colors.grey,
                    )
                  else
                    Flexible(
                      child: CachedNetworkImage(
                        imageUrl: profileImageUrl,
                        width: axisHorizontal ? sizeInHorizontalView : null,
                        height: axisHorizontal ? sizeInHorizontalView : sizeInVerticalView,
                        errorWidget: (ctx, _, __) => const SizedBox.shrink(),
                        fit: BoxFit.cover,
                        placeholder: (context, url) => ShimmerPlaceholder(
                          height: axisHorizontal ? sizeInHorizontalView : sizeInVerticalView,
                        ),
                      ),
                    ),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      mainAxisSize: axisHorizontal ? MainAxisSize.max : MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              trainingContentTypeString(trainingModel),
                              style: axisHorizontal
                                  ? context.textTheme.textXSmall.medium.accentGreenPrimary
                                  : context.textTheme.textSmall.medium.accentGreenPrimary,
                            ),
                            // Wrap(
                            //   children: [
                            //     Text(
                            //       '4.0',
                            //       style:
                            //           context.textTheme.smallButton.copyWith(fontWeight: FontWeight.w500),
                            //     ),
                            //     const SizedBox(width: 1),
                            //     const Icon(
                            //       Icons.star,
                            //       color: Colors.orange,
                            //       size: 16,
                            //     ),
                            //   ],
                            // )
                          ],
                        ),
                        const SizedBox(height: 4),
                        SizedBox(
                          width: textWidth,
                          child: Text(
                            trainingModel.title ?? '',
                            style: context.textTheme.textSmall.semiBold,
                            maxLines: 2,
                            softWrap: true,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (axisHorizontal) const Spacer() else const SizedBox(height: 4),
                        Text(
                          trainingModel.organizationName,
                          style: context.textTheme.textXSmall.greyPrimary.medium,
                          maxLines: 2,
                          softWrap: true,
                        ),
                        if (!axisHorizontal) ...[
                          const AppDivider(padding: EdgeInsets.symmetric(vertical: 12)),
                          const Spacer(),
                          TrainingCardBottomInfoPanel(trainingModel: trainingModel),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
