import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/utils/disable_sort_filter_buttons.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class SortButton extends StatelessWidget {
  const SortButton({required this.sortBottomSheet, required this.isSortApplied, super.key});

  final Widget sortBottomSheet;
  final bool isSortApplied;

  @override
  Widget build(BuildContext context) {
    final title = LocaleKeys.filterModal_Sort.tr();
    final learningTracksState = context.watch<LearningTracksBloc?>()?.state;
    final trainingsState = context.watch<TrainingsBloc?>()?.state;
    final isButtonDisabled = disableButton(learningTracksState, trainingsState);
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    return Semantics(
      button: true,
      label: title,
      excludeSemantics: true,
      child: GestureDetector(
        onTap: () => isButtonDisabled
            ? null
            : showModalBottomSheet(
                useRootNavigator: useRootNavigator,
                shape: const RoundedRectangleBorder(
                  borderRadius: SharedDecoration.borderTopLeftRight10,
                ),
                context: context,
                builder: (context) => sortBottomSheet,
              ),
        child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            SizedBox(
              height: 16,
              width: 16,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomLeft,
                    child: Image.asset(
                      AssetsPath.sortIcon,
                      height: 12,
                      color: isButtonDisabled ? AppColors.greySecondary : Colors.white,
                    ),
                  ),
                  if (isSortApplied)
                    Align(
                      alignment: Alignment.topRight,
                      child: CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 4.2,
                        child: Image.asset(
                          height: 4,
                          AssetsPath.checkIcon,
                          color: AppColors.greenAccentPrimary,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: context.textTheme.textSmall.medium.copyWith(
                color: isButtonDisabled ? AppColors.greySecondary : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
