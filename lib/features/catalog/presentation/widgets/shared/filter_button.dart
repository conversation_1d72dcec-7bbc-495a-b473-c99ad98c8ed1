import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_modal_sheet.dart';
import 'package:national_skills_platform/features/catalog/utils/disable_sort_filter_buttons.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class FilterButton extends StatelessWidget {
  const FilterButton({super.key});

  @override
  Widget build(BuildContext context) {
    final title = LocaleKeys.filterModal_Filter.tr();
    final learningTracksState = context.watch<LearningTracksBloc?>()?.state;
    final trainingsState = context.watch<TrainingsBloc?>()?.state;
    final isButtonDisabled = disableButton(learningTracksState, trainingsState);
    final useRootNavigator = !Platform.environment.containsKey(Constants.flutterTest);

    return Semantics(
      button: true,
      label: title,
      excludeSemantics: true,
      child: GestureDetector(
        onTap: () => isButtonDisabled
            ? null
            : Platform.isIOS
                ? CupertinoScaffold.showCupertinoModalBottomSheet(
                    context: context,
                    enableDrag: false,
                    useRootNavigator: useRootNavigator,
                    backgroundColor: Colors.white,
                    builder: (_) => buildFilterModalSheet(context),
                  )
                : showCupertinoModalBottomSheet(
                    context: context,
                    enableDrag: false,
                    useRootNavigator: useRootNavigator,
                    backgroundColor: Colors.white,
                    builder: (_) => buildFilterModalSheet(context),
                  ),
        child: Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            SizedBox(
              height: 18,
              width: 16,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Image.asset(
                      AssetsPath.filterIcon,
                      height: 12,
                      color: isButtonDisabled ? AppColors.greySecondary : Colors.white,
                    ),
                  ),
                  if (showCheckIcon(context))
                    Align(
                      alignment: Alignment.topRight,
                      child: CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 4.2,
                        child: Image.asset(
                          height: 4,
                          AssetsPath.checkIcon,
                          color: AppColors.greenAccentPrimary,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: context.textTheme.textSmall.medium.copyWith(
                color: isButtonDisabled ? AppColors.greySecondary : Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildFilterModalSheet(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: context.read<FilterBloc>()),
        if (context.read<LearningTracksBloc?>() != null)
          BlocProvider.value(value: context.read<LearningTracksBloc>()),
        if (context.read<TrainingsBloc?>() != null)
          BlocProvider.value(value: context.read<TrainingsBloc>()),
      ],
      child: const FilterModalSheet(),
    );
  }

  bool showCheckIcon(BuildContext context) {
    if (context.read<LearningTracksBloc?>() != null) {
      final state = context.read<LearningTracksBloc>().state;
      if (state is LearningTracksLoaded) {
        return !state.appliedFilter.noFiltersSelected;
      } else if (state is LearningTracksLoading) {
        return false;
      }
    } else if (context.read<TrainingsBloc?>() != null) {
      final state = context.read<TrainingsBloc>().state;
      if (state is TrainingsLoaded) {
        return !state.appliedFilter.noFiltersSelected;
      } else if (state is TrainingsLoading) {
        return false;
      }
    }

    return true;
  }
}
