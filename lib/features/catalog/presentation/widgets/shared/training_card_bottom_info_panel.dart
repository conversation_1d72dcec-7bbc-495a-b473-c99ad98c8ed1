import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class TrainingCardBottomInfoPanel extends StatelessWidget {
  const TrainingCardBottomInfoPanel({required this.trainingModel, super.key});

  final TrainingContentModel trainingModel;

  @override
  Widget build(BuildContext context) {
    final duration = trainingModel.duration != null
        ? LocaleKeys.trainingDetails_estimatedTimeValue.tr(args: [trainingModel.duration ?? ''])
        : null;

    return Row(
      children: [
        if (duration != null) ...[
          Text(_determineTrainingTimeUnit(duration), style: context.textTheme.textSmall),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Icon(Icons.circle, size: 3, color: AppColors.greyTertiary),
          ),
        ],
        if (trainingModel.level != null) ...[
          Text(trainingModel.level ?? '', style: context.textTheme.textSmall),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Icon(Icons.circle, size: 3, color: AppColors.greyTertiary),
          ),
        ],
        if (trainingModel.language != null) ...[
          Text(trainingModel.language ?? '', style: context.textTheme.textSmall),
        ],
      ],
    );
  }
}

String _determineTrainingTimeUnit(String range) {
  final parts = range.split(' ').first.split('-').map(int.parse).toList();
  final duration = parts.length > 1 ? parts[1] : parts.first;

  if (duration >= 1 && duration <= 4) {
    return LocaleKeys.durations_DAYS.tr();
  } else if (duration >= 5 && duration <= 19) {
    return LocaleKeys.durations_WEEKS.tr();
  } else if (duration >= 20) {
    return LocaleKeys.durations_MONTHS.tr();
  }

  return '';
}
