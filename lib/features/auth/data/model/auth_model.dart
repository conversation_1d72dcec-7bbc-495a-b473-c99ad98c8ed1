import 'package:equatable/equatable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';

class AuthModel extends Equatable {
  const AuthModel({
    required this.codeVerifier,
    required this.codeChallenge,
    this.token,
    this.role = Constants.TRAINEE,
  });

  final String? token;
  final String codeVerifier;
  final String codeChallenge;
  final String role;

  Map<String, dynamic> toJson() => {
        Constants.authCode: token,
        Constants.codeVerifier: codeVerifier,
        Constants.codeChallenge: codeChallenge,
        Constants.role: role,
        Constants.mobile: true,
      };

  @override
  List<Object?> get props => [token, codeVerifier, codeChallenge, role];
}

class RefreshTokenModel extends Equatable {
  const RefreshTokenModel({
    required this.refreshToken,
    this.isAdmin = false,
    this.role = Constants.TRAINEE,
  });

  final String refreshToken;
  final String role;
  final bool isAdmin;

  Map<String, dynamic> toJson() => {
        Constants.refreshToken: refreshToken,
        Constants.role: role,
        Constants.isAdmin: isAdmin,
      };

  @override
  List<Object?> get props => [isAdmin, refreshToken, role];
}

class LogoutModel extends Equatable {
  const LogoutModel({required this.token});

  final String token;

  Map<String, dynamic> toJson() => {Constants.token: token};

  @override
  List<Object?> get props => [token];
}
