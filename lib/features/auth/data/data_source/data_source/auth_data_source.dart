import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/app_print.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';

@injectable
class AuthDataSource {
  const AuthDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<(String, String)> signInWithToken(AuthModel authModel) async {
    appPrint(jsonEncode(authModel.toJson()));

    final response = await _dio.post(ApiConstants.signInPath, data: authModel.toJson());

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      if (data is Map) {
        final accessToken = '${data[Constants.accessToken]}';
        final refreshToken = '${data[Constants.refreshToken]}';

        return (accessToken, refreshToken);
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<(String, String)> refreshToken(RefreshTokenModel refreshTokenModel) async {
    final response = await _dio.post(
      ApiConstants.refreshTokenPath,
      data: refreshTokenModel.toJson(),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      if (data is Map) {
        final accessToken = '${data[Constants.accessToken]}';
        final refreshToken = '${data[Constants.refreshToken]}';

        return (accessToken, refreshToken);
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<String> logout({required String refreshToken}) async {
    final response = await _dio.post(ApiConstants.logoutPath, data: jsonEncode(refreshToken));

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      if (data is Map) {
        return data[Constants.token];
      }
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
