import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ChangeAppLanguageWidget extends StatelessWidget {
  const ChangeAppLanguageWidget({this.textColor, super.key});

  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final hive = Hive.box(HiveKeys.hiveNspStorage);
        if (context.locale == const Locale(Constants.localeAR)) {
          await context.setLocale(const Locale(Constants.localeEN));
          await hive.put(HiveKeys.currentLocale, Constants.localeEN);
        } else {
          await context.setLocale(const Locale(Constants.localeAR));
          await hive.put(HiveKeys.currentLocale, Constants.localeAR);
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            context.locale == const Locale(Constants.localeAR)
                ? LocaleKeys.languages_EN.tr()
                : LocaleKeys.languages_AR.tr(),
            style: context.textTheme.textMedium.copyWith(color: textColor),
          ),
          const SizedBox(width: 4),
          Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Image.asset(AssetsPath.globeIcon, width: 24, color: textColor),
          ),
        ],
      ),
    );
  }
}
