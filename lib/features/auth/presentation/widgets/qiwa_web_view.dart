import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/environment/environment_configs.dart';
import 'package:national_skills_platform/core/shared/app_print.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/core/utils/code_challange_generator.dart';
import 'package:national_skills_platform/core/utils/random_string_generator.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/router.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

// coverage:ignore-file
class QiwaWebView extends StatefulWidget {
  const QiwaWebView({super.key, this.logoutToken});

  final String? logoutToken;

  @override
  State<QiwaWebView> createState() => _QiwaWebViewState();
}

class _QiwaWebViewState extends State<QiwaWebView> {
  late final WebViewController _controller;
  late final String codeChallenge;
  final codeVerifier = generateCryptoRandomString();
  int loadingPercentage = 0;
  bool hasError = false;

  @override
  void initState() {
    super.initState();

    codeChallenge = generateCodeChallenge(codeVerifier);
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    _controller = WebViewController.fromPlatformCreationParams(params);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      const baseURL = EnvironmentConfigs.baseQiwaLoginUrl;
      const clientId = EnvironmentConfigs.clientId;
      const scope = 'openid+email+phone+profile';
      const redirectUri = 'nsp://nsp_login';

      String requestUrl =
          '$baseURL?client_id=$clientId&redirect_uri=$redirectUri&code_challenge=$codeChallenge&response_type=code&scope=$scope&code_challenge_method=S256';

      if (widget.logoutToken != null) {
        final logoutToken = widget.logoutToken;
        const logoutRedirectUri = "nsp://nsp_logout";
        requestUrl +=
            "&logout_token=$logoutToken&logout_redirect_uri=$logoutRedirectUri&logout_redirect=true";
      }

      _controller
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onProgress: _onProgress,
            onPageStarted: _onPageStarted,
            onPageFinished: _onPageFinished,
            onWebResourceError: _onWebResourceError,
            onUrlChange: _onUrlChange,
            onNavigationRequest: _onNavigationRequest,
          ),
        )
        ..loadRequest(Uri.parse(requestUrl));

      if (_controller.platform is AndroidWebViewController) {
        AndroidWebViewController.enableDebugging(kDebugMode);
        (_controller.platform as AndroidWebViewController).setMediaPlaybackRequiresUserGesture(
          false,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: hasError
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    Constants.generalErrorMsg,
                    style: context.textTheme.h2.statusWarning.bold,
                    textAlign: TextAlign.center,
                  ),
                  Container(
                    width: 80,
                    margin: const EdgeInsets.only(top: 10),
                    child: AppButton(onTap: () => router.pop(), buttonText: 'Back'),
                  ),
                ],
              ),
            )
          : Padding(
              padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
              child: Stack(
                children: [
                  WebViewWidget(controller: _controller),
                  if (loadingPercentage < 100) const BuildLoader(),
                ],
              ),
            ),
    );
  }

  void _onUrlChange(UrlChange change) {
    appPrint('url change to ${change.url}');
  }

  void _onWebResourceError(WebResourceError error) {
    if (!mounted) return;

    if (error.errorType != WebResourceErrorType.failedSslHandshake) {
      setState(() => hasError = true);
    }
    appPrint('''
    \nPage resource error:
      code: ${error.errorCode}
      description: ${error.description}
      errorType: ${error.errorType}
      isForMainFrame: ${error.isForMainFrame}\n
              ''');
  }

  void _onPageFinished(String url) {
    appPrint('Page finished loading: $url');
  }

  void _onPageStarted(String url) {
    if (!mounted) return;

    setState(() {
      loadingPercentage = 0;
    });
    appPrint('Page started loading: $url');
  }

  void _onProgress(int progress) {
    if (!mounted) return;

    setState(() {
      loadingPercentage = progress;
    });
    appPrint('WebView is loading (progress : $progress%)');
  }

  Future<NavigationDecision> _onNavigationRequest(NavigationRequest request) async {
    if (request.url.startsWith('nsp://nsp_login')) {
      appPrint('navigation to ${request.url}');

      final token = request.url.split('=').lastOrNull;

      final authModel = AuthModel(
        token: token,
        codeVerifier: codeVerifier,
        codeChallenge: codeChallenge,
      );

      router.pop(authModel);

      return NavigationDecision.prevent;
    } else if (request.url.startsWith('nsp://nsp_logout')) {
      router.go('${Routes.rootPage.path}${Routes.homePage.path}');

      return NavigationDecision.prevent;
    }

    return NavigationDecision.navigate;
  }
}
