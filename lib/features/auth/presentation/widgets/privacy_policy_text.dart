import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class PrivacyPolicyText extends StatelessWidget {
  const PrivacyPolicyText({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      runAlignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        Text(
          LocaleKeys.by_sign_in_i_accept.tr(),
          style: context.textTheme.textXSmall.greySecondary,
        ),
        TextButton(
          style: TextButton.styleFrom(padding: const EdgeInsets.symmetric(horizontal: 4)),
          onPressed: () {},
          child: Text(
            LocaleKeys.terms_of_use.tr(),
            style: context.textTheme.textXSmall.accentGreenPrimary,
          ),
        ),
        Text(LocaleKeys.and.tr(), style: context.textTheme.textXSmall.greySecondary),
        const SizedBox(width: 4),
        TextButton(
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          onPressed: () {},
          child: Text(
            LocaleKeys.the_privacy_policy.tr(),
            style: context.textTheme.textXSmall.accentGreenPrimary,
          ),
        ),
      ],
    );
  }
}
