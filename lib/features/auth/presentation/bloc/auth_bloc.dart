import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';
import 'package:national_skills_platform/features/auth/domain/repositories/auth_repository.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/router.dart';

part 'auth_event.dart';

part 'auth_state.dart';

@singleton
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    required AuthRepository authRepository,
    required UserBloc userBloc,
    required AuthTokenProvider authTokenProvider,
  })  : _userBloc = userBloc,
        _authRepository = authRepository,
        _authTokenProvider = authTokenProvider,
        super(const AuthInitial()) {
    on<Authenticate>(_authenticate);
    on<RefreshToken>(_refreshToken);
    on<Logout>(_logout);
  }

  final AuthTokenProvider _authTokenProvider;
  final AuthRepository _authRepository;
  final UserBloc _userBloc;

  Future<void> _authenticate(Authenticate event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());
    final authModel = event.authModel;

    if (authModel == null || authModel.token == null) {
      emit(const AuthError(errorMsg: 'Authentication Error'));
    } else {
      _userBloc.add(const UserEvent.resetUserData());
      await _authRepository.signIn(authModel).errorHandler(
            onSuccess: (result) async => emit(const AuthSuccess()),
            onError: (errorMsg) => emit(AuthError(errorMsg: errorMsg)),
          );
    }
  }

  Future<void> _logout(Logout event, Emitter<AuthState> emit) async {
    emit(const AuthLoading());

    await _authRepository.logout().errorHandler(
          onSuccess: (token) async => emit(LogoutSuccess(token: token)),
          onError: (errorMsg) => emit(LogoutError(errorMsg: errorMsg)),
        );
  }

  Future<void> _refreshToken(RefreshToken event, Emitter<AuthState> emit) async {
    await _authRepository.refreshToken(event.refreshTokenModel).errorHandler(
          onSuccess: (_) async => emit(const RefreshSuccess()),
          onError: (errorMsg) async {
            await _authTokenProvider.invalidateToken();
            if (router.routerDelegate.currentConfiguration.last.matchedLocation !=
                '${Routes.rootPage.path}${Routes.homePage.name}') {
              router
                ..popUntil((route) => route.matchedLocation == Routes.rootPage.path)
                ..pushNamed(Routes.homePage.name);
            }
          },
        );
  }
}
