part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();
}

class AuthInitial extends AuthState {
  const AuthInitial();

  @override
  List<Object?> get props => [];
}

class AuthLoading extends AuthState {
  const AuthLoading();

  @override
  List<Object?> get props => [];
}

class AuthSuccess extends AuthState {
  const AuthSuccess();

  @override
  List<Object?> get props => [];
}

class RefreshSuccess extends AuthState {
  const RefreshSuccess();

  @override
  List<Object?> get props => [];
}

class AuthError extends AuthState {
  const AuthError({required this.errorMsg});

  final String errorMsg;

  @override
  List<Object?> get props => [errorMsg];
}

class LogoutSuccess extends AuthState {
  const LogoutSuccess({required this.token});

  final String token;

  @override
  List<Object?> get props => [token];
}

class LogoutError extends AuthState {
  const LogoutError({required this.errorMsg});

  final String errorMsg;

  @override
  List<Object?> get props => [errorMsg];
}
