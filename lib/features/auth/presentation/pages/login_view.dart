import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/auth/presentation/widgets/change_app_language_widget.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_loading_overlay.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  late final AuthBloc authBloc;

  @override
  void initState() {
    super.initState();
    authBloc = context.read<AuthBloc>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthSuccess) {
            router.pop();
          } else if (state is AuthError) {
            showAppToast(context, message: state.errorMsg);
          }
        },
        builder: (context, state) {
          return AppLoadingOverlay(
            isLoading: state is AuthLoading,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  const SizedBox(height: 60),

                  ///Language Switcher
                  const Align(alignment: Alignment.topRight, child: ChangeAppLanguageWidget()),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          LocaleKeys.header_signIn.tr(),
                          style: context.textTheme.h2.orangeAccentDark.bold,
                        ),

                        const SizedBox(height: 24),

                        ///Qiwa
                        AppButton(
                          onTap: () async {
                            final authModel = await router.pushNamed<AuthModel>(
                              Routes.qiwaWebView.name,
                            );
                            authBloc.add(Authenticate(authModel: authModel));
                          },
                          leading: Image.asset(AssetsPath.qiwaLogo, width: 20),
                          backgroundColor: Colors.white,
                          borderColor: AppColors.accentLight,
                          buttonText: LocaleKeys.qiwaSignIn.tr(),
                          textStyle: context.textTheme.textSmall.medium,
                        ),

                        const SizedBox(height: 28),

                        Align(
                          child: TextButton(
                            onPressed: () => router.pop(),
                            child: Text(
                              LocaleKeys.signInLater.tr(),
                              style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                            ),
                          ),
                        ),

                        /// Code block below commented temporarily until further notice.
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(vertical: 16.0),
                        //   child: Row(
                        //     children: [
                        // const Expanded(child: Divider(color: AppColors.accentLight)),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(horizontal: 24),
                        //   child: Text(
                        //     LocaleKeys.or.tr(),
                        //     style: context.textTheme.nspSubtitle2.greySecondary,
                        //   ),
                        // ),
                        // Padding(
                        //   padding: const EdgeInsets.symmetric(vertical: 16.0),
                        //   child: Row(
                        //     children: [
                        //       const Expanded(child: Divider(color: AppColors.accentLight)),
                        //       Padding(
                        //         padding: const EdgeInsets.symmetric(horizontal: 24),
                        //         child: Text(
                        //           LocaleKeys.or.tr(),
                        //           style: theme.textTheme.nspSubtitle2.greySecondary,
                        //         ),
                        //       ),
                        //       const Expanded(child: Divider(color: AppColors.accentLight)),
                        //     ],
                        //   ),
                        // ),

                        // ///Login Area
                        // Text(
                        //   '${LocaleKeys.email_mobile_or_id.tr()}*',
                        //   style: context.textTheme.nspSubtitle2,
                        // ),
                        // const SizedBox(height: 8),
                        // AppInputField(hintText: LocaleKeys.enter_email_mobile_or_id.tr()),
                        // const SizedBox(height: 16),
                        // Text(
                        //   '${LocaleKeys.password.tr()}*',
                        //   style: context.textTheme.nspSubtitle2,
                        // ),
                        // const SizedBox(height: 8),
                        // AppInputField(
                        //   hintText: LocaleKeys.enter_password.tr(),
                        //   suffixIcon: Image.asset(
                        //     AssetsPath.passwordVisibilityIcon,
                        //     color: AppColors.greySecondary,
                        //   ),
                        // ),
                        // const Spacer(),
                        //
                        // AppButton(onTap: () {}, buttonText: LocaleKeys.header_signIn.tr()),
                        //
                        // const SizedBox(height: 10),

                        // const PrivacyPolicyText(),

                        // const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
