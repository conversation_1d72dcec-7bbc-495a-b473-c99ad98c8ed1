import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/auth/presentation/widgets/change_app_language_widget.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class GetStartedPage extends StatelessWidget {
  const GetStartedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        body: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            image: DecorationImage(image: AssetImage(AssetsPath.nspBackground), fit: BoxFit.cover),
          ),
          child: <PERSON>umn(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Image.asset(AssetsPath.logoWhiteText, height: 28),
                    ),
                    const ChangeAppLanguageWidget(textColor: Colors.white),
                  ],
                ),
              ),
              const Spacer(),
              const SizedBox(height: 11),
              Text(
                LocaleKeys.welcome_to_nsp.tr(),
                style: context.textTheme.h2.white.bold,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                LocaleKeys.unlock_new_opportunities.tr(),
                style: context.textTheme.textMedium.white,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              AppButton(
                onTap: () => router.pushNamed(Routes.homePage.name),
                buttonText: LocaleKeys.get_started.tr(),
                backgroundColor: Colors.white,
                textStyle: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              const SizedBox(height: 36),
            ],
          ),
        ),
      ),
    );
  }
}
