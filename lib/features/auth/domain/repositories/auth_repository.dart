import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/data/data_source/data_source/auth_data_source.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';

@injectable
class AuthRepository {
  const AuthRepository({
    required AuthDataSource dataSource,
    required AuthTokenProvider authTokenProvider,
  })  : _dataSource = dataSource,
        _authTokenProvider = authTokenProvider;

  final AuthDataSource _dataSource;
  final AuthTokenProvider _authTokenProvider;

  Future<void> signIn(AuthModel authModel) async {
    final (token, refreshToken) = await _dataSource.signInWithToken(authModel);
    await _authTokenProvider.updateTokens(token, refreshToken);
  }

  Future<void> refreshToken(RefreshTokenModel refreshTokenModel) async {
    final (token, refreshToken) = await _dataSource.refreshToken(refreshTokenModel);
    await _authTokenProvider.updateTokens(token, refreshToken);
  }

  Future<String> logout() async {
    final refreshToken = await _authTokenProvider.getRefreshToken() ?? '';
    final logoutToken = await _dataSource.logout(refreshToken: refreshToken);
    await _authTokenProvider.invalidateToken();
    return logoutToken;
  }
}
