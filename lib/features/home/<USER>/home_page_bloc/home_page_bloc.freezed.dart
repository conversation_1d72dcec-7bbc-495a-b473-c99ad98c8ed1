// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_page_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HomePageEvent {
  String get locale;

  /// Create a copy of HomePageEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomePageEventCopyWith<HomePageEvent> get copyWith =>
      _$HomePageEventCopyWithImpl<HomePageEvent>(this as HomePageEvent, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomePageEvent &&
            (identical(other.locale, locale) || other.locale == locale));
  }

  @override
  int get hashCode => Object.hash(runtimeType, locale);

  @override
  String toString() {
    return 'HomePageEvent(locale: $locale)';
  }
}

/// @nodoc
abstract mixin class $HomePageEventCopyWith<$Res> {
  factory $HomePageEventCopyWith(HomePageEvent value, $Res Function(HomePageEvent) _then) =
      _$HomePageEventCopyWithImpl;
  @useResult
  $Res call({String locale});
}

/// @nodoc
class _$HomePageEventCopyWithImpl<$Res> implements $HomePageEventCopyWith<$Res> {
  _$HomePageEventCopyWithImpl(this._self, this._then);

  final HomePageEvent _self;
  final $Res Function(HomePageEvent) _then;

  /// Create a copy of HomePageEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? locale = null,
  }) {
    return _then(_self.copyWith(
      locale: null == locale
          ? _self.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class HomePageLoad implements HomePageEvent {
  const HomePageLoad({required this.locale});

  @override
  final String locale;

  /// Create a copy of HomePageEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomePageLoadCopyWith<HomePageLoad> get copyWith =>
      _$HomePageLoadCopyWithImpl<HomePageLoad>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomePageLoad &&
            (identical(other.locale, locale) || other.locale == locale));
  }

  @override
  int get hashCode => Object.hash(runtimeType, locale);

  @override
  String toString() {
    return 'HomePageEvent.load(locale: $locale)';
  }
}

/// @nodoc
abstract mixin class $HomePageLoadCopyWith<$Res> implements $HomePageEventCopyWith<$Res> {
  factory $HomePageLoadCopyWith(HomePageLoad value, $Res Function(HomePageLoad) _then) =
      _$HomePageLoadCopyWithImpl;
  @override
  @useResult
  $Res call({String locale});
}

/// @nodoc
class _$HomePageLoadCopyWithImpl<$Res> implements $HomePageLoadCopyWith<$Res> {
  _$HomePageLoadCopyWithImpl(this._self, this._then);

  final HomePageLoad _self;
  final $Res Function(HomePageLoad) _then;

  /// Create a copy of HomePageEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? locale = null,
  }) {
    return _then(HomePageLoad(
      locale: null == locale
          ? _self.locale
          : locale // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$HomePageState {
//sectors features
  bool get isSectorsLoading;
  String get sectorsError;
  SectorsFeaturesResponseModel? get sectors;

  /// Create a copy of HomePageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HomePageStateCopyWith<HomePageState> get copyWith =>
      _$HomePageStateCopyWithImpl<HomePageState>(this as HomePageState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HomePageState &&
            (identical(other.isSectorsLoading, isSectorsLoading) ||
                other.isSectorsLoading == isSectorsLoading) &&
            (identical(other.sectorsError, sectorsError) || other.sectorsError == sectorsError) &&
            (identical(other.sectors, sectors) || other.sectors == sectors));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isSectorsLoading, sectorsError, sectors);

  @override
  String toString() {
    return 'HomePageState(isSectorsLoading: $isSectorsLoading, sectorsError: $sectorsError, sectors: $sectors)';
  }
}

/// @nodoc
abstract mixin class $HomePageStateCopyWith<$Res> {
  factory $HomePageStateCopyWith(HomePageState value, $Res Function(HomePageState) _then) =
      _$HomePageStateCopyWithImpl;
  @useResult
  $Res call({bool isSectorsLoading, String sectorsError, SectorsFeaturesResponseModel? sectors});
}

/// @nodoc
class _$HomePageStateCopyWithImpl<$Res> implements $HomePageStateCopyWith<$Res> {
  _$HomePageStateCopyWithImpl(this._self, this._then);

  final HomePageState _self;
  final $Res Function(HomePageState) _then;

  /// Create a copy of HomePageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isSectorsLoading = null,
    Object? sectorsError = null,
    Object? sectors = freezed,
  }) {
    return _then(_self.copyWith(
      isSectorsLoading: null == isSectorsLoading
          ? _self.isSectorsLoading
          : isSectorsLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      sectorsError: null == sectorsError
          ? _self.sectorsError
          : sectorsError // ignore: cast_nullable_to_non_nullable
              as String,
      sectors: freezed == sectors
          ? _self.sectors
          : sectors // ignore: cast_nullable_to_non_nullable
              as SectorsFeaturesResponseModel?,
    ));
  }
}

/// @nodoc

class _HomePageState implements HomePageState {
  const _HomePageState({this.isSectorsLoading = true, this.sectorsError = '', this.sectors = null});

//sectors features
  @override
  @JsonKey()
  final bool isSectorsLoading;
  @override
  @JsonKey()
  final String sectorsError;
  @override
  @JsonKey()
  final SectorsFeaturesResponseModel? sectors;

  /// Create a copy of HomePageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HomePageStateCopyWith<_HomePageState> get copyWith =>
      __$HomePageStateCopyWithImpl<_HomePageState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HomePageState &&
            (identical(other.isSectorsLoading, isSectorsLoading) ||
                other.isSectorsLoading == isSectorsLoading) &&
            (identical(other.sectorsError, sectorsError) || other.sectorsError == sectorsError) &&
            (identical(other.sectors, sectors) || other.sectors == sectors));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isSectorsLoading, sectorsError, sectors);

  @override
  String toString() {
    return 'HomePageState(isSectorsLoading: $isSectorsLoading, sectorsError: $sectorsError, sectors: $sectors)';
  }
}

/// @nodoc
abstract mixin class _$HomePageStateCopyWith<$Res> implements $HomePageStateCopyWith<$Res> {
  factory _$HomePageStateCopyWith(_HomePageState value, $Res Function(_HomePageState) _then) =
      __$HomePageStateCopyWithImpl;
  @override
  @useResult
  $Res call({bool isSectorsLoading, String sectorsError, SectorsFeaturesResponseModel? sectors});
}

/// @nodoc
class __$HomePageStateCopyWithImpl<$Res> implements _$HomePageStateCopyWith<$Res> {
  __$HomePageStateCopyWithImpl(this._self, this._then);

  final _HomePageState _self;
  final $Res Function(_HomePageState) _then;

  /// Create a copy of HomePageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isSectorsLoading = null,
    Object? sectorsError = null,
    Object? sectors = freezed,
  }) {
    return _then(_HomePageState(
      isSectorsLoading: null == isSectorsLoading
          ? _self.isSectorsLoading
          : isSectorsLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      sectorsError: null == sectorsError
          ? _self.sectorsError
          : sectorsError // ignore: cast_nullable_to_non_nullable
              as String,
      sectors: freezed == sectors
          ? _self.sectors
          : sectors // ignore: cast_nullable_to_non_nullable
              as SectorsFeaturesResponseModel?,
    ));
  }
}

// dart format on
