import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_features_repository.dart';

part 'home_page_bloc.freezed.dart';

part 'home_page_event.dart';

part 'home_page_state.dart';

@singleton
class HomePageBloc extends Bloc<HomePageEvent, HomePageState> {
  HomePageBloc({required SectorsFeaturesRepository sectorsFeaturesRepository})
      : _sectorsFeaturesRepository = sectorsFeaturesRepository,
        super(const HomePageState()) {
    on<HomePageLoad>(_loadHomePage);
  }

  final SectorsFeaturesRepository _sectorsFeaturesRepository;

  Future<void> _loadHomePage(HomePageLoad event, Emitter<HomePageState> emit) async {
    emit(const HomePageState());

    try {
      final sectorsFeatures = await _sectorsFeaturesRepository.getSectorsFeatures();
      emit(state.copyWith(sectors: sectorsFeatures, isSectorsLoading: false));
    } catch (e) {
      emit(state.copyWith(sectorsError: e.toString(), isSectorsLoading: false));
    }
  }
}
