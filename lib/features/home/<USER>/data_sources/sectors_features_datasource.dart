import 'package:dio/dio.dart';
import 'package:hive/hive.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';

@injectable
class SectorsFeaturesDataSource {
  const SectorsFeaturesDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<SectorsFeaturesResponseModel> getSectorsFeatures() async {
    final String locale = await Hive.box(
      HiveKeys.hiveNspStorage,
    ).get(HiveKeys.currentLocale, defaultValue: Constants.localeEN);

    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      ApiConstants.sectorsFeaturesPath,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return SectorsFeaturesResponseModel.fromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
