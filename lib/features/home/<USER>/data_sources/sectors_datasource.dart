import 'dart:async';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart';

@injectable
class SectorDataSource {
  const SectorDataSource({required Dio dio}) : _dio = dio;

  final Dio _dio;

  Future<List<SectorModel>> getSectors(String locale) async {
    final header = {Constants.acceptLanguageHeader: locale.toUpperCase()};

    final response = await _dio.get(
      ApiConstants.sectorsTreePath,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;

      return sectorsFromJson(data);
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }

  Future<Map<String, dynamic>> getActiveSectors({
    required String locale,
    int page = 0,
    int size = 10000, // Load all data in one page
    List<String>? sort,
    String? term,
    bool? shown,
  }) async {
    final header = {
      Constants.acceptLanguageHeader: locale.toUpperCase(),
      Constants.xCurrentRole: Constants.TRAINEE,
    };

    final queryParams = {
      'page': page.toString(),
      'size': size.toString(),
      if (sort != null && sort.isNotEmpty) 'sort': sort,
      if (term != null && term.isNotEmpty) 'term': term,
      if (shown != null) 'shown': shown.toString(),
    };

    final response = await _dio.get(
      '/trainings/sectors/active',
      queryParameters: queryParams,
      options: Options(headers: {..._dio.options.headers, ...header}),
    );

    if (response.statusCode == Constants.statusCode200 ||
        response.statusCode == Constants.statusCode201) {
      final data = response.data;
      log('Active sectors response: $data');

      return data;
    }

    throw DioException(requestOptions: response.requestOptions, response: response);
  }
}
