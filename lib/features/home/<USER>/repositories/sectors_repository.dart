import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/home/<USER>/data_sources/sectors_datasource.dart';
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart';

@singleton
class SectorRepository {
  SectorRepository({required SectorDataSource dataSource}) : _dataSource = dataSource;

  final SectorDataSource _dataSource;
  List<SectorModel>? _cachedSectors;
  String? _cachedLocale;
  ActiveSectorResponse? _cachedActiveSectors;
  String? _cachedActiveLocale;

  Future<List<SectorModel>> getSectors(String locale) async {
    if (_cachedSectors != null && _cachedLocale == locale) {
      return _cachedSectors!;
    }

    final sectors = await _dataSource.getSectors(locale);
    sectors.removeWhere((sector) => sector.title.isEmpty);

    _cachedSectors = sectors;
    _cachedLocale = locale;

    return sectors;
  }

  Future<ActiveSectorResponse> getActiveSectors({
    required String locale,
    int page = 0,
    int size = 10000, // Load all data in one page
    List<String>? sort,
    String? term,
    bool? shown,
  }) async {
    // Only use cache if no filters are applied and we have cached data for the same locale
    final useCache = term == null &&
        shown == null &&
        (sort == null || sort.isEmpty) &&
        _cachedActiveSectors != null &&
        _cachedActiveLocale == locale;

    if (useCache) {
      return _cachedActiveSectors!;
    }

    final response = await _dataSource.getActiveSectors(
      locale: locale,
      page: page,
      size: size,
      sort: sort,
      term: term,
      shown: shown,
    );

    final activeSectorResponse = ActiveSectorResponse.fromJson(response);

    // Only cache if no filters are applied
    if (term == null && shown == null && (sort == null || sort.isEmpty)) {
      _cachedActiveSectors = activeSectorResponse;
      _cachedActiveLocale = locale;
    }

    return activeSectorResponse;
  }

  // Convert ActiveSectorModel list to SectorModel list for compatibility with existing code
  List<SectorModel> convertActiveSectorsToSectorModels(List<ActiveSectorModel> activeSectors) {
    return activeSectors
        .map(
          (activeSector) => SectorModel(
            id: activeSector.id,
            title: activeSector.title,
            domains: const [], // Active sectors don't include domains
          ),
        )
        .toList();
  }
}
