import 'package:injectable/injectable.dart';
import 'package:national_skills_platform/features/home/<USER>/data_sources/sectors_features_datasource.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';

@injectable
class SectorsFeaturesRepository {
  SectorsFeaturesRepository({required SectorsFeaturesDataSource dataSource})
      : _dataSource = dataSource;

  final SectorsFeaturesDataSource _dataSource;
  SectorsFeaturesResponseModel? _cachedSectorsFeatures;

  Future<SectorsFeaturesResponseModel> getSectorsFeatures() async {
    if (_cachedSectorsFeatures != null) {
      return _cachedSectorsFeatures!;
    }

    final sectorsFeatures = await _dataSource.getSectorsFeatures();
    _cachedSectorsFeatures = sectorsFeatures;

    return sectorsFeatures;
  }

  void clearCache() {
    _cachedSectorsFeatures = null;
  }
}
