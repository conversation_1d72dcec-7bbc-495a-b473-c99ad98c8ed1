import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_back_button.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class SectorsPage extends StatelessWidget {
  const SectorsPage({required this.sectors, super.key});

  final List<SectorFeatureModel> sectors;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: Text(LocaleKeys.sectors.tr(), style: context.textTheme.textLarge.semiBold),
        bottom: const PreferredSize(preferredSize: Size.fromHeight(0), child: AppDivider()),
      ),
      body: ListView.builder(
        itemBuilder: (context, index) {
          return ListTile(
            onTap: () {
              // Apply Sector Filter using its sector.id and navigate to CatalogPage
              GetIt.instance<TrainingsBloc>().add(
                ApplyFilterToList(
                  FilterModel(
                    sectorFilterModel: SectorFilterModel(
                      selectedSectorIds: [sectors[index].id],
                    ),
                  ),
                ),
              );
              // Switch to the Catalog tab (index 1)
              router.goNamed(Routes.catalogPage.name);
            },
            title: Text(sectors[index].title, style: context.textTheme.textSmall.medium),
          );
        },
        itemCount: sectors.length,
      ),
    );
  }
}
