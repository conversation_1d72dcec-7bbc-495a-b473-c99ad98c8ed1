import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/home/<USER>/home_page_bloc/home_page_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/home_page_card.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/popular_trainings.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/recent_in_progress.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/sectors_section.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/welcome_card.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/features/shared/ui_components/nsp_app_bar.dart';

enum AuthStateEnum { loggedIn, notLoggedIn }

class HomePage extends StatefulWidget {
  const HomePage({required this.authState, super.key});

  final AuthStateEnum authState;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late HomePageBloc homePageBloc;
  late AuthTokenProvider authTokenProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    homePageBloc = GetIt.instance.get<HomePageBloc>()
      ..add(HomePageLoad(locale: context.locale.languageCode));
    authTokenProvider = GetIt.instance.get<AuthTokenProvider>();
  }

  @override
  void didUpdateWidget(covariant HomePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.authState != widget.authState) {
      homePageBloc.add(HomePageLoad(locale: context.locale.languageCode));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const NspAppBar(),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final maxHeight = constraints.maxHeight;

          return BlocProvider<HomePageBloc>.value(
            value: homePageBloc,
            child: StreamBuilder<bool>(
              stream: authTokenProvider.authStateStream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) return const BuildLoader();

                return ListView(
                  children: [
                    const SizedBox(height: 24),
                    if (snapshot.data == false)
                      HomePageCard(maxHeight: maxHeight)
                    else ...[
                      const WelcomeCard(),
                      const RecentInProgress(),
                    ],
                    const PopularTrainings(),
                    const SectorsSection(),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }
}
