// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sector_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SectorModel _$SectorModelFromJson(Map<String, dynamic> json) => SectorModel(
      id: json['id'] as String,
      title: json['title'] as String,
      domains: (json['domains'] as List<dynamic>)
          .map((e) => DomainModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SectorModelToJson(SectorModel instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'domains': instance.domains,
    };

DomainModel _$DomainModelFromJson(Map<String, dynamic> json) => DomainModel(
      id: json['id'] as String,
      title: json['title'] as String,
      sectorId: json['sectorId'] as String,
    );

Map<String, dynamic> _$DomainModelToJson(DomainModel instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'sectorId': instance.sectorId,
    };
