import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sectors_features_model.g.dart';

@JsonSerializable()
class SectorsFeaturesResponseModel extends Equatable {
  const SectorsFeaturesResponseModel({
    required this.sectors,
  });

  factory SectorsFeaturesResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SectorsFeaturesResponseModelFromJson(json);

  final List<SectorFeatureModel> sectors;

  Map<String, dynamic> toJson() => _$SectorsFeaturesResponseModelToJson(this);

  @override
  List<Object?> get props => [sectors];

  /// Returns dummy data for testing purposes
  factory SectorsFeaturesResponseModel.dummy() {
    return SectorsFeaturesResponseModel(
      sectors: [
        SectorFeatureModel(
          id: 'a22fe29c-0339-45c0-b0df-1bc64dcb85d9',
          title: 'IT Solutions',
          imageUrl: 'https://example.com/image1.jpg',
          order: 0,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: 'fb499a60-b149-4394-ae86-eab1d5586516',
          title: 'IT Operations and Maintenance',
          imageUrl: 'https://example.com/image2.jpg',
          order: 2,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: 'ac5112c4-031a-4193-9c0a-e32f9d920989',
          title: 'IT Infrastructure',
          imageUrl: 'https://example.com/image3.jpg',
          order: 3,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: 'b8b08ae7-83d7-4c61-9cce-6e70ea95a3e2',
          title: 'Postal Activities',
          imageUrl: 'https://example.com/image4.jpg',
          order: 4,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: '2bf4e2dc-1bd1-44b9-ab32-d2b23987434c',
          title: 'Wholesale and Retail',
          imageUrl: 'https://example.com/image5.jpg',
          order: 5,
          dateAdded: DateTime.now(),
        ),
      ],
    );
  }
}

@JsonSerializable()
class SectorFeatureModel extends Equatable {
  const SectorFeatureModel({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.order,
    required this.dateAdded,
  });

  factory SectorFeatureModel.fromJson(Map<String, dynamic> json) =>
      _$SectorFeatureModelFromJson(json);

  final String id;
  final String title;
  final String imageUrl;
  final int order;
  final DateTime dateAdded;

  Map<String, dynamic> toJson() => _$SectorFeatureModelToJson(this);

  @override
  List<Object?> get props => [id, title, imageUrl, order, dateAdded];
}
