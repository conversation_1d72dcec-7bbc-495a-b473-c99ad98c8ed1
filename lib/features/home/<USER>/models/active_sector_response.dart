import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'active_sector_response.g.dart';

@JsonSerializable()
class ActiveSectorResponse extends Equatable {
  final List<ActiveSectorModel> content;
  final int page;
  final int size;
  final int totalRecords;
  final int totalPages;

  const ActiveSectorResponse({
    required this.content,
    required this.page,
    required this.size,
    required this.totalRecords,
    required this.totalPages,
  });

  factory ActiveSectorResponse.fromJson(Map<String, dynamic> json) =>
      _$ActiveSectorResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ActiveSectorResponseToJson(this);

  @override
  List<Object?> get props => [content, page, size, totalRecords, totalPages];
}

@JsonSerializable()
class ActiveSectorModel extends Equatable {
  final String id;
  final String? code;
  final int sectorCode;
  final String title;
  final String status;

  const ActiveSectorModel({
    required this.id,
    this.code,
    required this.sectorCode,
    required this.title,
    required this.status,
  });

  factory ActiveSectorModel.fromJson(Map<String, dynamic> json) =>
      _$ActiveSectorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ActiveSectorModelToJson(this);

  @override
  List<Object?> get props => [id, code, sectorCode, title, status];
}
