// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'active_sector_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ActiveSectorResponse _$ActiveSectorResponseFromJson(Map<String, dynamic> json) =>
    ActiveSectorResponse(
      content: (json['content'] as List<dynamic>)
          .map((e) => ActiveSectorModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      totalRecords: (json['totalRecords'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
    );

Map<String, dynamic> _$ActiveSectorResponseToJson(ActiveSectorResponse instance) =>
    <String, dynamic>{
      'content': instance.content,
      'page': instance.page,
      'size': instance.size,
      'totalRecords': instance.totalRecords,
      'totalPages': instance.totalPages,
    };

ActiveSectorModel _$ActiveSectorModelFromJson(Map<String, dynamic> json) => ActiveSectorModel(
      id: json['id'] as String,
      code: json['code'] as String?,
      sectorCode: (json['sectorCode'] as num).toInt(),
      title: json['title'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$ActiveSectorModelToJson(ActiveSectorModel instance) => <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'sectorCode': instance.sectorCode,
      'title': instance.title,
      'status': instance.status,
    };
