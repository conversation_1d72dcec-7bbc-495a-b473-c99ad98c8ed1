// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sectors_features_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SectorsFeaturesResponseModel _$SectorsFeaturesResponseModelFromJson(Map<String, dynamic> json) =>
    SectorsFeaturesResponseModel(
      sectors: (json['sectors'] as List<dynamic>)
          .map((e) => SectorFeatureModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SectorsFeaturesResponseModelToJson(SectorsFeaturesResponseModel instance) =>
    <String, dynamic>{
      'sectors': instance.sectors,
    };

SectorFeatureModel _$SectorFeatureModelFromJson(Map<String, dynamic> json) => SectorFeatureModel(
      id: json['id'] as String,
      title: json['title'] as String,
      imageUrl: json['imageUrl'] as String,
      order: (json['order'] as num).toInt(),
      dateAdded: DateTime.parse(json['dateAdded'] as String),
    );

Map<String, dynamic> _$SectorFeatureModelToJson(SectorFeatureModel instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'imageUrl': instance.imageUrl,
      'order': instance.order,
      'dateAdded': instance.dateAdded.toIso8601String(),
    };
