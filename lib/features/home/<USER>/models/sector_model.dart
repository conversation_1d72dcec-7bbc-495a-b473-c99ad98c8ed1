import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sector_model.g.dart';

List<SectorModel> sectorsFromJson(List sectors) =>
    List<SectorModel>.from(sectors.map((x) => SectorModel.fromJson(x)));

@JsonSerializable()
class SectorModel extends Equatable {
  const SectorModel({required this.id, required this.title, required this.domains});

  factory SectorModel.fromJson(Map<String, dynamic> json) => _$SectorModelFromJson(json);
  final String id;
  final String title;
  final List<DomainModel> domains;

  @override
  List<Object?> get props => [id, title, domains];
}

@JsonSerializable()
class DomainModel extends Equatable {
  const DomainModel({required this.id, required this.title, required this.sectorId});

  factory DomainModel.fromJson(Map<String, dynamic> json) => _$DomainModelFromJson(json);
  final String id;
  final String title;
  final String sectorId;

  @override
  List<Object?> get props => [id, title, sectorId];
}
