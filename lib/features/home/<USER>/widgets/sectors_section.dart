import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart' as flutter;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/home_page_bloc/home_page_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_chip.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class SectorsSection extends StatelessWidget {
  const SectorsSection({super.key});

  static const _sectorsColors = [
    Color(0xFF173255),
    Color(0xFF0060FF),
    Color(0xFF2DB473),
    Color(0xFF14415A),
    Color(0xFF55A9B0),
    Color(0xFFFAB414),
  ];

  @override
  Widget build(BuildContext context) {
    final isDirectionRTL = flutter.Directionality.of(context) == flutter.TextDirection.rtl;

    return BlocConsumer<HomePageBloc, HomePageState>(
      buildWhen: (prev, curr) =>
          prev.isSectorsLoading != curr.isSectorsLoading || prev.sectors != curr.sectors,
      listener: (context, state) {
        if (state.sectorsError.isNotEmpty) showAppToast(context, message: state.sectorsError);
      },
      builder: (context, state) {
        // If there's an error or no sectors data, don't show this section
        if (state.sectorsError.isNotEmpty || state.sectors == null) return const SizedBox.shrink();

        final sectors = state.sectors!.sectors;
        if (sectors.isEmpty) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.sectors.tr(),
                    style: context.textTheme.h3.bold.accentExtraDark,
                  ),
                  TextButton(
                    onPressed: () {
                      router.pushNamed(Routes.sectorsPage.path, extra: state.sectors!.sectors);
                    },
                    child: Text(
                      LocaleKeys.view_all.tr(),
                      style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            if (state.isSectorsLoading)
              const ShimmerPlaceholder(
                height: 42,
                borderRadius: 8,
                padding: EdgeInsets.symmetric(horizontal: 16),
              )
            else
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      for (int i = 0; i < sectors.length; i++)
                        AppChip(
                          height: 42,
                          onTap: () {
                            // Apply Sector Filter using its sector.id and navigate to CatalogPage
                            GetIt.instance<TrainingsBloc>().add(
                              ApplyFilterToList(
                                FilterModel(
                                  sectorFilterModel: SectorFilterModel(
                                    selectedSectorIds: [sectors[i].id],
                                  ),
                                ),
                              ),
                            );
                            // Switch to the Catalog tab (index 1)
                            router.goNamed(Routes.catalogPage.name);
                          },
                          text: sectors[i].title,
                          textColor: Colors.white,
                          margin: isDirectionRTL
                              ? EdgeInsets.only(left: i == sectors.length - 1 ? 0 : 8)
                              : EdgeInsets.only(right: i == sectors.length - 1 ? 0 : 8),
                          backgroundColor: _sectorsColors[i % _sectorsColors.length],
                        ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 40),
            //
          ],
        );
      },
    );
  }
}
