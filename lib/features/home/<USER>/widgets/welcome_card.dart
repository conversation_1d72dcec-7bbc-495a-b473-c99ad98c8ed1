import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart' as flutter;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/temp/plural_translations.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class WelcomeCard extends StatefulWidget {
  const WelcomeCard({super.key});

  @override
  State<WelcomeCard> createState() => _WelcomeCardState();
}

class _WelcomeCardState extends State<WelcomeCard> {
  late UserBloc userBloc;

  @override
  void initState() {
    super.initState();
    userBloc = GetIt.instance.get<UserBloc>();
    if (userBloc.state.userData == null) {
      userBloc.add(const UserEvent.getUserData());
    }
  }

  @override
  Widget build(BuildContext context) {
    const cardHeight = 100.0;
    final isDirectionRTL = flutter.Directionality.of(context) == flutter.TextDirection.rtl;

    return BlocBuilder<UserBloc, UserState>(
      bloc: userBloc,
      buildWhen: (prev, curr) =>
          prev.isUserAvatarLoading != curr.isUserAvatarLoading ||
          prev.isUserDataLoading != curr.isUserDataLoading,
      builder: (context, state) {
        final userAvatar = state.userAvatar ?? '';

        return AppShimmer(
          isLoading: state.isUserAvatarLoading && state.isUserDataLoading,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: cardHeight,
              child: Stack(
                children: [
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.asset(
                        AssetsPath.imageWithLinesMiniNew,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    left: isDirectionRTL ? 0 : 16,
                    right: 16,
                    bottom: 20,
                    child: Row(
                      children: [
                        AppShimmer(
                          isLoading: state.isUserAvatarLoading,
                          child: CircleAvatar(
                            radius: 30,
                            backgroundColor: AppColors.accentLight,
                            child: AspectRatio(
                              aspectRatio: 1,
                              child: ClipOval(
                                child: Platform.environment.containsKey(Constants.flutterTest) ||
                                        userAvatar.isEmpty
                                    //this is for golden tests
                                    ? const Icon(Icons.person_rounded, size: 30)
                                    : CachedNetworkImage(
                                        fit: BoxFit.cover,
                                        imageUrl: userAvatar,
                                        placeholder: (context, url) =>
                                            const Icon(Icons.person_rounded, size: 30),
                                        errorWidget: (context, url, error) =>
                                            const Icon(Icons.person_rounded, size: 30),
                                      ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppShimmer(
                                isLoading: state.isUserDataLoading,
                                child: Text(
                                  LocaleKeys.welcome_user.tr(args: [(state.userData?.name ?? '')]),
                                  style: context.textTheme.textLarge.semiBold.white,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              _NumberOfTrainingsInProgress(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _NumberOfTrainingsInProgress extends flutter.StatelessWidget {
  @override
  flutter.Widget build(flutter.BuildContext context) {
    return BlocBuilder<MyLearningsBloc, MyLearningsState>(
      bloc: GetIt.instance.get<MyLearningsBloc>(),
      builder: (context, state) {
        return Text(
          mapNumberToTrainingsInProgress(
            state.allRecentTrainings.length,
            context.locale.languageCode,
          ),
          style: context.textTheme.textSmall.white,
        );
      },
    );
  }
}
