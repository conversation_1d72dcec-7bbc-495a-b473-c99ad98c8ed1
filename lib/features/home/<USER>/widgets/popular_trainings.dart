import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/shared/trainings_card.dart';
import 'package:national_skills_platform/features/shared/ui_components/shimmer_placeholder.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class PopularTrainings extends StatefulWidget {
  const PopularTrainings({super.key});

  @override
  State<PopularTrainings> createState() => _PopularTrainingsState();
}

class _PopularTrainingsState extends State<PopularTrainings> with WidgetsBindingObserver {
  late TrainingsBloc trainingsBloc;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    trainingsBloc = GetIt.instance.get<TrainingsBloc>();
    // Always reset to default state when showing on HomePage
    trainingsBloc.add(const GetTrainingsListEvent());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Reset filters when app is resumed (user comes back to the app)
    if (state == AppLifecycleState.resumed) {
      trainingsBloc.add(const GetTrainingsListEvent());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TrainingsBloc, TrainingsState>(
      bloc: trainingsBloc,
      listener: (context, state) {
        if (state is TrainingsError) showAppToast(context, message: state.message);
      },
      builder: (context, state) {
        if (state is TrainingsError) return const SizedBox.shrink();

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                LocaleKeys.popularTrainings.tr(),
                style: context.textTheme.h3.bold.accentExtraDark,
              ),
            ),
            const SizedBox(height: 20),
            if (state is TrainingsLoading)
              const ShimmerPlaceholder(height: 300, padding: EdgeInsets.all(16), borderRadius: 8)
            else if (state is TrainingsLoaded)
              LimitedBox(
                maxHeight: 335,
                child: ListView.builder(
                  itemCount: state.trainingsModel.trainingsList.length > 5
                      ? 6
                      : state.trainingsModel.trainingsList.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int i) {
                    if (i == 5) {
                      return Center(
                        child: TextButton(
                          onPressed: () {
                            router.go('${Routes.rootPage.path}${Routes.catalogPage.path}');
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 70),
                            child: Text(
                              LocaleKeys.view_all.tr(),
                              style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                            ),
                          ),
                        ),
                      );
                    }

                    return Padding(
                      padding: EdgeInsets.only(
                        left: i == 0 ? 16.0 : 8.0,
                        right: i == state.trainingsModel.trainingsList.length - 1 ? 16.0 : 0.0,
                      ),
                      child: TrainingCard(
                        axisHorizontal: false,
                        trainingModel: state.trainingsModel.trainingsList[i],
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }
}
