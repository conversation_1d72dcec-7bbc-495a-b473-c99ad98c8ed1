import 'package:flutter/material.dart' as flutter;
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/converters/training_type_string.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/completed_sections_progress_info.dart';

class RecentCourseDetails extends StatelessWidget {
  const RecentCourseDetails(this.training, {super.key});

  final ApplicantTraining training;

  @override
  Widget build(BuildContext context) {
    final isDirectionRTL = flutter.Directionality.of(context) == flutter.TextDirection.rtl;

    return Flexible(
      flex: 4,
      child: Column(
        children: [
          const SizedBox(height: 12),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  applicantTrainingTypeString(training.learningType),
                  style: context.textTheme.textSmall.medium.accentGreenPrimary,
                ),
                const SizedBox(height: 4),
                Flexible(
                  child: Text(
                    training.trainingTitle,
                    softWrap: false,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.textSmall.semiBold,
                  ),
                ),
                const SizedBox(height: 12),
                CompletedSectionsProgressInfo(
                  allSections: training.allSections,
                  passedSections: training.passedSections,
                  trainingCompletedPercent: training.passedScore,
                  shouldPercentageTextBeBlue: false,
                ),
              ],
            ),
          ),

          const SizedBox(height: 4),

          ///Progress Indicator
          ClipRRect(
            borderRadius: isDirectionRTL
                ? const BorderRadius.only(bottomLeft: Radius.circular(8))
                : const BorderRadius.only(bottomRight: Radius.circular(8)),
            child: LinearProgressIndicator(
              value: training.passedScore / 100,
              minHeight: 8,
              color: AppColors.greenAccentPrimary,
              backgroundColor: const Color(0xFFE6E6E7),
            ),
          ),
        ],
      ),
    );
  }
}
