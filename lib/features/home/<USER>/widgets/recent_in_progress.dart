import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart' as flutter;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/recent_course_details.dart';

import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class RecentInProgress extends StatefulWidget {
  const RecentInProgress({super.key});

  @override
  State<RecentInProgress> createState() => _RecentInProgressState();
}

class _RecentInProgressState extends State<RecentInProgress> {
  late MyLearningsBloc myLearningsBloc;

  @override
  void initState() {
    super.initState();
    myLearningsBloc = GetIt.instance.get<MyLearningsBloc>();
    if (!myLearningsBloc.state.isLoading || myLearningsBloc.state.error.isNotEmpty) {
      myLearningsBloc.add(const GetAllMyLearnings());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MyLearningsBloc, MyLearningsState>(
      bloc: myLearningsBloc,
      listener: (context, state) {
        if (state.error.isNotEmpty) showAppToast(context, message: state.error);
      },
      builder: (context, state) {
        if (!state.isLoading && state.allRecentTrainings.isEmpty) {
          //hides the section if there are no recent courses
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                LocaleKeys.recentInProgress.tr(),
                style: context.textTheme.h3.bold.accentExtraDark,
              ),
            ),
            const SizedBox(height: 20),
            if (state.isLoading)
              const ShimmerPlaceholder(
                height: 80,
                borderRadius: 8,
                padding: EdgeInsets.symmetric(horizontal: 16),
              )
            else if (state.allRecentTrainings.isNotEmpty ||
                state.myLearningsModel.applicantLearningTrackViewList.isNotEmpty)
              //Horizontal list of recent courses
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      ...state.allRecentTrainings.take(5).map((training) {
                        return GestureDetector(
                          onTap: () async {
                            if (training.id.isNotEmpty) {
                              await router.pushNamed(
                                Routes.trainingDetailsPage.name,
                                extra: training.trainingId,
                              );
                            } else {
                              showAppToast(context, message: 'Failed to open the card');
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.only(
                              right: training == state.allRecentTrainings.last ? 0 : 8,
                            ),
                            foregroundDecoration: BoxDecoration(
                              border: Border.all(color: AppColors.greyTertiary),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            width: MediaQuery.sizeOf(context).width * 0.87,
                            child: Row(
                              children: [
                                /// Training Image
                                if (training.profileImageUrl.isNotEmpty)
                                  _TrainingImage(profileImageUrl: training.profileImageUrl),

                                /// Training Details. Body Part
                                RecentCourseDetails(training),
                              ],
                            ),
                          ),
                        );
                      }),
                      if (state.allRecentTrainings.length > 5)
                        Center(
                          child: TextButton(
                            onPressed: () => router.go(
                              '${Routes.rootPage.path}${Routes.myLearningPage.path}',
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 54),
                              child: Text(
                                LocaleKeys.view_all.tr(),
                                style: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class _TrainingImage extends StatelessWidget {
  const _TrainingImage({required this.profileImageUrl});

  final String profileImageUrl;

  @override
  Widget build(BuildContext context) {
    final isDirectionRTL = flutter.Directionality.of(context) == flutter.TextDirection.rtl;

    return Flexible(
      child: ClipRRect(
        borderRadius: isDirectionRTL
            ? const BorderRadius.only(
                topRight: Radius.circular(8),
                bottomRight: Radius.circular(8),
              )
            : const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              ),
        child: Platform.environment.containsKey(Constants.flutterTest)
            //this is for golden tests
            ? Container(height: 100, color: Colors.grey)
            : CachedNetworkImage(height: 100, imageUrl: profileImageUrl, fit: BoxFit.fitHeight),
      ),
    );
  }
}
