import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

class HomePageCard extends StatelessWidget {
  const HomePageCard({required this.maxHeight, super.key});

  final double maxHeight;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: maxHeight - kBottomNavigationBarHeight,
      child: Stack(
        children: [
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.asset(
                AssetsPath.imageWithLinesNew,
                fit: BoxFit.cover,
              ),
            ),
          ),

          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(LocaleKeys.skills_portal.tr(), style: context.textTheme.h2.semiBold.white),

                  const SizedBox(height: 18),

                  Text(LocaleKeys.home_card_body.tr(), style: context.textTheme.textMedium.white),
                  const SizedBox(height: 32),

                  AppButton(
                    onTap: () => router.pushNamed(Routes.login.name),
                    buttonText: LocaleKeys.header_signIn.tr(),
                    backgroundColor: Colors.white,
                    textStyle: context.textTheme.textSmall.semiBold.accentGreenPrimary,
                  ),

                  //
                ],
              ),
            ),
          ),

          Positioned(
            bottom: 8,
            left: 0,
            right: 0,
            child: Column(
              children: [
                Text(
                  LocaleKeys.scrollDown.tr(),
                  style: context.textTheme.textMedium.copyWith(
                    color: AppColors.neutralWhite.withValues(alpha: 0.8),
                  ),
                ),
                Image.asset(AssetsPath.arrowIcon, width: 24),
              ],
            ),
          ),
          //
        ],
      ),
    );
  }
}
