import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';

class BuildLoader extends StatelessWidget {
  const BuildLoader({super.key, this.loaderColor, this.padding, this.strokeWidth, this.value});

  final Color? loaderColor;
  final EdgeInsets? padding;
  final double? strokeWidth;
  final double? value;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: padding ?? const EdgeInsets.all(8),
        child: CircularProgressIndicator(
          value: value,
          color: loaderColor ?? AppColors.orangeAccentPrimary,
          backgroundColor: AppColors.greyExtraLight,
          strokeCap: StrokeCap.round,
          strokeWidth: strokeWidth ?? 6,
        ),
      ),
    );
  }
}
