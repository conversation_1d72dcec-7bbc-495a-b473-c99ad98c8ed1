import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

class AppInputField extends StatefulWidget {
  const AppInputField({
    super.key,
    this.controller,
    this.enabled = true,
    this.hintText = '',
    this.obscureText = false,
    this.keyboardType,
    this.onChanged,
    this.backgroundColor,
    this.prefixIcon,
    this.suffixIcon,
    this.isDense = false,
    this.inputFormatters,
    this.contentPadding,
    this.textAlign = TextAlign.start,
    this.textStyle,
    this.initialValue,
    this.validator,
    this.onTap,
    this.readOnly,
    this.label,
    this.errorStyle,
    this.borderColor,
    this.onSubmit,
    this.onClear,
  });

  final TextEditingController? controller;
  final bool enabled;
  final bool? readOnly;
  final String hintText;
  final String? label;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Function(String)? onChanged;
  final Function(String)? onSubmit;
  final Color? backgroundColor;
  final Color? borderColor;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool isDense;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? contentPadding;
  final TextAlign textAlign;
  final TextStyle? textStyle;
  final TextStyle? errorStyle;
  final String? initialValue;
  final Function(String?)? validator;
  final VoidCallback? onTap;
  final VoidCallback? onClear;

  @override
  State<AppInputField> createState() => _AppInputFieldState();
}

class _AppInputFieldState extends State<AppInputField> {
  late TextEditingController textEditingController;
  bool isEmpty = true;

  @override
  void initState() {
    super.initState();
    textEditingController = widget.controller ?? TextEditingController(text: widget.initialValue);
    isEmpty = textEditingController.text.isEmpty;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      textEditingController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TextFormField(
      onTap: widget.onTap,
      readOnly: widget.readOnly ?? false,
      controller: textEditingController,
      obscureText: widget.obscureText,
      onChanged: (text) {
        widget.onChanged?.call(text);
        setState(() {
          isEmpty = textEditingController.text.isEmpty;
        });
      },
      onFieldSubmitted: (text) {
        widget.onSubmit?.call(text);
        setState(() {
          isEmpty = textEditingController.text.isEmpty;
        });
      },
      validator: (text) => widget.validator?.call(text),
      textAlign: widget.textAlign,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      style: widget.textStyle,
      decoration: InputDecoration(
        enabled: widget.enabled,
        fillColor: widget.backgroundColor ?? theme.colorScheme.surface,
        filled: true,
        prefixIcon: widget.prefixIcon,
        suffixIcon: isEmpty
            ? null
            : widget.isDense
                ? null
                : widget.suffixIcon ??
                    IconButton(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onPressed: () {
                        widget.onClear?.call();
                        textEditingController.clear();
                        setState(() => isEmpty = textEditingController.text.isEmpty);
                      },
                      icon: const Icon(Icons.close_rounded, color: AppColors.greySecondary),
                    ),
        hintText: widget.hintText,
        hintStyle: context.textTheme.textSmall.greySecondary,
        label: widget.label != null ? Text(widget.label!) : null,
        labelStyle: TextStyle(
          leadingDistribution: (defaultTargetPlatform == TargetPlatform.iOS)
              ? TextLeadingDistribution.even
              : TextLeadingDistribution.proportional,
        ),
        contentPadding: widget.contentPadding ?? const EdgeInsets.all(10),
        isDense: widget.isDense,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide(color: widget.borderColor ?? AppColors.accentLight),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide(color: widget.borderColor ?? AppColors.accentLight),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: BorderSide(color: widget.borderColor ?? AppColors.accentLight),
        ),
        errorStyle: widget.errorStyle,
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.red),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: Colors.red),
        ),
      ),
    );
  }
}
