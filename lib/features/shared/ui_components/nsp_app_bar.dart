import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_bar_logo.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_divider.dart';

class NspAppBar extends StatelessWidget implements PreferredSizeWidget {
  const NspAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leadingWidth: MediaQuery.sizeOf(context).width / 3.5,
      leading: const AppBarLogo(),
      scrolledUnderElevation: 0,
      actions: [
        StreamBuilder<bool>(
          stream: GetIt.instance.get<AuthTokenProvider>().authStateStream,
          builder: (context, isAuthenticated) {
            if (isAuthenticated.data == true) {
              return GestureDetector(
                onTap: () {},
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Image.asset(
                    width: 44,
                    AssetsPath.notificationIcon,
                    color: AppColors.greenAccentPrimary,
                  ),
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ],
      bottom: const PreferredSize(preferredSize: Size.fromHeight(0), child: AppDivider()),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
