import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';

class AppDivider extends StatelessWidget {
  const AppDivider({super.key, this.padding = EdgeInsets.zero});

  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: const Divider(color: AppColors.accentLight, height: 0.5, thickness: 0.5),
    );
  }
}
