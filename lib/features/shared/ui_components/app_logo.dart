import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:national_skills_platform/core/shared/constants.dart';

class AppLogo extends StatelessWidget {
  const AppLogo({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topLeft,
      child: Container(
        margin: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
        child: SvgPicture.asset(AssetsPath.logoSvg, semanticsLabel: 'Logo', height: 40),
      ),
    );
  }
}
