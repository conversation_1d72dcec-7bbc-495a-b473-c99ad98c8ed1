import 'package:easy_localization/easy_localization.dart' as localization;
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';

class EnvironmentBanner extends StatelessWidget {
  const EnvironmentBanner({required this.child, required this.environmentName, super.key});

  final Widget child;
  final String environmentName;

  @override
  Widget build(BuildContext context) {
    final isRtlMode = context.locale.languageCode == Constants.localeAR;

    return Directionality(
      textDirection: isRtlMode ? TextDirection.rtl : TextDirection.ltr,
      child: Banner(
        message: environmentName,
        location: BannerLocation.topEnd,
        color: Colors.green[300]!,
        child: child,
      ),
    );
  }
}
