import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

class AppTabBar extends StatelessWidget {
  const AppTabBar({
    required this.tabController,
    required this.onTabTapped,
    required this.tabList,
    super.key,
  });

  final TabController tabController;
  final List<String> tabList;
  final Function(int) onTabTapped;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
      height: 44,
      child: TabBar(
        controller: tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        unselectedLabelStyle: context.textTheme.textSmall.medium,
        indicatorColor: AppColors.greenAccentPrimary,
        labelStyle: context.textTheme.textSmall.accentGreenPrimary.medium,
        onTap: onTabTapped,
        splashFactory: NoSplash.splashFactory,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: tabList.map((e) => Tab(text: e)).toList(),
      ),
    );
  }
}
