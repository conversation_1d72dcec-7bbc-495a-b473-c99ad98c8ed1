import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

enum ToastType { error, warning, success }

void showAppToast(
  BuildContext context, {
  required String message,
  String? title,
  ToastType type = ToastType.error,
}) {
  late Widget body;

  switch (type) {
    case ToastType.warning:
      body = _ToastWidget.warning(title: title, msg: message);
    case ToastType.success:
      body = _ToastWidget.success(title: title, msg: message);
    case ToastType.error:
      body = _ToastWidget.error(title: title, msg: message);
  }

  showToastWidget(
    body,
    context: context,
    animation: StyledToastAnimation.slideFromTop,
    reverseAnimation: StyledToastAnimation.slideToTop,
    position: StyledToastPosition.top,
    startOffset: const Offset(0, -3),
    reverseEndOffset: const Offset(0, -3),
    duration: const Duration(seconds: 7),
    animDuration: const Duration(seconds: 1),
    curve: Curves.fastOutSlowIn,
    dismissOtherToast: true,
    reverseCurve: Curves.fastOutSlowIn,
  );
}

class _ToastWidget extends StatelessWidget {
  const _ToastWidget({required this.assetName, this.backgroundColor, this.title, this.message});

  factory _ToastWidget.error({required String msg, String? title}) => _ToastWidget(
        title: title ?? LocaleKeys.error.tr(),
        message: msg,
        assetName: AssetsPath.errorIcon,
        backgroundColor: AppColors.statusWarningLight,
      );

  factory _ToastWidget.warning({required String msg, String? title}) => _ToastWidget(
        title: title,
        message: msg,
        assetName: AssetsPath.warningIcon,
        backgroundColor: AppColors.orangeAccentLight,
      );

  factory _ToastWidget.success({required String msg, String? title}) => _ToastWidget(
        title: title,
        message: msg,
        assetName: AssetsPath.successIcon,
        backgroundColor: AppColors.statusSuccessLight,
      );
  final Color? backgroundColor;
  final String? message;
  final String? title;
  final String assetName;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              offset: const Offset(0, 1),
              blurRadius: 1,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5),
              child: Image.asset(assetName, width: 24, height: 24),
            ),
            const SizedBox(width: 12),
            Flexible(
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (title != null) Text(title ?? '', style: context.textTheme.textSmall.medium),
                    Text(message ?? '', style: context.textTheme.textSmall),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
