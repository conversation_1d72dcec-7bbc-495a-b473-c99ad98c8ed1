import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';

class AppBarLogo extends StatelessWidget {
  const AppBarLogo({super.key});

  @override
  Widget build(BuildContext context) {
    final isDirectionRTL = Directionality.of(context) == TextDirection.rtl;

    return Padding(
      padding: EdgeInsets.only(
        top: 10,
        bottom: 10,
        left: isDirectionRTL ? 0 : 16.0,
        right: isDirectionRTL ? 16 : 0,
      ),
      child: Image.asset(AssetsPath.logoBlackText),
    );
  }
}
