import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';

class AppCustomRadio extends StatelessWidget {
  const AppCustomRadio({required this.isSelected, this.onChanged, super.key});

  final bool isSelected;
  final ValueChanged<bool>? onChanged;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (onChanged != null) {
          onChanged?.call(!isSelected);
        }
      },
      child: Image.asset(
        isSelected ? AssetsPath.radioIconActive : AssetsPath.radioIconInactive,
        height: 16,
      ),
    );
  }
}
