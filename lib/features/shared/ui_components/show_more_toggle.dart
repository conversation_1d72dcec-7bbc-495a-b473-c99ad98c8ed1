import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

class ShowMoreToggle extends StatelessWidget {
  const ShowMoreToggle({required this.isExpanded, required this.onTap, super.key});

  final bool isExpanded;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return IntrinsicWidth(
      child: AppButton(
        padding: EdgeInsets.zero,
        leading: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: Icon(
            isExpanded ? Icons.keyboard_arrow_up_rounded : Icons.keyboard_arrow_down_rounded,
            color: AppColors.greenAccentPrimary,
            size: 20,
          ),
        ),
        onTap: onTap,
        backgroundColor: Colors.transparent,
        buttonText: isExpanded ? LocaleKeys.showLess.tr() : LocaleKeys.showMore.tr(),
        textStyle: context.textTheme.textSmall.semiBold.accentGreenPrimary,
      ),
    );
  }
}
