import 'dart:math';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:national_skills_platform/core/shared/shared.dart';

class AppBackButton extends StatelessWidget {
  const AppBackButton({super.key, this.iconColor});

  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    final isArabic = Localizations.localeOf(context).languageCode == Constants.localeAR;

    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: context.pop,
      icon: Transform(
        alignment: Alignment.center,
        transform: isArabic ? Matrix4.rotationY(pi) : Matrix4.identity(),
        child: Image.asset(AssetsPath.backIcon, height: 28, color: iconColor),
      ),
    );
  }
}
