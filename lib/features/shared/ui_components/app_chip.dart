import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';

class AppChip extends StatelessWidget {
  const AppChip({
    required this.text,
    super.key,
    this.textColor,
    this.backgroundColor,
    this.onTap,
    this.margin,
    this.height,
  });

  final String text;
  final Color? textColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final EdgeInsets? margin;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: onTap != null,
      label: text,
      child: GestureDetector(
        onTap: onTap,
        excludeFromSemantics: true,
        child: ConstrainedBox(
          constraints: BoxConstraints(minHeight: height ?? 34),
          child: Container(
            margin: margin,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 7),
            decoration: BoxDecoration(
              color: backgroundColor ?? AppColors.accentExtraLight,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: backgroundColor ?? AppColors.accentLight),
            ),
            child: IntrinsicWidth(
              child: Center(
                child: Text(
                  text,
                  style: context.textTheme.textXSmall.medium.copyWith(color: textColor),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
