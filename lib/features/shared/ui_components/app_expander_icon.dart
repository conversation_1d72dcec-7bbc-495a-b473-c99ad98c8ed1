import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';

class AppExpanderIcon extends StatelessWidget {
  const AppExpanderIcon({required this.isExpanded, this.disabled = false, super.key})
      : assert(!(disabled && isExpanded));

  final bool isExpanded;
  final bool disabled;

  @override
  Widget build(BuildContext context) {
    return Icon(
      isExpanded ? Icons.remove_circle_outline : Icons.add_circle_outline,
      size: 20,
      color: disabled ? AppColors.greySecondary : AppColors.neutralBlack,
      semanticLabel: isExpanded ? 'collapse contents' : 'expand contents',
    );
  }
}
