import 'package:flutter/material.dart';
import 'package:loading_overlay_pro/loading_overlay_pro.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';

class AppLoadingOverlay extends StatelessWidget {
  const AppLoadingOverlay({required this.isLoading, required this.child, super.key});

  final bool isLoading;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return LoadingOverlayPro(
      isLoading: isLoading,
      progressIndicator: const BuildLoader(),
      child: child,
    );
  }
}
