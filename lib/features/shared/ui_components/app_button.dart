import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

class AppButton extends StatelessWidget {
  const AppButton({
    required this.onTap,
    super.key,
    this.backgroundColor = AppColors.greenAccentPrimary,
    this.buttonText,
    this.leading,
    this.trailing,
    this.margin,
    this.textStyle,
    this.height = 48,
    this.width,
    this.borderColor,
    this.padding,
    this.borderRadius = 4.0,
    this.mainAxisAlignment = MainAxisAlignment.center,
  });

  final String? buttonText;
  final VoidCallback onTap;
  final Widget? leading;
  final Widget? trailing;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final Color? borderColor;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final MainAxisAlignment mainAxisAlignment;
  final double borderRadius;
  final double height;
  final double? width;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      button: true,
      child: GestureDetector(
        onTap: onTap,
        excludeFromSemantics: true,
        child: Container(
          margin: margin,
          height: height,
          padding: padding ?? const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius),
            border: borderColor != null ? Border.all(color: borderColor!) : null,
            color: backgroundColor,
          ),
          width: width ?? double.infinity,
          child: Row(
            mainAxisAlignment: mainAxisAlignment,
            children: [
              if (leading != null) leading!,
              const SizedBox(width: 10),
              Text(buttonText ?? '', style: textStyle ?? getTextStyle(context)),
              const SizedBox(width: 10),
              if (trailing != null) trailing!,
            ],
          ),
        ),
      ),
    );
  }

  TextStyle getTextStyle(BuildContext context) {
    if (backgroundColor == AppColors.greenAccentPrimary) {
      return context.textTheme.textSmall.semiBold.white;
    }
    return context.textTheme.textSmall.semiBold;
  }
}
