import 'package:chewie/chewie.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:video_player/video_player.dart';

class AppVideoPlayer extends StatefulWidget {
  const AppVideoPlayer(
    this.promoVideoUrl, {
    super.key,
    this.allowFullScreen = true,
    this.allowMuting = true,
    this.disableBorderCurve = false,
    this.customControls,
    this.markAsCompleted,
  });

  final String promoVideoUrl;
  final bool allowFullScreen;
  final bool allowMuting;
  final bool disableBorderCurve;
  final Widget? customControls;
  final VoidCallback? markAsCompleted;

  @override
  _AppVideoPlayerState createState() => _AppVideoPlayerState();
}

class _AppVideoPlayerState extends State<AppVideoPlayer> {
  late VideoPlayerController _controller;
  ChewieController? _chewieController;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.promoVideoUrl))
      ..initialize().then((_) {
        _chewieController = ChewieController(
          videoPlayerController: _controller,
          autoInitialize: true,
          showOptions: false,
          customControls: widget.customControls ??
              const CupertinoControls(
                backgroundColor: Color.fromRGBO(41, 41, 41, 0.7),
                iconColor: Color.fromARGB(255, 200, 200, 200),
              ),
          allowFullScreen: widget.allowFullScreen,
          allowMuting: widget.allowMuting,
          deviceOrientationsAfterFullScreen: [
            DeviceOrientation.portraitUp,
            DeviceOrientation.portraitDown,
          ],
        );
        setState(() {});
      });

    _controller.addListener(videoListener);
  }

  void videoListener() {
    if (_controller.value.duration == Duration.zero) return;

    //if video watched 90% mark as completed
    if (_controller.value.position >= (_controller.value.duration * 0.9)) {
      widget.markAsCompleted?.call();
    }
  }

  @override
  void dispose() {
    _controller
      ..removeListener(videoListener)
      ..dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: ClipRRect(
        borderRadius:
            widget.disableBorderCurve ? BorderRadius.circular(0) : BorderRadius.circular(8),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: _controller.value.isInitialized && _chewieController != null
              ? Chewie(controller: _chewieController!)
              : const BuildLoader(),
        ),
      ),
    );
  }
}
