import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class VideoActionButton extends StatelessWidget {
  const VideoActionButton({super.key, required this.onTap, required this.child});

  final VoidCallback onTap;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.orientationOf(context);
    final buttonPadding = orientation == Orientation.portrait ? 16.0 : 24.0;
    const backgroundColor = Color.fromRGBO(41, 41, 41, 0.7);

    return Padding(
      padding: const EdgeInsets.all(5.0),
      child: GestureDetector(
        onTap: onTap,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10.0),
            child: Container(
              color: backgroundColor,
              height: orientation == Orientation.portrait ? 30.0 : 47.0,
              padding: EdgeInsets.only(left: buttonPadding, right: buttonPadding),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}
