import 'package:chewie/chewie.dart';
//
//ignore: implementation_imports
import 'package:chewie/src/notifiers/index.dart' show PlayerNotifier;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/video_player/widgets/video_action_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/share_lesson.dart';
import 'package:provider/provider.dart';

class PlayerCustomControls extends StatelessWidget {
  const PlayerCustomControls(this.lessonParams, {super.key});

  final LessonParams lessonParams;

  @override
  Widget build(BuildContext context) {
    final chewieController = ChewieController.of(context);
    final trainingConsumptionModel =
        context.read<TrainingConsumptionBloc>().state.trainingConsumptionModel;
    final showControls = !Provider.of<PlayerNotifier>(context).hideStuff;
    const iconColor = Color.fromARGB(255, 200, 200, 200);
    const backgroundColor = Color.fromRGBO(41, 41, 41, 0.7);

    return Stack(
      children: [
        const CupertinoControls(backgroundColor: backgroundColor, iconColor: iconColor),

        if (showControls)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: AbsorbPointer(
              absorbing: !showControls,
              child: Row(
                children: [
                  // close button
                  VideoActionButton(
                    child: const Icon(Icons.close, color: iconColor, size: 18),
                    onTap: () {
                      if (chewieController.isFullScreen) chewieController.toggleFullScreen();
                      Navigator.of(context).pop();
                    },
                  ),

                  _VideoTitle(title: lessonParams.lesson.title),

                  if (trainingConsumptionModel != null)
                    ShareLessonButton(
                      lessonID: lessonParams.lesson.id,
                      trainingID: trainingConsumptionModel.trainingId,
                    ),

                  // full screen button
                  VideoActionButton(
                    onTap: () => chewieController.toggleFullScreen(),
                    child: Icon(
                      chewieController.isFullScreen
                          ? CupertinoIcons.arrow_down_right_arrow_up_left
                          : CupertinoIcons.arrow_up_left_arrow_down_right,
                      color: iconColor,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        //
      ],
    );
  }
}

class _VideoTitle extends StatelessWidget {
  const _VideoTitle({required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Text(
          title,
          maxLines: 1,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
          style: context.textTheme.textSmall.semiBold.white,
        ),
      ),
    );
  }
}
