import 'package:flutter/widgets.dart';

/// Helpful extensions method to modify list of widgets.
extension ListWidgetExt on List<Widget> {
  /// Return new list of widget with inserted [divider] widget between items.
  ///
  /// It similar to [ListView.separated] for [Column] and [Row].
  ///
  ///
  /// ```dart
  /// Column(
  ///   children: const [
  ///     Text('item1'),
  ///     Text('item2'),
  ///     Text('item3'),
  ///   ].divide(divider: const SizedBox(height: 8)),
  /// );
  ///
  /// // Equals to
  ///
  /// Column(
  ///   children: const [
  ///     Text('item1'),
  ///     SizedBox(height: 8),
  ///     Text('item2'),
  ///     SizedBox(height: 8),
  ///     Text('item3'),
  ///   ],
  /// );
  /// ```
  ///
  /// This extension can be very useful for non static lists of widgets
  /// ```dart
  /// Column(
  ///   children: [
  ///     for (final item in items)
  ///       Text(item),
  ///   ].divide(divider: const SizedBox(height: 8)),
  /// );
  /// ```
  List<Widget> divide({required Widget divider, bool bottom = false, bool top = false}) {
    final children = this;
    if (children.isEmpty) return children;
    if (children.length == 1 && !(top || bottom)) return children;

    final listBuilder = <Widget>[];
    if (top) listBuilder.add(divider);
    for (var index = 0; index < children.length; index++) {
      final child = children[index];
      final hasNext = index + 1 < children.length;
      listBuilder.add(child);
      if (bottom || hasNext) {
        listBuilder.add(divider);
      }
    }
    return listBuilder;
  }
}
