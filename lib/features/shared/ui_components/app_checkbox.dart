import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';

class AppCheckBox extends StatefulWidget {
  const AppCheckBox({super.key, this.initialValue = false, this.onChanged});

  final bool initialValue;
  final Function(bool)? onChanged;

  @override
  State<AppCheckBox> createState() => _AppCheckBoxState();
}

class _AppCheckBoxState extends State<AppCheckBox> {
  Color backgroundColor = AppColors.uiBackgroundPrimary;
  late bool checkBoxState;

  @override
  void initState() {
    super.initState();
    checkBoxState = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant AppCheckBox oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialValue != widget.initialValue) {
      // setState(() {
      checkBoxState = widget.initialValue;
      // });
    }
  }

  @override
  void didChangeDependencies() {
    // isDarkMode = theme.isDarkMode(context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    // final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        setState(() {
          checkBoxState = !checkBoxState;
        });
        widget.onChanged?.call(checkBoxState);
      },
      child: Container(
        width: 18,
        height: 18,
        margin: const EdgeInsets.all(2),
        padding: const EdgeInsets.all(3.5),
        decoration: BoxDecoration(
          color: checkBoxState ? AppColors.greenAccentPrimary : Colors.white,
          borderRadius: BorderRadius.circular(4),
          border: checkBoxState ? const Border() : Border.all(color: AppColors.accentLight),
        ),
        child: Image.asset(AssetsPath.checkIcon, color: AppColors.uiBackgroundPrimary),
      ),
    );
  }
}
