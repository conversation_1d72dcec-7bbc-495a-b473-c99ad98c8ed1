import 'package:flutter/material.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_back_button.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage({required this.errorMessage, super.key});

  final String errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(leading: const AppBackButton()),
      body: Center(
        child: Text(
          errorMessage,
          style: context.textTheme.h2.medium.copyWith(color: Colors.red[700]),
        ),
      ),
    );
  }
}
