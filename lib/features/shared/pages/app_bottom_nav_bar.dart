import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/core/theme/app_text_theme.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

// had issues with navigationShell when I wrote golden test for this widget, thus included coverage:ignore-file. if you have a better solution, please let me know.
// coverage:ignore-file
class AppBottomNavBar extends StatelessWidget {
  const AppBottomNavBar(this.navigationShell, {super.key});
  static int tabIndex = 0;

  final StatefulNavigationShell navigationShell;

  static const _bottomNavbarIconSize = 24.0;

  void _goBranch(int index) {
    // If navigating to Home tab (index 0), reset the TrainingsBloc to show all trainings
    if (index == 0) {
      GetIt.instance<TrainingsBloc>().add(const GetTrainingsListEvent());
    }

    navigationShell.goBranch(
      index,
      // A common pattern when using bottom navigation bars is to support
      // navigating to the initial location when tapping the item that is
      // already active. This example demonstrates how to support this behavior,
      // using the initialLocation parameter of goBranch.
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: GetIt.instance.get<AuthBloc>(),
      child: Builder(
        builder: (context) {
          return PopScope(
            canPop: false,
            child: Scaffold(
              body: navigationShell,
              backgroundColor:
                  context.watch<AuthBloc>().state is! AuthLoading ? Colors.transparent : null,
              bottomNavigationBar: Container(
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: AppColors.greyLight,
                      width: 0.5,
                    ),
                  ),
                ),
                child: BottomNavigationBar(
                  elevation: context.watch<AuthBloc>().state is AuthLoading ? 0 : null,
                  backgroundColor:
                      context.watch<AuthBloc>().state is AuthLoading ? Colors.black54 : null,
                  type: BottomNavigationBarType.fixed,
                  currentIndex: navigationShell.currentIndex,
                  selectedLabelStyle: context.textTheme.bottomNavigation.medium.accentGreenPrimary,
                  showUnselectedLabels: false,
                  showSelectedLabels: true,
                  useLegacyColorScheme: false,
                  onTap: _goBranch,
                  items: [
                    BottomNavigationBarItem(
                      activeIcon: Image.asset(
                        AssetsPath.bottomNavBar_home_active,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      icon: Image.asset(
                        AssetsPath.bottomNavBar_home_inactive,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      label: LocaleKeys.bottom_nav_bar_home.tr(),
                    ),
                    BottomNavigationBarItem(
                      activeIcon: Image.asset(
                        AssetsPath.bottomNavBar_catalog_active,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      icon: Image.asset(
                        AssetsPath.bottomNavBar_catalog_inactive,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      label: LocaleKeys.bottom_nav_bar_catalog.tr(),
                    ),
                    BottomNavigationBarItem(
                      activeIcon: Image.asset(
                        AssetsPath.bottomNavBar_myLearning_active,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      icon: Image.asset(
                        AssetsPath.bottomNavBar_myLearning_inactive,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      label: LocaleKeys.bottom_nav_bar_myLearnings.tr(),
                    ),
                    BottomNavigationBarItem(
                      activeIcon: Image.asset(
                        AssetsPath.bottomNavBar_profile_active,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      icon: Image.asset(
                        AssetsPath.bottomNavBar_profile_inactive,
                        height: _bottomNavbarIconSize,
                        width: _bottomNavbarIconSize,
                      ),
                      label: LocaleKeys.bottom_nav_bar_profile.tr(),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DoubleProperty('bottomNavbarIconSize', _bottomNavbarIconSize));
  }
}
