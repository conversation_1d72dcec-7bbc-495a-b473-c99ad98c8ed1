import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

enum LessonNavigationType { initial, forward, backward }

class MultiTransition extends CustomTransitionPage<void> {
  MultiTransition({required super.child, required this.forwardType})
      : super(
          reverseTransitionDuration: const Duration(milliseconds: 300),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            final isReverse = animation.status == AnimationStatus.reverse;

            /// For pop/close actions, always slide down
            if (isReverse) {
              final curvedAnimation = CurvedAnimation(parent: animation, curve: Curves.easeInOut);

              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 1.0),
                  end: Offset.zero,
                ).animate(curvedAnimation),
                child: child,
              );
            }

            /// For push actions, use directional animations
            switch (forwardType) {
              case LessonNavigationType.forward:

                /// Slide from right
                const begin = Offset(1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOut;

                final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
                final offsetAnimation = animation.drive(tween);

                return SlideTransition(position: offsetAnimation, child: child);
              case LessonNavigationType.backward:

                /// Slide from left
                const begin = Offset(-1.0, 0.0);
                const end = Offset.zero;
                const curve = Curves.easeInOut;

                final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
                final offsetAnimation = animation.drive(tween);

                return SlideTransition(position: offsetAnimation, child: child);
              case LessonNavigationType.initial:

                /// Slide from bottom
                const begin = Offset(0.0, 1.0);
                const end = Offset.zero;
                const curve = Curves.easeInOut;

                final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
                final offsetAnimation = animation.drive(tween);

                return SlideTransition(position: offsetAnimation, child: child);
            }
          },
        );

  final LessonNavigationType forwardType;
}
