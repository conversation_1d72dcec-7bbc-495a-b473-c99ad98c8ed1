import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class SlideBottomToTopTransition extends CustomTransitionPage<void> {
  SlideBottomToTopTransition({required super.child})
      : super(
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const Offset begin = Offset(0.0, 1.0);
            const Offset end = Offset.zero;
            const Curve curve = Curves.easeInOut;

            final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            final offsetAnimation = animation.drive(tween);

            return SlideTransition(position: offsetAnimation, child: child);
          },
        );
}

class SlideTopToBottomTransition extends CustomTransitionPage<void> {
  SlideTopToBottomTransition({required super.child})
      : super(
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0, -1);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            final tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
            final offsetAnimation = animation.drive(tween);

            return SlideTransition(position: offsetAnimation, child: child);
          },
        );
}
