import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/home/<USER>/pages/home_page.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/router.dart';

class RootPage extends StatefulWidget {
  const RootPage({super.key});

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> {
  @override
  void initState() {
    super.initState();
    _navigate();
  }

  void _navigate() {
    final isVisitedBefore = Hive.box(HiveKeys.hiveNspStorage).get(HiveKeys.visitedBefore);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (isVisitedBefore == null) {
        // TODO: redesign
        _markUserAsVisited();
        router.goNamed(Routes.getStarted.name);
      } else {
        final token = await GetIt.instance.get<AuthTokenProvider>().getToken();
        router.goNamed(
          Routes.homePage.name,
          extra: token != null ? AuthStateEnum.loggedIn : AuthStateEnum.notLoggedIn,
        );
      }
    });
  }

  void _markUserAsVisited() {
    ///GetStarted Page needs to be shown only once on first visit, thus we're saving this info
    Hive.box(HiveKeys.hiveNspStorage).put(HiveKeys.visitedBefore, true);
  }

  @override
  Widget build(BuildContext context) => const ColoredBox(color: Colors.white, child: BuildLoader());
}
