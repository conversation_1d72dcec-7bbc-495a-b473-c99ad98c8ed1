<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="pre-commit" type="DartScriptConfigurationType">
    <option name="SCRIPT_TEXT" value="git hook run pre-commit" />
    <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
    <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="EXECUTE_IN_TERMINAL" value="false" />
    <envs />
    <method v="2" />
  </configuration>
</component>