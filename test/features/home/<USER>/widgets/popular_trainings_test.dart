import 'dart:async'; // Add this import for StreamController

import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/widgets/popular_trainings.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';

// Mocks
class MockTrainingsBloc extends MockBloc<TrainingsEvent, TrainingsState> implements TrainingsBloc {}

class MockTrainingsState extends Fake {}

class MockTrainingsEvent extends Fake implements TrainingsEvent {}

class MockGoRouter extends Mock implements GoRouter {}

void main() {
  late MockTrainingsBloc mockTrainingsBloc;
  late MockGoRouter mockGoRouter; // Keep the mock instance for setting up mocks

  setUpAll(() async {
    registerFallbackValue(MockTrainingsState());
    registerFallbackValue(MockTrainingsEvent());
    mockGoRouter = MockGoRouter();
    if (GetIt.I.isRegistered<GoRouter>()) {
      GetIt.I.unregister<GoRouter>();
    }
    GetIt.I.registerSingleton<GoRouter>(mockGoRouter);
    await testEnvSetup();
  });

  setUp(() {
    mockTrainingsBloc = MockTrainingsBloc();
    if (GetIt.I.isRegistered<TrainingsBloc>()) GetIt.I.unregister<TrainingsBloc>();
    GetIt.I.registerSingleton<TrainingsBloc>(mockTrainingsBloc);

    clearInteractions(mockGoRouter); // Clear interactions on the mock instance
    when(() => mockGoRouter.go(any())).thenAnswer((_) {}); // Stub the mock instance

    when(() => mockTrainingsBloc.state).thenReturn(const TrainingsInitial());
  });

  tearDown(() {
    GetIt.I.reset();
    // No need to restore router if we mocked the global instance directly
  });

  tearDownAll(() async {
    await testEnvTearDown(); // From nsp_test_wrapper
  });

  group('PopularTrainings widget', () {
    // TEST 1: Initial event dispatch
    testWidgets('dispatches GetTrainingsListEvent on initialization', (WidgetTester tester) async {
      // Arrange - Default behavior set in setUp

      // Act - Render the widget
      await tester.pumpWidget(nspTestWrapper(child: const PopularTrainings()));
      await tester.pump(); // Process pending frames

      // Assert - Verify the event was dispatched
      verify(() => mockTrainingsBloc.add(const GetTrainingsListEvent())).called(1);
      expect(find.text(LocaleKeys.popularTrainings.tr()), findsOneWidget);
    });

    // TEST 2: Lifecycle management - Resuming the app
    testWidgets('dispatches GetTrainingsListEvent when app lifecycle changes to resumed',
        (WidgetTester tester) async {
      // Arrange - Default behavior set in setUp

      // Act - Render the widget
      await tester.pumpWidget(nspTestWrapper(child: const PopularTrainings()));
      await tester.pump();

      // Verify initial event dispatch
      verify(() => mockTrainingsBloc.add(const GetTrainingsListEvent())).called(1);

      // Simulate app lifecycle change to resumed
      tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);
      await tester.pump();

      // Assert - Verify event was dispatched again
      verify(() => mockTrainingsBloc.add(const GetTrainingsListEvent())).called(1);
    });

    // TEST 3: Simple state change test
    testWidgets('rebuilds when bloc state changes', (WidgetTester tester) async {
      // Arrange - Create a bloc that will emit states in a controlled way
      final streamController = StreamController<TrainingsState>();
      when(() => mockTrainingsBloc.stream).thenAnswer((_) => streamController.stream);

      // Act - Render the widget with direct BlocProvider
      await tester.pumpWidget(
        nspTestWrapper(
          child: BlocProvider<TrainingsBloc>.value(
            value: mockTrainingsBloc,
            child: const PopularTrainings(),
          ),
        ),
      );
      await tester.pump();

      // Emit a loading state and verify title remains visible
      streamController.add(const TrainingsLoading());
      await tester.pump();
      expect(find.text(LocaleKeys.popularTrainings.tr()), findsOneWidget);

      // Clean up
      streamController.close();
    });
  });
}
