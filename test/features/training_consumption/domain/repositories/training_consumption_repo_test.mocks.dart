// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in national_skills_platform/test/features/training_consumption/domain/repositories/training_consumption_repo_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart'
    as _i6;
import 'package:national_skills_platform/features/training_consumption/data/data_sources/training_consumption_datasource.dart'
    as _i3;
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTrainingConsumptionModel_0 extends _i1.SmartFake
    implements _i2.TrainingConsumptionModel {
  _FakeTrainingConsumptionModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [TrainingConsumptionDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingConsumptionDatasource extends _i1.Mock
    implements _i3.TrainingConsumptionDatasource {
  MockTrainingConsumptionDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.TrainingConsumptionModel> getTrainingConsumptionDetails(
    String? trainingId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#getTrainingConsumptionDetails, [trainingId]),
        returnValue: _i4.Future<_i2.TrainingConsumptionModel>.value(
          _FakeTrainingConsumptionModel_0(
            this,
            Invocation.method(#getTrainingConsumptionDetails, [trainingId]),
          ),
        ),
      ) as _i4.Future<_i2.TrainingConsumptionModel>);

  @override
  _i4.Future<void> markLessonAsCompleted({
    required String? trainingId,
    required String? lessonId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#markLessonAsCompleted, [], {
          #trainingId: trainingId,
          #lessonId: lessonId,
        }),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String> getVideoUrl(String? videoKey) => (super.noSuchMethod(
        Invocation.method(#getVideoUrl, [videoKey]),
        returnValue: _i4.Future<String>.value(
          _i5.dummyValue<String>(
            this,
            Invocation.method(#getVideoUrl, [videoKey]),
          ),
        ),
      ) as _i4.Future<String>);

  @override
  _i4.Future<void> downloadSlide(
    _i6.Resource? resource,
    dynamic Function(double)? onProgress,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#downloadSlide, [resource, onProgress]),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> downloadFile(String? fileKey, String? fileName) => (super.noSuchMethod(
        Invocation.method(#downloadFile, [fileKey, fileName]),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
