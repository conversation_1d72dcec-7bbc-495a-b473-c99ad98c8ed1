import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/training_consumption/data/data_sources/training_consumption_datasource.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/domain/repositories/training_consumption_repository.dart';

import '../../training_consumption_seed.dart';
import 'training_consumption_repo_test.mocks.dart';

@GenerateMocks([TrainingConsumptionDatasource])
void main() {
  late MockTrainingConsumptionDatasource mockDataSource;
  late TrainingConsumptionRepository repository;
  const trainingId = 'id';

  setUp(() {
    mockDataSource = MockTrainingConsumptionDatasource();
    repository = TrainingConsumptionRepository(dataSource: mockDataSource);

    when(
      mockDataSource.getTrainingConsumptionDetails(trainingId),
    ).thenAnswer((_) async => TrainingConsumptionModel.fromJson(tTrainingConsumptionDetailsJson));
  });

  group('getTrainingConsumptionDetails', () {
    test('should get TrainingConsumptionModel from the data source', () async {
      final result = await repository.getTrainingConsumptionDetails(trainingId);
      final trainingConsumptionModel = TrainingConsumptionModel.fromJson(
        tTrainingConsumptionDetailsJson,
      );

      verify(mockDataSource.getTrainingConsumptionDetails(trainingId));
      expect(
        result,
        equals(
          trainingConsumptionModel.copyWith(
            completedLessonsIDs: trainingConsumptionModel.progress.map((e) => e.lessonId).toSet(),
          ),
        ),
      );
    });

    test('should throw an exception when the data source throws an exception', () {
      when(mockDataSource.getTrainingConsumptionDetails(trainingId)).thenThrow(Exception());

      expect(() => repository.getTrainingConsumptionDetails(trainingId), throwsException);
    });

    test('should mark lesson as completed', () async {
      const lessonId = 'lessonId';
      await repository.markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId);

      verify(mockDataSource.markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId));
    });

    test('should throw an exception when the data source throws an exception', () {
      const lessonId = 'lessonId';
      when(
        mockDataSource.markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId),
      ).thenThrow(Exception());

      expect(
        () => repository.markLessonAsCompleted(trainingId: trainingId, lessonId: lessonId),
        throwsException,
      );
    });
  });

  group('getVideoUrl', () {
    test('should get video url from the data source', () async {
      const videoKey = 'videoKey';
      const videoUrl = 'videoUrl';
      when(mockDataSource.getVideoUrl(videoKey)).thenAnswer((_) async => videoUrl);

      final result = await repository.getVideoUrl(videoKey);

      verify(mockDataSource.getVideoUrl(videoKey));
      expect(result, equals(videoUrl));
    });

    test('should throw an exception when the data source throws an exception', () {
      const videoKey = 'videoKey';
      when(mockDataSource.getVideoUrl(videoKey)).thenThrow(Exception());

      expect(() => repository.getVideoUrl(videoKey), throwsException);
    });
  });

  group('downloadFile', () {
    test('should download file from the data source', () async {
      const fileKey = 'fileKey';
      const fileName = 'fileName';
      await repository.downloadFile(fileKey, fileName);

      verify(mockDataSource.downloadFile(fileKey, fileName));
    });

    test('should throw an exception when the data source throws an exception', () {
      const fileKey = 'fileKey';
      const fileName = 'fileName';
      when(mockDataSource.downloadFile(fileKey, fileName)).thenThrow(Exception());

      expect(() => repository.downloadFile(fileKey, fileName), throwsException);
    });
  });
}
