import 'dart:io';

import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/domain/repositories/training_consumption_repository.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';
import 'package:path_provider/path_provider.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../training_consumption_seed.dart';
import 'training_consumption_bloc_test.mocks.dart';

@GenerateMocks([TrainingConsumptionRepository])
@GenerateNiceMocks([MockSpec<GoRouter>()])
Directory? mockAppDir;

void main() {
  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockAppDir = Directory.systemTemp.createTempSync();
    await testEnvSetup();
  });

  tearDownAll(() {
    try {
      if (mockAppDir != null && mockAppDir!.existsSync()) {
        mockAppDir!.deleteSync(recursive: true);
      }
    } catch (e) {
      // Ignore errors during cleanup
    }
  });
  late final MockTrainingConsumptionRepository mockTrainingConsumptionRepository;
  final trainingConsumptionModel = TrainingConsumptionModel.fromJson(
    tTrainingConsumptionDetailsJson,
  );
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockGoRouter = MockGoRouter();
  const videoLesson = Lesson(id: '1', title: 'Test Lesson', index: 1, lessonType: LessonType.Video);

  setUpAll(() async {
    await testEnvSetup();

    GetIt.instance
      ..registerSingleton<Alice>(Alice())
      ..registerSingleton<GoRouter>(mockGoRouter);
    mockTrainingConsumptionRepository = MockTrainingConsumptionRepository();
    when(
      mockTrainingConsumptionRepository.getVideoUrl(any),
    ).thenAnswer((_) async => 'videoLessonUrl');
    when(mockTrainingConsumptionRepository.downloadSlide(any, any)).thenAnswer((_) async => {});
  });

  tearDown(() async {
    await testEnvTearDown();
    reset(mockGoRouter);
  });

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionLoading, TrainingConsumptionLoaded] when TrainingConsumptionEvent is added',
    build: () {
      when(
        mockTrainingConsumptionRepository.getTrainingConsumptionDetails(any),
      ).thenAnswer((_) async => trainingConsumptionModel);
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) => bloc.add(LoadTrainingConsumptionPageEvent(trainingDetailsModel)),
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(
        trainingConsumptionModel: trainingConsumptionModel,
        trainingStructure: trainingDetailsModel.trainingStructure,
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with updated trainingConsumptionModel when RefreshTrainingConsumptionPageEvent is added',
    seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) {
      bloc
        ..trainingDetailsModel = trainingDetailsModel
        ..add(const RefreshTrainingConsumptionPageEvent());
    },
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(
        trainingConsumptionModel: trainingConsumptionModel,
        trainingStructure: trainingDetailsModel.trainingStructure,
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionLoading, TrainingConsumptionLoaded] when TrainingConsumptionEvent is added and trainingStructure is null',
    build: () {
      when(
        mockTrainingConsumptionRepository.getTrainingConsumptionDetails(any),
      ).thenAnswer((_) async => trainingConsumptionModel);

      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) => bloc.add(
      LoadTrainingConsumptionPageEvent(trainingDetailsModel.copyWith(trainingStructure: null)),
    ),
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionLoading, TrainingConsumptionError] when TrainingConsumptionEvent is added and TrainingConsumptionRepository returns an error',
    build: () {
      when(
        mockTrainingConsumptionRepository.getTrainingConsumptionDetails(any),
      ).thenAnswer((_) async => throw Exception());
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) => bloc.add(LoadTrainingConsumptionPageEvent(trainingDetailsModel)),
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with updated completedLessonsIDs when MarkLessonAsCompletedEvent is added',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
    act: (bloc) {
      bloc.add(
        const MarkLessonAsCompletedEvent(
          lesson: Lesson(id: 'lessonId', title: '', index: 0, lessonType: LessonType.Article),
        ),
      );
    },
    expect: () => [
      TrainingConsumptionState(
        trainingConsumptionModel: trainingConsumptionModel.copyWith(
          completedLessonsIDs: {...trainingConsumptionModel.completedLessonsIDs, 'lessonId'},
        ),
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with downloadingFiles when CancelAllDownloadsEvent is added',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) {
      bloc
        ..add(const DownloadFileEvent(fileKey: 'fileKey', fileName: 'fileName'))
        ..add(const CancelAllDownloadsEvent());
    },
    expect: () => [
      TrainingConsumptionState(downloadingFiles: {'fileKey'}),
      TrainingConsumptionState(downloadingFiles: {}),
    ],
  );
  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with errorMessage when MarkLessonAsCompletedEvent is added and TrainingConsumptionRepository returns an error',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
    act: (bloc) {
      when(
        mockTrainingConsumptionRepository.markLessonAsCompleted(
          trainingId: anyNamed('trainingId'),
          lessonId: anyNamed('lessonId'),
        ),
      ).thenAnswer((_) async => throw Exception());
      bloc.add(
        const MarkLessonAsCompletedEvent(
          lesson: Lesson(id: 'lessonId', title: '', index: 0, lessonType: LessonType.Article),
        ),
      );
    },
    expect: () => [
      TrainingConsumptionState(
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        trainingConsumptionModel: trainingConsumptionModel,
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with videoUrl when GetVideoUrlEvent is added',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) {
      bloc.add(
        const GetVideoUrlEvent(
          videoLesson,
        ),
      );
    },
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(videoLessonUrl: 'videoLessonUrl'),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with errorMessage when GetVideoUrlEvent is added and TrainingConsumptionRepository returns an error',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) {
      when(
        mockTrainingConsumptionRepository.getVideoUrl(any),
      ).thenAnswer((_) async => throw Exception());
      bloc.add(
        const GetVideoUrlEvent(videoLesson),
      );
    },
    expect: () => [
      TrainingConsumptionState(isLoading: true),
      TrainingConsumptionState(
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with downloadingFiles when DownloadFileEvent is added',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) => bloc.add(const DownloadFileEvent(fileKey: 'fileKey', fileName: 'fileName')),
    expect: () => [
      TrainingConsumptionState(downloadingFiles: {'fileKey'}),
      TrainingConsumptionState(downloadingFiles: {}),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with errorMessage when DownloadFileEvent is added and TrainingConsumptionRepository returns an error',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) {
      when(
        mockTrainingConsumptionRepository.downloadFile(any, any),
      ).thenAnswer((_) async => throw Exception());
      bloc.add(const DownloadFileEvent(fileKey: 'fileKey', fileName: 'fileName'));
    },
    expect: () => [
      TrainingConsumptionState(downloadingFiles: {'fileKey'}),
      TrainingConsumptionState(
        errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
    'emits [TrainingConsumptionState] with errorMessage when OpenFileEvent is added and TrainingConsumptionRepository returns an error',
    build: () {
      return TrainingConsumptionBloc(
        trainingConsumptionRepository: mockTrainingConsumptionRepository,
      );
    },
    act: (bloc) => bloc.add(const OpenFileEvent('fileName')),
    expect: () => [],
  );

  group('OpenLiveSessionLessonEvent', () {
    final meeting = Meeting(
      id: '1',
      title: 'Test Meeting',
      trainingId: '1',
      liveSessionId: '1',
      startDate: DateTime.now(),
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: 'https://test.com',
      zoomPassword: '123',
      meetingId: '1',
      status: 'active',
      cancelled: false,
    );
    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'navigates to the correct page when OpenLessonEvent is added',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) {
        bloc
          ..add(LoadTrainingConsumptionPageEvent(trainingDetailsModel))
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: true,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons.first,
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          )
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: true,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[1],
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          )
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: true,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[2],
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          )
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: true,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[3],
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          )
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: false,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[4],
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          )
          ..add(
            OpenLessonEvent(
              LessonParams(
                isCompleted: true,
                completedLessonsInSection: 1,
                section: trainingDetailsModel.trainingStructure!.sections.first,
                lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[4].copyWith(
                  lessonType: null,
                ),
                trainingConsumptionBloc: TrainingConsumptionBloc(
                  trainingConsumptionRepository: mockTrainingConsumptionRepository,
                ),
              ),
            ),
          );
      },
      verify: (_) {
        verify(mockGoRouter.pushNamed(Routes.articlePage.name, extra: anyNamed('extra'))).called(1);
        verify(
          mockGoRouter.pushNamed(Routes.videoLessonPage.name, extra: anyNamed('extra')),
        ).called(1);
      },
    );
    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'pushes live session page when navigation type is initial',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(
        OpenLiveSessionLessonEvent(meeting: meeting, subLesson: videoLesson),
      ),
      verify: (_) {
        verify(
          mockGoRouter.pushNamed(Routes.liveSessionPage.name, extra: anyNamed('extra')),
        ).called(1);
      },
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'pushes replacement live session page when navigation type is not initial',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(
        OpenLiveSessionLessonEvent(
          meeting: meeting,
          lessonNavigationType: LessonNavigationType.forward,
          subLesson: videoLesson,
        ),
      ),
      verify: (_) {
        verify(
          mockGoRouter.pushReplacementNamed(Routes.liveSessionPage.name, extra: anyNamed('extra')),
        ).called(1);
      },
    );
  });

  group('OpenPostQualificationTest', () {
    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'does nothing when post qualification test is null',
      seed: () => TrainingConsumptionState(
        trainingConsumptionModel: trainingConsumptionModel.copyWith(postQualificationTest: null),
      ),
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const OpenPostQualificationTestEvent()),
      verify: (_) {
        verifyNever(
          mockGoRouter.pushReplacementNamed(
            Routes.qualificationTestPage.name,
            extra: anyNamed('extra'),
          ),
        );
      },
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'pushes qualification test page and refreshes when post qualification test exists',
      seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
      setUp: () {
        when(
          mockTrainingConsumptionRepository.getTrainingConsumptionDetails(any),
        ).thenAnswer((_) async => trainingConsumptionModel);
      },
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      act: (bloc) => bloc.add(const OpenPostQualificationTestEvent()),
      verify: (_) {
        verify(
          mockGoRouter.pushReplacementNamed(
            Routes.qualificationTestPage.name,
            extra: anyNamed('extra'),
          ),
        ).called(1);
      },
      skip: 1,
    );
  });

  group('OpenPreQualificationTest', () {
    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'does nothing when pre qualification test is null',
      seed: () => TrainingConsumptionState(
        trainingConsumptionModel: trainingConsumptionModel.copyWith(preQualificationTest: null),
      ),
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const OpenPreQualificationTestEvent()),
      verify: (_) {
        verifyNever(
          mockGoRouter.pushReplacementNamed(
            Routes.qualificationTestPage.name,
            extra: anyNamed('extra'),
          ),
        );
      },
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'pushes qualification test page and refreshes when pre qualification test exists',
      seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
      setUp: () {
        when(
          mockTrainingConsumptionRepository.getTrainingConsumptionDetails(any),
        ).thenAnswer((_) async => trainingConsumptionModel);
      },
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      act: (bloc) => bloc.add(const OpenPreQualificationTestEvent()),
      verify: (_) {
        verify(
          mockGoRouter.pushReplacementNamed(
            Routes.qualificationTestPage.name,
            extra: anyNamed('extra'),
          ),
        ).called(1);
      },
      skip: 1,
    );
  });

  group('OpenLessonEvent', () {
    const section = Section(
      id: '1',
      title: 'Test Section',
      index: 1,
      lessons: [videoLesson],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'pushes replacement lesson page when navigation type is not initial',
      seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      act: (bloc) => bloc.add(
        OpenLessonEvent(
          LessonParams(
            section: section,
            lesson: videoLesson,
            completedLessonsInSection: 0,
            isCompleted: false,
            trainingConsumptionBloc: bloc,
            lessonNavigationType: LessonNavigationType.forward,
          ),
        ),
      ),
      verify: (_) {
        verify(
          mockGoRouter.pushReplacementNamed(
            Routes.videoLessonPage.name,
            extra: anyNamed('extra'),
          ),
        ).called(1);
      },
    );
  });

  group('UpdatePostQualificationTest', () {
    const postQualificationTest = PostQualificationTest(
      id: 'post_test_1',
      type: QualificationTestType.POST,
      questions: [],
      applicantAnswers: [],
      applicantSavedAnswers: {},
      description: '',
      firstAttempt: true,
      mandatory: true,
      minimumScore: 70,
      randomized: false,
      timeLimit: 30,
      title: 'Post Test',
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'does nothing when training consumption model is null',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const UpdatePostQualificationTestEvent(postQualificationTest)),
      expect: () => [],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'updates post qualification test when training consumption model exists',
      seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const UpdatePostQualificationTestEvent(postQualificationTest)),
      expect: () => [
        isA<TrainingConsumptionState>().having(
          (state) => state.trainingConsumptionModel?.postQualificationTest?.id,
          'postQualificationTest.id',
          'post_test_1',
        ),
      ],
    );
  });

  group('GetSlideFilePath', () {
    const slideFileName = 'test_slide.pdf';
    const slideKey = 'slide_key';
    const slideResource = Resource(
      originalFilename: slideFileName,
      key: slideKey,
      size: 1024,
    );
    const slideLesson = Lesson(
      id: '1',
      title: 'Test Lesson',
      index: 1,
      lessonType: LessonType.Slide,
      resources: [slideResource],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'emits error when lesson has no resources',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(
        GetSlideFilePathEvent(
          slideLesson.copyWith(resources: null),
        ),
      ),
      expect: () => [
        TrainingConsumptionState(isLoading: true),
        TrainingConsumptionState(errorMessage: LocaleKeys.somethingWentWrongPage_errorMessage.tr()),
      ],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'emits slide file path when file exists',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      setUp: () async {
        final directory = await getApplicationDocumentsDirectory();
        await directory.create(recursive: true);
        final filePath = '${directory.absolute.path}/$slideFileName';
        File(filePath).createSync(recursive: true);
      },
      act: (bloc) => bloc.add(const GetSlideFilePathEvent(slideLesson)),
      expect: () => [
        TrainingConsumptionState(isLoading: true),
        predicate<TrainingConsumptionState>(
          (state) => state.slideFilePath?.endsWith(slideFileName) == true,
        ),
      ],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'downloads slide when file does not exist',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      setUp: () {
        when(
          mockTrainingConsumptionRepository.downloadSlide(slideResource, argThat(isA<Function>())),
        ).thenAnswer((invocation) async {
          final onProgress = invocation.positionalArguments[1] as Function(double);
          onProgress(0.5);
        });
      },
      act: (bloc) => bloc.add(const GetSlideFilePathEvent(slideLesson)),
      expect: () => [
        TrainingConsumptionState(isLoading: true),
        TrainingConsumptionState(slideDownloadProgress: 0.5),
        TrainingConsumptionState(),
      ],
      verify: (_) {
        verify(
          mockTrainingConsumptionRepository.downloadSlide(slideResource, argThat(isA<Function>())),
        ).called(1);
      },
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'emits error when download fails',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        )..trainingDetailsModel = trainingDetailsModel;
      },
      setUp: () {
        when(
          mockTrainingConsumptionRepository.downloadSlide(slideResource, argThat(isA<Function>())),
        ).thenAnswer((_) async => throw 'Download failed');
      },
      act: (bloc) => bloc.add(const GetSlideFilePathEvent(slideLesson)),
      expect: () => [
        TrainingConsumptionState(isLoading: true),
        TrainingConsumptionState(
          errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );
  });

  group('UpdatePreQualificationTest', () {
    const preQualificationTest = PreQualificationTest(
      id: 'pre_test_1',
      type: QualificationTestType.PRE,
      questions: [],
      applicantAnswers: [],
      applicantSavedAnswers: {},
      description: '',
      firstAttempt: true,
      mandatory: true,
      minimumScore: 70,
      randomized: false,
      timeLimit: 30,
      title: 'Pre Test',
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'does nothing when training consumption model is null',
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const UpdatePreQualificationTestEvent(preQualificationTest)),
      expect: () => [],
    );

    blocTest<TrainingConsumptionBloc, TrainingConsumptionState>(
      'updates pre qualification test when training consumption model exists',
      seed: () => TrainingConsumptionState(trainingConsumptionModel: trainingConsumptionModel),
      build: () {
        return TrainingConsumptionBloc(
          trainingConsumptionRepository: mockTrainingConsumptionRepository,
        );
      },
      act: (bloc) => bloc.add(const UpdatePreQualificationTestEvent(preQualificationTest)),
      expect: () => [
        isA<TrainingConsumptionState>().having(
          (state) => state.trainingConsumptionModel?.preQualificationTest?.id,
          'preQualificationTest.id',
          'pre_test_1',
        ),
      ],
    );
  });
}
