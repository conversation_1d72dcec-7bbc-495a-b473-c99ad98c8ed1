import 'package:collection/collection.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/usecases/update_study_streams.dart';

import '../../../catalog/shared/catalog_test_seed.dart';

void main() {
  test('UpdateStudyStreams updates meeting statuses and orders them', () {
    final now = DateTime.now().toUtc();

    // Meeting in the past -> Passed
    final passedMeeting = Meeting(
      id: '1',
      title: 'Passed Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now.subtract(const Duration(hours: 5)),
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: false,
      cardNotes: [],
    );

    // Meeting currently live -> Live
    final liveMeeting = Meeting(
      id: '2',
      title: 'Live Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now.subtract(const Duration(minutes: 30)),
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: false,
      cardNotes: [],
    );

    // Meeting in the future -> Upcoming
    final upcomingMeeting = Meeting(
      id: '3',
      title: 'Upcoming Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now.add(const Duration(hours: 1)),
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: false,
      cardNotes: [],
    );

    // Another upcoming meeting with same status but different (later) start time
    final anotherUpcomingMeeting = Meeting(
      id: '6',
      title: 'Another Upcoming Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now.add(const Duration(hours: 2)),
      // starts later
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: false,
      cardNotes: [],
    );

    // Cancelled meeting -> Cancelled
    final cancelledMeeting = Meeting(
      id: '4',
      title: 'Cancelled Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now,
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: true,
      cardNotes: [],
    );

    // Rescheduled meeting -> Rescheduled
    final rescheduledMeeting = Meeting(
      id: '5',
      title: 'Rescheduled Meeting',
      trainingId: 't1',
      liveSessionId: 'l1',
      startDate: now.add(const Duration(hours: 3)),
      durationHours: 1,
      durationMinutes: 0,
      zoomLink: '',
      zoomPassword: '',
      meetingId: '',
      status: Constants.PUBLISHED,
      cancelled: false,
      cardNotes: [
        CardNote(
          createdDate: DateTime.now(),
          localizedContents: {
            'EN': const LocalizedContent(
              title: 'Meeting moved',
              message: 'Meeting was moved to another date',
            ),
          },
        ),
      ],
    );

    final studyStream = StudyStream(
      id: 'stream1',
      startDate: now.subtract(const Duration(days: 1)),
      endDate: now.add(const Duration(days: 1)),
      maxNumberOfParticipants: 10,
      currentNumberOfParticipants: 5,
      noLimits: false,
      trainingId: 't1',
      status: Constants.PUBLISHED,
      cancelled: false,
      closedForEnrollmentDate: now.add(const Duration(days: 1)),
      cardNotes: [],
      type: StudyStreamType.IN_PERSON,
      location: null,
      comment: '',
      liveSession: LiveSession(
        id: 'l1',
        startDate: now.subtract(const Duration(days: 1)),
        durationHours: 1,
        durationMinutes: 0,
        recurringMeeting: false,
        zoomLink: '',
        zoomPassword: '',
        meetingId: '',
        status: Constants.PUBLISHED,
        meetings: [
          passedMeeting,
          liveMeeting,
          upcomingMeeting,
          anotherUpcomingMeeting,
          cancelledMeeting,
          rescheduledMeeting,
        ],
      ),
    );

    final trainingDetailsModel = TrainingDetailsModel.fromJson(
      tTrainingDetailsJson,
    ).copyWith(studyStreams: [studyStream]);

    final updateStudyStreams = UpdateStudyStreams();
    final updatedStreams = updateStudyStreams(trainingDetailsModel);
    final updatedMeetings = updatedStreams.first?.liveSession?.meetings ?? [];

    // Check that all meeting statuses are updated
    final passed = updatedMeetings.firstWhereOrNull((m) => m.id == '1');
    final live = updatedMeetings.firstWhereOrNull((m) => m.id == '2');
    final upcoming = updatedMeetings.firstWhereOrNull((m) => m.id == '3');
    final anotherUpcoming = updatedMeetings.firstWhereOrNull((m) => m.id == '6');
    final rescheduled = updatedMeetings.firstWhereOrNull((m) => m.id == '5');
    final cancelled = updatedMeetings.firstWhereOrNull((m) => m.id == '4');

    expect(passed?.meetingStatus, MeetingStatus.Passed);
    expect(live?.meetingStatus, MeetingStatus.Live);
    expect(upcoming?.meetingStatus, MeetingStatus.Upcoming);
    expect(anotherUpcoming?.meetingStatus, MeetingStatus.Upcoming);
    expect(rescheduled?.meetingStatus, MeetingStatus.Rescheduled);
    expect(cancelled?.meetingStatus, MeetingStatus.Cancelled);

    // Check the order: Live (2), Upcoming (3, 6), Passed (1), Rescheduled (4), Cancelled (5)
    // According to the sorting, it should be:
    // Live -> Upcoming (closest first) -> Passed -> Rescheduled -> Cancelled
    // Within Upcoming, the one that starts sooner (id=3) should appear before the one that starts later (id=6)
    // because we sort by status first, then by startDate with closest dates first.
    final expectedOrder = ['2', '3', '6', '1', '5', '4'];
    expect(updatedMeetings.map((m) => m.id).toList(), expectedOrder);
  });
}
