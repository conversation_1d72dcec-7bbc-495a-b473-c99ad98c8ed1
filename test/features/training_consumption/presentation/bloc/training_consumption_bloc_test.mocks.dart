// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in national_skills_platform/test/features/training_consumption/presentation/bloc/training_consumption_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;

import 'package:flutter/widgets.dart' as _i4;
import 'package:go_router/src/configuration.dart' as _i3;
import 'package:go_router/src/delegate.dart' as _i5;
import 'package:go_router/src/information_provider.dart' as _i6;
import 'package:go_router/src/match.dart' as _i14;
import 'package:go_router/src/parser.dart' as _i7;
import 'package:go_router/src/router.dart' as _i13;
import 'package:go_router/src/state.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i11;
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart'
    as _i12;
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart'
    as _i2;
import 'package:national_skills_platform/features/training_consumption/domain/repositories/training_consumption_repository.dart'
    as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTrainingConsumptionModel_0 extends _i1.SmartFake
    implements _i2.TrainingConsumptionModel {
  _FakeTrainingConsumptionModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRouteConfiguration_1 extends _i1.SmartFake implements _i3.RouteConfiguration {
  _FakeRouteConfiguration_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeBackButtonDispatcher_2 extends _i1.SmartFake implements _i4.BackButtonDispatcher {
  _FakeBackButtonDispatcher_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoRouterDelegate_3 extends _i1.SmartFake implements _i5.GoRouterDelegate {
  _FakeGoRouterDelegate_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoRouteInformationProvider_4 extends _i1.SmartFake
    implements _i6.GoRouteInformationProvider {
  _FakeGoRouteInformationProvider_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoRouteInformationParser_5 extends _i1.SmartFake
    implements _i7.GoRouteInformationParser {
  _FakeGoRouteInformationParser_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGoRouterState_6 extends _i1.SmartFake implements _i8.GoRouterState {
  _FakeGoRouterState_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [TrainingConsumptionRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingConsumptionRepository extends _i1.Mock
    implements _i9.TrainingConsumptionRepository {
  MockTrainingConsumptionRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i10.Future<_i2.TrainingConsumptionModel> getTrainingConsumptionDetails(String? courseId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTrainingConsumptionDetails,
          [courseId],
        ),
        returnValue:
            _i10.Future<_i2.TrainingConsumptionModel>.value(_FakeTrainingConsumptionModel_0(
          this,
          Invocation.method(
            #getTrainingConsumptionDetails,
            [courseId],
          ),
        )),
      ) as _i10.Future<_i2.TrainingConsumptionModel>);

  @override
  _i10.Future<void> markLessonAsCompleted({
    required String? trainingId,
    required String? lessonId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #markLessonAsCompleted,
          [],
          {
            #trainingId: trainingId,
            #lessonId: lessonId,
          },
        ),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<String> getVideoUrl(String? videoKey) => (super.noSuchMethod(
        Invocation.method(
          #getVideoUrl,
          [videoKey],
        ),
        returnValue: _i10.Future<String>.value(_i11.dummyValue<String>(
          this,
          Invocation.method(
            #getVideoUrl,
            [videoKey],
          ),
        )),
      ) as _i10.Future<String>);

  @override
  _i10.Future<void> downloadSlide(
    _i12.Resource? resource,
    dynamic Function(double)? onProgress,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadSlide,
          [
            resource,
            onProgress,
          ],
        ),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<void> downloadFile(
    String? fileKey,
    String? fileName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadFile,
          [
            fileKey,
            fileName,
          ],
        ),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);
}

/// A class which mocks [GoRouter].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoRouter extends _i1.Mock implements _i13.GoRouter {
  @override
  _i3.RouteConfiguration get configuration => (super.noSuchMethod(
        Invocation.getter(#configuration),
        returnValue: _FakeRouteConfiguration_1(
          this,
          Invocation.getter(#configuration),
        ),
        returnValueForMissingStub: _FakeRouteConfiguration_1(
          this,
          Invocation.getter(#configuration),
        ),
      ) as _i3.RouteConfiguration);

  @override
  _i4.BackButtonDispatcher get backButtonDispatcher => (super.noSuchMethod(
        Invocation.getter(#backButtonDispatcher),
        returnValue: _FakeBackButtonDispatcher_2(
          this,
          Invocation.getter(#backButtonDispatcher),
        ),
        returnValueForMissingStub: _FakeBackButtonDispatcher_2(
          this,
          Invocation.getter(#backButtonDispatcher),
        ),
      ) as _i4.BackButtonDispatcher);

  @override
  _i5.GoRouterDelegate get routerDelegate => (super.noSuchMethod(
        Invocation.getter(#routerDelegate),
        returnValue: _FakeGoRouterDelegate_3(
          this,
          Invocation.getter(#routerDelegate),
        ),
        returnValueForMissingStub: _FakeGoRouterDelegate_3(
          this,
          Invocation.getter(#routerDelegate),
        ),
      ) as _i5.GoRouterDelegate);

  @override
  _i6.GoRouteInformationProvider get routeInformationProvider => (super.noSuchMethod(
        Invocation.getter(#routeInformationProvider),
        returnValue: _FakeGoRouteInformationProvider_4(
          this,
          Invocation.getter(#routeInformationProvider),
        ),
        returnValueForMissingStub: _FakeGoRouteInformationProvider_4(
          this,
          Invocation.getter(#routeInformationProvider),
        ),
      ) as _i6.GoRouteInformationProvider);

  @override
  _i7.GoRouteInformationParser get routeInformationParser => (super.noSuchMethod(
        Invocation.getter(#routeInformationParser),
        returnValue: _FakeGoRouteInformationParser_5(
          this,
          Invocation.getter(#routeInformationParser),
        ),
        returnValueForMissingStub: _FakeGoRouteInformationParser_5(
          this,
          Invocation.getter(#routeInformationParser),
        ),
      ) as _i7.GoRouteInformationParser);

  @override
  bool get overridePlatformDefaultLocation => (super.noSuchMethod(
        Invocation.getter(#overridePlatformDefaultLocation),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i8.GoRouterState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeGoRouterState_6(
          this,
          Invocation.getter(#state),
        ),
        returnValueForMissingStub: _FakeGoRouterState_6(
          this,
          Invocation.getter(#state),
        ),
      ) as _i8.GoRouterState);

  @override
  set configuration(_i3.RouteConfiguration? _configuration) => super.noSuchMethod(
        Invocation.setter(
          #configuration,
          _configuration,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set routerDelegate(_i5.GoRouterDelegate? _routerDelegate) => super.noSuchMethod(
        Invocation.setter(
          #routerDelegate,
          _routerDelegate,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set routeInformationProvider(_i6.GoRouteInformationProvider? _routeInformationProvider) =>
      super.noSuchMethod(
        Invocation.setter(
          #routeInformationProvider,
          _routeInformationProvider,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set routeInformationParser(_i7.GoRouteInformationParser? _routeInformationParser) =>
      super.noSuchMethod(
        Invocation.setter(
          #routeInformationParser,
          _routeInformationParser,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool canPop() => (super.noSuchMethod(
        Invocation.method(
          #canPop,
          [],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  String namedLocation(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    String? fragment,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #namedLocation,
          [name],
          {
            #pathParameters: pathParameters,
            #queryParameters: queryParameters,
            #fragment: fragment,
          },
        ),
        returnValue: _i11.dummyValue<String>(
          this,
          Invocation.method(
            #namedLocation,
            [name],
            {
              #pathParameters: pathParameters,
              #queryParameters: queryParameters,
              #fragment: fragment,
            },
          ),
        ),
        returnValueForMissingStub: _i11.dummyValue<String>(
          this,
          Invocation.method(
            #namedLocation,
            [name],
            {
              #pathParameters: pathParameters,
              #queryParameters: queryParameters,
              #fragment: fragment,
            },
          ),
        ),
      ) as String);

  @override
  void go(
    String? location, {
    Object? extra,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #go,
          [location],
          {#extra: extra},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void restore(_i14.RouteMatchList? matchList) => super.noSuchMethod(
        Invocation.method(
          #restore,
          [matchList],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void goNamed(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
    String? fragment,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #goNamed,
          [name],
          {
            #pathParameters: pathParameters,
            #queryParameters: queryParameters,
            #extra: extra,
            #fragment: fragment,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i10.Future<T?> push<T extends Object?>(
    String? location, {
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #push,
          [location],
          {#extra: extra},
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  _i10.Future<T?> pushNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pushNamed,
          [name],
          {
            #pathParameters: pathParameters,
            #queryParameters: queryParameters,
            #extra: extra,
          },
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  _i10.Future<T?> pushReplacement<T extends Object?>(
    String? location, {
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pushReplacement,
          [location],
          {#extra: extra},
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  _i10.Future<T?> pushReplacementNamed<T extends Object?>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pushReplacementNamed,
          [name],
          {
            #pathParameters: pathParameters,
            #queryParameters: queryParameters,
            #extra: extra,
          },
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  _i10.Future<T?> replace<T>(
    String? location, {
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #replace,
          [location],
          {#extra: extra},
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  _i10.Future<T?> replaceNamed<T>(
    String? name, {
    Map<String, String>? pathParameters = const {},
    Map<String, dynamic>? queryParameters = const {},
    Object? extra,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #replaceNamed,
          [name],
          {
            #pathParameters: pathParameters,
            #queryParameters: queryParameters,
            #extra: extra,
          },
        ),
        returnValue: _i10.Future<T?>.value(),
        returnValueForMissingStub: _i10.Future<T?>.value(),
      ) as _i10.Future<T?>);

  @override
  void pop<T extends Object?>([T? result]) => super.noSuchMethod(
        Invocation.method(
          #pop,
          [result],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void refresh() => super.noSuchMethod(
        Invocation.method(
          #refresh,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
