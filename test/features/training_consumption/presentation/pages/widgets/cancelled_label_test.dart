import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/widgets/cancelled_label.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('CancelledLabel Golden Test', () {
    testGoldens('CancelledLabel widget', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const Scaffold(body: Center(child: CancelledLabel()))),
        surfaceSize: const Size(100, 50),
      );

      await screenMatchesGolden(tester, 'cancelled_label_widget');
    });
  });
}
