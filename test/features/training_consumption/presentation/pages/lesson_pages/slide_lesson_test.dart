import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/slide_lesson.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/widgets/slide_widgets/slide_viewer_controls.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_consumption_seed.dart';
import '../../bloc/training_consumption_bloc_test.mocks.dart';
import '../training_consumption_page_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();
  final mockGoRouter = MockGoRouter();

  setUpAll(() async {
    await testEnvSetup();

    GetIt.instance
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc)
      ..registerSingleton<GoRouter>(mockGoRouter);

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
          slideFilePath: 'assets/test_assets/test.pdf',
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });
  tearDown(() async {
    await testEnvTearDown();
  });

  group('slide lesson page', () {
    testGoldens('slide lesson page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: SlideLesson(
            lessonParams: LessonParams(
              isCompleted: true,
              completedLessonsInSection: 1,
              trainingConsumptionBloc: mockTrainingConsumptionBloc,
              section: trainingDetailsModel.trainingStructure!.sections.first,
              lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[4],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'slide_lesson_page_portrait', devices: [Device.iphone11]);

      //trigger onDoubleTapDown
    });

    testGoldens('SlideViewerControls golden test', (tester) async {
      final currentPageNotifier = ValueNotifier<int>(1);

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Scaffold(
            body: ColoredBox(
              color: Colors.black,
              child: SlideViewerControls(
                screenOrientationToggle: () {},
                totalPages: '10',
                currentPageNotifier: currentPageNotifier,
                goToPreviousPage: () {
                  currentPageNotifier.value = (currentPageNotifier.value - 1).clamp(1, 10);
                },
                goToNextPage: () {
                  currentPageNotifier.value = (currentPageNotifier.value + 1).clamp(1, 10);
                },
              ),
            ),
          ),
        ),
        surfaceSize: const Size(370, 60),
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'slide_viewer_controls_landscape');

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MediaQuery(
            data: const MediaQueryData(size: Size(370, 370)),
            child: Scaffold(
              body: ColoredBox(
                color: Colors.black,
                child: SlideViewerControls(
                  screenOrientationToggle: () {},
                  totalPages: '10',
                  currentPageNotifier: currentPageNotifier,
                  goToPreviousPage: () {
                    currentPageNotifier.value = (currentPageNotifier.value - 1).clamp(1, 10);
                  },
                  goToNextPage: () {
                    currentPageNotifier.value = (currentPageNotifier.value + 1).clamp(1, 10);
                  },
                ),
              ),
            ),
          ),
        ),
        surfaceSize: const Size(370, 60),
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'slide_viewer_controls_portrait');
    });
  });
}
