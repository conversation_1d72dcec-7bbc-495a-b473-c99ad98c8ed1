import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/video_lesson.dart';
import 'package:video_player_platform_interface/video_player_platform_interface.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../../catalog/shared/catalog_test_seed.dart';
import '../../../../training_details/mock/fake_video_platform.dart';
import '../../../training_consumption_seed.dart';
import '../training_consumption_page_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

  setUpAll(() async {
    await testEnvSetup();
    GetIt.instance.registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc);

    VideoPlayerPlatform.instance = FakeVideoPlayerPlatform();
    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
          videoLessonUrl: 'url',
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('video lesson page', () {
    testGoldens('video lesson page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: VideoLesson(
            lessonParams: LessonParams(
              isCompleted: true,
              completedLessonsInSection: 1,
              trainingConsumptionBloc: mockTrainingConsumptionBloc,
              section: trainingDetailsModel.trainingStructure!.sections.first,
              lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[1],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'video_lesson_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
    testGoldens('video not found lesson page', (tester) async {
      whenListen(
        mockTrainingConsumptionBloc,
        Stream<TrainingConsumptionState>.value(TrainingConsumptionState()),
        initialState: TrainingConsumptionState(),
      );
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: VideoLesson(
            lessonParams: LessonParams(
              isCompleted: true,
              completedLessonsInSection: 1,
              trainingConsumptionBloc: mockTrainingConsumptionBloc,
              section: trainingDetailsModel.trainingStructure!.sections.first,
              lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[1],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'video_not_found_lesson_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
