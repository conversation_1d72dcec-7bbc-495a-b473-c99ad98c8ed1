import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/file_lesson.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_consumption_seed.dart';
import '../../bloc/training_consumption_bloc_test.mocks.dart';
import '../training_consumption_page_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

  final mockGoRouter = MockGoRouter();
  setUpAll(() async {
    await testEnvSetup();

    GetIt.instance
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc)
      ..registerSingleton<GoRouter>(mockGoRouter);

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('file page', () {
    testGoldens('file page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: FileLesson(
            lessonParams: LessonParams(
              isCompleted: true,
              completedLessonsInSection: 1,
              trainingConsumptionBloc: mockTrainingConsumptionBloc,
              section: trainingDetailsModel.trainingStructure!.sections.first,
              lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[2],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'file_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      await tester.tap(find.byType(ListTile).first);
    });
  });
}
