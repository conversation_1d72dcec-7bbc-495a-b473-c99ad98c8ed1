import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/article_lesson.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_consumption_seed.dart';
import '../../bloc/training_consumption_bloc_test.mocks.dart';
import '../training_consumption_page_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

  setUpAll(() async {
    await testEnvSetup();
    GetIt.instance
      ..registerSingleton<GoRouter>(MockGoRouter())
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc);

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('article page', () {
    testGoldens('article page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: ArticleLesson(
            lessonParams: LessonParams(
              isCompleted: true,
              completedLessonsInSection: 1,
              trainingConsumptionBloc: mockTrainingConsumptionBloc,
              section: trainingDetailsModel.trainingStructure!.sections.first,
              lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons.first,
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'article_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      await tester.drag(find.byType(ListView), const Offset(0, -500));
      await tester.pumpAndSettle();
      await tester.tap(find.byType(CloseButton));
    });
  });
}
