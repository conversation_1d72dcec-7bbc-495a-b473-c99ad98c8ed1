import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/live_session_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/lesson_pages/live_session_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

import '../../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_consumption_seed.dart';

class MockTrainingConsumptionBloc
    extends MockBloc<TrainingConsumptionEvent, TrainingConsumptionState>
    implements TrainingConsumptionBloc {}

class MockUrlLauncher extends Mock with MockPlatformInterfaceMixin implements UrlLauncherPlatform {}

MockUrlLauncher setupMockUrlLauncher() {
  final mock = MockUrlLauncher();
  registerFallbackValue(const LaunchOptions());
  when(() => mock.launchUrl(any(), any())).thenAnswer((_) async => true);
  return mock;
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late MockTrainingConsumptionBloc mockTrainingConsumptionBloc;
  late MockUrlLauncher mockUrlLauncher;

  setUp(() async {
    await testEnvSetup();
    mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();
    mockUrlLauncher = setupMockUrlLauncher();
    UrlLauncherPlatform.instance = mockUrlLauncher;
    GetIt.instance.registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc);

    const section = Section(
      id: 'section1',
      title: 'Test Section',
      index: 0,
      lessons: [
        Lesson(
          id: 'lesson1',
          title: 'Test Lesson',
          index: 0,
          lessonType: LessonType.Article,
          text: 'Test lesson content',
          resources: [],
        ),
      ],
    );

    const trainingStructure = TrainingStructureModel(sections: [section]);

    final trainingDetails = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
    when(() => mockTrainingConsumptionBloc.trainingDetailsModel).thenReturn(trainingDetails);

    when(() => mockTrainingConsumptionBloc.state).thenReturn(
      TrainingConsumptionState(
        trainingConsumptionModel: TrainingConsumptionModel.fromJson(
          tTrainingConsumptionDetailsJson,
        ),
        trainingStructure: trainingStructure,
      ),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
    GetIt.instance.reset();
  });

  group('Live Session Page', () {
    testGoldens('Live Session page in different states', (tester) async {
      final meeting = Meeting(
        id: 'test_meeting',
        title: 'Test Meeting',
        trainingId: 'test_training',
        liveSessionId: 'test_session',
        startDate: DateTime.now(),
        durationHours: 1,
        durationMinutes: 30,
        zoomLink: 'zoom.us/test',
        zoomPassword: '123456',
        meetingId: '123456789',
        status: Constants.PUBLISHED,
        cancelled: false,
        sections: ['section1'],
      );

      final params = LiveSessionParams(
        meeting: meeting,
        trainingConsumptionBloc: mockTrainingConsumptionBloc,
      );

      await tester.pumpWidgetBuilder(nspTestWrapper(child: LiveSessionPage(params: params)));

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'live_session_page_upcoming',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      await tester.tap(find.text(LocaleKeys.join_meeting.tr()));
      await tester.pumpAndSettle();

      final expectedUrl = Constants.httpPrefix + meeting.zoomLink!;
      verify(() => mockUrlLauncher.launchUrl(expectedUrl, any())).called(1);

      /// Test with cancelled meeting
      final cancelledMeeting = meeting.copyWith(
        meetingStatus: MeetingStatus.Cancelled,
        cancelled: true,
        status: Constants.PUBLISHED,
        cancellationReason: 'Cancelled due professors request',
      );
      final cancelledParams = params.copyWith(meeting: cancelledMeeting);

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: LiveSessionPage(params: cancelledParams)),
      );

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'live_session_page_cancelled',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      /// Test with passed meeting
      final passedMeeting = meeting.copyWith(
        meetingStatus: MeetingStatus.Passed,
        status: Constants.PUBLISHED,
      );
      final passedParams = params.copyWith(meeting: passedMeeting);

      await tester.pumpWidgetBuilder(nspTestWrapper(child: LiveSessionPage(params: passedParams)));

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'live_session_page_passed',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      /// Test with rescheduled meeting
      final rescheduledMeeting = meeting.copyWith(
        meetingStatus: MeetingStatus.Rescheduled,
        startDate: DateTime.now().toUtc().add(const Duration(hours: 3)),
        status: Constants.PUBLISHED,
        cancelled: false,
        cardNotes: [
          CardNote(
            createdDate: DateTime.now(),
            localizedContents: {
              'EN': const LocalizedContent(
                title: 'Meeting moved',
                message: 'Meeting was moved to another date',
              ),
            },
          ),
        ],
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: LiveSessionPage(params: params.copyWith(meeting: rescheduledMeeting)),
        ),
      );

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'live_session_page_rescheduled',
        devices: [Device.tabletPortrait, Device.iphone11],
      );
    });
  });
}
