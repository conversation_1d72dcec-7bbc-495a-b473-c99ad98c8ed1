import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/pages/training_consumption_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../training_consumption_seed.dart';
import '../bloc/training_consumption_bloc_test.mocks.dart';

class MockTrainingConsumptionBloc
    extends MockBloc<TrainingConsumptionEvent, TrainingConsumptionState>
    implements TrainingConsumptionBloc {
  @override
  TrainingDetailsModel get trainingDetailsModel =>
      TrainingDetailsModel.fromJson(tTrainingDetailsJson);
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

  setUpAll(() async {
    GetIt.instance
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerSingleton<Alice>(Alice())
      ..registerSingleton<GoRouter>(MockGoRouter())
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc);

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );

    await testEnvSetup();
  });

  tearDown(() => testEnvTearDown());

  group('Training Consumption', () {
    testGoldens('Training Consumption page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: TrainingConsumptionPage(TrainingDetailsModel.fromJson(tTrainingDetailsJson)),
        ),
      );

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'training_consumption_page',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      if (find.byIcon(Icons.add_circle_outline).first.evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.add_circle_outline).first);
      }
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'training_consumption_page_expanded',
        devices: [
          Device.tabletPortrait,
          const Device(size: Size(600, 1278.5), name: 'long height'),
        ],
      );

      await tester.tap(find.text(LocaleKeys.trainingOverview.tr()));
    });
  });
}
