import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart' as mock;
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/mixin/unified_navigation_mixin.dart';

import '../presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../training_consumption_seed.dart';

class MockTrainingConsumptionBloc
    extends MockBloc<TrainingConsumptionEvent, TrainingConsumptionState>
    implements TrainingConsumptionBloc {
  @override
  TrainingDetailsModel get trainingDetailsModel => const TrainingDetailsModel(
        id: 't1',
        title: 'Test Training',
        description: 'Test Description',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 60,
        durationMax: 60,
        level: 'Beginner',
        skillLevel: 'Beginner',
        profileImage: null,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: null,
        qualificationTests: [],
        sector: null,
        domain: null,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: 'org1',
        promoVideoUrl: '',
        type: TrainingType.SelfPaced,
        seatingCapacities: [],
        studyStreams: [],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
}

class MockOpenLessonEvent extends Mock implements OpenLessonEvent {}

class TestNavigation with UnifiedNavigationMixin {}

void main() {
  late TestNavigation navigation;
  late MockTrainingConsumptionBloc mockTrainingConsumptionBloc;
  late MockGoRouter mockGoRouter;

  final baseTrainingConsumption = TrainingConsumptionModel(
    id: 't1',
    trainingId: 't1',
    status: 'Active',
    progress: [],
    passedScore: 0,
    allSections: 1,
    passedSections: 0,
    enrolledDate: DateTime.now(),
    historyOfTests: [],
    completedLessonsIDs: {},
  );

  setUpAll(() {
    registerFallbackValue(MockOpenLessonEvent());
    mockGoRouter = MockGoRouter();
    GetIt.instance.registerSingleton<GoRouter>(mockGoRouter);
    navigation = TestNavigation();
    mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });

  group('UnifiedNavigationMixin', () {
    group('getSections', () {
      test('returns empty list when trainingStructure is null', () {
        when(() => mockTrainingConsumptionBloc.state).thenReturn(TrainingConsumptionState());

        final result = navigation.getSections(mockTrainingConsumptionBloc);
        expect(result, isEmpty);
      });

      test('returns sections from trainingStructure', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(trainingStructure: TrainingStructureModel(sections: sections)),
        );

        final result = navigation.getSections(mockTrainingConsumptionBloc);
        expect(result, equals(sections));
      });
    });

    group('navigateToFirstLesson', () {
      test('should pop when sections are empty', () {
        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(trainingStructure: const TrainingStructureModel(sections: [])),
        );

        navigation.navigateToFirstLesson(mockTrainingConsumptionBloc);
        mock.verify(mockGoRouter.pop()).called(1);
      });

      test('should pop when first section has no lessons', () {
        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(
            trainingStructure: const TrainingStructureModel(
              sections: [Section(id: 's1', title: 'Section 1', index: 0, lessons: [])],
            ),
          ),
        );

        navigation.navigateToFirstLesson(mockTrainingConsumptionBloc);
        mock.verify(mockGoRouter.pop()).called(1);
      });

      test('should navigate to first lesson', () {
        const firstLesson = Lesson(
          id: 'l1',
          title: 'Lesson 1',
          lessonType: LessonType.Video,
          index: 0,
          resources: [],
        );

        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(
            trainingConsumptionModel: baseTrainingConsumption.copyWith(completedLessonsIDs: {'l2'}),
            trainingStructure: const TrainingStructureModel(
              sections: [
                Section(id: 's1', title: 'Section 1', index: 0, lessons: [firstLesson]),
              ],
            ),
          ),
        );

        navigation.navigateToFirstLesson(mockTrainingConsumptionBloc);
        verify(() => mockTrainingConsumptionBloc.add(any(that: isA<OpenLessonEvent>()))).called(1);
      });
    });

    group('getNextLesson', () {
      test('returns next lesson in same section', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
              Lesson(
                id: 'l2',
                title: 'Lesson 2',
                lessonType: LessonType.Video,
                index: 1,
                resources: [],
              ),
            ],
          ),
        ];

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        final (nextLesson, nextSection) = navigation.getNextLesson(sections, lessonParams);
        expect(nextLesson?.id, equals('l2'));
        expect(nextSection?.id, equals('s1'));
      });

      test('returns first lesson of next section', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
          const Section(
            id: 's2',
            title: 'Section 2',
            index: 1,
            lessons: [
              Lesson(
                id: 'l2',
                title: 'Lesson 2',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        final (nextLesson, nextSection) = navigation.getNextLesson(sections, lessonParams);
        expect(nextLesson?.id, equals('l2'));
        expect(nextSection?.id, equals('s2'));
      });
    });

    group('getPreviousLesson', () {
      test('returns previous lesson in same section', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
              Lesson(
                id: 'l2',
                title: 'Lesson 2',
                lessonType: LessonType.Video,
                index: 1,
                resources: [],
              ),
            ],
          ),
        ];

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[1],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        final (prevLesson, prevSection) = navigation.getPreviousLesson(sections, lessonParams);
        expect(prevLesson?.id, equals('l1'));
        expect(prevSection?.id, equals('s1'));
      });

      test('returns last lesson of previous section', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
          const Section(
            id: 's2',
            title: 'Section 2',
            index: 1,
            lessons: [
              Lesson(
                id: 'l2',
                title: 'Lesson 2',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        final lessonParams = LessonParams(
          section: sections[1],
          lesson: sections[1].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        final (prevLesson, prevSection) = navigation.getPreviousLesson(sections, lessonParams);
        expect(prevLesson?.id, equals('l1'));
        expect(prevSection?.id, equals('s1'));
      });
    });

    group('isPostTestAvailable', () {
      test('returns false when model is null', () {
        expect(navigation.isPostTestAvailable(null), isFalse);
      });

      test('returns true when post qualification test exists', () {
        final model = baseTrainingConsumption.copyWith(
          postQualificationTest: const PostQualificationTest(
            id: 'pt1',
            randomized: true,
            title: 'Post Test',
            description: 'Post Test Description',
            minimumScore: 70,
            timeLimit: 60,
            mandatory: true,
            firstAttempt: true,
            type: QualificationTestType.POST,
            applicantAnswers: [],
            questions: [],
            applicantSavedAnswers: {},
          ),
        );

        expect(navigation.isPostTestAvailable(model), isTrue);
      });
    });

    group('canNavigateToPostQualificationTest', () {
      test('returns true for last lesson with available post test', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Last Lesson',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(
            trainingConsumptionModel: baseTrainingConsumption.copyWith(
              postQualificationTest: const PostQualificationTest(
                id: 'pt1',
                randomized: true,
                title: 'Post Test',
                description: 'Post Test Description',
                minimumScore: 70,
                timeLimit: 60,
                mandatory: true,
                firstAttempt: true,
                type: QualificationTestType.POST,
                applicantAnswers: [],
                questions: [],
                applicantSavedAnswers: {},
              ),
            ),
            trainingStructure: TrainingStructureModel(sections: sections),
          ),
        );

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        expect(navigation.canNavigateToPostQualificationTest(lessonParams), isTrue);
      });
    });

    group('getMeetingLessons', () {
      test('returns all lessons when meeting sections is empty', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        final meeting = Meeting(
          id: 'm1',
          title: 'Meeting 1',
          meetingStatus: MeetingStatus.Live,
          sections: [],
          trainingId: 't1',
          liveSessionId: 'ls1',
          startDate: DateTime.now(),
          durationHours: 1,
          durationMinutes: 0,
          zoomLink: 'https://zoom.us/j/123',
          zoomPassword: '123456',
          meetingId: '123456789',
          status: 'active',
          cancelled: false,
        );

        final lessons = navigation.getMeetingLessons(meeting, sections);
        expect(lessons.length, equals(1));
        expect(lessons.first.id, equals('l1'));
      });

      test('returns only lessons from specified sections', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l1',
                title: 'Lesson 1',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
          const Section(
            id: 's2',
            title: 'Section 2',
            index: 1,
            lessons: [
              Lesson(
                id: 'l2',
                title: 'Lesson 2',
                lessonType: LessonType.Video,
                index: 0,
                resources: [],
              ),
            ],
          ),
        ];

        final meeting = Meeting(
          id: 'm1',
          title: 'Meeting 1',
          meetingStatus: MeetingStatus.Live,
          sections: ['s1'],
          trainingId: 't1',
          liveSessionId: 'ls1',
          startDate: DateTime.now(),
          durationHours: 1,
          durationMinutes: 0,
          zoomLink: 'https://zoom.us/j/123',
          zoomPassword: '123456',
          meetingId: '123456789',
          status: 'active',
          cancelled: false,
        );

        final lessons = navigation.getMeetingLessons(meeting, sections);
        expect(lessons.length, equals(1));
        expect(lessons.first.id, equals('l1'));
      });
    });

    group('isLastContent', () {
      test('returns true when no next content and no post-test', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l2',
                title: 'Last Lesson',
                lessonType: LessonType.Video,
                index: 1,
                resources: [],
              ),
            ],
          ),
        ];

        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(
            trainingConsumptionModel: baseTrainingConsumption,
            trainingStructure: TrainingStructureModel(sections: sections),
          ),
        );

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        expect(navigation.isLastContent(lessonParams), isTrue);
      });

      test('returns false when post-test exists and accessible', () {
        final sections = [
          const Section(
            id: 's1',
            title: 'Section 1',
            index: 0,
            lessons: [
              Lesson(
                id: 'l2',
                title: 'Last Lesson',
                lessonType: LessonType.Video,
                index: 1,
                resources: [],
              ),
            ],
          ),
        ];

        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(
            trainingConsumptionModel: baseTrainingConsumption.copyWith(
              postQualificationTest: const PostQualificationTest(
                id: '1',
                title: 'Post Test',
                questions: [],
                description: '',
                minimumScore: 0,
                mandatory: false,
                applicantAnswers: [],
                applicantSavedAnswers: {},
              ),
            ),
            trainingStructure: TrainingStructureModel(sections: sections),
          ),
        );

        final lessonParams = LessonParams(
          section: sections[0],
          lesson: sections[0].lessons[0],
          completedLessonsInSection: 0,
          isCompleted: false,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        );

        expect(navigation.isLastContent(lessonParams), isFalse);
      });
    });
  });
}
