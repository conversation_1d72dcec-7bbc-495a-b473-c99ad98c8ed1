const tPreQualificationTestJson = {
  "questions": [
    {
      "id": "question_1",
      "questionType": "multiple_choice",
      "question": "What is Dar<PERSON>?",
      "answers": [
        {"id": "answer_1", "answer": "A programming language", "correct": true, "index": 0},
        {"id": "answer_2", "answer": "B programming lang.", "correct": false, "index": 0},
      ],
      "index": 0,
      "lastModifiedDate": "2023-09-01T00:00:00.000Z",
    },
    {
      "id": "question_2",
      "questionType": "multiple_choice",
      "question": "What is Flutter?",
      "answers": [
        {"id": "answer_3", "answer": "A UI framework", "correct": true, "index": 0},
        {"id": "answer_4", "answer": "A database", "correct": false, "index": 0},
      ],
      "index": 1,
      "lastModifiedDate": "2023-09-01T00:00:00.000Z",
    },
  ],
  "applicantAnswers": [
    {"questionId": "question_1", "correct": true, "questionTitle": "What is Dart?"},
    {"questionId": "question_2", "correct": false, "questionTitle": "What is Flutter?"},
  ],
  "applicantSavedAnswers": {
    "question_1": {"questionId": "question_1", "question": "What is Dart?", "answerId": "answer_1"},
    "question_2": {
      "questionId": "question_2",
      "question": "What is Flutter?",
      "answerId": "answer_4",
    },
  },
  "id": "pre_test_1",
  "randomized": true,
  "title": "Pre-Qualification Test",
  "description": "A test before starting the training",
  "minimumScore": 50,
  "timeLimit": 30,
  "mandatory": true,
  "finalScore": 50,
  "resolutionDate": "2023-09-02T00:00:00.000Z",
  "firstAttempt": true,
  "type": "PRE",
};

const tPostQualificationTestJson = {
  "questions": [
    {
      "id": "question_3",
      "questionType": "multiple_choice",
      "question": "What is Dart?",
      "answers": [
        {"id": "answer_5", "answer": "A programming language", "correct": true, "index": 0},
        {"id": "answer_6", "answer": "B programming lang.", "correct": false, "index": 0},
      ],
      "index": 0,
      "lastModifiedDate": "2023-09-01T00:00:00.000Z",
    },
    {
      "id": "question_4",
      "questionType": "multiple_choice",
      "question": "What is Flutter?",
      "answers": [
        {"id": "answer_7", "answer": "A UI framework", "correct": true, "index": 0},
        {"id": "answer_8", "answer": "A database", "correct": false, "index": 0},
      ],
      "index": 1,
      "lastModifiedDate": "2023-09-01T00:00:00.000Z",
    },
  ],
  "applicantAnswers": [
    {"questionId": "question_3", "correct": false, "questionTitle": "What is DART?"},
    {"questionId": "question_4", "correct": true, "questionTitle": "What is Flutter?"},
  ],
  "applicantSavedAnswers": {
    "answer_7": {
      "questionId": "question_4",
      "question": "What is Flutter?",
      "answerId": "answer_7",
    },
  },
  "id": "post_test_1",
  "randomized": false,
  "title": "Post-Qualification Test",
  "description": "A test after completing the training",
  "minimumScore": 60,
  "timeLimit": 30,
  "mandatory": true,
  "finalScore": 90,
  "resolutionDate": "2023-10-02T00:00:00.000Z",
  "firstAttempt": true,
  "type": "POST",
};

const tTrainingConsumptionDetailsJson = {
  "id": "dummy_id",
  "trainingId": "dummy_training_id",
  "status": "in_progress",
  "progress": [
    {
      "lessonId": "lesson_1",
      "quizResult": {
        "totalScore": 80,
        "questionAnswerPairs": {
          "question_5": {
            "answerIds": ["answer_9"],
            "questionId": "question_5",
            "question": "What is Flutter?",
            "correct": true,
          },
        },
      },
      "isPassed": true,
    },
  ],
  "passedScore": 75,
  "allSections": 10,
  "passedSections": 5,
  "completedLessonsIDs": ["lesson_1", "lesson_2"],
  "completedDate": "2023-10-01T00:00:00.000Z",
  "enrolledDate": "2023-09-01T00:00:00.000Z",
  "certificateId": 123,
  "preQualificationTest": tPreQualificationTestJson,
  "postQualificationTest": tPostQualificationTestJson,
  "preTestPassed": true,
  "historyOfTests": [
    {
      "id": "history_test_1",
      "randomized": true,
      "questions": [
        {
          "id": "question_6",
          "lastModifiedDate": "2023-09-01T00:00:00.000Z",
          "questionType": "multiple_choice",
          "question": "What is Dart used for?",
          "answers": [
            {"id": "answer_10", "answer": "Building mobile apps", "correct": true, "index": 0},
          ],
          "index": 0,
        },
      ],
      "title": "History Test",
      "description": "A test from the history",
      "minimumScore": 70,
      "timeLimit": 30,
      "mandatory": true,
      "finalScore": 85,
      "applicantAnswers": [
        {"questionId": "question_6", "correct": true, "questionTitle": "What is Dart used for?"},
      ],
      "resolutionDate": "2023-09-03T00:00:00.000Z",
      "firstAttempt": true,
      "applicantSavedAnswers": {
        "answer_10": {
          "questionId": "question_6",
          "question": "What is Dart used for?",
          "answerId": "answer_10",
        },
      },
    },
  ],
};
