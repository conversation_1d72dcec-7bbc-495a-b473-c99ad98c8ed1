import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/search/data/data_sources/search_data_source.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';

import '../../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../search_seed.dart';

void main() {
  late final MockDio mockDio;
  late final SearchDataSource dataSource;
  const searchQuery = 'test';

  setUpAll(() {
    mockDio = MockDio();
    dataSource = SearchDataSource(dio: mockDio);

    when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
      (_) async => Response(requestOptions: RequestOptions(), data: tSearchJson, statusCode: 200),
    );
  });

  group('search', () {
    test('should get SearchModel from the data source when status code is 200', () async {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(requestOptions: RequestOptions(), data: tSearchJson, statusCode: 200),
      );

      final result = await dataSource.search(searchQuery);

      verify(mockDio.get(any, queryParameters: anyNamed('queryParameters')));
      expect(result, isA<SearchModel>());
    });

    test('should get SearchModel from the data source when status code is 201', () async {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(requestOptions: RequestOptions(), data: tSearchJson, statusCode: 201),
      );

      final result = await dataSource.search(searchQuery);

      verify(mockDio.get(any, queryParameters: anyNamed('queryParameters')));
      expect(result, isA<SearchModel>());
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(requestOptions: RequestOptions(), data: tSearchJson, statusCode: 404),
      );

      expect(() => dataSource.search(searchQuery), throwsA(isA<DioException>()));
    });
  });
}
