import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/presentation/bloc/search_bloc.dart';

import '../../search_seed.dart';
import '../pages/search_page_test.mocks.dart';

void main() {
  late final MockSearchRepository mockSearchRepository;

  setUpAll(() {
    mockSearchRepository = MockSearchRepository();
  });

  blocTest<SearchBloc, SearchState>(
    'emits [Searching, SearchResultsState] when SearchEvent is added',
    build: () {
      when(mockSearchRepository.search(any)).thenAnswer(
        (_) async => SearchModel.fromJson(tSearchJson),
      );
      return SearchBloc(searchRepository: mockSearchRepository);
    },
    act: (bloc) => bloc.add(const SearchEvent(query: 'test')),
    expect: () => [const Searching(), isA<SearchResultsState>()],
  );

  blocTest<SearchBloc, SearchState>(
    'emits [SearchInitial] when SearchReset is added',
    build: () => SearchBloc(searchRepository: mockSearchRepository),
    act: (bloc) => bloc.add(const SearchReset()),
    expect: () => [const SearchInitial()],
  );

  blocTest<SearchBloc, SearchState>(
    'emits [Searching, SearchError] when SearchEvent is added and searchRepository.search returns an error',
    build: () {
      when(mockSearchRepository.search(any)).thenAnswer((_) async => throw Exception());
      return SearchBloc(searchRepository: mockSearchRepository);
    },
    act: (bloc) => bloc.add(const SearchEvent(query: 'test')),
    expect: () => [const Searching(), isA<SearchError>()],
  );
}
