import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/presentation/bloc/search_bloc.dart';
import 'package:national_skills_platform/features/search/presentation/pages/view_all_page.dart';
import 'package:provider/provider.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/presentation/catalog_page_test.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../search_seed.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final injector = GetIt.instance;
  late final MockLearningTracksBloc mockLearningTracksBloc;
  late final MockTrainingsBloc mockTrainingsBloc;

  setUpAll(() async {
    await testEnvSetup();
    mockLearningTracksBloc = MockLearningTracksBloc();
    mockTrainingsBloc = MockTrainingsBloc();

    injector
      ..registerFactory<LearningTracksBloc>(() => mockLearningTracksBloc)
      ..registerFactory<TrainingsBloc>(() => mockTrainingsBloc);

    ///Bloc State Stub
    whenListen(
      mockTrainingsBloc,
      Stream<TrainingsState>.value(
        TrainingsLoaded(
          tTrainingsModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
        ),
      ),
      initialState: const TrainingsInitial(),
    );
    whenListen(
      mockLearningTracksBloc,
      Stream<LearningTracksState>.value(
        LearningTracksLoaded(
          tLearningTrackModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
          appliedFilter: const FilterModel(),
        ),
      ),
      initialState: LearningTracksInitial(),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('view all page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Provider.value(
            value: GetIt.instance,
            child: ViewAllPage(
              courseType: CourseType.training,
              searchResultsState: SearchResultsState(
                SearchModel.fromJson(tSearchJson),
                'searchKey',
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'view_all_page_training',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Provider.value(
            value: GetIt.instance,
            child: ViewAllPage(
              courseType: CourseType.learningTrack,
              searchResultsState: SearchResultsState(
                SearchModel.fromJson(tSearchJson),
                'searchKey',
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'view_all_page_learning_track', devices: [Device.iphone11]);
    });
  });
}
