import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/domain/repositories/search_repository.dart';
import 'package:national_skills_platform/features/search/presentation/bloc/search_bloc.dart';
import 'package:national_skills_platform/features/search/presentation/pages/search_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../../search_seed.dart';
import 'search_page_test.mocks.dart';

class MockSearchBloc extends MockBloc<SearchEvent, SearchState> implements SearchBloc {}

@GenerateMocks([SearchRepository])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockSearchBloc = MockSearchBloc();
  final mockSearchRepository = MockSearchRepository();

  final searchModel = SearchModel.fromJson(tSearchJson);
  final emptySearchResults = SearchModel.fromJson(tEmptySearchJson);

  setUpAll(() async {
    GetIt.instance
      ..registerSingleton<GoRouter>(MockGoRouter())
      ..registerFactory<SearchBloc>(() => mockSearchBloc);

    ///Repositories stub
    when(
      mockSearchRepository.search(''),
    ).thenAnswer((_) => Future<SearchModel>.value(searchModel));

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Search Page initial state', () {
    ///Bloc State Stub
    whenListen(
      mockSearchBloc,
      Stream<SearchState>.value(const SearchInitial()),
      initialState: const SearchInitial(),
    );

    testGoldens('Training Search', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const SearchPage(courseType: CourseType.training)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'training_search_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });

    testGoldens('learning_track_search_page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const SearchPage(courseType: CourseType.learningTrack)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'learning_track_search_page', devices: [Device.iphone11]);
    });
  });

  group('search page states', () {
    testGoldens('no_results_found', (tester) async {
      ///Bloc State Stub
      whenListen(
        mockSearchBloc,
        Stream<SearchState>.value(SearchResultsState(emptySearchResults, '')),
        initialState: const SearchInitial(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const SearchPage(courseType: CourseType.learningTrack)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'no_results_found', devices: [Device.iphone11]);
    });

    testGoldens('results_state_training', (tester) async {
      ///Bloc State Stub
      whenListen(
        mockSearchBloc,
        Stream<SearchState>.value(SearchResultsState(searchModel, '')),
        initialState: const SearchInitial(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const SearchPage(courseType: CourseType.training)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'results_state_training', devices: [Device.iphone11]);
      await tester.tap(find.byType(ListTile).first);
    });

    testGoldens('results_state_learning_track', (tester) async {
      ///Bloc State Stub
      whenListen(
        mockSearchBloc,
        Stream<SearchState>.value(SearchResultsState(searchModel, '')),
        initialState: const SearchInitial(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const SearchPage(courseType: CourseType.learningTrack)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'results_state_learning_track', devices: [Device.iphone11]);
      await tester.tap(find.text(LocaleKeys.view_all.tr()));
    });
  });
}
