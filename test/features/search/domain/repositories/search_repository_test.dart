import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/search/data/data_sources/search_data_source.dart';
import 'package:national_skills_platform/features/search/data/models/search_model.dart';
import 'package:national_skills_platform/features/search/domain/repositories/search_repository.dart';

import '../../search_seed.dart';
import 'search_repository_test.mocks.dart';

@GenerateMocks([SearchDataSource])
void main() {
  late final MockSearchDataSource mockDataSource;
  late final SearchRepository repository;
  const tQuery = 'test';

  setUp(() {
    mockDataSource = MockSearchDataSource();
    repository = SearchRepository(searchDataSource: mockDataSource);

    when(mockDataSource.search(tQuery)).thenAnswer(
      (_) async => SearchModel(trainings: mockContentData, learningTracks: mockContentData),
    );
  });

  group('search', () {
    test('should get SearchResultsEntity from the data source', () async {
      final result = await repository.search(tQuery);

      verify(mockDataSource.search(tQuery));
      expect(result, isA<SearchModel>());
    });
  });
}
