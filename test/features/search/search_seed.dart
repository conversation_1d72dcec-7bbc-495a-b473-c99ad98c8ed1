import 'package:national_skills_platform/features/search/data/models/search_model.dart';

final mockContentData = ContentData(
  content: [
    Content(
      id: '1',
      title: 'Test Title',
      profileImageUrl: 'Test URL',
      organizationName: 'Test Organization',
    ),
  ],
  pageable: Pageable(pageNumber: 1, pageSize: 1, sort: [], offset: 0, paged: true, unpaged: false),
  totalElements: 1,
  totalPages: 1,
  last: true,
  numberOfElements: 1,
  size: 1,
  number: 1,
  sort: [],
  first: false,
  empty: false,
);

final Map<String, dynamic> tSearchJson = {
  "trainings": {
    "content": [
      {
        "id": "40e75643-6f4d-4ea8-82db-dbbe5058e63f",
        "title": "Useful Excel for Beginners 1 ",
        "profileImageUrl":
            "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/2024-04-24T14%3A44%3A52.088174609?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240505T121204Z&X-Amz-SignedHeaders=host&X-Amz-Expires=18000&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240505%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=b6ba2dc9706e7337ceed07b4dfedbd220e4f9cfdac6cc433da319e7b3a475462",
        "organizationName": "Takamol",
      },
      {
        "id": "960e57dd-382e-4719-91d2-330e41af7416",
        "title": " 3 Useful Excel for Beginners",
        "profileImageUrl":
            "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/2024-04-24T14%3A44%3A52.088174609?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240505T121204Z&X-Amz-SignedHeaders=host&X-Amz-Expires=18000&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240505%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=b6ba2dc9706e7337ceed07b4dfedbd220e4f9cfdac6cc433da319e7b3a475462",
        "organizationName": "Takamol",
      },
      {
        "id": "3c87e427-8f3f-49f0-978f-857d98727f4a",
        "title": "Useful Excel for Beginners 2 ",
        "profileImageUrl":
            "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/2024-04-24T14%3A44%3A52.088174609?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240505T121204Z&X-Amz-SignedHeaders=host&X-Amz-Expires=18000&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240505%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=b6ba2dc9706e7337ceed07b4dfedbd220e4f9cfdac6cc433da319e7b3a475462",
        "organizationName": "Takamol",
      },
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 3,
      "sort": [],
      "offset": 0,
      "paged": true,
      "unpaged": false,
    },
    "totalElements": 10,
    "totalPages": 4,
    "last": false,
    "numberOfElements": 3,
    "size": 3,
    "number": 0,
    "sort": [],
    "first": true,
    "empty": false,
  },
  "learningTracks": {
    "content": [
      {
        "id": "4c1e0948-36d8-49fe-8cf5-52eafe76cca6",
        "title": "Learning Tracks Test v1.0",
        "type": null,
        "profileImageUrl":
            "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/4c1e0948-36d8-49fe-8cf5-52eafe76cca6/2024-07-21T11%3A44%3A43.555679657?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240912T064325Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240912%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=fd82244ac96130eac359c9d37c5e8ea57977a1214947de0828fae89254ed1fb0",
        "organizationName": "Takamol",
      },
      {
        "id": "b0128733-1228-4730-9220-08f3a44323e5",
        "title": "Learning Tracks Test v2.0",
        "type": null,
        "profileImageUrl":
            "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/268fd1eb-55cf-4a51-9365-c3c0acd41450/2024-07-21T12%3A31%3A20.174749846?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240912T064325Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240912%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=b58f12873395e86a35c10a251a421bffeb3456ac41b5f394350992aae686f218",
        "organizationName": "Takamol",
      },
    ],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 3,
      "sort": [],
      "offset": 0,
      "paged": true,
      "unpaged": false,
    },
    "totalElements": 2,
    "totalPages": 1,
    "last": true,
    "numberOfElements": 2,
    "size": 3,
    "number": 0,
    "sort": [],
    "first": true,
    "empty": false,
  },
};

final Map<String, dynamic> tEmptySearchJson = {
  "trainings": {
    "content": [],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 3,
      "sort": [],
      "offset": 0,
      "paged": true,
      "unpaged": false,
    },
    "totalElements": 0,
    "totalPages": 0,
    "last": true,
    "numberOfElements": 0,
    "size": 3,
    "number": 0,
    "sort": [],
    "first": true,
    "empty": true,
  },
  "learningTracks": {
    "content": [],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 3,
      "sort": [],
      "offset": 0,
      "paged": true,
      "unpaged": false,
    },
    "totalElements": 0,
    "totalPages": 0,
    "last": true,
    "numberOfElements": 0,
    "size": 3,
    "number": 0,
    "sort": [],
    "first": true,
    "empty": true,
  },
};
