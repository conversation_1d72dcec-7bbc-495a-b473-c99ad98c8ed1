// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in national_skills_platform/test/features/profile/repository/user_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:flutter/material.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:national_skills_platform/features/profile_page/data/datasource/user_datasource.dart'
    as _i3;
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserModel_0 extends _i1.SmartFake implements _i2.UserModel {
  _FakeUserModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UserDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserDataSource extends _i1.Mock implements _i3.UserDataSource {
  MockUserDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.UserModel> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i4.Future<_i2.UserModel>.value(_FakeUserModel_0(
          this,
          Invocation.method(
            #getUserData,
            [],
          ),
        )),
      ) as _i4.Future<_i2.UserModel>);

  @override
  _i4.Future<String> getUserAvatar() => (super.noSuchMethod(
        Invocation.method(
          #getUserAvatar,
          [],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #getUserAvatar,
            [],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<String> uploadUserAvatar(_i6.MemoryImage? image) => (super.noSuchMethod(
        Invocation.method(
          #uploadUserAvatar,
          [image],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #uploadUserAvatar,
            [image],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<void> deleteUserAvatar() => (super.noSuchMethod(
        Invocation.method(
          #deleteUserAvatar,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
