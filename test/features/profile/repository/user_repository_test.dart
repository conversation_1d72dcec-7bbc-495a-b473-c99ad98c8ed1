import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/profile_page/data/datasource/user_datasource.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/domain/repository/user_repository.dart';

import '../profile_test_seed.dart';
import 'user_repository_test.mocks.dart';

@GenerateMocks([UserDataSource])
void main() {
  late MockUserDataSource mockDataSource;
  late UserRepository repository;
  final tMemoryImage = MemoryImage(Uint8List(10));

  setUp(() {
    mockDataSource = MockUserDataSource();
    repository = UserRepository(dataSource: mockDataSource);

    when(mockDataSource.getUserData()).thenAnswer((_) async => UserModel.fromJson(tUserDataJson));
    when(mockDataSource.getUserAvatar()).thenAnswer((_) async => 'avatar_url');
    when(mockDataSource.uploadUserAvatar(tMemoryImage)).thenAnswer((_) async => 'avatar_url');
  });

  group('user repository test', () {
    test('should get UserModel from the data source', () async {
      final result = await repository.getUserData();

      verify(mockDataSource.getUserData());
      expect(result, equals(UserModel.fromJson(tUserDataJson)));
    });

    test('should get user avatar url from the data source', () async {
      final result = await repository.getUserAvatar();

      verify(mockDataSource.getUserAvatar());
      expect(result, equals('avatar_url'));
    });

    test('should upload user avatar', () async {
      final result = await repository.uploadUserAvatar(tMemoryImage);

      verify(mockDataSource.uploadUserAvatar(tMemoryImage));
      expect(result, equals('avatar_url'));
    });
  });

  test('should delete user avatar', () async {
    await repository.deleteUserAvatar();

    verify(mockDataSource.deleteUserAvatar());
  });
}
