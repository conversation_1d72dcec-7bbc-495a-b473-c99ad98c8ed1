import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/profile_page/data/datasource/user_datasource.dart';
import 'package:path_provider/path_provider.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../profile_test_seed.dart';

import 'user_datasource_test.mocks.dart';

class MockFormData extends Mock implements FormDataHelper {
  @override
  Future<FormData> createFormData(String filePath, String imageTitle) {
    return super.noSuchMethod(
      Invocation.method(#createFormData, [filePath, imageTitle]),
      returnValue: Future.value(FormData()),
      returnValueForMissingStub: Future.value(FormData()),
    );
  }
}

@GenerateNiceMocks([MockSpec<File>()])
void main() {
  late UserDataSource dataSource;
  late MockDio mockDio;
  late MockFile mockFile;
  late MockFormData mockFormDataHelper;
  late final Directory directory;

  setUpAll(() async {
    mockDio = MockDio();
    mockFile = MockFile();
    await testEnvSetup();

    final tempDir = await getTemporaryDirectory();

    directory = Directory(tempDir.path);
    mockFormDataHelper = MockFormData();
    dataSource = UserDataSource(dio: mockDio, formDataHelper: MockFormData());
  });

  tearDownAll(() async {
    if (directory.existsSync()) await directory.delete(recursive: true);
    await testEnvTearDown();
  });

  group('get user avatar', () {
    test('should perform a GET request', () async {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 200,
          data: 'image_url',
        ),
      );

      await dataSource.getUserAvatar();

      verify(mockDio.get(ApiConstants.userAvatarPath)).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.getUserAvatar(), throwsA(isA<DioException>()));
    });
  });

  group('get user data', () {
    test('should perform a GET request', () async {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userPath),
          statusCode: 200,
          data: tUserDataJson,
        ),
      );

      await dataSource.getUserData();

      verify(mockDio.get(ApiConstants.userPath)).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any, queryParameters: anyNamed('queryParameters'))).thenAnswer(
        (_) async =>
            Response(requestOptions: RequestOptions(path: ApiConstants.userPath), statusCode: 404),
      );

      expect(() => dataSource.getUserData(), throwsA(isA<DioException>()));
    });
  });

  group('upload user avatar', () {
    final tMemoryImage = MemoryImage(Uint8List(10));

    setUpAll(() async {
      final tempDir = await getTemporaryDirectory();

      const imageTitle = 'profile.png';
      when(mockFile.path).thenReturn('${tempDir.path}/$imageTitle');
      when(mockFile.writeAsBytes(tMemoryImage.bytes)).thenAnswer((_) async => mockFile);
      when(mockDio.options).thenReturn(BaseOptions(baseUrl: 'https://example.com'));

      when(mockFormDataHelper.createFormData(mockFile.path, imageTitle)).thenAnswer(
        (_) async => FormData.fromMap({
          Constants.file: await MultipartFile.fromFile(mockFile.path, filename: imageTitle),
        }),
      );
    });

    test('should perform a POST request', () async {
      when(mockDio.post(any, data: anyNamed('data'), options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 200,
          data: 'image_url',
        ),
      );

      if (!directory.existsSync()) {
        await directory.create(recursive: true);
      }

      await dataSource.uploadUserAvatar(tMemoryImage);

      verify(
        mockDio.post(
          '${ApiConstants.userAvatarPath}?${Constants.profileType}=${Constants.TRAINEE}',
          data: anyNamed('data'),
          options: anyNamed('options'),
        ),
      ).called(1);
      // Clean up after test completion
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.post(any, data: anyNamed('data'), options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.uploadUserAvatar(tMemoryImage), throwsA(isA<DioException>()));
    });

    test('should perform a DELETE request', () async {
      when(mockDio.delete(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 200,
        ),
      );

      await dataSource.deleteUserAvatar();

      verify(mockDio.delete(ApiConstants.userAvatarPath)).called(1);
    });
    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.delete(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.userAvatarPath),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.deleteUserAvatar(), throwsA(isA<DioException>()));
    });
  });

  test('should call createFormData', () async {
    const imageTitle = 'image_title.jpg';
    final formData = FormData.fromMap({
      Constants.file: MultipartFile.fromBytes(Uint8List(10), filename: imageTitle),
    });
    final formDataHelper = FormDataHelper();
    final tempDir = await getTemporaryDirectory();
    final tMemoryImage = MemoryImage(Uint8List(10));

    final directory = Directory(tempDir.path);
    if (!directory.existsSync()) {
      await directory.create(recursive: true);
    }
    final filePath = '${tempDir.path}/$imageTitle';
    final file = await File(filePath).writeAsBytes(tMemoryImage.bytes);
    final result = await formDataHelper.createFormData(file.path, imageTitle);

    expect(result.fields, formData.fields);
    // Clean up after test completion
    await directory.delete(recursive: true);
  });
}
