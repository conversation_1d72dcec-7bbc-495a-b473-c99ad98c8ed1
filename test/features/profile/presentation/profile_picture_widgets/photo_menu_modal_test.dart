import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/delete_photo_modal.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/profile_photo_menu_modal.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../auth/login_page/login_page_test.mocks.dart';
import '../../../home_page/presentation/pages/home_page_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockUserBloc = MockUserBloc();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(MockAuthBloc.new);

    await testEnvSetup();
    whenListen(
      mockUserBloc,
      Stream<UserState>.value(const UserState()),
      initialState: const UserState(userAvatar: 'img'),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden logout bottom sheet', () {
    testGoldens('logout bottom sheet', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: BlocProvider<UserBloc>(
            create: (context) => mockUserBloc,
            child: BottomSheet(
              backgroundColor: Colors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: SharedDecoration.borderTopLeftRight10,
              ),
              builder: (context) => const ProfilePhotoMenuModal(),
              onClosing: () {},
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'logout_bottom_sheet');
    });

    testGoldens('delete photo bottom sheet', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: BlocProvider<UserBloc>(
            create: (context) => mockUserBloc,
            child: BottomSheet(
              backgroundColor: Colors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: SharedDecoration.borderTopLeftRight10,
              ),
              builder: (context) => const DeletePhotoModal(),
              onClosing: () {},
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'delete_photo_modal');
    });
  });
}
