import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/profile_picture/image_crop_view.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../auth/login_page/login_page_test.mocks.dart';
import '../../../home_page/presentation/pages/home_page_test.dart';
import '../../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final mockUserBloc = MockUserBloc();
  final mockGoRouter = MockGoRouter();

  setUpAll(() async {
    GetIt.instance
      ..registerFactory<AuthBloc>(MockAuthBloc.new)
      ..registerFactory<UserBloc>(() => mockUserBloc)
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerSingleton<GoRouter>(mockGoRouter);
    await testEnvSetup();

    whenListen(
      mockUserBloc,
      Stream<UserState>.value(
        const UserState(isUserDataLoading: false, isUserAvatarLoading: false),
      ),
      initialState: const UserState(isUserAvatarLoading: false, isCropInProgress: true),
    );
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('image crop view', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const ImageCropView()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle(const Duration(seconds: 10));
      await multiScreenGolden(
        tester,
        'image_crop_view',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
