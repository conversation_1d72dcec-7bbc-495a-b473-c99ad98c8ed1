import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/personal_info_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/login_page/login_page_test.mocks.dart';
import '../../home_page/presentation/pages/home_page_test.dart';
import '../profile_test_seed.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final mockUserBloc = MockUserBloc();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(MockAuthBloc.new);
    GetIt.instance.registerFactory<UserBloc>(() => mockUserBloc);
    GetIt.instance.registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider);
    when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => 'token');

    whenListen(
      mockUserBloc,
      Stream<UserState>.value(
        UserState(
          isUserDataLoading: false,
          userData: UserModel.fromJson(tUserDataJson),
          isUserAvatarLoading: false,
          userAvatar: '',
        ),
      ),
      initialState: const UserState(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('personal info page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const PersonalInfoPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'personal_info_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });

  group('Golden Tests', () {
    testGoldens('personal info page error', (tester) async {
      whenListen(
        mockUserBloc,
        Stream<UserState>.value(
          UserState(
            isUserDataLoading: false,
            userDataError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
            userAvatar: '',
            isUserAvatarLoading: false,
          ),
        ),
        initialState: const UserState(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const PersonalInfoPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'personal_info_page_error', devices: [Device.iphone11]);
      dismissAllToast();
    });
  });
}
