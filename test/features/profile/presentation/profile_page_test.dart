import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/pages/profile_page.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/login_page/login_page_test.mocks.dart';
import '../../home_page/presentation/pages/home_page_test.dart';
import '../profile_test_seed.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final mockUserBloc = MockUserBloc();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(MockAuthBloc.new);
    GetIt.instance.registerFactory<UserBloc>(() => mockUserBloc);
    GetIt.instance.registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider);

    whenListen(
      mockUserBloc,
      Stream<UserState>.value(
        UserState(
          isUserDataLoading: false,
          userData: UserModel.fromJson(tUserDataJson),
          isUserAvatarLoading: false,
          userAvatar: '',
        ),
      ),
      initialState: const UserState(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('profile page', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(true));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const ProfilePage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'profile_page_authenticated',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });

    testGoldens('profile page', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(false));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const ProfilePage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'profile_page_non_authenticated',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
