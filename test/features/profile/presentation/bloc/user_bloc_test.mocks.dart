// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in national_skills_platform/test/features/profile/presentation/bloc/user_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:flutter/material.dart' as _i7;
import 'package:image_picker/image_picker.dart' as _i8;
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart' as _i2;
import 'package:national_skills_platform/features/profile_page/domain/repository/user_repository.dart'
    as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserModel_0 extends _i1.SmartFake implements _i2.UserModel {
  _FakeUserModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLostDataResponse_1 extends _i1.SmartFake implements _i3.LostDataResponse {
  _FakeLostDataResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i4.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.UserModel> getUserData() => (super.noSuchMethod(
        Invocation.method(
          #getUserData,
          [],
        ),
        returnValue: _i5.Future<_i2.UserModel>.value(_FakeUserModel_0(
          this,
          Invocation.method(
            #getUserData,
            [],
          ),
        )),
      ) as _i5.Future<_i2.UserModel>);

  @override
  _i5.Future<String> getUserAvatar() => (super.noSuchMethod(
        Invocation.method(
          #getUserAvatar,
          [],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #getUserAvatar,
            [],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<String> uploadUserAvatar(_i7.MemoryImage? image) => (super.noSuchMethod(
        Invocation.method(
          #uploadUserAvatar,
          [image],
        ),
        returnValue: _i5.Future<String>.value(_i6.dummyValue<String>(
          this,
          Invocation.method(
            #uploadUserAvatar,
            [image],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<void> deleteUserAvatar() => (super.noSuchMethod(
        Invocation.method(
          #deleteUserAvatar,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [ImagePicker].
///
/// See the documentation for Mockito's code generation for more information.
class MockImagePicker extends _i1.Mock implements _i8.ImagePicker {
  @override
  _i5.Future<_i3.XFile?> pickImage({
    required _i3.ImageSource? source,
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    _i3.CameraDevice? preferredCameraDevice = _i3.CameraDevice.rear,
    bool? requestFullMetadata = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pickImage,
          [],
          {
            #source: source,
            #maxWidth: maxWidth,
            #maxHeight: maxHeight,
            #imageQuality: imageQuality,
            #preferredCameraDevice: preferredCameraDevice,
            #requestFullMetadata: requestFullMetadata,
          },
        ),
        returnValue: _i5.Future<_i3.XFile?>.value(),
        returnValueForMissingStub: _i5.Future<_i3.XFile?>.value(),
      ) as _i5.Future<_i3.XFile?>);

  @override
  _i5.Future<List<_i3.XFile>> pickMultiImage({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    int? limit,
    bool? requestFullMetadata = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pickMultiImage,
          [],
          {
            #maxWidth: maxWidth,
            #maxHeight: maxHeight,
            #imageQuality: imageQuality,
            #limit: limit,
            #requestFullMetadata: requestFullMetadata,
          },
        ),
        returnValue: _i5.Future<List<_i3.XFile>>.value(<_i3.XFile>[]),
        returnValueForMissingStub: _i5.Future<List<_i3.XFile>>.value(<_i3.XFile>[]),
      ) as _i5.Future<List<_i3.XFile>>);

  @override
  _i5.Future<_i3.XFile?> pickMedia({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    bool? requestFullMetadata = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pickMedia,
          [],
          {
            #maxWidth: maxWidth,
            #maxHeight: maxHeight,
            #imageQuality: imageQuality,
            #requestFullMetadata: requestFullMetadata,
          },
        ),
        returnValue: _i5.Future<_i3.XFile?>.value(),
        returnValueForMissingStub: _i5.Future<_i3.XFile?>.value(),
      ) as _i5.Future<_i3.XFile?>);

  @override
  _i5.Future<List<_i3.XFile>> pickMultipleMedia({
    double? maxWidth,
    double? maxHeight,
    int? imageQuality,
    int? limit,
    bool? requestFullMetadata = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pickMultipleMedia,
          [],
          {
            #maxWidth: maxWidth,
            #maxHeight: maxHeight,
            #imageQuality: imageQuality,
            #limit: limit,
            #requestFullMetadata: requestFullMetadata,
          },
        ),
        returnValue: _i5.Future<List<_i3.XFile>>.value(<_i3.XFile>[]),
        returnValueForMissingStub: _i5.Future<List<_i3.XFile>>.value(<_i3.XFile>[]),
      ) as _i5.Future<List<_i3.XFile>>);

  @override
  _i5.Future<_i3.XFile?> pickVideo({
    required _i3.ImageSource? source,
    _i3.CameraDevice? preferredCameraDevice = _i3.CameraDevice.rear,
    Duration? maxDuration,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #pickVideo,
          [],
          {
            #source: source,
            #preferredCameraDevice: preferredCameraDevice,
            #maxDuration: maxDuration,
          },
        ),
        returnValue: _i5.Future<_i3.XFile?>.value(),
        returnValueForMissingStub: _i5.Future<_i3.XFile?>.value(),
      ) as _i5.Future<_i3.XFile?>);

  @override
  _i5.Future<_i3.LostDataResponse> retrieveLostData() => (super.noSuchMethod(
        Invocation.method(
          #retrieveLostData,
          [],
        ),
        returnValue: _i5.Future<_i3.LostDataResponse>.value(_FakeLostDataResponse_1(
          this,
          Invocation.method(
            #retrieveLostData,
            [],
          ),
        )),
        returnValueForMissingStub: _i5.Future<_i3.LostDataResponse>.value(_FakeLostDataResponse_1(
          this,
          Invocation.method(
            #retrieveLostData,
            [],
          ),
        )),
      ) as _i5.Future<_i3.LostDataResponse>);

  @override
  bool supportsImageSource(_i3.ImageSource? source) => (super.noSuchMethod(
        Invocation.method(
          #supportsImageSource,
          [source],
        ),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);
}
