import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/domain/repository/user_repository.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../../profile_test_seed.dart';
import 'user_bloc_test.mocks.dart';

@GenerateMocks([UserRepository])
@GenerateNiceMocks([MockSpec<ImagePicker>()])
void main() {
  final MockUserRepository mockUserRepository = MockUserRepository();
  final mockGoRouter = MockGoRouter();
  final tMemoryImage = MemoryImage(Uint8List(10));

  setUpAll(() async {
    GetIt.instance.registerSingleton<GoRouter>(mockGoRouter);
    await testEnvSetup();
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  const userAvatar = 'avatar_url';
  final userData = UserModel.fromJson(tUserDataJson);

  blocTest<UserBloc, UserState>(
    'emits correct states when getUserAvatar and getUserData succeed',
    build: () {
      when(mockUserRepository.getUserData()).thenAnswer((_) async => userData);
      when(mockUserRepository.getUserAvatar()).thenAnswer((_) async => userAvatar);
      return UserBloc(userRepository: mockUserRepository);
    },
    act: (bloc) => bloc.add(const UserEvent.getUserData()),
    expect: () => [
      const UserState(),
      const UserState(isUserAvatarLoading: false, userAvatar: userAvatar),
      UserState(
        isUserAvatarLoading: false,
        userAvatar: userAvatar,
        isUserDataLoading: false,
        userData: userData,
      ),
    ],
  );
  blocTest<UserBloc, UserState>(
    'emits correct states when UpdateProfileImage event is added and uploadUserAvatar succeeds',
    build: () {
      when(mockUserRepository.uploadUserAvatar(any)).thenAnswer((_) async => userAvatar);
      return UserBloc(userRepository: mockUserRepository);
    },
    act: (bloc) {
      bloc.add(UserEvent.updateProfileImage(croppedImage: tMemoryImage));
    },
    expect: () => [
      const UserState(),
      const UserState(userAvatar: userAvatar, isUserAvatarLoading: false),
    ],
  );

  blocTest<UserBloc, UserState>(
    'emits correct states when UpdateProfileImage event is added and uploadUserAvatar fails',
    build: () {
      when(mockUserRepository.uploadUserAvatar(any)).thenAnswer(
        (_) async => throw Exception(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
      );
      return UserBloc(userRepository: mockUserRepository);
    },
    act: (bloc) {
      final image = MemoryImage(Uint8List(10));
      bloc.add(UserEvent.updateProfileImage(croppedImage: image));
    },
    expect: () => [
      const UserState(),
      UserState(
        isUserAvatarLoading: false,
        userAvatarError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<UserBloc, UserState>(
    'emits correct states when both getUserAvatar and getUserData fail',
    build: () {
      when(mockUserRepository.getUserAvatar()).thenAnswer(
        (_) async => throw Exception(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
      );
      when(mockUserRepository.getUserData()).thenAnswer(
        (_) async => throw Exception(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
      );
      return UserBloc(userRepository: mockUserRepository);
    },
    act: (bloc) => bloc.add(const UserEvent.getUserData()),
    expect: () => [
      const UserState(),
      UserState(
        isUserAvatarLoading: false,
        userAvatarError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
      UserState(
        isUserAvatarLoading: false,
        isUserDataLoading: false,
        userAvatarError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        userDataError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<UserBloc, UserState>(
    'emits correct states when ResetUserData event is added',
    build: () => UserBloc(userRepository: mockUserRepository),
    act: (bloc) => bloc.add(const UserEvent.resetUserData()),
    expect: () => [const UserState()],
  );

  //test for UserEvent.cropInProgress
  blocTest<UserBloc, UserState>(
    'emits correct states when CropInProgress event is added',
    build: () => UserBloc(userRepository: mockUserRepository),
    act: (bloc) => bloc.add(const UserEvent.cropInProgress(isCropInProgress: true)),
    expect: () => [const UserState(isCropInProgress: true)],
  );

  //test for UserEvent.deleteProfileImage
  blocTest<UserBloc, UserState>(
    'emits correct states when DeleteProfileImage event is added',
    build: () => UserBloc(userRepository: mockUserRepository),
    act: (bloc) => bloc.add(const UserEvent.deleteProfileImage()),
    seed: () => const UserState(userAvatar: 'userAvatar.png', isUserAvatarLoading: false),
    expect: () => [
      const UserState(userAvatar: 'userAvatar.png'),
      const UserState(isUserAvatarLoading: false),
    ],
  );

  blocTest<UserBloc, UserState>(
    'emits correct states when DeleteProfileImage event is added and deleteProfileImage fails',
    build: () {
      when(mockUserRepository.deleteUserAvatar()).thenAnswer(
        (_) async => throw Exception(LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
      );
      return UserBloc(userRepository: mockUserRepository);
    },
    seed: () => const UserState(userAvatar: 'userAvatar.png', isUserAvatarLoading: false),
    act: (bloc) => bloc.add(const UserEvent.deleteProfileImage()),
    expect: () => [
      const UserState(userAvatar: 'userAvatar.png'),
      UserState(
        userAvatar: 'userAvatar.png',
        isUserAvatarLoading: false,
        userAvatarError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );
}
