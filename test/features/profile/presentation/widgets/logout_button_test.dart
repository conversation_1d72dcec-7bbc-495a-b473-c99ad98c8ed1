import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/logout_button.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../auth/login_page/login_page_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthBloc = MockAuthBloc();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(() => mockAuthBloc);
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('logout button', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Scaffold(
            body: BlocProvider<AuthBloc>(
              create: (context) => mockAuthBloc,
              child: Builder(
                builder: (context) {
                  return const LogoutButton();
                },
              ),
            ),
          ),
        ),
        surfaceSize: const Size(120, 50),
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'logout_button');

      await tester.tap(find.text(LocaleKeys.header_logOut.tr()));
    });
  });
}
