import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/core/theme/app_colors.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/widgets/logout_bottom_sheet.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/features/shared/ui_components/ui_components.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../auth/login_page/login_page_test.mocks.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(MockAuthBloc.new);

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden logout bottom sheet', () {
    testGoldens('logout bottom sheet', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: ColoredBox(
            color: AppColors.uiBackgroundPrimary,
            child: BottomSheet(
              backgroundColor: Colors.white,
              shape: const RoundedRectangleBorder(
                borderRadius: SharedDecoration.borderTopLeftRight10,
              ),
              builder: (context) => const LogoutBottomSheet(),
              onClosing: () {},
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'logout_bottom_sheet');
    });
  });
}
