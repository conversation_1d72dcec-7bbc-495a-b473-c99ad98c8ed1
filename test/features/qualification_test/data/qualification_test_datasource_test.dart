import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../training_consumption/training_consumption_seed.dart';

void main() {
  late QualificationTestDatasource dataSource;
  late MockDio mockDio;
  final tPreQualificationTest = PreQualificationTest.fromJson(tPreQualificationTestJson);
  final tPostQualificationTest = PostQualificationTest.fromJson(tPostQualificationTestJson);

  setUp(() {
    mockDio = MockDio();
    dataSource = QualificationTestDatasource(dio: mockDio);
  });

  group('submitAnswer', () {
    const tTrainingId = 'trainingId';
    const tTestId = 'testId';
    const tAnswerModel = ApplicantSavedAnswer(questionId: 'questionId', answerId: 'answerId');

    test('should perform a PUT request and return PreQualificationTest', () async {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.qualificationTestAnswerSubmitPath +
                tTestId,
          ),
          statusCode: 200,
          data: tPreQualificationTestJson,
        ),
      );

      final result = await dataSource.submitAnswer(
        tTrainingId,
        tTestId,
        tAnswerModel,
        QualificationTestType.PRE,
      );

      verify(
        mockDio.put(
          ApiConstants.applicantsTrainings +
              tTrainingId +
              ApiConstants.qualificationTestAnswerSubmitPath +
              tTestId,
          data: [tAnswerModel.toJson()],
        ),
      ).called(1);
      expect(result, equals(tPreQualificationTest));
    });

    test('should perform a PUT request and return PostQualificationTest', () async {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.qualificationTestAnswerSubmitPath +
                tTestId,
          ),
          statusCode: 200,
          data: tPostQualificationTestJson,
        ),
      );

      final result = await dataSource.submitAnswer(
        tTrainingId,
        tTestId,
        tAnswerModel,
        QualificationTestType.POST,
      );

      verify(
        mockDio.put(
          ApiConstants.applicantsTrainings +
              tTrainingId +
              ApiConstants.qualificationTestAnswerSubmitPath +
              tTestId,
          data: [tAnswerModel.toJson()],
        ),
      ).called(1);
      expect(result, equals(tPostQualificationTest));
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.qualificationTestAnswerSubmitPath +
                tTestId,
          ),
          statusCode: 404,
        ),
      );

      expect(
        () =>
            dataSource.submitAnswer(tTrainingId, tTestId, tAnswerModel, QualificationTestType.PRE),
        throwsA(isA<DioException>()),
      );
    });
  });

  group('retakeTest', () {
    const tTrainingId = 'trainingId';
    test('should perform a POST request and return PreQualificationTest', () async {
      when(mockDio.post(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.retakeQualificationTestPath +
                QualificationTestType.PRE.name,
          ),
          statusCode: 200,
          data: tTrainingConsumptionDetailsJson,
        ),
      );

      final result = await dataSource.retakeTest(tTrainingId, QualificationTestType.PRE);

      verify(
        mockDio.post(
          ApiConstants.applicantsTrainings +
              tTrainingId +
              ApiConstants.retakeQualificationTestPath +
              QualificationTestType.PRE.name,
        ),
      ).called(1);
      expect(result, equals(tPreQualificationTest));
    });

    test('should perform a POST request and return PostQualificationTest', () async {
      when(mockDio.post(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.retakeQualificationTestPath +
                QualificationTestType.POST.name,
          ),
          statusCode: 200,
          data: tTrainingConsumptionDetailsJson,
        ),
      );

      final result = await dataSource.retakeTest(tTrainingId, QualificationTestType.POST);

      verify(
        mockDio.post(
          ApiConstants.applicantsTrainings +
              tTrainingId +
              ApiConstants.retakeQualificationTestPath +
              QualificationTestType.POST.name,
        ),
      ).called(1);
      expect(result, equals(tPostQualificationTest));
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.post(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.retakeQualificationTestPath +
                QualificationTestType.PRE.name,
          ),
          statusCode: 404,
        ),
      );

      expect(
        () => dataSource.retakeTest(tTrainingId, QualificationTestType.PRE),
        throwsA(isA<DioException>()),
      );
    });
  });
}
