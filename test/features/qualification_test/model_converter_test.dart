import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/domain/converters/model_converter.dart';

void main() {
  group('convertAnswerToApplicantSavedAnswer', () {
    test('should return null if answer is null', () {
      final result = convertAnswerToApplicantSavedAnswer(null, 'questionId', 'question');
      expect(result, isNull);
    });

    test('should return null if questionId is null', () {
      const answer = Answer(id: 'answerId', answer: '', correct: true, index: 0);
      final result = convertAnswerToApplicantSavedAnswer(answer, null, 'question');
      expect(result, isNull);
    });

    test('should return ApplicantSavedAnswer if answer and questionId are not null', () {
      const answer = Answer(id: 'answerId', answer: '', correct: true, index: 0);
      final result = convertAnswerToApplicantSavedAnswer(answer, 'questionId', 'question');
      expect(result, isNotNull);
      expect(result!.questionId, 'questionId');
      expect(result.question, 'question');
      expect(result.answerId, 'answerId');
    });
  });
}
