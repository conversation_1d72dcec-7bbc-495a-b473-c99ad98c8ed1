import 'dart:math';
import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/domain/repositories/qualification_test_repository.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../training_consumption/training_consumption_seed.dart';
import 'qualification_test_bloc_test.mocks.dart';

@GenerateMocks([QualificationTestRepository])
void main() {
  late MockQualificationTestRepository mockRepository;
  late QualificationTestBloc bloc;
  const tTrainingId = 'trainingId';

  setUp(() {
    mockRepository = MockQualificationTestRepository();
    bloc = QualificationTestBloc(qualificationTestRepository: mockRepository, random: Random(42));
  });

  group('Pre-QualificationTestBloc', () {
    final tPreQualificationTest = PreQualificationTest.fromJson(tPreQualificationTestJson);

    final tAnswer = tPreQualificationTest.questions.first.answers.first;
    final questionId = tPreQualificationTest.questions.first.id;
    final applicantSavedAnswer = ApplicantSavedAnswer(answerId: tAnswer.id, questionId: questionId);
    final tUpdatedQualificationTest = tPreQualificationTest.copyWith(
      applicantSavedAnswers: {
        questionId: ApplicantSavedAnswer(questionId: questionId, answerId: tAnswer.id),
      },
    );

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] when LoadQualificationTestEvent is added',
      build: () => bloc,
      act: (bloc) => bloc.add(LoadQualificationTestEvent(tPreQualificationTest, tTrainingId)),
      expect: () {
        final answeredQuestions = tPreQualificationTest.questions
            .where(
              (question) => tPreQualificationTest.applicantSavedAnswers.keys.contains(question.id),
            )
            .toList();
        final unansweredQuestions = tPreQualificationTest.questions
            .where(
              (question) => !tPreQualificationTest.applicantSavedAnswers.keys.contains(question.id),
            )
            .toList();

        final finalQuestions = [
          ...answeredQuestions,
          ...tPreQualificationTest.randomized == true
              ? bloc.shuffleQuestions(unansweredQuestions, Random(42))
              : unansweredQuestions,
        ];

        // Create expected question order map
        final expectedQuestionOrderMap = <String, int>{};
        for (int i = 0; i < finalQuestions.length; i++) {
          expectedQuestionOrderMap[finalQuestions[i].id] = i;
        }

        return [
          QualificationTestState(
            qualificationTest: tPreQualificationTest.copyWith(questions: finalQuestions),
            selectedAnswers: Map.from(tPreQualificationTest.applicantSavedAnswers),
            trainingId: tTrainingId,
            currentQuestionIndex: tPreQualificationTest.applicantSavedAnswers.length,
            questionOrderMap: expectedQuestionOrderMap,
          ),
        ];
      },
    );

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] with updated selectedAnswers when SelectAnswerEvent is added',
      build: () => bloc,
      act: (bloc) => bloc.add(SelectAnswerEvent(applicantSavedAnswer)),
      seed: () => QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {},
        trainingId: tTrainingId,
        questionOrderMap: {questionId: 0},
        testStarted: true,
      ),
      expect: () => [
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {},
          trainingId: tTrainingId,
          questionOrderMap: {questionId: 0},
          updatingAnswerSelection: true,
          testStarted: true,
        ),
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {questionId: applicantSavedAnswer},
          trainingId: tTrainingId,
          questionOrderMap: {questionId: 0},
          testStarted: true,
        ),
      ],
    );

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] with updated qualificationTest when SubmitAnswerEvent is added',
      build: () {
        when(
          mockRepository.submitAnswer(any, any, any, any),
        ).thenAnswer((_) async => tUpdatedQualificationTest);
        return bloc;
      },
      seed: () => QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {questionId: applicantSavedAnswer},
        trainingId: tTrainingId,
        questionOrderMap: {questionId: 0},
        testStarted: true,
      ),
      act: (bloc) => bloc.add(const SubmitAnswerEvent()),
      expect: () => [
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {questionId: applicantSavedAnswer},
          trainingId: tTrainingId,
          questionOrderMap: {questionId: 0},
          showLoadingIndicator: true,
          testStarted: true,
        ),
        QualificationTestState(
          qualificationTest: tUpdatedQualificationTest,
          selectedAnswers: {questionId: applicantSavedAnswer},
          trainingId: tTrainingId,
          questionOrderMap: {questionId: 0},
          currentQuestionIndex: 1,
          testStarted: true,
        ),
      ],
    );

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] with reset state when RetakeQualificationTestEvent is added',
      build: () {
        when(mockRepository.retakeTest(any, any)).thenAnswer((_) async => tPreQualificationTest);
        return bloc;
      },
      seed: () => QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {},
        trainingId: tTrainingId,
        questionOrderMap: {},
        testStarted: true,
      ),
      act: (bloc) =>
          bloc.add(const RetakeQualificationTestEvent(tTrainingId, QualificationTestType.PRE)),
      expect: () => [
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {},
          trainingId: tTrainingId,
          questionOrderMap: {},
          showLoadingIndicator: true,
          testStarted: true,
        ),
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {},
          trainingId: tTrainingId,
          questionOrderMap: {},
        ),
      ],
    );

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] with error message when RetakeQualificationTestEvent is added',
      build: () {
        when(mockRepository.retakeTest(any, any)).thenAnswer((_) async => throw Exception());
        return bloc;
      },
      seed: () => QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {},
        trainingId: tTrainingId,
        questionOrderMap: {},
        testStarted: true,
      ),
      act: (bloc) =>
          bloc.add(const RetakeQualificationTestEvent(tTrainingId, QualificationTestType.PRE)),
      expect: () => [
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {},
          trainingId: tTrainingId,
          questionOrderMap: {},
          showLoadingIndicator: true,
          testStarted: true,
        ),
        QualificationTestState(
          qualificationTest: tPreQualificationTest,
          selectedAnswers: {},
          trainingId: tTrainingId,
          questionOrderMap: {},
          errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );
  });

  group('Post-QualificationTestBloc', () {
    final tPostQualificationTest = PostQualificationTest.fromJson(tPostQualificationTestJson);

    blocTest<QualificationTestBloc, QualificationTestState>(
      'emits [QualificationTestState] when LoadPostQualificationTestEvent is added',
      build: () => bloc,
      seed: () => QualificationTestState(
        qualificationTest: tPostQualificationTest,
        selectedAnswers: {},
        trainingId: tTrainingId,
        questionOrderMap: {},
      ),
      act: (bloc) => bloc.add(LoadQualificationTestEvent(tPostQualificationTest, tTrainingId)),
      expect: () {
        final answeredQuestions = tPostQualificationTest.questions
            .where(
              (question) => tPostQualificationTest.applicantSavedAnswers.keys.contains(question.id),
            )
            .toList();
        final unansweredQuestions = tPostQualificationTest.questions
            .where(
              (question) =>
                  !tPostQualificationTest.applicantSavedAnswers.keys.contains(question.id),
            )
            .toList();

        final finalQuestions = [
          ...answeredQuestions,
          ...tPostQualificationTest.randomized == true
              ? bloc.shuffleQuestions(unansweredQuestions, Random(42))
              : unansweredQuestions,
        ];

        // Create expected question order map
        final expectedQuestionOrderMap = <String, int>{};
        for (int i = 0; i < finalQuestions.length; i++) {
          expectedQuestionOrderMap[finalQuestions[i].id] = i;
        }

        return [
          QualificationTestState(
            qualificationTest: tPostQualificationTest.copyWith(questions: finalQuestions),
            selectedAnswers: Map.from(tPostQualificationTest.applicantSavedAnswers),
            trainingId: tTrainingId,
            currentQuestionIndex: tPostQualificationTest.applicantSavedAnswers.length,
            questionOrderMap: expectedQuestionOrderMap,
          ),
        ];
      },
    );
  });
}
