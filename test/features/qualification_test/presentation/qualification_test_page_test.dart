import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/pages/qualification_test_page.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../training_consumption/presentation/pages/training_consumption_page_test.dart';
import '../../training_consumption/training_consumption_seed.dart';

class MockQualificationTestBloc extends MockBloc<QualificationTestEvent, QualificationTestState>
    implements QualificationTestBloc {}

void main() {
  late MockQualificationTestBloc mockQualificationTestBloc;
  late TrainingConsumptionBloc mockTrainingConsumptionBloc;
  final tQualificationTest = PreQualificationTest.fromJson(tPreQualificationTestJson);
  const tTrainingId = 'trainingId';

  setUpAll(() async {
    mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();
    mockQualificationTestBloc = MockQualificationTestBloc();

    GetIt.instance
      ..registerFactory<QualificationTestBloc>(() => mockQualificationTestBloc)
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc);
    await testEnvSetup();

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('QualificationTestPage', () {
    testGoldens('qualification test page', (tester) async {
      whenListen(
        mockQualificationTestBloc,
        Stream<QualificationTestState>.value(
          QualificationTestState(
            qualificationTest: tQualificationTest,
            selectedAnswers: {},
            trainingId: tTrainingId,
            testStarted: true,
          ),
        ),
        initialState: QualificationTestState(selectedAnswers: {}, trainingId: ''),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MultiBlocProvider(
            providers: [
              BlocProvider.value(value: mockTrainingConsumptionBloc),
              BlocProvider.value(value: mockQualificationTestBloc),
            ],
            child: QualificationTestPage(tQualificationTest, tTrainingId),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'qualification_test_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });

    testGoldens('qualification results page', (tester) async {
      whenListen(
        mockQualificationTestBloc,
        Stream<QualificationTestState>.value(
          QualificationTestState(
            qualificationTest: tQualificationTest,
            currentQuestionIndex: 10,
            selectedAnswers: {},
            trainingId: '',
            testStarted: true,
          ),
        ),
        initialState: QualificationTestState(selectedAnswers: {}, trainingId: ''),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MultiBlocProvider(
            providers: [
              BlocProvider.value(value: mockTrainingConsumptionBloc),
              BlocProvider.value(value: mockQualificationTestBloc),
            ],
            child: QualificationTestPage(tQualificationTest, tTrainingId),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'qualification_results_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
