import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/bloc/qualification_test_bloc.dart';
import 'package:national_skills_platform/features/qualification_test/presentation/widgets/qualification_test_bottom_navigation.dart';
import 'package:national_skills_platform/features/shared/transitions/multi_transition.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_button.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../training_consumption/mixin/unified_navigation_mixin_test.dart';
import '../qualification_test_page_test.dart';

class MockGoRouter extends Mock implements GoRouter {}

List<Question> tQuestions = [
  const Question(id: '1', question: 'Question 1', answers: [], questionType: 'single', index: 1),
  const Question(id: '2', question: 'Question 2', answers: [], questionType: 'single', index: 2),
];

PreQualificationTest tPreQualificationTest = PreQualificationTest(
  id: '1',
  type: QualificationTestType.PRE,
  questions: tQuestions,
  applicantAnswers: [],
  applicantSavedAnswers: {},
  description: 'Test description',
  firstAttempt: true,
  mandatory: true,
  minimumScore: 70,
  randomized: false,
  timeLimit: 30,
  title: 'Pre Test',
);

void main() {
  late MockQualificationTestBloc mockQualificationTestBloc;
  late MockTrainingConsumptionBloc mockTrainingConsumptionBloc;
  late MockGoRouter mockGoRouter;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    await testEnvSetup();
    mockGoRouter = MockGoRouter();
    GetIt.instance.registerSingleton<GoRouter>(mockGoRouter);

    mockQualificationTestBloc = MockQualificationTestBloc();
    mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();

    registerFallbackValue(
      const UpdatePostQualificationTestEvent(
        PostQualificationTest(
          id: '1',
          type: QualificationTestType.POST,
          questions: [],
          applicantAnswers: [],
          applicantSavedAnswers: {},
          description: '',
          firstAttempt: true,
          mandatory: true,
          minimumScore: 70,
          randomized: false,
          timeLimit: 30,
          title: 'Post Test',
        ),
      ),
    );

    registerFallbackValue(
      OpenLessonEvent(
        LessonParams(
          lesson: const Lesson(
            id: 'lesson1',
            title: 'Test Lesson',
            index: 0,
            lessonType: LessonType.Article,
            text: 'Test lesson content',
            resources: [],
          ),
          section: const Section(id: 'section1', title: 'Test Section', index: 0, lessons: []),
          completedLessonsInSection: 0,
          isCompleted: true,
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
          lessonNavigationType: LessonNavigationType.backward,
        ),
      ),
    );
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  Widget buildTestWidget({required QualificationTestState state}) {
    when(() => mockQualificationTestBloc.state).thenReturn(state);

    return MultiBlocProvider(
      providers: [
        BlocProvider<QualificationTestBloc>.value(value: mockQualificationTestBloc),
        BlocProvider<TrainingConsumptionBloc>.value(value: mockTrainingConsumptionBloc),
      ],
      child: nspTestWrapper(
        child: QualificationTestBottomNavigation(
          trainingConsumptionBloc: mockTrainingConsumptionBloc,
        ),
      ),
    );
  }

  group('QualificationTestBottomNavigation', () {
    testWidgets('should show submit button in disabled state when no answer selected', (
      tester,
    ) async {
      final state = QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {},
        trainingId: '123',
        testStarted: true,
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      final button = find.byType(AppButton);
      expect(button, findsOneWidget);

      final buttonWidget = tester.widget<AppButton>(button);
      expect(buttonWidget.backgroundColor, equals(AppColors.greenAccentSecondary));
    });

    testWidgets('should show submit button in enabled state when answer is selected', (
      tester,
    ) async {
      final state = QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {
          '1': const ApplicantSavedAnswer(questionId: '1', question: 'Question 1', answerId: '1'),
        },
        trainingId: '123',
        testStarted: true,
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      final button = find.byType(AppButton);
      expect(button, findsOneWidget);

      final buttonWidget = tester.widget<AppButton>(button);
      expect(buttonWidget.backgroundColor, equals(AppColors.greenAccentPrimary));
    });

    testWidgets('should show back button when not on first question', (tester) async {
      final state = QualificationTestState(
        qualificationTest: tPreQualificationTest,
        currentQuestionIndex: 1,
        selectedAnswers: {},
        trainingId: '123',
        testStarted: true,
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      final button = find.byWidgetPredicate(
        (widget) => widget is AppButton && widget.buttonText == LocaleKeys.questions_goBack.tr(),
      );
      expect(button, findsOneWidget);
    });

    testWidgets('should not show back button on first question', (tester) async {
      final state = QualificationTestState(
        qualificationTest: tPreQualificationTest,
        selectedAnswers: {},
        trainingId: '123',
        testStarted: true,
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      final button = find.byWidgetPredicate(
        (widget) => widget is AppButton && widget.buttonText == LocaleKeys.questions_goBack.tr(),
      );
      expect(button, findsNothing);
    });

    testWidgets('should close test page when Done button is tapped on failed PRE test',
        (tester) async {
      // Create a failed pre-test (score below minimum)
      final failedPreTest = tPreQualificationTest.copyWith(
        resolutionDate: DateTime.now(),
        finalScore: 60, // Below minimum score of 70
      );

      final state = QualificationTestState(
        qualificationTest: failedPreTest,
        currentQuestionIndex: tQuestions.length, // Test is completed
        selectedAnswers: {},
        trainingId: '123',
        testStarted: true,
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      // Find the Done button
      final doneButton = find.byWidgetPredicate(
        (widget) => widget is AppButton && widget.buttonText == LocaleKeys.done.tr(),
      );
      expect(doneButton, findsOneWidget);

      // Tap the Done button
      await tester.tap(doneButton);
      await tester.pumpAndSettle();

      // Verify that UpdatePreQualificationTestEvent was called
      verify(() => mockTrainingConsumptionBloc.add(any())).called(1);

      // Verify that router.pop was called (test page was closed)
      verify(() => mockGoRouter.pop()).called(1);
    });

    testWidgets('should navigate to first lesson when Done button is tapped on passed PRE test',
        (tester) async {
      // Create a passed pre-test (score above minimum)
      final passedPreTest = tPreQualificationTest.copyWith(
        resolutionDate: DateTime.now(),
        finalScore: 80, // Above minimum score of 70
      );

      final state = QualificationTestState(
        qualificationTest: passedPreTest,
        currentQuestionIndex: tQuestions.length, // Test is completed
        selectedAnswers: {},
        trainingId: '123',
        testStarted: true,
      );

      // Mock the training consumption bloc state with sections
      when(() => mockTrainingConsumptionBloc.state).thenReturn(
        TrainingConsumptionState(
          trainingStructure: const TrainingStructureModel(
            sections: [
              Section(
                id: 'section1',
                title: 'Section 1',
                index: 0,
                lessons: [
                  Lesson(
                    id: 'lesson1',
                    title: 'Lesson 1',
                    index: 0,
                    lessonType: LessonType.Article,
                  ),
                ],
              ),
            ],
          ),
        ),
      );

      await tester.pumpWidget(buildTestWidget(state: state));
      await tester.pumpAndSettle();

      // Find the Done button
      final doneButton = find.byWidgetPredicate(
        (widget) => widget is AppButton && widget.buttonText == LocaleKeys.done.tr(),
      );
      expect(doneButton, findsOneWidget);

      // Tap the Done button
      await tester.tap(doneButton);
      await tester.pumpAndSettle();

      // Verify that UpdatePreQualificationTestEvent was called
      verify(() => mockTrainingConsumptionBloc.add(any())).called(greaterThanOrEqualTo(1));

      // Verify that router.pop was NOT called
      verifyNever(() => mockGoRouter.pop());
    });
  });

  group('HandlePreviousNavigation', () {
    testWidgets(
      'should pop when qualification test is post and trainingStructure has empty sections',
      (tester) async {
        final tPostQualificationTest = PostQualificationTest(
          id: '1',
          type: QualificationTestType.POST,
          questions: tQuestions,
          applicantAnswers: [],
          applicantSavedAnswers: {},
          description: 'Test description',
          firstAttempt: true,
          mandatory: true,
          minimumScore: 70,
          randomized: false,
          timeLimit: 30,
          title: 'Post Test',
        );
        final state = QualificationTestState(
          qualificationTest: tPostQualificationTest,
          currentQuestionIndex: tQuestions.length,
          selectedAnswers: {},
          trainingId: '123',
          testStarted: true,
        );
        when(() => mockTrainingConsumptionBloc.state).thenReturn(
          TrainingConsumptionState(trainingStructure: const TrainingStructureModel(sections: [])),
        );

        await tester.pumpWidget(buildTestWidget(state: state));
        await tester.pumpAndSettle();
        final button = find.byWidgetPredicate(
          (widget) =>
              widget is AppButton && widget.buttonText == LocaleKeys.trainingView_previous.tr(),
        );
        expect(button, findsOneWidget);
        await tester.tap(button);
        await tester.pumpAndSettle();
        verify(() => mockGoRouter.pop()).called(1);
        verifyNever(() => mockTrainingConsumptionBloc.add(any()));
      },
    );
  });
}
