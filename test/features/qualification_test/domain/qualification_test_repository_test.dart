import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/qualification_test/data/data_sources/qualification_test_datasource.dart';
import 'package:national_skills_platform/features/qualification_test/domain/repositories/qualification_test_repository.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';

import '../../training_consumption/training_consumption_seed.dart';
import 'qualification_test_repository_test.mocks.dart';

@GenerateMocks([QualificationTestDatasource])
void main() {
  late MockQualificationTestDatasource mockDataSource;
  late QualificationTestRepository repository;

  setUp(() {
    mockDataSource = MockQualificationTestDatasource();
    repository = QualificationTestRepository(dataSource: mockDataSource);
  });

  group('submitAnswer', () {
    const tTrainingId = 'trainingId';
    const tTestId = 'testId';
    const tAnswerModel = ApplicantSavedAnswer(questionId: 'questionId', answerId: 'answerId');
    final tPreQualificationTest = PreQualificationTest.fromJson(tPreQualificationTestJson);
    final tPostQualificationTest = PostQualificationTest.fromJson(tPostQualificationTestJson);

    test('should submit answer and return PreQualificationTest', () async {
      when(
        mockDataSource.submitAnswer(any, any, any, any),
      ).thenAnswer((_) async => tPreQualificationTest);

      final result = await repository.submitAnswer(
        tTrainingId,
        tTestId,
        tAnswerModel,
        QualificationTestType.PRE,
      );

      verify(
        mockDataSource.submitAnswer(tTrainingId, tTestId, tAnswerModel, QualificationTestType.PRE),
      );
      expect(result, equals(tPreQualificationTest));
    });

    test('should submit answer and return PostQualificationTest', () async {
      when(
        mockDataSource.submitAnswer(any, any, any, any),
      ).thenAnswer((_) async => tPostQualificationTest);

      final result = await repository.submitAnswer(
        tTrainingId,
        tTestId,
        tAnswerModel,
        QualificationTestType.POST,
      );

      verify(
        mockDataSource.submitAnswer(tTrainingId, tTestId, tAnswerModel, QualificationTestType.POST),
      );
      expect(result, equals(tPostQualificationTest));
    });

    test('should throw an exception when the data source throws', () {
      when(mockDataSource.submitAnswer(any, any, any, any)).thenThrow(Exception());

      expect(
        () =>
            repository.submitAnswer(tTrainingId, tTestId, tAnswerModel, QualificationTestType.PRE),
        throwsA(isA<Exception>()),
      );
    });
  });

  group('retakeTest', () {
    const tTrainingId = 'trainingId';
    final tPreQualificationTest = PreQualificationTest.fromJson(tPreQualificationTestJson);
    final tPostQualificationTest = PostQualificationTest.fromJson(tPostQualificationTestJson);

    test('should retake test and return PreQualificationTest', () async {
      when(mockDataSource.retakeTest(any, any)).thenAnswer((_) async => tPreQualificationTest);

      final result = await repository.retakeTest(tTrainingId, QualificationTestType.PRE);

      verify(mockDataSource.retakeTest(tTrainingId, QualificationTestType.PRE));
      expect(result, equals(tPreQualificationTest));
    });

    test('should retake test and return PostQualificationTest', () async {
      when(mockDataSource.retakeTest(any, any)).thenAnswer((_) async => tPostQualificationTest);

      final result = await repository.retakeTest(tTrainingId, QualificationTestType.POST);

      verify(mockDataSource.retakeTest(tTrainingId, QualificationTestType.POST));
      expect(result, equals(tPostQualificationTest));
    });

    test('should throw an exception when the data source throws', () {
      when(mockDataSource.retakeTest(any, any)).thenThrow(Exception());

      expect(
        () => repository.retakeTest(tTrainingId, QualificationTestType.PRE),
        throwsA(isA<Exception>()),
      );
    });
  });
}
