import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/presentation/pages/learning_track_details_page.dart';

import '../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../shared_test_utils/nsp_test_wrapper.dart';
import '../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../training_details/training_details_page/training_details_page_test.mocks.dart';
import 'shared/learning_track_details_test_seed.dart';

class MockLearningTrackDetailsBloc extends MockBloc<CourseDetailsEvent, CourseDetailsState>
    implements CourseDetailsBloc {}

void main() {
  provideDummy<CourseDetailsState>(const CourseDetailsState());

  TestWidgetsFlutterBinding.ensureInitialized();
  final mockLearningTrackDetailsRepository = MockLearningTrackDetailsRepository();
  final MockLearningTrackDetailsBloc mockLearningTrackDetailsBloc = MockLearningTrackDetailsBloc();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final learningTrackDetailsModel = LearningTrackDetailsModel.fromJson(tLearningTrackDetailsJson);
  final injector = GetIt.instance;
  const learningTrackId = 'id1';

  setUpAll(() async {
    injector
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerSingleton<GoRouter>(MockGoRouter())
      ..registerFactory<CourseDetailsBloc>(() => mockLearningTrackDetailsBloc);
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Learning Track Details', () {
    ///Bloc State Stub
    whenListen(
      mockLearningTrackDetailsBloc,
      Stream<CourseDetailsState>.value(
        CourseDetailsState(learningTrackDetailsModel: learningTrackDetailsModel),
      ),
      initialState: const CourseDetailsState(),
    );

    ///Repositories stub
    when(
      mockLearningTrackDetailsRepository.loadLearningTracks(learningTrackId),
    ).thenAnswer((_) => Future<LearningTrackDetailsModel>.value(learningTrackDetailsModel));

    testGoldens('Learning Track Structure', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const LearningTrackDetailsPage(learningTrackID: learningTrackId)),
        surfaceSize: Device.iphone11.size,
      );

      await multiScreenGolden(
        tester,
        'learning_track_details_page',
        devices: [
          const Device(name: 'tablet_portrait', size: Size(1024, 2150)),
          const Device(
            name: 'iphone11',
            size: Size(414, 2150),
            safeArea: EdgeInsets.only(top: 44, bottom: 34),
          ),
        ],
      );

      // await tester.tap(find.text(LocaleKeys.trainingDetails_goToTraining.tr()).first);
    });
  });
}
