import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/data_sources/learning_track_details_datasource.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../shared/learning_track_details_test_seed.dart';

void main() {
  late LearningTrackDetailsDataSource dataSource;
  late MockDio mockDio;

  setUp(() async {
    await testEnvSetup();
    mockDio = MockDio();
    dataSource = LearningTrackDetailsDataSource(dio: mockDio);
    when(mockDio.options).thenReturn(BaseOptions(headers: {}));
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('LearningTrackDetailsDataSource', () {
    const tPath = '${ApiConstants.learningTracksPath}/id1';
    final tHeader = {Constants.acceptLanguageHeader: 'EN'};

    test('should perform a GET request', () async {
      when(mockDio.get(any, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: tPath),
          statusCode: 200,
          data: tLearningTrackDetailsJson,
        ),
      );

      when(mockDio.options).thenReturn(BaseOptions(headers: tHeader));

      await dataSource.getLearningTracks('id1');

      verify(mockDio.get(tPath, options: anyNamed('options'))).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(requestOptions: RequestOptions(path: tPath), statusCode: 404),
      );

      expect(() => dataSource.getLearningTracks('id1'), throwsA(isA<DioException>()));
    });

    test('should enroll successfully when status code is 200', () async {
      when(
        mockDio.post(any),
      ).thenAnswer((_) async => Response(statusCode: 200, requestOptions: RequestOptions()));

      await expectLater(dataSource.enrollLearningTrack('123'), completes);
    });

    test('should enroll successfully when status code is 201', () async {
      when(
        mockDio.post(any),
      ).thenAnswer((_) async => Response(statusCode: 201, requestOptions: RequestOptions()));

      await expectLater(dataSource.enrollLearningTrack('123'), completes);
    });

    test('should throw DioException when status code is not 200 or 201', () {
      when(
        mockDio.post(any),
      ).thenAnswer((_) async => Response(statusCode: 400, requestOptions: RequestOptions()));

      expect(() => dataSource.enrollLearningTrack('123'), throwsA(isA<DioException>()));
    });
  });
}
