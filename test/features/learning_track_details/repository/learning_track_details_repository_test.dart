import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/data_sources/learning_track_details_datasource.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/domain/repositories/learning_track_details_repository.dart';

import '../shared/learning_track_details_test_seed.dart';
import 'learning_track_details_repository_test.mocks.dart';

@GenerateNiceMocks([MockSpec<LearningTrackDetailsDataSource>()])
void main() {
  late final MockLearningTrackDetailsDataSource mockDataSource;
  late final LearningTrackDetailsRepository repository;
  final learningTracksModel = LearningTrackDetailsModel.fromJson(tLearningTrackDetailsJson);
  const learningTrackId = '0c4ea5dd-5a91-482e-867d-50bf23b5d265';

  setUpAll(() {
    mockDataSource = MockLearningTrackDetailsDataSource();
    repository = LearningTrackDetailsRepository(dataSource: mockDataSource);

    when(mockDataSource.getLearningTracks(learningTrackId))
        .thenAnswer((_) async => learningTracksModel);
  });

  group('loadLearningTracks', () {
    test('should get TrainingDetailsModel from the data source', () async {
      final result = await repository.loadLearningTracks(learningTrackId);
      verify(mockDataSource.getLearningTracks(learningTrackId));
      expect(result, equals(learningTracksModel));
    });
  });

  test('verify enroll learning track called', () async {
    await repository.enrollLearningTrack(courseId: learningTrackId);

    verify(mockDataSource.enrollLearningTrack(learningTrackId));
  });
}
