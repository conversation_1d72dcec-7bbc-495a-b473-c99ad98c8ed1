// Mocks generated by Mockito 5.4.5 from annotations
// in national_skills_platform/test/features/learning_track_details/repository/learning_track_details_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/course_details/learning_track_details/data/data_sources/learning_track_details_datasource.dart'
    as _i3;
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLearningTrackDetailsModel_0 extends _i1.SmartFake
    implements _i2.LearningTrackDetailsModel {
  _FakeLearningTrackDetailsModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [LearningTrackDetailsDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockLearningTrackDetailsDataSource extends _i1.Mock
    implements _i3.LearningTrackDetailsDataSource {
  @override
  _i4.Future<_i2.LearningTrackDetailsModel> getLearningTracks(
    String? courseId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#getLearningTracks, [courseId]),
        returnValue: _i4.Future<_i2.LearningTrackDetailsModel>.value(
          _FakeLearningTrackDetailsModel_0(
            this,
            Invocation.method(#getLearningTracks, [courseId]),
          ),
        ),
        returnValueForMissingStub: _i4.Future<_i2.LearningTrackDetailsModel>.value(
          _FakeLearningTrackDetailsModel_0(
            this,
            Invocation.method(#getLearningTracks, [courseId]),
          ),
        ),
      ) as _i4.Future<_i2.LearningTrackDetailsModel>);

  @override
  _i4.Future<void> enrollLearningTrack(String? courseId) => (super.noSuchMethod(
        Invocation.method(#enrollLearningTrack, [courseId]),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
