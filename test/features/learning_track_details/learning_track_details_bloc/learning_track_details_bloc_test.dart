import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/course_details/usecases/update_study_streams.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../my_learnings/presentation/my_learnings_test.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../../training_details/training_details_page/training_details_page_test.mocks.dart';
import '../shared/learning_track_details_test_seed.dart';

void main() {
  late final LearningTrackDetailsModel learningTrackDetailsModel;

  late final MockTrainingDetailsRepository mockTrainingDetailsRepository;
  late final MockLearningTrackDetailsRepository mockLearningTrackDetailsRepository;
  late final MockMyLearningsBloc mockMyLearningsBloc;
  final UpdateStudyStreams updateAndSortStudyStreams = UpdateStudyStreams();

  setUpAll(() {
    try {
      learningTrackDetailsModel = LearningTrackDetailsModel.fromJson(tLearningTrackDetailsJson);
    } catch (e) {
      // Create a minimal model for testing if JSON parsing fails
      learningTrackDetailsModel = const LearningTrackDetailsModel(
        id: '0c4ea5dd-5a91-482e-867d-50bf23b5d265',
        title: 'Web Design Learning Track',
        description: 'Complete web design learning track covering wireframes to prototypes',
        level: 'BEGINNER',
        status: 'PUBLISHED',
        profileImage: null,
        profileImageUrl: '',
        sector: null,
        domain: null,
        organizationName: 'Tech Academy',
        forNominationOnly: false,
        languageCode: 'en',
        trainingProviderName: 'Web Design Institute',
        trainingCount: 2,
        trainings: [],
        requirements: [],
        outcomes: [],
      );
    }

    mockTrainingDetailsRepository = MockTrainingDetailsRepository();
    mockLearningTrackDetailsRepository = MockLearningTrackDetailsRepository();
    mockMyLearningsBloc = MockMyLearningsBloc();

    // Configure the mock to return a valid state
    whenListen(
      mockMyLearningsBloc,
      Stream<MyLearningsState>.value(MyLearningsState()),
      initialState: MyLearningsState(),
    );

    GetIt.instance.registerSingleton<GoRouter>(MockGoRouter());
  });

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [CourseDetailsLoading, LearningTrackDetailsLoaded] when LoadLearningTrackDetailsEvent is added',
    build: () {
      when(
        mockLearningTrackDetailsRepository.loadLearningTracks(any),
      ).thenAnswer((_) async => learningTrackDetailsModel);
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        myLearningsBloc: mockMyLearningsBloc,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
      );
    },
    act: (bloc) {
      bloc.add(const LoadLearningTrackDetailsEvent(id: '1'));
    },
    expect: () => [
      const CourseDetailsState(isCourseDetailsLoading: true),
      CourseDetailsState(learningTrackDetailsModel: learningTrackDetailsModel),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [CourseDetailsLoading, CourseDetailsError] when LoadLearningTrackDetailsEvent is added and repository throws an error',
    build: () {
      when(
        mockLearningTrackDetailsRepository.loadLearningTracks(any),
      ).thenAnswer((_) async => throw Exception());
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        myLearningsBloc: mockMyLearningsBloc,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
      );
    },
    act: (bloc) {
      bloc.add(const LoadLearningTrackDetailsEvent(id: '1'));
    },
    expect: () => [
      const CourseDetailsState(isCourseDetailsLoading: true),
      CourseDetailsState(
        courseDetailsError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [CourseDetailsLoading, CourseDetailsState] when LoadLearningTrackDetailsEvent is added with enrollTrainingSuccess',
    build: () {
      when(
        mockLearningTrackDetailsRepository.loadLearningTracks(any),
      ).thenAnswer((_) async => learningTrackDetailsModel);
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        myLearningsBloc: mockMyLearningsBloc,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
      );
    },
    act: (bloc) {
      bloc.add(const LoadLearningTrackDetailsEvent(id: '1', enrollTrainingSuccess: true));
    },
    expect: () => [
      const CourseDetailsState(isCourseDetailsLoading: true),
      CourseDetailsState(
        learningTrackDetailsModel: learningTrackDetailsModel,
        enrollTrainingSuccess: true,
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [CourseDetailsLoading, CourseDetailsState] when EnrollLearningTrackEvent is added and succeeds',
    build: () {
      when(
        mockLearningTrackDetailsRepository.enrollLearningTrack(courseId: anyNamed('courseId')),
      ).thenAnswer((_) async {});
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollLearningTrackEvent());
    },
    expect: () => [
      const CourseDetailsState(isLearningTrackEnrollmentInProgress: true),
      const CourseDetailsState(enrollLearningTrackSuccess: true),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollLearningTrackError when EnrollLearningTrackEvent is added and fails',
    build: () {
      when(
        mockLearningTrackDetailsRepository.enrollLearningTrack(courseId: anyNamed('courseId')),
      ).thenAnswer((_) async => throw Exception());
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollLearningTrackEvent());
    },
    expect: () => [
      const CourseDetailsState(isLearningTrackEnrollmentInProgress: true),
      CourseDetailsState(
        enrollLearningTrackError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollLearningTrackSuccess when EnrollLearningTrackEvent is added',
    build: () {
      when(
        mockLearningTrackDetailsRepository.enrollLearningTrack(courseId: ''),
      ).thenAnswer((_) async {});
      return CourseDetailsBloc(
        trainingDetailsRepository: mockTrainingDetailsRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollLearningTrackEvent());
    },
    expect: () => [
      const CourseDetailsState(isLearningTrackEnrollmentInProgress: true),
      const CourseDetailsState(enrollLearningTrackSuccess: true),
    ],
  );
}
