final Map<String, dynamic> tLearningTrackDetailsJson = {
  "id": "0c4ea5dd-5a91-482e-867d-50bf23b5d265",
  "title": "Web Design Learning Track",
  "description": "Complete web design learning track covering wireframes to prototypes",
  "languageCode": "en",
  "level": "BEGINNER",
  "status": "PUBLISHED",
  "createdDate": "2024-03-24T11:18:59.210507",
  "profileImage": {
    "originalFilename": "Screenshot 2024-03-20 112337.png",
    "key": "images/2024-03-24T11:19:22.908158734",
    "size": 919542,
  },
  "profileImageUrl": "",
  "sector": {"id": "sector1", "title": "Technology"},
  "domain": {"id": "domain1", "title": "Web Development"},
  "organizationName": "Tech Academy",
  "trainingProviderName": "Web Design Institute",
  "forNominationOnly": false,
  "trainingCount": 2,
  "trainings": [],
  "titleAr": "مسار تعلم تصميم الويب",
  "descriptionAr": "مسار تعلم تصميم الويب الكامل الذي يغطي من الإطارات السلكية إلى النماذج الأولية",
  "skillLevel": "BEGINNER",
  "requirements": [
    {"value": "Basic understanding of HTML and CSS", "index": 0},
    {"value": "Familiarity with design concepts", "index": 1},
  ],
  "outcomes": [
    {"value": "Create professional wireframes", "index": 0},
    {"value": "Build interactive prototypes", "index": 1},
  ],
  "applicantDto": null,
};
