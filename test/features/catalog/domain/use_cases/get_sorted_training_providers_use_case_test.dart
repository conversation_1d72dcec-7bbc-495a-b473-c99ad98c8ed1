import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/training_providers_repository.dart';
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart';

import '../../filter_bloc_test/seed/training_providers_seed.dart';
import 'get_sorted_training_providers_use_case_test.mocks.dart';

@GenerateMocks([TrainingProvidersRepository])
void main() {
  late GetSortedTrainingProvidersUseCase useCase;
  late MockTrainingProvidersRepository mockRepository;
  late List<TrainingProviderModel> trainingProviders;

  setUp(() {
    mockRepository = MockTrainingProvidersRepository();
    useCase = GetSortedTrainingProvidersUseCase(mockRepository);
    trainingProviders =
        trainingProviderSeed.map((json) => TrainingProviderModel.fromJson(json)).toList();

    when(mockRepository.getTrainingProviders(any)).thenAnswer((_) async => trainingProviders);
  });

  group('call', () {
    test('should get and sort training providers for English locale', () async {
      final result = await useCase('en');

      verify(mockRepository.getTrainingProviders('en'));
      expect(result, isA<List<TrainingProviderModel>>());

      // Verify sorting for English locale
      for (int i = 0; i < result.length - 1; i++) {
        final isCurrentArabic = _isArabicText(result[i].organizationName);
        final isNextArabic = _isArabicText(result[i + 1].organizationName);

        if (isCurrentArabic != isNextArabic) {
          expect(isCurrentArabic, isFalse);
        } else {
          expect(
            result[i].organizationName.compareTo(result[i + 1].organizationName),
            lessThanOrEqualTo(0),
          );
        }
      }
    });

    test('should get and sort training providers for Arabic locale', () async {
      final result = await useCase(Constants.localeAR);

      verify(mockRepository.getTrainingProviders(Constants.localeAR));
      expect(result, isA<List<TrainingProviderModel>>());

      // Verify sorting for Arabic locale
      for (int i = 0; i < result.length - 1; i++) {
        final isCurrentArabic = _isArabicText(result[i].organizationName);
        final isNextArabic = _isArabicText(result[i + 1].organizationName);

        if (isCurrentArabic != isNextArabic) {
          expect(isCurrentArabic, isTrue);
        } else {
          expect(
            result[i].organizationName.compareTo(result[i + 1].organizationName),
            lessThanOrEqualTo(0),
          );
        }
      }
    });
  });
}

bool _isArabicText(String text) {
  return text.contains(RegExp(r'[\u0600-\u06FF]'));
}
