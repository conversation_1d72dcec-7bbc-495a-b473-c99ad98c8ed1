// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in national_skills_platform/test/features/catalog/domain/use_cases/get_sorted_training_providers_use_case_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart'
    as _i4;
import 'package:national_skills_platform/features/catalog/domain/repositories/training_providers_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [TrainingProvidersRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingProvidersRepository extends _i1.Mock implements _i2.TrainingProvidersRepository {
  MockTrainingProvidersRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.TrainingProviderModel>> getTrainingProviders(String? locale) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTrainingProviders,
          [locale],
        ),
        returnValue:
            _i3.Future<List<_i4.TrainingProviderModel>>.value(<_i4.TrainingProviderModel>[]),
      ) as _i3.Future<List<_i4.TrainingProviderModel>>);
}
