import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/training_providers_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';

import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../filter_bloc_test/seed/training_providers_seed.dart';

void main() {
  late TrainingProvidersDataSource dataSource;
  late MockDio mockDio;
  final baseOptions = BaseOptions(headers: {'test': 'test'});

  setUp(() {
    mockDio = MockDio();
    dataSource = TrainingProvidersDataSource(mockDio);
    when(mockDio.options).thenReturn(baseOptions);
  });

  group('getTrainingProviders', () {
    test('should perform a GET request with correct headers', () async {
      when(mockDio.get(ApiConstants.organizations, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.organizations),
          statusCode: 200,
          data: trainingProviderSeed,
        ),
      );

      await dataSource.getTrainingProviders('en');

      verify(
        mockDio.get(
          ApiConstants.organizations,
          options: argThat(
            predicate<Options>(
              (options) => options.headers?[Constants.acceptLanguageHeader] == 'EN',
            ),
            named: 'options',
          ),
        ),
      ).called(1);
    });

    test('should return List<TrainingProviderModel> when response is successful', () async {
      when(mockDio.get(ApiConstants.organizations, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.organizations),
          statusCode: 200,
          data: trainingProviderSeed,
        ),
      );

      final result = await dataSource.getTrainingProviders('en');

      expect(result, isA<List<TrainingProviderModel>>());
      expect(result.length, equals(trainingProviderSeed.length));
      expect(result.first.organizationName, equals(trainingProviderSeed.first['organizationName']));
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      final requestOptions = RequestOptions(path: ApiConstants.organizations);
      when(
        mockDio.get(any, options: anyNamed('options')),
      ).thenAnswer((_) async => Response(requestOptions: requestOptions, statusCode: 404));

      expect(() => dataSource.getTrainingProviders('en'), throwsA(isA<DioException>()));
    });
  });
}
