import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/home/<USER>/data_sources/sectors_datasource.dart';

import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../filter_bloc_test/seed/sectors_seed.dart';

// Test data for active sectors
final tActiveSectorsJson = {
  "content": [
    {"id": "sector-1", "sectorCode": 1, "title": "Sector 1", "status": "ACTIVE"},
    {"id": "sector-2", "sectorCode": 2, "title": "Sector 2", "status": "ACTIVE"},
  ],
  "page": 0,
  "size": 10,
  "totalRecords": 2,
  "totalPages": 1,
};

void main() {
  late SectorDataSource dataSource;
  late MockDio mockDio;

  setUp(() {
    mockDio = MockDio();
    dataSource = SectorDataSource(dio: mockDio);
    when(mockDio.options).thenReturn(BaseOptions(headers: {}));
  });

  group('get sectors', () {
    test('should perform a GET request', () {
      when(mockDio.get(ApiConstants.sectorsTreePath, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.sectorsTreePath),
          statusCode: 200,
          data: tSectorsJson,
        ),
      );

      dataSource.getSectors('en');

      verify(mockDio.get(ApiConstants.sectorsTreePath, options: anyNamed('options'))).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.sectorsTreePath),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.getSectors('en'), throwsA(isA<DioException>()));
    });
  });

  group('get active sectors', () {
    test('should perform a GET request', () async {
      when(
        mockDio.get(
          any,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: '/api/v1/trainings/sectors/active'),
          statusCode: 200,
          data: tActiveSectorsJson,
        ),
      );

      await dataSource.getActiveSectors(locale: 'en');

      verify(
        mockDio.get(
          any,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        ),
      ).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(
        mockDio.get(
          any,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: '/api/v1/trainings/sectors/active'),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.getActiveSectors(locale: 'en'), throwsA(isA<DioException>()));
    });

    test('should return ActiveSectorResponse when the response code is 200', () async {
      when(
        mockDio.get(
          any,
          queryParameters: anyNamed('queryParameters'),
          options: anyNamed('options'),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: '/api/v1/trainings/sectors/active'),
          statusCode: 200,
          data: tActiveSectorsJson,
        ),
      );

      final result = await dataSource.getActiveSectors(locale: 'en');

      expect(result, isA<Map<String, dynamic>>());
      expect(result['content'], isA<List>());
      expect((result['content'] as List).length, 2);
    });
  });
}
