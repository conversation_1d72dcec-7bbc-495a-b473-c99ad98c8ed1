import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/location_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';

import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../filter_bloc_test/seed/location_seed.dart';

void main() {
  late LocationDataSource dataSource;
  late MockDio mockDio;
  final baseOptions = BaseOptions(headers: {'test': 'test'});

  setUp(() {
    mockDio = MockDio();
    dataSource = LocationDataSource(mockDio);
    when(mockDio.options).thenReturn(baseOptions);
  });

  group('getLocations', () {
    test('should perform a GET request with correct headers', () async {
      when(
        mockDio.get(
          any,
          options: argThat(
            predicate<Options>(
              (options) => options.headers?[Constants.acceptLanguageHeader] == 'EN',
            ),
            named: 'options',
          ),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.cities),
          statusCode: 200,
          data: locationSeed,
        ),
      );

      await dataSource.getLocations('en');

      verify(
        mockDio.get(
          ApiConstants.cities,
          options: argThat(
            predicate<Options>(
              (options) => options.headers?[Constants.acceptLanguageHeader] == 'EN',
            ),
            named: 'options',
          ),
        ),
      ).called(1);
    });

    test('should return sorted List<LocationModel> when response is successful', () async {
      when(
        mockDio.get(
          any,
          options: argThat(
            predicate<Options>(
              (options) => options.headers?[Constants.acceptLanguageHeader] == 'EN',
            ),
            named: 'options',
          ),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.cities),
          statusCode: 200,
          data: locationSeed,
        ),
      );

      final result = await dataSource.getLocations('en');

      expect(result, isA<List<LocationModel>>());
      expect(result.length, equals(locationSeed.length));
      expect(result.first.cityName, equals(locationSeed.first['cityName']));

      /// Verify sorting
      for (int i = 0; i < result.length - 1; i++) {
        expect(
          result[i].cityName.compareTo(result[i + 1].cityName) <= 0,
          isTrue,
          reason: 'List should be sorted by cityName',
        );
      }
    });

    test('should throw DioException when the response code is not 200 or 201', () {
      when(
        mockDio.get(
          any,
          options: argThat(
            predicate<Options>(
              (options) => options.headers?[Constants.acceptLanguageHeader] == 'EN',
            ),
            named: 'options',
          ),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.cities),
          statusCode: 404,
        ),
      );

      expect(
        () => dataSource.getLocations('en'),
        throwsA(isA<DioException>()),
      );
    });
  });
}
