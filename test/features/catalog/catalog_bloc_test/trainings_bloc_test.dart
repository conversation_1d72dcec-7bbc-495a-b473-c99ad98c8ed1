import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

import '../presentation/catalog_page_test.mocks.dart';
import '../shared/catalog_test_seed.dart';

void main() {
  late final MockTrainingsRepository mockTrainingsRepository;

  setUpAll(() {
    mockTrainingsRepository = MockTrainingsRepository();
  });

  blocTest(
    'Emits [Loading, Loaded] when successful',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) => bloc.add(const GetTrainingsListEvent()),
    expect: () => [const TrainingsLoading(), isA<TrainingsLoaded>()],
  );

  blocTest(
    'Emits [Loading, Error] when unsuccessful',
    build: () {
      when(
        mockTrainingsRepository.getTrainings(any),
      ).thenAnswer((_) async => throw Exception('Error'));
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) => bloc.add(const GetTrainingsListEvent()),
    expect: () => [const TrainingsLoading(), isA<TrainingsError>()],
  );

  blocTest(
    'Emits [Loading, Loaded] when FetchMoreEvent is successful',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetTrainingsListEvent())
        ..add(const FetchMoreEvent());
    },
    expect: () => [const TrainingsLoading(), isA<TrainingsLoaded>(), isA<TrainingsLoaded>()],
  );

  blocTest(
    'Emits [Loading, Error] when FetchMoreEvent is unsuccessful',
    build: () {
      when(
        mockTrainingsRepository.getTrainings(any),
      ).thenAnswer((_) async => throw Exception('Error'));
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetTrainingsListEvent())
        ..add(const FetchMoreEvent());
    },
    expect: () => [const TrainingsLoading(), isA<TrainingsError>()],
  );

  blocTest(
    'Emits [Loading, Loaded] when ApplyFilterToList is successful',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetTrainingsListEvent())
        ..add(const ApplyFilterToList(FilterModel()));
    },
    expect: () => [
      const TrainingsLoading(),
      isA<TrainingsLoaded>(),
      const TrainingsLoading(),
      isA<TrainingsLoaded>(),
    ],
  );

  blocTest(
    'Emits [Loading, Loaded] when ApplySortToList is successful',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      return TrainingsBloc(trainingsRepository: mockTrainingsRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetTrainingsListEvent())
        ..add(const ApplySortToList(SortOptionsEnum.createdDate));
    },
    expect: () => [
      const TrainingsLoading(),
      isA<TrainingsLoaded>(),
      const TrainingsLoading(),
      isA<TrainingsLoaded>(),
    ],
  );
}
