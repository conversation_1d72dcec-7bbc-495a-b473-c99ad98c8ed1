import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

import '../presentation/catalog_page_test.mocks.dart';
import '../shared/catalog_test_seed.dart';

void main() {
  late final MockLearningTracksRepository mockLearningTracksRepository;

  setUpAll(() {
    mockLearningTracksRepository = MockLearningTracksRepository();
  });

  blocTest(
    'Emits [Loading, Loaded] when successful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => tLearningTrackModel);
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) => bloc.add(const GetLearningTracksEvent()),
    expect: () => [const LearningTracksLoading(), isA<LearningTracksLoaded>()],
  );

  blocTest(
    'Emits [Loading, Error] when unsuccessful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => throw Exception('Error'));
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) => bloc.add(const GetLearningTracksEvent()),
    expect: () => [const LearningTracksLoading(), isA<LearningTracksError>()],
  );

  blocTest(
    'Emits [Loading, Loaded] when FetchMoreEvent is successful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => tLearningTrackModel);
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetLearningTracksEvent())
        ..add(const FetchMoreEvent());
    },
    expect: () => [
      const LearningTracksLoading(),
      isA<LearningTracksLoaded>(),
      isA<LearningTracksLoaded>(),
    ],
  );

  blocTest(
    'Emits [Loading, Error] when FetchMoreEvent is unsuccessful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => throw Exception('Error'));
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetLearningTracksEvent())
        ..add(const FetchMoreEvent());
    },
    expect: () => [const LearningTracksLoading(), isA<LearningTracksError>()],
  );

  blocTest(
    'Emits [Loading, Loaded] when ApplyFilterToList is successful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => tLearningTrackModel);
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetLearningTracksEvent())
        ..add(const ApplyFilterToLtList(FilterModel()));
    },
    expect: () => [
      const LearningTracksLoading(),
      isA<LearningTracksLoaded>(),
      const LearningTracksLoading(),
      isA<LearningTracksLoaded>(),
    ],
  );

  blocTest(
    'Emits [Loading, Loaded] when ApplySortToList is successful',
    build: () {
      when(
        mockLearningTracksRepository.getLearningTracks(any),
      ).thenAnswer((_) async => tLearningTrackModel);
      return LearningTracksBloc(learningTracksRepository: mockLearningTracksRepository);
    },
    act: (bloc) {
      bloc
        ..add(const GetLearningTracksEvent())
        ..add(const ApplySortToList(SortOptionsEnum.createdDate));
    },
    expect: () => [
      const LearningTracksLoading(),
      isA<LearningTracksLoaded>(),
      const LearningTracksLoading(),
      isA<LearningTracksLoaded>(),
    ],
  );
}
