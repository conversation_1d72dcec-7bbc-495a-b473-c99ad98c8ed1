import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/catalog/data/converter/filters_converter.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';

void main() {
  group('FilterModelToLearningTracksRequestParamsConverter', () {
    final converter = FilterModelToLearningTracksRequestParamsConverter();

    test('converts FilterModel to LearningTracksRequestParams correctly', () {
      const filterModel = FilterModel(
        languageFilterModel: LanguageFilterModel(english: true),
        durationFilterModel: DurationFilterModel(days: true, months: true),
      );

      final result = converter.convert(filterModel);

      expect(result.level, isNull);
      expect(result.languages, equals('EN'));
      expect(result.duration, equals('DAYS,MONTHS'));
    });
  });

  group('FilterModelToTrainingsRequestParamsConverter', () {
    final converter = FilterModelToTrainingsRequestParamsConverter();

    test('converts FilterModel to TrainingsRequestParams correctly', () {
      const filterModel = FilterModel(
        languageFilterModel: LanguageFilterModel(english: true),
        durationFilterModel: DurationFilterModel(days: true, months: true),
      );

      final result = converter.convert(filterModel);

      expect(result.level, isNull);
      expect(result.languages, equals('EN'));
      expect(result.duration, equals('DAYS,MONTHS'));
    });
  });
}
