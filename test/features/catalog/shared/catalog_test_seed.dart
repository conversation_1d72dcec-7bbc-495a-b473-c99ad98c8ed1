import 'dart:convert';

import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';

const tLearningTracksRequestParams = LearningTracksRequestParams(page: 0, size: 10);
const tTrainingsRequestParams = TrainingsRequestParams(page: 0, size: 10);

final Map<String, dynamic> tLearningTrackJsonMap = jsonDecode('''
{
  "content": [
    {
      "id": "00d87865-de90-46ae-9d95-69f9298f090b",
      "title": "Training with 2 parts",
      "level": "BEGINNER",
      "language": "EN",
      "duration": "144",
      "profileImageUrl":"",
      "trainingProviderName": "OrgName1",
      "trainingProviderImageUrl": null
    },
    {
      "id": "b91aa51e-aaaa-4146-b535-40b2bbfaa086",
      "title": "Learning Track 3",
      "level": "INTERMEDIATE",
      "language": "EN",
      "duration": "143",
      "profileImageUrl":"",
      "trainingProviderName": "OrgName1",
      "trainingProviderImageUrl": null
    },
    {
      "id": "c6021c24-57ac-4499-88ed-23458f72ab10",
      "title": "Training 2 with 2 parts",
      "level": "BEGINNER",
      "language": "EN",
      "duration": "143",
      "profileImageUrl":"",
      "trainingProviderName": "OrgName1",
      "trainingProviderImageUrl": null
    },
    {
      "id": "3124eb08-335a-47a4-803f-3ff1c6c34794",
      "title": "KanstantsinNominate #3",
      "level": "BEGINNER",
      "language": "EN",
      "duration": "214",
      "profileImageUrl":"",
      "trainingProviderName": "OrgName1",
      "trainingProviderImageUrl": null
    },
    {
      "id": "d11973f6-7ff0-4e90-969d-0f29106f0743",
      "title": "KanstantsinNominate #6",
      "level": "BEGINNER",
      "language": "EN",
      "duration": "214",
      "profileImageUrl":"",
      "trainingProviderName": "OrgName1",
      "trainingProviderImageUrl": null
    }
  ],
  "page": 0,
  "size": 10,
  "totalRecords": 13,
  "totalPages": 2
}''');

final Map<String, dynamic> tTrainingsJsonMap = jsonDecode('''
{
  "content": [
    {
      "id": "01d7bcf7-aa50-44a2-bc0e-6a48415c968a",
      "title": "KanstantsinTestNomination",
      "duration": "1-66",
      "profileImage": {
        "originalFilename": "Screenshot 2024-02-16 at 4.10.51 PM.png",
        "key": "images/2024-02-20T16:07:20.744549858",
        "size": 115499
      },
      "profileImageUrl":"",
      "language": "AR",
      "level": "BEGINNER",
      "status": "PUBLISHED",
      "skills": [
        "KanstantsinTestNominationdsf",
        "sdfsd"
      ],
      "createdDate": "2024-02-20T16:06:52.485359",
      "lastModifiedDate": "2024-02-27T11:14:45.291988",
      "enrolledCount": 323,
      "trainingType": "SELF_PACED",
      "avatarUrl": "",
      "hidden": false,
      "hasFutureInPerson": false,
      "hasFutureOnline": false,
      "cities": [],
      "nearestOpenForEnrollmentStreamDate": null
    },
    {
      "id": "3c374812-3fc5-45fc-866f-4fbfe40ab218",
      "title": "PERF 34TrueFalseCheck",
      "duration": "1-68",
      "profileImage": {
        "originalFilename": "Screenshot 2024-01-02 at 10.58.53 AM.png",
        "key": "images/2024-01-02T11:06:49.361165338",
        "size": 211386
      },
      "profileImageUrl":"",
      "language": "EN",
      "level": "ALL",
      "status": "PUBLISHED",
      "skills": [
        "TrueFalseCheck",
        "TrueFalseCheck2"
      ],
      "createdDate": "2024-01-02T11:06:17.79434",
      "lastModifiedDate": "2024-03-01T12:51:18.197835",
      "enrolledCount": 64,
      "trainingType": "SELF_PACED",
      "avatarUrl": "",
      "hidden": false,
      "hasFutureInPerson": false,
      "hasFutureOnline": false,
      "cities": [],
      "nearestOpenForEnrollmentStreamDate": null
    },
    {
      "id": "ee830cba-19ba-4deb-be11-cb6d9e0d2054",
      "title": "PERF 33TrueFalseCheck",
      "duration": "1-68",
      "profileImage": {
        "originalFilename": "Screenshot 2024-01-02 at 10.58.53 AM.png",
        "key": "images/2024-01-02T11:06:49.361165338",
        "size": 211386
      },
            "profileImageUrl":"",
      "language": "EN",
      "level": "ALL",
      "status": "PUBLISHED",
      "skills": [
        "TrueFalseCheck",
        "TrueFalseCheck2"
      ],
      "createdDate": "2024-01-02T11:06:17.79434",
      "lastModifiedDate": "2024-02-20T17:18:28.019623",
      "enrolledCount": 53,
      "trainingType": "SELF_PACED",
      "avatarUrl": "",
      "hidden": false,
      "hasFutureInPerson": false,
      "hasFutureOnline": false,
      "cities": [],
      "nearestOpenForEnrollmentStreamDate": null
    },
    {
      "id": "79836b19-eda8-4992-be39-ecd573fddadc",
      "title": "PERF 25TrueFalseCheck",
      "duration": "1-68",
      "profileImage": {
        "originalFilename": "Screenshot 2024-01-02 at 10.58.53 AM.png",
        "key": "images/2024-01-02T11:06:49.361165338",
        "size": 211386
      },
           "profileImageUrl":"",
      "language": "EN",
      "level": "ALL",
      "status": "PUBLISHED",
      "skills": [
        "TrueFalseCheck",
        "TrueFalseCheck2"
      ],
      "createdDate": "2024-01-02T11:06:17.79434",
      "lastModifiedDate": "2024-03-01T15:34:28.81282",
      "enrolledCount": 35,
      "trainingType": "SELF_PACED",
      "avatarUrl": "",
      "hidden": false,
      "hasFutureInPerson": false,
      "hasFutureOnline": false,
      "cities": [],
      "nearestOpenForEnrollmentStreamDate": null
    }
  ],
  "page": 0,
  "size": 10,
  "totalRecords": 15079,
  "totalPages": 1508
}
''');

final Map<String, dynamic> tTrainingDetailsJson = {
  "id": "0c4ea5dd-5a91-482e-867d-50bf23b5d265",
  "title":
      "Web Design: Wireframes to Prototypes The Complete Guide 2023 (incl. React Router & Redux) Dive into",
  "description":
      "<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum</p> The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from de Finibus Bonorum et Malorum by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham<p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum</p> The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from de Finibus Bonorum et Malorum by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham",
  "overview": "aa",
  "language": "EN",
  "duration": "1-30",
  "level": "ALL",
  "skillLevel": null,
  "profileImage": {
    "originalFilename": "Screenshot 2024-03-20 112337.png",
    "key": "images/2024-03-24T11:19:22.908158734",
    "size": 919542,
  },
  "profileImageUrl": "",
  "promoVideo": {
    "originalFilename": "Skills Portal - Google Chrome 2024-02-27 15-53-30.mp4",
    "key": "videos/0c4ea5dd-5a91-482e-867d-50bf23b5d265/2024-03-24T11:19:25.790368629",
    "size": 12646215,
  },
  "promoVideoUrl":
      "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/videos/0c4ea5dd-5a91-482e-867d-50bf23b5d265/2024-03-24T11%3A19%3A25.790368629?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20240327T194645Z&X-Amz-SignedHeaders=host&X-Amz-Expires=18000&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20240327%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Signature=a1c2c55053810962ba96923ab568d234a1cd59a6608d25505e31df9115967e91",
  "requirements": [
    {"value": "Web Design: Wireframes to Prototypes The Complete Guide 2023", "index": 0},
    {"value": "Web Design: Wireframes to Prototypes The Complete Guide 2023", "index": 1},
  ],
  "outcomes": [
    {"value": "aa", "index": 0},
    {"value": "aa", "index": 1},
  ],
  "skills": ["aaa", "qqq"],
  "status": "PUBLISHED",
  "createdDate": "2024-03-24T11:18:59.210507",
  "lastModifiedDate": "2024-03-25T15:41:45.160671",
  "trainingStructure": {
    "sections": [
      {
        "id": "4fafa48e-aa63-4649-b6a3-8e81c2f3a4f8",
        "title": "Web Design: Wireframes to Prototypes The Complete Guide 2023",
        "index": 0,
        "lessons": [
          {
            "id": "bf1f04ac-4ffd-4c8c-861e-70d82a1f5e7a",
            "title": "Web Design: Wireframes to Prototypes The Complete Guide 2023",
            "index": 0,
            "text":
                "There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.",
            "type": "ARTICLE",
            "resources": [
              {"originalFilename": "resource1.pdf", "key": "resources/resource1.pdf", "size": 1024},
            ],
            "quiz": {
              "id": "quiz1",
              "randomized": true,
              "questions": [
                {
                  "id": "question1",
                  "questionType": "multipleChoice",
                  "question": "What is the capital of France?",
                  "answers": [
                    {"id": "answer1", "answer": "Paris", "correct": true, "index": 0},
                    {"id": "answer2", "answer": "London", "correct": false, "index": 1},
                  ],
                  "index": 0,
                },
              ],
            },
          },
          {
            "id": "788189ba-bba2-442c-921e-47905cff7127",
            "title": "Web Design: Wireframes to Prototypes The Complete Guide 2023",
            "index": 0,
            "text": null,
            "type": "VIDEO",
            "resources": [
              {
                "originalFilename": "Sample-Video-File-For-Testing 2.mp4",
                "key": "videos/b8f5fc15-ec5f-4c84-a708-2008de04a05a/2024-07-30T10:05:02.593573189",
                "size": 100450390,
              },
            ],
            "quiz": null,
          },
          {
            "id": "788189ba-bba2-442c-921e-47905cff7127",
            "title": "Web Design: Wireframes to Prototypes The Complete Guide 2023",
            "index": 0,
            "text": null,
            "type": "FILE",
            "resources": [
              {
                "originalFilename": "Sample-Video-File-For-Testing 2.mp4",
                "key": "videos/b8f5fc15-ec5f-4c84-a708-2008de04a05a/2024-07-30T10:05:02.593573189",
                "size": 100450390,
              },
            ],
            "quiz": null,
          },
          {
            "id": "new-lesson-id",
            "title": "New Quiz Lesson",
            "index": 1,
            "text": null,
            "type": "QUIZ",
            "resources": [],
            "quiz": {
              "id": "new-quiz-id",
              "randomized": true,
              "questions": [
                {
                  "id": "new-question-id",
                  "questionType": "multipleChoice",
                  "question": "Sample question?",
                  "answers": [
                    {"id": "new-answer-id-1", "answer": "Option 1", "correct": true, "index": 0},
                    {"id": "new-answer-id-2", "answer": "Option 2", "correct": false, "index": 1},
                  ],
                  "index": 0,
                },
              ],
            },
          },
          {
            "id": "9d721684-d4bd-40f6-40506c44",
            "title": "Web Design: Wireframes to Prototypes The Complete Guide 2023",
            "index": 4,
            "text": null,
            "type": "SLIDE",
            "resources": [
              {
                "originalFilename": "Sample-Slide.pdf",
                "key": "files/b8f5fc15-ec5f-4c84a/2024-07-30T10:06:08.609617998",
                "size": 1832836,
              },
            ],
            "quiz": null,
          },
        ],
      },
    ],
  },
  "studyStreams": [
    {
      "id": "d4eaebe7-efee-4475-956a-edd15e80cb99",
      "startDate": "2024-11-23T00:00:00",
      "endDate": "2024-12-31T23:59:59",
      "maxNumberOfParticipants": 10,
      "currentNumberOfParticipants": 4,
      "noLimits": false,
      "liveSession": {
        "id": "da972c82-bf62-492b-979a-89d0799527e6",
        "startDate": "2024-11-23T13:00:00",
        "durationHours": 2,
        "durationMinutes": 5,
        "recurringMeeting": false,
        "zoomLink": "link.com",
        "zoomPassword": "123456",
        "meetingId": "123456",
        "recurrence": null,
        "repeatEvery": 1,
        "endDate": null,
        "endAfterOccurrences": null,
        "daysOfWeek": [],
        "weekOfMonth": null,
        "dayOfMonth": null,
        "meetings": [
          {
            "id": "dda76ad6-4c55-463d-8146-5bf509f815a6",
            "title": null,
            "trainingId": null,
            "liveSessionId": null,
            "startDate": "2024-11-23T13:00:00",
            "durationHours": 2,
            "durationMinutes": 5,
            "zoomLink": "link.com",
            "zoomPassword": "123456",
            "meetingId": "123456",
            "status": "PUBLISHED",
            "cancelled": false,
            "cancellationReason": null,
            "cancellationDate": null,
            "rootId": null,
            "cardNotes": [],
            "section": [],
          },
        ],
        "status": "PUBLISHED",
        "rootId": null,
      },
      "trainingId": null,
      "status": "PUBLISHED",
      "cancelled": false,
      "cancelledDate": null,
      "rootId": null,
      "closedForEnrollmentDate": "2024-11-23T15:05:00",
      "cardNotes": [],
      "type": "ONLINE",
      "location": {
        "id": "loc123",
        "addressPlaceId": "place123",
        "addressNameEn": "Test Address English",
        "addressNameAr": "Test Address Arabic",
        "latitude": 24.7136,
        "longitude": 46.6753,
        "city": {
          "id": "city123",
          "cityPlaceId": "cityplace123",
          "cityNameEn": "Riyadh",
          "cityNameAr": "الرياض",
          "region": {
            "id": "region123",
            "regionPlaceId": "regionplace123",
            "regionNameEn": "Riyadh Region",
            "regionNameAr": "منطقة الرياض",
          },
        },
        "comment": "Test location comment",
      },
      "comment": "Test study stream comment",
    },
    {
      "id": "45013a51-0f3d-437d-bebd-429076d7a5f4",
      "startDate": "2024-11-30T00:00:00",
      "endDate": "2024-12-31T23:59:59",
      "maxNumberOfParticipants": 10,
      "currentNumberOfParticipants": 3,
      "noLimits": false,
      "liveSession": {
        "id": "df2a57f0-7733-4994-a16e-997bbf80c2b6",
        "startDate": "2024-11-30T13:00:00",
        "durationHours": 2,
        "durationMinutes": 27,
        "recurringMeeting": false,
        "zoomLink": "superlink.com",
        "zoomPassword": "987654",
        "meetingId": "987654",
        "recurrence": null,
        "repeatEvery": 1,
        "endDate": null,
        "endAfterOccurrences": null,
        "daysOfWeek": [],
        "weekOfMonth": null,
        "dayOfMonth": null,
        "meetings": [
          {
            "id": "24828ba0-af43-4688-93b1-8ebecb991b35",
            "title": null,
            "trainingId": null,
            "liveSessionId": null,
            "startDate": "2024-11-30T13:00:00",
            "durationHours": 2,
            "durationMinutes": 27,
            "zoomLink": "superlink.com",
            "zoomPassword": "987654",
            "meetingId": "987654",
            "status": "PUBLISHED",
            "cancelled": false,
            "cancellationReason": null,
            "cancellationDate": null,
            "rootId": null,
            "cardNotes": [],
            "section": [],
          },
        ],
        "status": "PUBLISHED",
        "rootId": null,
      },
      "trainingId": null,
      "status": "PUBLISHED",
      "cancelled": false,
      "cancelledDate": null,
      "rootId": null,
      "closedForEnrollmentDate": "2024-11-30T15:27:00",
      "cardNotes": [],
      "type": "IN_PERSON",
      "location": {
        "id": "loc456",
        "addressPlaceId": "place456",
        "addressNameEn": "Test Address 2 English",
        "addressNameAr": "Test Address 2 Arabic",
        "latitude": 21.4858,
        "longitude": 39.1925,
        "city": {
          "id": "city456",
          "cityPlaceId": "cityplace456",
          "cityNameEn": "Jeddah",
          "cityNameAr": "جدة",
          "region": {
            "id": "region456",
            "regionPlaceId": "regionplace456",
            "regionNameEn": "Makkah Region",
            "regionNameAr": "منطقة مكة المكرمة",
          },
        },
        "comment": "Test location 2 comment",
      },
      "comment": "Test study stream 2 comment",
    },
  ],
  "qualificationTests": jsonDecode(_qualificationTests),
  "applicantDto": {
    "id": "applicant1",
    "status": "active",
    "requestId": "request1",
    "studyStreams": [
      {
        "streamId": "d4eaebe7-efee-4475-956a-edd15e80cb99",
        "status": "ENROLLED",
        "startDate": null,
        "endDate": null,
        "nextSessionDate": null,
        "cancelled": false,
        "cancellationDate": null,
      },
    ],
  },
  "sector": {"id": "b24ee46e-af91-417c-8217-4a4303026e41", "title": "Manufacturing"},
  "domain": {
    "id": "9b8e35e7-e205-4614-8fd9-8f660c44446a",
    "title": "Artificial Intelligence and Internet of Things",
  },
  "seatingCapacities": [
    {
      "seatingCapacityId": "capacity1",
      "dtoStatus": "Active",
      "maxNumberOfEnrollments": 50,
      "noLimits": false,
      "actualNumberOfEnrollments": 45,
      "endDate": "2024-12-31T00:00:00.000Z",
      "lastModifiedDate": "2024-01-01T00:00:00.000Z",
    },
  ],
  "copiedFromId": null,
  "version": 1,
  "copiedFromFirstId": null,
  "organizationName": "rejectionone",
  "avatarUrl": "",
  "organizationId": "608e448d-faa4-4421-b9fd-a693e3ad9e6b",
};

const _qualificationTests = '''
[
  {
    "id": "test1",
    "questions": [
      {
        "id": "question1",
        "questionType": "multipleChoice",
        "question": "What is the capital of France?",
        "answers": [
          {
            "id": "answer1",
            "answer": "Paris"
          },
          {
            "id": "answer2",
            "answer": "London"
          }
        ]
      },
      {
        "id": "question2",
        "questionType": "trueOrFalse",
        "question": "Is the sky blue?",
        "answers": [
          {
            "id": "answer3",
            "answer": "True"
          },
          {
            "id": "answer4",
            "answer": "False"
          }
        ]
      }
    ],
    "title": "General Knowledge",
    "description": "A test on general knowledge",
    "minimumScore": 70,
    "mandatory": true,
    "qualificationTestType": "multipleChoice",
    "qualificationTestStatus": "active",
    "hidden": false,
    "lastModifiedDate": "2022-01-01T00:00:00.000Z",
    "answersMap": {
      "question1": "answer1",
      "question2": "answer3"
    }
  }
]
''';

final tLearningTrackModel = LearningTracksModel.fromJson(tLearningTrackJsonMap);
final tTrainingsModel = TrainingsModel.fromJson(tTrainingsJsonMap);

const trainingsContentModelJson = {
  'id': 'training1',
  'title': 'Introduction to Flutter',
  'duration': '10 hours',
  'profileImage': {
    'originalFilename': 'flutter_intro.png',
    'key': 'images/flutter_intro.png',
    'size': 2048,
  },
  'profileImageUrl': 'https://example.com/images/flutter_intro.png',
  'language': 'EN',
  'level': 'BEGINNER',
  'status': 'PUBLISHED',
  'skills': ['Dart', 'Flutter', 'Mobile Development'],
  'createdDate': '2023-01-01T00:00:00.000Z',
  'lastModifiedDate': '2023-01-02T00:00:00.000Z',
  'enrolledCount': 150,
  'organizationName': 'Tech Academy',
  'trainingType': 'SelfPaced',
};
