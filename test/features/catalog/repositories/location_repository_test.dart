import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/location_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/location_repository.dart';

import '../filter_bloc_test/seed/location_seed.dart';

class MockLocationDataSource extends Mock implements LocationDataSource {}

void main() {
  late LocationRepository locationRepository;
  late MockLocationDataSource mockLocationDataSource;

  const locale = 'en';
  final locations = locationSeed.map((json) => LocationModel.fromJson(json)).toList();

  setUp(() {
    mockLocationDataSource = MockLocationDataSource();
    locationRepository = LocationRepository(locationDataSource: mockLocationDataSource);
  });

  group('getLocations', () {
    test('should return list of locations from data source', () async {
      when(() => mockLocationDataSource.getLocations(locale)).thenAnswer((_) async => locations);

      final result = await locationRepository.getLocations(locale);

      expect(result, equals(locations));
      verify(() => mockLocationDataSource.getLocations(locale)).called(1);
    });

    test('should throw exception when data source fails', () {
      when(() => mockLocationDataSource.getLocations(locale))
          .thenThrow(Exception('Failed to load locations'));

      expect(
        () => locationRepository.getLocations(locale),
        throwsA(isA<Exception>()),
      );
      verify(() => mockLocationDataSource.getLocations(locale)).called(1);
    });
  });
}
