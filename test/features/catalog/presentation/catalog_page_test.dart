import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/learning_tracks_repository.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/trainings_repositories.dart';
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/learning_tracks_bloc/learning_tracks_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/pages/catalog_page.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/filter_bottom_sheet/filter_option_tile.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../filter_bloc_test/seed/sectors_seed.dart';
import '../shared/catalog_test_seed.dart';
import 'catalog_page_test.mocks.dart';

class MockLearningTracksBloc extends MockBloc<LearningTracksEvent, LearningTracksState>
    implements LearningTracksBloc {}

class MockTrainingsBloc extends MockBloc<TrainingsEvent, TrainingsState> implements TrainingsBloc {}

class MockFilterBloc extends MockBloc<FilterEvent, FilterState> implements FilterBloc {}

@GenerateNiceMocks([
  MockSpec<LearningTracksRepository>(),
  MockSpec<TrainingsRepository>(),
  MockSpec<SectorRepository>(),
  MockSpec<GetSortedTrainingProvidersUseCase>(),
])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final injector = GetIt.instance;

  late final MockLearningTracksBloc mockLearningTracksBloc;
  late final MockTrainingsBloc mockTrainingsBloc;
  late final MockLearningTracksRepository mockLearningTracksRepository;
  late final MockTrainingsRepository mockTrainingsRepository;
  late final MockSectorRepository mockSectorRepository;
  late final GetSortedTrainingProvidersUseCase mockGetSortedTrainingProvidersUseCase;
  late MockFilterBloc filterBloc;
  late MockAuthTokenProvider mockAuthTokenProvider;
  final sectors = tSectorsJson.map((json) => SectorModel.fromJson(json)).toList();

  setUpAll(() async {
    mockLearningTracksRepository = MockLearningTracksRepository();
    mockLearningTracksBloc = MockLearningTracksBloc();
    mockAuthTokenProvider = MockAuthTokenProvider();
    mockSectorRepository = MockSectorRepository();
    mockTrainingsRepository = MockTrainingsRepository();
    mockGetSortedTrainingProvidersUseCase = MockGetSortedTrainingProvidersUseCase();

    mockTrainingsBloc = MockTrainingsBloc();
    filterBloc = MockFilterBloc();

    injector
      ..registerFactory<LearningTracksBloc>(() => mockLearningTracksBloc)
      ..registerSingleton<AuthTokenProvider>(mockAuthTokenProvider)
      ..registerFactory<TrainingsBloc>(() => mockTrainingsBloc)
      ..registerSingleton<SectorRepository>(mockSectorRepository)
      ..registerFactory<GetSortedTrainingProvidersUseCase>(
        () => mockGetSortedTrainingProvidersUseCase,
      );

    ///Bloc State Stub
    whenListen(
      mockTrainingsBloc,
      Stream<TrainingsState>.value(
        TrainingsLoaded(
          tTrainingsModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
        ),
      ),
      initialState: const TrainingsInitial(),
    );

    when(mockSectorRepository.getSectors(any)).thenAnswer((_) async => sectors);

    // Mock the getActiveSectors method
    final activeSectorResponse = ActiveSectorResponse(
      content: sectors
          .map(
            (sector) => ActiveSectorModel(
              id: sector.id,
              sectorCode: 0,
              title: sector.title,
              status: 'ACTIVE',
            ),
          )
          .toList(),
      page: 0,
      size: sectors.length,
      totalRecords: sectors.length,
      totalPages: 1,
    );

    when(
      mockSectorRepository.getActiveSectors(
        locale: anyNamed('locale'),
        page: anyNamed('page'),
        size: anyNamed('size'),
        sort: anyNamed('sort'),
        shown: anyNamed('shown'),
      ),
    ).thenAnswer((_) async => activeSectorResponse);

    when(mockGetSortedTrainingProvidersUseCase.call('en')).thenAnswer(
      (_) async => [
        const TrainingProviderModel(
          id: '1',
          organizationName: 'Test Provider',
          commercialRegistration: 'CR123',
        ),
      ],
    );

    whenListen(
      mockLearningTracksBloc,
      Stream<LearningTracksState>.value(
        LearningTracksLoaded(
          tLearningTrackModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
          appliedFilter: const FilterModel(),
        ),
      ),
      initialState: LearningTracksInitial(),
    );

    whenListen(filterBloc, Stream<FilterState>.fromIterable([const FilterUpdated(FilterModel())]));

    ///Repositories stub
    when(
      mockLearningTracksRepository.getLearningTracks(
        const LearningTracksRequestParams(page: 0, size: 10),
      ),
    ).thenAnswer((_) => Future<LearningTracksModel>.value(tLearningTrackModel));

    when(
      mockTrainingsRepository.getTrainings(const TrainingsRequestParams(page: 0, size: 10)),
    ).thenAnswer((_) => Future<TrainingsModel>.value(tTrainingsModel));

    ///env setup
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Catalog Page Tests', () {
    testGoldens('trainings page scenario', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const CatalogPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'trainings_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      await tester.ensureVisible(find.text(LocaleKeys.filterModal_Sort.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Sort.tr()));
      await multiScreenGolden(
        tester,
        'sort_modal_bottom_sheet_trainings',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      await tester.tap(find.text(LocaleKeys.mostPopular.tr()));
      await tester.pumpAndSettle();
      await tester.ensureVisible(find.text(LocaleKeys.filterModal_Filter.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Filter.tr()));
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'filter_modal_bottom_sheet_trainings',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      await tester.tap(find.byIcon(Icons.clear));
      await tester.pumpAndSettle();
    });

    testGoldens('learning tracks scenario', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const CatalogPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await tester.ensureVisible(find.text(LocaleKeys.header_learningTracks.tr()));
      await tester.tap(find.text(LocaleKeys.header_learningTracks.tr()));
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'learning_tracks_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      await tester.ensureVisible(find.text(LocaleKeys.filterModal_Sort.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Sort.tr()));
      await multiScreenGolden(
        tester,
        'sort_modal_bottom_sheet_learning_tracks',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
      await tester.tap(find.text(LocaleKeys.mostPopular.tr()));

      await tester.pumpAndSettle();
      await tester.ensureVisible(find.text(LocaleKeys.filterModal_Filter.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Filter.tr()));
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'filter_modal_bottom_sheet_learning_tracks',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      ///Just opening several submenus but not generating it (this is done to test coverage)
      ///Not generating images because generation of "level" submenu is enough
      await tester.tap(find.text(LocaleKeys.sectors.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const ValueKey(Icons.keyboard_backspace_outlined)));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.sectors.tr()));
      await tester.pumpAndSettle();

      await tester.tap(find.byType(FilterOptionTile).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));

      await tester.tap(find.text(LocaleKeys.trainingDetails_apply.tr()));
      await tester.pumpAndSettle();

      await tester.tap(find.text(LocaleKeys.skills_level.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const ValueKey(Icons.keyboard_backspace_outlined)));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.skills_level.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.BASIC.tr()));
      await tester.tap(find.text(LocaleKeys.GENERAL.tr()));
      await tester.tap(find.text(LocaleKeys.ADVANCED.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));
      await tester.tap(find.text(LocaleKeys.trainingDetails_apply.tr()));
      await tester.pumpAndSettle();

      await tester.tap(find.text(LocaleKeys.header_trainingProvider.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.byKey(const ValueKey(Icons.keyboard_backspace_outlined)));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.header_trainingProvider.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.byType(FilterOptionTile).first);
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.trainingDetails_apply.tr()));
      await tester.pumpAndSettle();

      await tester.tap(find.text(LocaleKeys.duration.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.durations_ALL.tr()));
      await tester.tap(find.text(LocaleKeys.durations_MONTHS.tr()));
      await tester.tap(find.text(LocaleKeys.durations_WEEKS.tr()));
      await tester.tap(find.text(LocaleKeys.durations_DAYS.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));
      await tester.tap(find.byKey(const ValueKey(Icons.keyboard_backspace_outlined)));
      await tester.pumpAndSettle();

      await tester.tap(find.text(LocaleKeys.language.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.filterModal_english.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_arabic.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));
      await tester.tap(find.text(LocaleKeys.trainingDetails_apply.tr()));
      await tester.pumpAndSettle();

      await tester.tap(find.text(LocaleKeys.type.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.in_person.tr()));
      await tester.tap(find.text(LocaleKeys.trainingDetails_online.tr()));
      await tester.tap(find.text(LocaleKeys.selfPaced.tr()));
      await tester.tap(find.text(LocaleKeys.filterModal_Reset.tr()));
      await tester.tap(find.text(LocaleKeys.trainingDetails_apply.tr()));
      await tester.pumpAndSettle();

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const CatalogPage()),
        surfaceSize: Device.iphone11.size,
      );
    });
  });
}
