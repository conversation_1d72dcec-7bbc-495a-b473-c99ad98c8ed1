// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in national_skills_platform/test/features/catalog/presentation/catalog_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart'
    as _i7;
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart'
    as _i9;
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart'
    as _i13;
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart'
    as _i2;
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart' as _i3;
import 'package:national_skills_platform/features/catalog/domain/repositories/learning_tracks_repository.dart'
    as _i5;
import 'package:national_skills_platform/features/catalog/domain/repositories/trainings_repositories.dart'
    as _i8;
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart'
    as _i12;
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart'
    as _i4;
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart' as _i11;
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart'
    as _i10;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLearningTracksModel_0 extends _i1.SmartFake implements _i2.LearningTracksModel {
  _FakeLearningTracksModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeTrainingsModel_1 extends _i1.SmartFake implements _i3.TrainingsModel {
  _FakeTrainingsModel_1(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeActiveSectorResponse_2 extends _i1.SmartFake implements _i4.ActiveSectorResponse {
  _FakeActiveSectorResponse_2(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [LearningTracksRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockLearningTracksRepository extends _i1.Mock implements _i5.LearningTracksRepository {
  @override
  _i6.Future<_i2.LearningTracksModel> getLearningTracks(
    _i7.LearningTracksRequestParams? requestPrams,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#getLearningTracks, [requestPrams]),
        returnValue: _i6.Future<_i2.LearningTracksModel>.value(
          _FakeLearningTracksModel_0(
            this,
            Invocation.method(#getLearningTracks, [requestPrams]),
          ),
        ),
        returnValueForMissingStub: _i6.Future<_i2.LearningTracksModel>.value(
          _FakeLearningTracksModel_0(
            this,
            Invocation.method(#getLearningTracks, [requestPrams]),
          ),
        ),
      ) as _i6.Future<_i2.LearningTracksModel>);
}

/// A class which mocks [TrainingsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingsRepository extends _i1.Mock implements _i8.TrainingsRepository {
  @override
  _i6.Future<_i3.TrainingsModel> getTrainings(
    _i9.TrainingsRequestParams? requestPrams,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#getTrainings, [requestPrams]),
        returnValue: _i6.Future<_i3.TrainingsModel>.value(
          _FakeTrainingsModel_1(
            this,
            Invocation.method(#getTrainings, [requestPrams]),
          ),
        ),
        returnValueForMissingStub: _i6.Future<_i3.TrainingsModel>.value(
          _FakeTrainingsModel_1(
            this,
            Invocation.method(#getTrainings, [requestPrams]),
          ),
        ),
      ) as _i6.Future<_i3.TrainingsModel>);
}

/// A class which mocks [SectorRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSectorRepository extends _i1.Mock implements _i10.SectorRepository {
  @override
  _i6.Future<List<_i11.SectorModel>> getSectors(String? locale) => (super.noSuchMethod(
        Invocation.method(#getSectors, [locale]),
        returnValue: _i6.Future<List<_i11.SectorModel>>.value(
          <_i11.SectorModel>[],
        ),
        returnValueForMissingStub: _i6.Future<List<_i11.SectorModel>>.value(
          <_i11.SectorModel>[],
        ),
      ) as _i6.Future<List<_i11.SectorModel>>);

  @override
  _i6.Future<_i4.ActiveSectorResponse> getActiveSectors({
    required String? locale,
    int? page = 0,
    int? size = 10000,
    List<String>? sort,
    String? term,
    bool? shown,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#getActiveSectors, [], {
          #locale: locale,
          #page: page,
          #size: size,
          #sort: sort,
          #term: term,
          #shown: shown,
        }),
        returnValue: _i6.Future<_i4.ActiveSectorResponse>.value(
          _FakeActiveSectorResponse_2(
            this,
            Invocation.method(#getActiveSectors, [], {
              #locale: locale,
              #page: page,
              #size: size,
              #sort: sort,
              #term: term,
              #shown: shown,
            }),
          ),
        ),
        returnValueForMissingStub: _i6.Future<_i4.ActiveSectorResponse>.value(
          _FakeActiveSectorResponse_2(
            this,
            Invocation.method(#getActiveSectors, [], {
              #locale: locale,
              #page: page,
              #size: size,
              #sort: sort,
              #term: term,
              #shown: shown,
            }),
          ),
        ),
      ) as _i6.Future<_i4.ActiveSectorResponse>);

  @override
  List<_i11.SectorModel> convertActiveSectorsToSectorModels(
    List<_i4.ActiveSectorModel>? activeSectors,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#convertActiveSectorsToSectorModels, [
          activeSectors,
        ]),
        returnValue: <_i11.SectorModel>[],
        returnValueForMissingStub: <_i11.SectorModel>[],
      ) as List<_i11.SectorModel>);
}

/// A class which mocks [GetSortedTrainingProvidersUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetSortedTrainingProvidersUseCase extends _i1.Mock
    implements _i12.GetSortedTrainingProvidersUseCase {
  @override
  _i6.Future<List<_i13.TrainingProviderModel>> call(String? locale) => (super.noSuchMethod(
        Invocation.method(#call, [locale]),
        returnValue: _i6.Future<List<_i13.TrainingProviderModel>>.value(
          <_i13.TrainingProviderModel>[],
        ),
        returnValueForMissingStub: _i6.Future<List<_i13.TrainingProviderModel>>.value(
          <_i13.TrainingProviderModel>[],
        ),
      ) as _i6.Future<List<_i13.TrainingProviderModel>>);
}
