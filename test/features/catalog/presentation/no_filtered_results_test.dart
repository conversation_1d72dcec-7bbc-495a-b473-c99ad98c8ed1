import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/no_filtered_results.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('no filtered page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const Scaffold(body: NoFilteredResults())),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'no_filtered_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
