import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_data_source.dart';

import '../../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../shared/catalog_test_seed.dart';

void main() {
  late final TrainingsDataSource dataSource;
  late final MockDio mockDio;

  setUpAll(() {
    mockDio = MockDio();
    dataSource = TrainingsDataSource(dio: mockDio);
  });

  group('TrainingsDataSource Tests', () {
    test('returns TrainingsModel when response code is 200 or 201', () async {
      when(
        mockDio.get(
          ApiConstants.trainingCataloguePath,
          queryParameters: anyNamed('queryParameters'),
        ),
      ).thenAnswer(
        (_) async =>
            Response(requestOptions: RequestOptions(), data: tTrainingsJsonMap, statusCode: 200),
      );

      final result = await dataSource.getTrainingsList(tTrainingsRequestParams);

      expect(result, tTrainingsModel);
      verify(
        mockDio.get(
          ApiConstants.trainingCataloguePath,
          queryParameters: tTrainingsRequestParams.getParams(),
        ),
      );
      verifyNoMoreInteractions(mockDio);
    });

    test('throws DioException when response code is not 200 or 201', () {
      when(
        mockDio.get(
          ApiConstants.trainingCataloguePath,
          queryParameters: anyNamed('queryParameters'),
        ),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), statusCode: 400));

      expect(
        () => dataSource.getTrainingsList(tTrainingsRequestParams),
        throwsA(isInstanceOf<DioException>()),
      );
    });
  });

  test('throws DioException when Dio throws an exception', () {
    when(
      mockDio.get(ApiConstants.trainingCataloguePath, queryParameters: anyNamed('queryParameters')),
    ).thenThrow(
      DioException(requestOptions: RequestOptions(path: ApiConstants.trainingCataloguePath)),
    );

    expect(
      () => dataSource.getTrainingsList(tTrainingsRequestParams),
      throwsA(isInstanceOf<DioException>()),
    );
  });
}
