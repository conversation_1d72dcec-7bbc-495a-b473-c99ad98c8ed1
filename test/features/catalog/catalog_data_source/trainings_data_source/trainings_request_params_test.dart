import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';

void main() {
  group('TrainingsRequestParams', () {
    test('getParams returns correct map', () {
      const params = TrainingsRequestParams(
        page: 1,
        size: 10,
        skillLevels: 'skill1,skill2',
        sectors: 'sector1,sector2',
        domains: ['domain1', 'domain2'],
        trainingProviders: 'provider1,provider2',
        title: 'title',
        duration: 'duration',
        level: 'level',
        languages: 'language',
      );

      final result = params.getParams();

      expect(
        result,
        equals({
          'page': '1',
          'size': '10',
          'sort': SortOptionsEnum.enrolledCount.name,
          'skillLevels': 'skill1,skill2',
          'sectors': 'sector1,sector2',
          'domains': 'domain2',
          'trainingProviders': 'provider1,provider2',
          'title': 'title',
          'durations': 'duration',
          'levels': 'level',
          'languages': 'language',
        }),
      );
    });

    test('copyWith returns correct object', () {
      const params = TrainingsRequestParams.fallback();
      final copiedParams = params.copyWith(
        page: 2,
        size: 20,
        sortBy: SortOptionsEnum.enrolledCount,
        skillLevels: 'skill1,skill2',
        sectors: 'sector1,sector2',
        domains: ['domain1', 'domain2'],
        trainingProviders: 'provider1,provider2',
        title: 'title',
        duration: 'duration',
        level: 'level',
        languages: 'language',
        filterModel: const FilterModel(),
      );

      expect(copiedParams.page, equals(2));
      expect(copiedParams.size, equals(20));
      expect(copiedParams.sortBy, equals(SortOptionsEnum.enrolledCount));
      expect(copiedParams.skillLevels, equals('skill1,skill2'));
      expect(copiedParams.sectors, equals('sector1,sector2'));
      expect(copiedParams.domains, equals(['domain1', 'domain2']));
      expect(copiedParams.trainingProviders, equals('provider1,provider2'));
      expect(copiedParams.title, equals('title'));
      expect(copiedParams.duration, equals('duration'));
      expect(copiedParams.level, equals('level'));
      expect(copiedParams.languages, equals('language'));
      expect(copiedParams.filterModel, isNotNull);
    });
  });
}
