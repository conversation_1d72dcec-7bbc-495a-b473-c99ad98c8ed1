import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_data_source.dart';

import '../../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../shared/catalog_test_seed.dart';

void main() {
  late final LearningTracksDataSource dataSource;
  late final MockDio mockDio;

  setUpAll(() {
    mockDio = MockDio();
    dataSource = LearningTracksDataSource(dio: mockDio);
  });

  group('LearningTracksDataSource Tests', () {
    test('returns LearningTracksModel when response code is 200 or 201', () async {
      when(
        mockDio.get(
          ApiConstants.learningTracksCataloguePath,
          queryParameters: anyNamed('queryParameters'),
        ),
      ).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(),
          data: tLearningTrackJsonMap,
          statusCode: 200,
        ),
      );

      final result = await dataSource.getLearningTracks(tLearningTracksRequestParams);

      expect(result, tLearningTrackModel);
      verify(
        mockDio.get(
          ApiConstants.learningTracksCataloguePath,
          queryParameters: tLearningTracksRequestParams.getParams(),
        ),
      );
      verifyNoMoreInteractions(mockDio);
    });

    test('throws DioException when response code is not 200 or 201', () {
      when(
        mockDio.get(
          ApiConstants.learningTracksCataloguePath,
          queryParameters: anyNamed('queryParameters'),
        ),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), statusCode: 400));

      expect(
        () => dataSource.getLearningTracks(tLearningTracksRequestParams),
        throwsA(isInstanceOf<DioException>()),
      );
    });
  });

  test('throws DioException when Dio throws an exception', () {
    when(
      mockDio.get(
        ApiConstants.learningTracksCataloguePath,
        queryParameters: anyNamed('queryParameters'),
      ),
    ).thenThrow(
      DioException(requestOptions: RequestOptions(path: ApiConstants.learningTracksCataloguePath)),
    );

    expect(
      () => dataSource.getLearningTracks(tLearningTracksRequestParams),
      throwsA(isInstanceOf<DioException>()),
    );
  });
}
