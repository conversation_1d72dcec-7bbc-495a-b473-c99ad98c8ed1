import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';

void main() {
  group('LearningTracksRequestParams', () {
    test('getParams returns correct map', () {
      const requestParams = LearningTracksRequestParams(page: 1, size: 10);

      final result = requestParams.getParams();

      expect(
        result,
        equals({'page': '1', 'size': '10', 'sort': 'enrolledCount', 'forEmployer': 'false'}),
      );
    });

    test('getParams includes studyStreamTypes when present', () {
      const requestParams =
          LearningTracksRequestParams(page: 1, size: 10, studyStreamTypes: 'ONLINE,IN_PERSON');

      final result = requestParams.getParams();

      expect(
        result,
        equals({
          'page': '1',
          'size': '10',
          'sort': 'enrolledCount',
          'forEmployer': 'false',
          'studyStreamTypes': 'ONLINE,IN_PERSON',
        }),
      );
    });
  });
}
