import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/sector_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/sector_filter_bloc/sector_filter_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../presentation/catalog_page_test.dart';
import 'seed/sectors_seed.dart';

class MockSectorRepository extends Mock implements SectorRepository {}

void main() {
  late SectorFilterBloc sectorFilterBloc;
  late MockSectorRepository mockSectorRepository;
  late MockFilterBloc mockFilterBloc;
  late List<SectorModel> sectors;

  setUpAll(() async {
    await testEnvSetup();
    sectors = tSectorsJson.map((json) => SectorModel.fromJson(json)).toList();
  });

  setUp(() {
    mockSectorRepository = MockSectorRepository();
    mockFilterBloc = MockFilterBloc();

    when(() => mockFilterBloc.state).thenReturn(const FilterUpdated(FilterModel()));
    when(() => mockSectorRepository.getSectors(any())).thenAnswer((_) async => sectors);

    // Mock the getActiveSectors method
    final activeSectorResponse = ActiveSectorResponse(
      content: sectors
          .map(
            (sector) => ActiveSectorModel(
              id: sector.id,
              sectorCode: 0,
              title: sector.title,
              status: 'ACTIVE',
            ),
          )
          .toList(),
      page: 0,
      size: sectors.length,
      totalRecords: sectors.length,
      totalPages: 1,
    );

    when(
      () => mockSectorRepository.getActiveSectors(
        locale: any(named: 'locale'),
        page: any(named: 'page'),
        size: any(named: 'size'),
        sort: any(named: 'sort'),
        shown: any(named: 'shown'),
      ),
    ).thenAnswer((_) async => activeSectorResponse);

    sectorFilterBloc = SectorFilterBloc(
      sectorRepository: mockSectorRepository,
      filterBloc: mockFilterBloc,
    );
  });

  tearDown(() {
    sectorFilterBloc.close();
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  test('initial state is correct', () {
    expect(sectorFilterBloc.state, equals(const SectorFilterState()));
  });

  group('LoadActiveSectors', () {
    late ActiveSectorResponse activeSectorResponse;

    setUp(() {
      activeSectorResponse = ActiveSectorResponse(
        content: sectors
            .map(
              (sector) => ActiveSectorModel(
                id: sector.id,
                sectorCode: 0,
                title: sector.title,
                status: 'ACTIVE',
              ),
            )
            .toList(),
        page: 0,
        size: sectors.length,
        totalRecords: sectors.length,
        totalPages: 1,
      );
    });

    blocTest<SectorFilterBloc, SectorFilterState>(
      'emits loading state and then success state with active sectors',
      build: () => sectorFilterBloc,
      act: (bloc) => bloc.add(const LoadActiveSectors(locale: 'en')),
      expect: () => [
        const SectorFilterState(isLoadingActiveSectors: true),
        SectorFilterState(
          activeSectorResponse: activeSectorResponse,
        ),
      ],
      verify: (_) {
        verify(
          () => mockSectorRepository.getActiveSectors(
            locale: 'en',
            page: any(named: 'page'),
            size: any(named: 'size'),
            sort: any(named: 'sort'),
            shown: any(named: 'shown'),
          ),
        ).called(1);
      },
    );

    blocTest<SectorFilterBloc, SectorFilterState>(
      'emits loading state and then error state when repository fails',
      setUp: () {
        when(
          () => mockSectorRepository.getActiveSectors(
            locale: any(named: 'locale'),
            page: any(named: 'page'),
            size: any(named: 'size'),
            sort: any(named: 'sort'),
            shown: any(named: 'shown'),
          ),
        ).thenAnswer((_) async => throw Exception('Failed to load sectors'));
      },
      build: () => sectorFilterBloc,
      act: (bloc) => bloc.add(const LoadActiveSectors(locale: 'en')),
      expect: () => [
        const SectorFilterState(isLoadingActiveSectors: true),
        const SectorFilterState(
          activeError: 'Exception: Failed to load sectors',
        ),
      ],
    );

    blocTest<SectorFilterBloc, SectorFilterState>(
      'loads with selected sectors from filter bloc',
      setUp: () {
        when(() => mockFilterBloc.state).thenReturn(
          const FilterUpdated(
            FilterModel(sectorFilterModel: SectorFilterModel(selectedSectorIds: ['sector-1'])),
          ),
        );
      },
      build: () => sectorFilterBloc,
      act: (bloc) => bloc.add(const LoadActiveSectors(locale: 'en')),
      expect: () => [
        const SectorFilterState(isLoadingActiveSectors: true, selectedSectorIds: ['sector-1']),
        SectorFilterState(
          activeSectorResponse: activeSectorResponse,
          selectedSectorIds: const ['sector-1'],
        ),
      ],
    );
  });
}
