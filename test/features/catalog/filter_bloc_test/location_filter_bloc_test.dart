import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/location_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/location_repository.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/location_filter_bloc/location_filter_state.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import 'seed/location_seed.dart';

class MockLocationRepository extends Mock implements LocationRepository {}

class MockFilterBloc extends Mock implements FilterBloc {}

void main() {
  late LocationFilterBloc locationFilterBloc;
  late MockLocationRepository mockLocationRepository;
  late MockFilterBloc mockFilterBloc;
  late List<LocationModel> locations;

  setUpAll(() async {
    await testEnvSetup();
    locations = locationSeed.map((json) => LocationModel.fromJson(json)).toList();
  });

  setUp(() {
    mockLocationRepository = MockLocationRepository();
    mockFilterBloc = MockFilterBloc();

    when(() => mockFilterBloc.state).thenReturn(const FilterUpdated(FilterModel()));
    when(
      () => mockLocationRepository.getLocations(any()),
    ).thenAnswer((_) async => locations);

    locationFilterBloc = LocationFilterBloc(
      locationRepository: mockLocationRepository,
      filterBloc: mockFilterBloc,
    );
  });

  tearDown(() {
    locationFilterBloc.close();
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  test('initial state is correct', () {
    expect(locationFilterBloc.state, equals(const LocationFilterState()));
  });

  group('LoadLocations', () {
    blocTest<LocationFilterBloc, LocationFilterState>(
      'emits loading state and then success state with locations',
      build: () => locationFilterBloc,
      act: (bloc) => bloc.add(const LoadLocations(locale: 'en')),
      expect: () => [
        const LocationFilterState(),
        LocationFilterState(isLoading: false, locations: locations),
      ],
      verify: (_) {
        verify(() => mockLocationRepository.getLocations('en')).called(1);
      },
    );

    blocTest<LocationFilterBloc, LocationFilterState>(
      'emits loading state and then error state when data source fails',
      setUp: () {
        when(
          () => mockLocationRepository.getLocations(any()),
        ).thenAnswer((_) async => throw Exception('Failed to load locations'));
      },
      build: () => locationFilterBloc,
      act: (bloc) => bloc.add(const LoadLocations(locale: 'en')),
      expect: () => [
        const LocationFilterState(),
        LocationFilterState(
          isLoading: false,
          error: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<LocationFilterBloc, LocationFilterState>(
      'loads with selected locations from filter bloc',
      setUp: () {
        when(() => mockFilterBloc.state).thenReturn(
          const FilterUpdated(
            FilterModel(
              locationFilterModel: LocationFilterModel(
                selectedLocationIds: ['1', '2'],
              ),
            ),
          ),
        );
      },
      build: () => locationFilterBloc,
      act: (bloc) => bloc.add(const LoadLocations(locale: 'en')),
      expect: () => [
        const LocationFilterState(
          selectedLocationIds: ['1', '2'],
        ),
        LocationFilterState(
          isLoading: false,
          locations: locations,
          selectedLocationIds: ['1', '2'],
        ),
      ],
    );
  });
}
