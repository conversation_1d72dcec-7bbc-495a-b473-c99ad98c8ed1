import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';
import 'package:national_skills_platform/features/catalog/domain/use_cases/get_sorted_training_providers_use_case.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_event.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/training_provider_filter_bloc/training_provider_filter_state.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../presentation/catalog_page_test.dart';
import 'seed/training_providers_seed.dart';

class MockGetSortedTrainingProvidersUseCase extends Mock
    implements GetSortedTrainingProvidersUseCase {}

void main() {
  late TrainingProviderFilterBloc trainingProviderFilterBloc;
  late MockGetSortedTrainingProvidersUseCase mockGetSortedTrainingProvidersUseCase;
  late MockFilterBloc mockFilterBloc;
  late List<TrainingProviderModel> trainingProviders;

  setUpAll(() async {
    await testEnvSetup();
    trainingProviders =
        trainingProviderSeed.map((json) => TrainingProviderModel.fromJson(json)).toList();
  });

  setUp(() {
    mockGetSortedTrainingProvidersUseCase = MockGetSortedTrainingProvidersUseCase();
    mockFilterBloc = MockFilterBloc();

    when(() => mockFilterBloc.state).thenReturn(const FilterUpdated(FilterModel()));
    when(
      () => mockGetSortedTrainingProvidersUseCase(any()),
    ).thenAnswer((_) async => trainingProviders);

    trainingProviderFilterBloc = TrainingProviderFilterBloc(
      getSortedTrainingProvidersUseCase: mockGetSortedTrainingProvidersUseCase,
      filterBloc: mockFilterBloc,
    );
  });

  tearDown(() {
    trainingProviderFilterBloc.close();
  });

  tearDownAll(() async {
    await testEnvTearDown();
  });

  test('initial state is correct', () {
    expect(trainingProviderFilterBloc.state, equals(const TrainingProviderFilterState()));
  });

  group('LoadTrainingProviders', () {
    blocTest<TrainingProviderFilterBloc, TrainingProviderFilterState>(
      'emits loading state and then success state with training providers',
      build: () => trainingProviderFilterBloc,
      act: (bloc) => bloc.add(const LoadTrainingProviders(locale: 'en')),
      expect: () => [
        const TrainingProviderFilterState(),
        TrainingProviderFilterState(isLoading: false, trainingProviders: trainingProviders),
      ],
      verify: (_) {
        verify(() => mockGetSortedTrainingProvidersUseCase('en')).called(1);
      },
    );

    blocTest<TrainingProviderFilterBloc, TrainingProviderFilterState>(
      'emits loading state and then error state when use case fails',
      setUp: () {
        when(
          () => mockGetSortedTrainingProvidersUseCase(any()),
        ).thenAnswer((_) async => throw Exception('Failed to load providers'));
      },
      build: () => trainingProviderFilterBloc,
      act: (bloc) => bloc.add(const LoadTrainingProviders(locale: 'en')),
      expect: () => [
        const TrainingProviderFilterState(),
        TrainingProviderFilterState(
          isLoading: false,
          error: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<TrainingProviderFilterBloc, TrainingProviderFilterState>(
      'loads with selected providers from filter bloc',
      setUp: () {
        when(() => mockFilterBloc.state).thenReturn(
          const FilterUpdated(
            FilterModel(
              trainingProviderFilterModel: TrainingProviderFilterModel(
                selectedTrainingProviderIds: ['8a89cc21-ba08-489d-81ee-766f6d838813'],
              ),
            ),
          ),
        );
      },
      build: () => trainingProviderFilterBloc,
      act: (bloc) => bloc.add(const LoadTrainingProviders(locale: 'en')),
      expect: () => [
        const TrainingProviderFilterState(
          selectedTrainingProviderIds: ['8a89cc21-ba08-489d-81ee-766f6d838813'],
        ),
        TrainingProviderFilterState(
          isLoading: false,
          trainingProviders: trainingProviders,
          selectedTrainingProviderIds: const ['8a89cc21-ba08-489d-81ee-766f6d838813'],
        ),
      ],
    );
  });
}
