import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/duration_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/language_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/skill_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_filter_model.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_type_filter_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/filter_bloc/filter_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';

import '../presentation/catalog_page_test.dart';

void main() {
  group('FilterBloc', () {
    late FilterBloc filterBloc;

    setUp(() {
      filterBloc = FilterBloc(learningTracksBloc: MockLearningTracksBloc());
    });

    tearDown(() async {
      await filterBloc.close();
    });

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] when InitFilterPage is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const InitFilterPage(FilterModel())),
      expect: () => [const FilterUpdated(FilterModel())],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] with correct languageFilterModel when SetLanguage is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const SetLanguage(LanguageFilterModel(arabic: true))),
      expect: () => [
        const FilterUpdated(
          FilterModel(languageFilterModel: LanguageFilterModel(arabic: true)),
        ),
      ],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] with correct skillFilterModel when SetSkill is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const SetSkill(SkillFilterModel(basic: true))),
      expect: () => [
        const FilterUpdated(
          FilterModel(skillFilterModel: SkillFilterModel(basic: true)),
        ),
      ],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] with correct durationFilterModel when SetDuration is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const SetDuration(DurationFilterModel(days: true))),
      expect: () => [
        const FilterUpdated(FilterModel(durationFilterModel: DurationFilterModel(days: true))),
      ],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] with reset FilterModel and [ApplyFilters] event when ResetAllFilter is added.',
      build: () => filterBloc,
      act: (bloc) {
        bloc
          ..add(
            const InitFilterPage(FilterModel(durationFilterModel: DurationFilterModel(days: true))),
          )
          ..add(const ResetAllFilter());
      },
      expect: () => [
        const FilterUpdated(FilterModel(durationFilterModel: DurationFilterModel(days: true))),
        const FilterUpdated(FilterModel(), reset: true),
        const FiltersApplied(FilterModel()),
      ],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FiltersApplied] with correct FilterModel when ApplyFilters is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const ApplyFilters()),
      expect: () => [const FiltersApplied(FilterModel())],
    );
    blocTest<FilterBloc, FilterState>(
      'emits [FiltersApplied] with correct FilterModel when ApplyFilters is added. (TrainingsBloc)',
      build: () {
        final mockTrainingsBloc = MockTrainingsBloc();
        whenListen(
          mockTrainingsBloc,
          Stream.value(const TrainingsInitial()),
          initialState: const TrainingsInitial(),
        );
        return FilterBloc(trainingsBloc: mockTrainingsBloc);
      },
      act: (bloc) => bloc.add(const ApplyFilters()),
      expect: () => [const FiltersApplied(FilterModel())],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FiltersNotApplied] when DontApplyFilters is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const DontApplyFilters()),
      expect: () => [const FiltersNotApplied()],
    );

    blocTest<FilterBloc, FilterState>(
      'emits [FilterUpdated] with correct trainingTypeFilterModel when SetTrainingType is added.',
      build: () => filterBloc,
      act: (bloc) => bloc.add(const SetTrainingType(TrainingTypeFilterModel(selfPaced: true))),
      expect: () => [
        const FilterUpdated(
          FilterModel(trainingTypeFilterModel: TrainingTypeFilterModel(selfPaced: true)),
        ),
      ],
    );

    group('Training Provider Selection', () {
      blocTest<FilterBloc, FilterState>(
        'should add training provider when less than 6 are selected',
        build: () => filterBloc,
        act: (bloc) => bloc.add(const SetTrainingProviders(trainingProviderId: 'provider1')),
        expect: () => [
          const FilterUpdated(
            FilterModel(
              trainingProviderFilterModel: TrainingProviderFilterModel(
                selectedTrainingProviderIds: ['provider1'],
              ),
            ),
          ),
        ],
      );

      blocTest<FilterBloc, FilterState>(
        'should remove training provider when already selected',
        seed: () => const FilterUpdated(
          FilterModel(
            trainingProviderFilterModel: TrainingProviderFilterModel(
              selectedTrainingProviderIds: ['provider1'],
            ),
          ),
        ),
        build: () => filterBloc,
        act: (bloc) => bloc.add(const SetTrainingProviders(trainingProviderId: 'provider1')),
        expect: () => [const FilterUpdated(FilterModel())],
      );

      blocTest<FilterBloc, FilterState>(
        'should reset training providers when SetTrainingProviders is called without id',
        build: () => filterBloc,
        seed: () => const FilterUpdated(
          FilterModel(
            trainingProviderFilterModel: TrainingProviderFilterModel(
              selectedTrainingProviderIds: ['provider1', 'provider2'],
            ),
          ),
        ),
        act: (bloc) => bloc.add(const SetTrainingProviders()),
        expect: () => [const FilterUpdated(FilterModel())],
      );
    });
  });
}
