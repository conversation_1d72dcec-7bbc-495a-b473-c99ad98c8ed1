// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in national_skills_platform/test/features/catalog/repository/learning_tracks_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_data_source.dart'
    as _i3;
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart'
    as _i5;
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLearningTracksModel_0 extends _i1.SmartFake implements _i2.LearningTracksModel {
  _FakeLearningTracksModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LearningTracksDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockLearningTracksDataSource extends _i1.Mock implements _i3.LearningTracksDataSource {
  @override
  _i4.Future<_i2.LearningTracksModel> getLearningTracks(
          _i5.LearningTracksRequestParams? requestPrams) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLearningTracks,
          [requestPrams],
        ),
        returnValue: _i4.Future<_i2.LearningTracksModel>.value(_FakeLearningTracksModel_0(
          this,
          Invocation.method(
            #getLearningTracks,
            [requestPrams],
          ),
        )),
        returnValueForMissingStub:
            _i4.Future<_i2.LearningTracksModel>.value(_FakeLearningTracksModel_0(
          this,
          Invocation.method(
            #getLearningTracks,
            [requestPrams],
          ),
        )),
      ) as _i4.Future<_i2.LearningTracksModel>);
}
