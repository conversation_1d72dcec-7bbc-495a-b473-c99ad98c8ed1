import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_data_source.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/trainings_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/trainings_repositories.dart';

import 'trainings_repository_test.mocks.dart';

class MockTrainingsRequestParams extends Mock implements TrainingsRequestParams {}

@GenerateMocks([TrainingsDataSource])
void main() {
  group('TrainingsRepository', () {
    final mockDataSource = MockTrainingsDataSource();
    final repository = TrainingsRepository(dataSource: mockDataSource);
    final mockRequestParams = MockTrainingsRequestParams();

    test('returns TrainingsModel when the response code is 200', () async {
      when(
        mockDataSource.getTrainingsList(mockRequestParams),
      ).thenAnswer((_) async => const TrainingsModel(trainingsList: [], totalElements: 0));

      final result = await repository.getTrainings(mockRequestParams);

      expect(result, isInstanceOf<TrainingsModel>());
    });
  });
}
