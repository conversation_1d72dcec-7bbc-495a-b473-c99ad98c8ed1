import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/training_providers_datasource.dart';
import 'package:national_skills_platform/features/catalog/data/models/filter_models/training_provider_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/training_providers_repository.dart';

import '../filter_bloc_test/seed/training_providers_seed.dart';
import 'training_providers_repository_test.mocks.dart';

@GenerateMocks([TrainingProvidersDataSource])
void main() {
  late MockTrainingProvidersDataSource mockDataSource;
  late TrainingProvidersRepository repository;
  late List<TrainingProviderModel> trainingProviders;

  setUp(() {
    mockDataSource = MockTrainingProvidersDataSource();
    repository = TrainingProvidersRepository(dataSource: mockDataSource);
    trainingProviders =
        trainingProviderSeed.map((json) => TrainingProviderModel.fromJson(json)).toList();

    when(mockDataSource.getTrainingProviders(any)).thenAnswer((_) async => trainingProviders);
  });

  group('getTrainingProviders', () {
    test('should get List<TrainingProviderModel> from the datasource', () async {
      final result = await repository.getTrainingProviders('en');

      verify(mockDataSource.getTrainingProviders('en'));
      expect(result, equals(trainingProviders));
    });

    test('should return cached providers if available', () async {
      await repository.getTrainingProviders('en');

      final result = await repository.getTrainingProviders('en');

      verify(mockDataSource.getTrainingProviders('en')).called(1);
      expect(result, equals(trainingProviders));
    });
  });
}
