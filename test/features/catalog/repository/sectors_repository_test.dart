import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/home/<USER>/data_sources/sectors_datasource.dart';
import 'package:national_skills_platform/features/home/<USER>/models/active_sector_response.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sector_model.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_repository.dart';

import '../filter_bloc_test/seed/sectors_seed.dart';
import 'sectors_repository_test.mocks.dart';

// Test data for active sectors
final tActiveSectorsJson = {
  "content": [
    {"id": "sector-1", "sectorCode": 1, "title": "Sector 1", "status": "ACTIVE"},
    {"id": "sector-2", "sectorCode": 2, "title": "Sector 2", "status": "ACTIVE"},
  ],
  "page": 0,
  "size": 10,
  "totalRecords": 2,
  "totalPages": 1,
};

@GenerateMocks([SectorDataSource])
void main() {
  late MockSectorDataSource mockDataSource;
  late SectorRepository repository;

  setUp(() {
    mockDataSource = MockSectorDataSource();
    repository = SectorRepository(dataSource: mockDataSource);

    when(mockDataSource.getSectors(any)).thenAnswer((_) async => sectorsFromJson(tSectorsJson));
  });

  group('getSectors', () {
    test('should get List<SectorModel> from the data source', () async {
      final result = await repository.getSectors('en');

      verify(mockDataSource.getSectors('en'));
      expect(result, equals(sectorsFromJson(tSectorsJson)));
    });
  });

  group('getActiveSectors', () {
    test('should return ActiveSectorResponse when the call to data source is successful', () async {
      when(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).thenAnswer((_) async => tActiveSectorsJson);

      final result = await repository.getActiveSectors(locale: 'en');

      expect(result, isA<ActiveSectorResponse>());
      expect(result.content.length, 2);
      expect(result.content[0].id, 'sector-1');
      expect(result.content[1].id, 'sector-2');
    });

    test('should throw an exception when the call to data source is unsuccessful', () {
      when(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).thenThrow(Exception('Failed to load active sectors'));

      expect(() => repository.getActiveSectors(locale: 'en'), throwsA(isA<Exception>()));
    });

    test('should use cached data when available and no filters are applied', () async {
      when(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).thenAnswer((_) async => tActiveSectorsJson);

      await repository.getActiveSectors(locale: 'en');

      // Second call with same parameters should use cache
      await repository.getActiveSectors(locale: 'en');

      verify(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).called(1);
    });

    test('should not use cached data when filters are applied', () async {
      when(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).thenAnswer((_) async => tActiveSectorsJson);

      // First call should hit the data source
      await repository.getActiveSectors(locale: 'en');

      // Second call with different parameters should not use cache
      await repository.getActiveSectors(locale: 'en', term: 'search');

      // Verify data source was called twice
      verify(
        mockDataSource.getActiveSectors(
          locale: anyNamed('locale'),
          page: anyNamed('page'),
          size: anyNamed('size'),
          sort: anyNamed('sort'),
          term: anyNamed('term'),
          shown: anyNamed('shown'),
        ),
      ).called(2);
    });
  });
}
