import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_data_source.dart';
import 'package:national_skills_platform/features/catalog/data/data_sources/learning_tracks_request_params.dart';
import 'package:national_skills_platform/features/catalog/data/models/learning_tracks_model.dart';
import 'package:national_skills_platform/features/catalog/domain/repositories/learning_tracks_repository.dart';

import '../shared/catalog_test_seed.dart';
import 'learning_tracks_repository_test.mocks.dart';

class MockLearningTracksRequestParams extends Mock implements LearningTracksRequestParams {}

@GenerateNiceMocks([MockSpec<LearningTracksDataSource>()])
void main() {
  group('LearningTracksRepository', () {
    final mockDataSource = MockLearningTracksDataSource();
    final repository = LearningTracksRepository(dataSource: mockDataSource);
    final mockRequestParams = MockLearningTracksRequestParams();

    test('returns LearningTracksModel when the response code is 200', () async {
      when(
        mockDataSource.getLearningTracks(mockRequestParams),
      ).thenAnswer((_) async => tLearningTrackModel);

      final result = await repository.getLearningTracks(mockRequestParams);

      expect(result, isInstanceOf<LearningTracksModel>());
    });
  });
}
