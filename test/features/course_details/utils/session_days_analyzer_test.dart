import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/utils/session_days_analyzer.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

void main() {
  late SessionDaysAnalyzer analyzer;

  // Dummy objects for required fields
  const dummyImage = ImageModel(originalFilename: '', key: '', size: 0);
  const dummyTrainingStructure = null;
  const dummySector = null;
  const dummyDomain = null;

  LiveSession dummyLiveSessionWithDays(List<String> days) => LiveSession(
        id: '',
        startDate: null,
        durationHours: 0,
        durationMinutes: 0,
        recurringMeeting: false,
        zoomLink: '',
        zoomPassword: '',
        meetingId: '',
        status: '',
        daysOfWeek: days,
      );

  StudyStream dummyStudyStreamWithDays(List<String> days) => StudyStream(
        id: '',
        startDate: null,
        endDate: null,
        maxNumberOfParticipants: 0,
        currentNumberOfParticipants: 0,
        noLimits: false,
        liveSession: dummyLiveSessionWithDays(days),
        trainingId: '',
        type: StudyStreamType.NONE,
        status: '',
        cancelled: false,
        cardNotes: [],
        location: null,
        comment: '',
      );

  StudyStream dummyStudyStreamWithLiveSession(LiveSession? liveSession) => StudyStream(
        id: '',
        startDate: null,
        endDate: null,
        maxNumberOfParticipants: 0,
        currentNumberOfParticipants: 0,
        noLimits: false,
        liveSession: liveSession,
        trainingId: '',
        type: StudyStreamType.NONE,
        status: '',
        cancelled: false,
        cardNotes: [],
        location: null,
        comment: '',
      );

  setUp(() {
    analyzer = const SessionDaysAnalyzer();
  });

  group('SessionDaysAnalyzer', () {
    test('returns empty string when trainingDetailsModel is null', () {
      expect(analyzer.getSessionDaysInfo(null), '');
    });

    test('returns empty string when studyStreams is empty', () {
      const model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(analyzer.getSessionDaysInfo(model), '');
    });

    test('returns weekends pattern when sessions are only on weekends', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithDays([
            LocaleKeys.freq_days_of_week_friday.tr(),
            LocaleKeys.freq_days_of_week_saturday.tr(),
          ]),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(
        analyzer.getSessionDaysInfo(model),
        ', ${LocaleKeys.freq_weekend.tr().capitalizeFirst()}',
      );
    });

    test('returns weekdays pattern when sessions are only on weekdays', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithDays([
            LocaleKeys.freq_days_of_week_sunday.tr(),
            LocaleKeys.freq_days_of_week_monday.tr(),
            LocaleKeys.freq_days_of_week_tuesday.tr(),
          ]),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(
        analyzer.getSessionDaysInfo(model),
        ', ${LocaleKeys.freq_weekdays.tr().capitalizeFirst()}',
      );
    });

    test('returns empty string when sessions are on both weekends and weekdays', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithDays([
            LocaleKeys.freq_days_of_week_friday.tr(),
            LocaleKeys.freq_days_of_week_monday.tr(),
          ]),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(analyzer.getSessionDaysInfo(model), '');
    });

    test('returns empty string when liveSession is null', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithLiveSession(null),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(analyzer.getSessionDaysInfo(model), '');
    });

    test('returns empty string when daysOfWeek is empty', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithDays([]),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(analyzer.getSessionDaysInfo(model), '');
    });

    test('analyzes multiple study streams correctly', () {
      final model = TrainingDetailsModel(
        id: '',
        title: '',
        description: '',
        overview: '',
        language: '',
        languageCode: '',
        duration: '',
        durationMin: 0,
        durationMax: 0,
        level: '',
        skillLevel: '',
        profileImage: dummyImage,
        profileImageUrl: '',
        status: '',
        createdDate: null,
        lastModifiedDate: null,
        requirements: [],
        outcomes: [],
        skills: [],
        trainingStructure: dummyTrainingStructure,
        qualificationTests: [],
        sector: dummySector,
        domain: dummyDomain,
        organizationName: '',
        trainingProviderName: '',
        enrolledCount: 0,
        avatarUrl: '',
        organizationId: '',
        promoVideoUrl: '',
        type: TrainingType.none,
        seatingCapacities: [],
        studyStreams: [
          dummyStudyStreamWithDays([
            LocaleKeys.freq_days_of_week_sunday.tr(),
          ]),
          dummyStudyStreamWithDays([
            LocaleKeys.freq_days_of_week_monday.tr(),
          ]),
        ],
        copiedFromId: '',
        version: 1,
        rootId: '',
        createdBy: '',
        instructorPermission: '',
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(
        analyzer.getSessionDaysInfo(model),
        ', ${LocaleKeys.freq_weekdays.tr().capitalizeFirst()}',
      );
    });
  });
}
