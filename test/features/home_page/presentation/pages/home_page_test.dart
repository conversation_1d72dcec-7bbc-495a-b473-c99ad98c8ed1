import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/core/utils/merge_applicant_training_lists.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';
import 'package:national_skills_platform/features/home/<USER>/home_page_bloc/home_page_bloc.dart';
import 'package:national_skills_platform/features/home/<USER>/pages/home_page.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../../my_learnings/my_learnings_seed.dart';
import '../../../profile/profile_test_seed.dart';

class MockHomePageBloc extends MockBloc<HomePageEvent, HomePageState> implements HomePageBloc {}

class MockUserBloc extends MockBloc<UserEvent, UserState> implements UserBloc {}

class MockMyLearningsBloc extends MockBloc<MyLearningsEvent, MyLearningsState>
    implements MyLearningsBloc {}

class MockTrainingsBloc extends MockBloc<TrainingsEvent, TrainingsState> implements TrainingsBloc {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockHomePageBloc = MockHomePageBloc();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final mockUserBloc = MockUserBloc();
  final mockMyLearningsBloc = MockMyLearningsBloc();
  final mockTrainingsBloc = MockTrainingsBloc();
  final myLearningsModel = MyLearningsModel.fromJson(tMyLearningsJson);
  final tTrainingsModel = TrainingsModel.fromJson(tTrainingsJsonMap);

  setUpAll(() async {
    GetIt.instance
      ..registerFactory<UserBloc>(() => mockUserBloc)
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerFactory<HomePageBloc>(() => mockHomePageBloc)
      ..registerFactory<MyLearningsBloc>(() => mockMyLearningsBloc)
      ..registerFactory<TrainingsBloc>(() => mockTrainingsBloc);

    whenListen(
      mockHomePageBloc,
      Stream<HomePageState>.value(
        const HomePageState(isSectorsLoading: false),
      ),
      initialState: const HomePageState(),
    );

    whenListen(
      mockUserBloc,
      Stream<UserState>.value(
        UserState(
          isUserDataLoading: false,
          userData: UserModel.fromJson(tUserDataJson),
          isUserAvatarLoading: false,
          userAvatar: '',
        ),
      ),
      initialState: const UserState(),
    );

    whenListen(
      mockMyLearningsBloc,
      Stream<MyLearningsState>.value(
        MyLearningsState(
          myLearningsModel: myLearningsModel,
          allRecentTrainings: mergeApplicantTrainingLists(myLearningsModel),
        ),
      ),
      initialState: MyLearningsState(),
    );

    whenListen(
      mockTrainingsBloc,
      Stream<TrainingsState>.value(
        TrainingsLoaded(
          tTrainingsModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
        ),
      ),
      initialState: const TrainingsInitial(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Home Page Golden Tests', () {
    testGoldens('home page auth state', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(true));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const HomePage(authState: AuthStateEnum.loggedIn)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'home_page_auth_state', devices: [Device.iphone11]);

      // scroll down
      await tester.drag(find.byType(HomePage), const Offset(0, -500));
      await tester.pumpAndSettle();

      await multiScreenGolden(tester, 'home_page_auth_state_scrolled', devices: [Device.iphone11]);
    });

    testGoldens('home page non-auth state', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(false));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const HomePage(authState: AuthStateEnum.notLoggedIn)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'home_page_non_auth_state', devices: [Device.iphone11]);

      //scroll down
      await tester.drag(find.byType(HomePage), const Offset(0, -500));
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'home_page_non_auth_state_scrolled',
        devices: [Device.iphone11],
      );
    });
  });
}
