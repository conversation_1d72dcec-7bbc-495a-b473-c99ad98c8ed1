import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';
import 'package:national_skills_platform/features/home/<USER>/pages/sectors_page.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Sectors Golden Tests', () {
    testGoldens('sectors page', (tester) async {
      // Create test data for SectorFeatureModel
      final testSectors = [
        SectorFeatureModel(
          id: 'sector-1',
          title: 'Information Technology',
          imageUrl: 'https://example.com/image1.jpg',
          order: 0,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: 'sector-2',
          title: 'Healthcare',
          imageUrl: 'https://example.com/image2.jpg',
          order: 1,
          dateAdded: DateTime.now(),
        ),
        SectorFeatureModel(
          id: 'sector-3',
          title: 'Education',
          imageUrl: 'https://example.com/image3.jpg',
          order: 2,
          dateAdded: DateTime.now(),
        ),
      ];

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: SectorsPage(sectors: testSectors)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'sectors_page', devices: [Device.iphone11]);
    });
  });
}
