// Mocks generated by <PERSON>cki<PERSON> 5.4.5 from annotations
// in national_skills_platform/test/features/home_page/presentation/bloc/home_page_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart'
    as _i3;
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_features_repository.dart'
    as _i6;
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart'
    as _i2;
import 'package:national_skills_platform/features/my_learnings/domain/repositories/my_learnings_repository.dart'
    as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeMyLearningsModel_0 extends _i1.SmartFake implements _i2.MyLearningsModel {
  _FakeMyLearningsModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeSectorsFeaturesResponseModel_1 extends _i1.SmartFake
    implements _i3.SectorsFeaturesResponseModel {
  _FakeSectorsFeaturesResponseModel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(parent, parentInvocation);
}

/// A class which mocks [MyLearningsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockMyLearningsRepository extends _i1.Mock implements _i4.MyLearningsRepository {
  MockMyLearningsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<(_i2.MyLearningsModel, List<_i2.ApplicantTraining>)> getMyLearnings() =>
      (super.noSuchMethod(
        Invocation.method(#getMyLearnings, []),
        returnValue: _i5.Future<(_i2.MyLearningsModel, List<_i2.ApplicantTraining>)>.value((
          _FakeMyLearningsModel_0(
            this,
            Invocation.method(#getMyLearnings, []),
          ),
          <_i2.ApplicantTraining>[],
        )),
      ) as _i5.Future<(_i2.MyLearningsModel, List<_i2.ApplicantTraining>)>);
}

/// A class which mocks [SectorsFeaturesRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSectorsFeaturesRepository extends _i1.Mock implements _i6.SectorsFeaturesRepository {
  MockSectorsFeaturesRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i3.SectorsFeaturesResponseModel> getSectorsFeatures() => (super.noSuchMethod(
        Invocation.method(#getSectorsFeatures, []),
        returnValue: _i5.Future<_i3.SectorsFeaturesResponseModel>.value(
          _FakeSectorsFeaturesResponseModel_1(
            this,
            Invocation.method(#getSectorsFeatures, []),
          ),
        ),
      ) as _i5.Future<_i3.SectorsFeaturesResponseModel>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(#clearCache, []),
        returnValueForMissingStub: null,
      );
}
