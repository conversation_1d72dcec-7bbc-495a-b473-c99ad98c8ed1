import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/home/<USER>/models/sectors_features_model.dart';
import 'package:national_skills_platform/features/home/<USER>/repositories/sectors_features_repository.dart';
import 'package:national_skills_platform/features/home/<USER>/home_page_bloc/home_page_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/domain/repositories/my_learnings_repository.dart';

import '../../../catalog/presentation/catalog_page_test.mocks.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import 'home_page_bloc_test.mocks.dart';

@GenerateMocks([MyLearningsRepository, SectorsFeaturesRepository])
void main() {
  late MockTrainingsRepository mockTrainingsRepository;
  late MockSectorsFeaturesRepository mockSectorsFeaturesRepository;
  late HomePageBloc homePageBloc;

  setUp(() {
    mockTrainingsRepository = MockTrainingsRepository();
    mockSectorsFeaturesRepository = MockSectorsFeaturesRepository();
    homePageBloc = HomePageBloc(sectorsFeaturesRepository: mockSectorsFeaturesRepository);
  });

  tearDown(() {
    homePageBloc.close();
  });

  blocTest<HomePageBloc, HomePageState>(
    'emits correct states when HomePageLoad event is added',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      when(mockSectorsFeaturesRepository.getSectorsFeatures())
          .thenAnswer((_) async => const SectorsFeaturesResponseModel(sectors: []));
      return homePageBloc;
    },
    act: (bloc) => bloc.add(const HomePageLoad(locale: 'en')),
    expect: () => [
      const HomePageState(),
      const HomePageState(
        isSectorsLoading: false,
        sectors: SectorsFeaturesResponseModel(sectors: []),
      ),
    ],
  );

  blocTest<HomePageBloc, HomePageState>(
    'emits correct states when getTrainings fails',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => throw Exception(''));
      when(mockSectorsFeaturesRepository.getSectorsFeatures())
          .thenAnswer((_) async => const SectorsFeaturesResponseModel(sectors: []));
      return homePageBloc;
    },
    act: (bloc) => bloc.add(const HomePageLoad(locale: 'en')),
    expect: () => [
      const HomePageState(),
      const HomePageState(
        isSectorsLoading: false,
        sectors: SectorsFeaturesResponseModel(sectors: []),
      ),
    ],
  );

  blocTest<HomePageBloc, HomePageState>(
    'emits correct states when getCoursesInProgress fails',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      when(mockSectorsFeaturesRepository.getSectorsFeatures())
          .thenAnswer((_) async => const SectorsFeaturesResponseModel(sectors: []));
      return homePageBloc;
    },
    act: (bloc) => bloc.add(const HomePageLoad(locale: 'en')),
    expect: () => [
      const HomePageState(),
      const HomePageState(
        isSectorsLoading: false,
        sectors: SectorsFeaturesResponseModel(sectors: []),
      ),
    ],
  );

  blocTest<HomePageBloc, HomePageState>(
    'emits correct states when getSectors fails',
    build: () {
      when(mockTrainingsRepository.getTrainings(any)).thenAnswer((_) async => tTrainingsModel);
      when(mockSectorsFeaturesRepository.getSectorsFeatures())
          .thenAnswer((_) async => throw Exception('Sectors error'));
      return homePageBloc;
    },
    act: (bloc) => bloc.add(const HomePageLoad(locale: 'en')),
    expect: () => [
      const HomePageState(),
      const HomePageState(
        isSectorsLoading: false,
        sectorsError: 'Exception: Sectors error',
      ),
    ],
  );
}
