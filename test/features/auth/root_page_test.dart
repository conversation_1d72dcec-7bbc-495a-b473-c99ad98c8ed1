import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/shared/build_loader.dart';
import 'package:national_skills_platform/root_page.dart';
import 'package:national_skills_platform/router.dart';
import 'package:provider/src/provider.dart';

import '../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../shared_test_utils/nsp_test_wrapper.dart';
import '../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';

@GenerateNiceMocks([MockSpec<GoRouter>()])
void main() {
  late MockAuthTokenProvider mockAuthTokenProvider;
  final mockRouter = MockGoRouter();

  setUp(() async {
    await testEnvSetup();
    mockAuthTokenProvider = MockAuthTokenProvider();
    GetIt.instance
      ..registerSingleton<AuthTokenProvider>(mockAuthTokenProvider)
      ..registerSingleton<GoRouter>(mockRouter)
      ..registerSingleton<Alice>(Alice());
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  testWidgets('RootPage shows BuildLoader and navigates to HomePage when token is available', (
    tester,
  ) async {
    when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => 'token');

    await tester.pumpWidget(
      MaterialApp(home: Provider.value(value: GetIt.instance, child: const RootPage())),
    );

    expect(find.byType(BuildLoader), findsOneWidget);
    verify(GetIt.instance<GoRouter>().goNamed(Routes.getStarted.name, extra: anyNamed('extra')));
  });
}
