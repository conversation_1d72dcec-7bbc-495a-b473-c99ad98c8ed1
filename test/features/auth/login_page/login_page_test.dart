import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/annotations.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/auth/presentation/pages/login_page.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import 'login_page_test.mocks.dart';

@GenerateMocks([SharedPreferences])
@GenerateNiceMocks([MockSpec<AuthBloc>()])
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    GetIt.instance.registerFactory<AuthBloc>(MockAuthBloc.new);
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Golden Tests', () {
    testGoldens('login page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const LoginPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'login_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
