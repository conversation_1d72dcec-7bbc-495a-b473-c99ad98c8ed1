// Mocks generated by Mockito 5.4.5 from annotations
// in national_skills_platform/test/features/auth/login_page/login_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:bloc/bloc.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart' as _i2;
import 'package:shared_preferences/src/shared_preferences_legacy.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthState_0 extends _i1.SmartFake implements _i2.AuthState {
  _FakeAuthState_0(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

/// A class which mocks [SharedPreferences].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedPreferences extends _i1.Mock implements _i3.SharedPreferences {
  MockSharedPreferences() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> getKeys() => (super.noSuchMethod(
        Invocation.method(#getKeys, []),
        returnValue: <String>{},
      ) as Set<String>);

  @override
  Object? get(String? key) => (super.noSuchMethod(Invocation.method(#get, [key])) as Object?);

  @override
  bool? getBool(String? key) => (super.noSuchMethod(Invocation.method(#getBool, [key])) as bool?);

  @override
  int? getInt(String? key) => (super.noSuchMethod(Invocation.method(#getInt, [key])) as int?);

  @override
  double? getDouble(String? key) =>
      (super.noSuchMethod(Invocation.method(#getDouble, [key])) as double?);

  @override
  String? getString(String? key) =>
      (super.noSuchMethod(Invocation.method(#getString, [key])) as String?);

  @override
  bool containsKey(String? key) => (super.noSuchMethod(
        Invocation.method(#containsKey, [key]),
        returnValue: false,
      ) as bool);

  @override
  List<String>? getStringList(String? key) =>
      (super.noSuchMethod(Invocation.method(#getStringList, [key])) as List<String>?);

  @override
  _i4.Future<bool> setBool(String? key, bool? value) => (super.noSuchMethod(
        Invocation.method(#setBool, [key, value]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> setInt(String? key, int? value) => (super.noSuchMethod(
        Invocation.method(#setInt, [key, value]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> setDouble(String? key, double? value) => (super.noSuchMethod(
        Invocation.method(#setDouble, [key, value]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> setString(String? key, String? value) => (super.noSuchMethod(
        Invocation.method(#setString, [key, value]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> setStringList(String? key, List<String>? value) => (super.noSuchMethod(
        Invocation.method(#setStringList, [key, value]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> remove(String? key) => (super.noSuchMethod(
        Invocation.method(#remove, [key]),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> commit() => (super.noSuchMethod(
        Invocation.method(#commit, []),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> clear() => (super.noSuchMethod(
        Invocation.method(#clear, []),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> reload() => (super.noSuchMethod(
        Invocation.method(#reload, []),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [AuthBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthBloc extends _i1.Mock implements _i2.AuthBloc {
  @override
  _i2.AuthState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeAuthState_0(this, Invocation.getter(#state)),
        returnValueForMissingStub: _FakeAuthState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.AuthState);

  @override
  _i4.Stream<_i2.AuthState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i4.Stream<_i2.AuthState>.empty(),
        returnValueForMissingStub: _i4.Stream<_i2.AuthState>.empty(),
      ) as _i4.Stream<_i2.AuthState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void add(_i2.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(#add, [event]),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i2.AuthEvent? event) => super.noSuchMethod(
        Invocation.method(#onEvent, [event]),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.AuthState? state) => super.noSuchMethod(
        Invocation.method(#emit, [state]),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i2.AuthEvent>(
    _i5.EventHandler<E, _i2.AuthState>? handler, {
    _i5.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(#on, [handler], {#transformer: transformer}),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(_i5.Transition<_i2.AuthEvent, _i2.AuthState>? transition) => super.noSuchMethod(
        Invocation.method(#onTransition, [transition]),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> close() => (super.noSuchMethod(
        Invocation.method(#close, []),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void onChange(_i5.Change<_i2.AuthState>? change) => super.noSuchMethod(
        Invocation.method(#onChange, [change]),
        returnValueForMissingStub: null,
      );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
        Invocation.method(#addError, [error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
        Invocation.method(#onError, [error, stackTrace]),
        returnValueForMissingStub: null,
      );
}
