import 'package:flutter_alice/alice.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/auth/data/data_source/data_source/auth_data_source.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';
import 'package:national_skills_platform/features/auth/domain/repositories/auth_repository.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import 'auth_repository_test.mocks.dart';

@GenerateMocks([AuthDataSource])
void main() {
  group('AuthRepository', () {
    final mockDataSource = MockAuthDataSource();
    final mockAuthTokenProvider = MockAuthTokenProvider();
    final repository = AuthRepository(
      dataSource: mockDataSource,
      authTokenProvider: mockAuthTokenProvider,
    );
    const mockAuthModel = AuthModel(codeVerifier: 'verifier', codeChallenge: 'challenge');
    const mockRefreshTokenModel = RefreshTokenModel(refreshToken: 'refresh');
    GetIt.instance
      ..registerSingleton<Alice>(Alice())
      ..registerSingleton<GoRouter>(MockGoRouter());

    test('signIn successfully updates tokens', () async {
      when(
        mockDataSource.signInWithToken(mockAuthModel),
      ).thenAnswer((_) => Future.value(('token', 'refreshToken')));

      await repository.signIn(mockAuthModel);

      verify(mockAuthTokenProvider.updateTokens('token', 'refreshToken')).called(1);
    });

    test('refreshToken successfully updates tokens', () async {
      when(
        mockDataSource.refreshToken(mockRefreshTokenModel),
      ).thenAnswer((_) => Future.value(('newToken', 'newRefreshToken')));

      await repository.refreshToken(mockRefreshTokenModel);

      verify(mockAuthTokenProvider.updateTokens('newToken', 'newRefreshToken')).called(1);
    });

    test('logout returns a string', () async {
      when(mockAuthTokenProvider.getRefreshToken()).thenAnswer((_) => Future.value('refreshToken'));
      when(
        mockDataSource.logout(refreshToken: 'refreshToken'),
      ).thenAnswer((_) => Future.value('logoutToken'));

      final result = await repository.logout();

      expect(result, equals('logoutToken'));
      verify(mockAuthTokenProvider.invalidateToken()).called(1);
    });
  });
}
