// Mocks generated by Mockito 5.4.5 from annotations
// in national_skills_platform/test/features/auth/auth_repository/auth_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;
import 'package:national_skills_platform/features/auth/data/data_source/data_source/auth_data_source.dart'
    as _i2;
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthDataSource extends _i1.Mock implements _i2.AuthDataSource {
  MockAuthDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<(String, String)> signInWithToken(_i4.AuthModel? authModel) => (super.noSuchMethod(
        Invocation.method(#signInWithToken, [authModel]),
        returnValue: _i3.Future<(String, String)>.value((
          _i5.dummyValue<String>(
            this,
            Invocation.method(#signInWithToken, [authModel]),
          ),
          _i5.dummyValue<String>(
            this,
            Invocation.method(#signInWithToken, [authModel]),
          ),
        )),
      ) as _i3.Future<(String, String)>);

  @override
  _i3.Future<(String, String)> refreshToken(
    _i4.RefreshTokenModel? refreshTokenModel,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#refreshToken, [refreshTokenModel]),
        returnValue: _i3.Future<(String, String)>.value((
          _i5.dummyValue<String>(
            this,
            Invocation.method(#refreshToken, [refreshTokenModel]),
          ),
          _i5.dummyValue<String>(
            this,
            Invocation.method(#refreshToken, [refreshTokenModel]),
          ),
        )),
      ) as _i3.Future<(String, String)>);

  @override
  _i3.Future<String> logout({required String? refreshToken}) => (super.noSuchMethod(
        Invocation.method(#logout, [], {#refreshToken: refreshToken}),
        returnValue: _i3.Future<String>.value(
          _i5.dummyValue<String>(
            this,
            Invocation.method(#logout, [], {#refreshToken: refreshToken}),
          ),
        ),
      ) as _i3.Future<String>);
}
