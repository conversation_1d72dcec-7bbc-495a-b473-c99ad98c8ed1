import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/auth/presentation/pages/get_started_page.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Get started Golden Test', () {
    testGoldens('get started page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const GetStartedPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.languages_AR.tr()));
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.languages_EN.tr()));

      await multiScreenGolden(
        tester,
        'get_started_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
