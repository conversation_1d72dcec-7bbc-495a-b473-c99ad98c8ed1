import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/auth/domain/repositories/auth_repository.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:national_skills_platform/router.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../shared/seed/auth_seed.dart';
import 'auth_bloc_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<AuthRepository>(),
  MockSpec<UserBloc>(),
  MockSpec<GoRouterDelegate>(),
  MockSpec<RouteMatchList>(),
  MockSpec<RouteMatch>(),
])
void main() {
  late final MockAuthRepository mockAuthRepository;
  late final MockUserBloc mockUserBloc;
  late final MockAuthTokenProvider mockAuthTokenProvider;
  final mockGoRouter = MockGoRouter();
  final mockRouterDelegate = MockGoRouterDelegate();
  final mockRouteMatchList = MockRouteMatchList();
  final mockRouteMatch = MockRouteMatch();

  setUpAll(() {
    mockAuthRepository = MockAuthRepository();
    mockUserBloc = MockUserBloc();
    mockAuthTokenProvider = MockAuthTokenProvider();

    GetIt.instance.registerSingleton<GoRouter>(mockGoRouter);
  });

  blocTest<AuthBloc, AuthState>(
    'should throw error if auth token is null',
    build: () {
      return AuthBloc(
        authRepository: mockAuthRepository,
        userBloc: mockUserBloc,
        authTokenProvider: mockAuthTokenProvider,
      );
    },
    act: (bloc) => bloc.add(Authenticate(authModel: const AuthTestSeed().invalidMockAuthModel)),
    expect: () => [const AuthLoading(), const AuthError(errorMsg: 'Authentication Error')],
    verify: (_) => verifyNever(mockUserBloc.add(any)),
  );

  blocTest<AuthBloc, AuthState>(
    'Auth Success',
    build: () => AuthBloc(
      authRepository: mockAuthRepository,
      userBloc: mockUserBloc,
      authTokenProvider: mockAuthTokenProvider,
    ),
    act: (bloc) => bloc.add(Authenticate(authModel: const AuthTestSeed().validMockAuthModel)),
    expect: () => [const AuthLoading(), const AuthSuccess()],
    verify: (_) => verify(mockUserBloc.add(const UserEvent.resetUserData())),
  );

  blocTest<AuthBloc, AuthState>(
    'Logout Success',
    build: () => AuthBloc(
      authRepository: mockAuthRepository,
      userBloc: mockUserBloc,
      authTokenProvider: mockAuthTokenProvider,
    ),
    setUp: () {
      when(mockAuthRepository.logout()).thenAnswer((_) async => 'token');
    },
    act: (bloc) => bloc.add(const Logout()),
    expect: () => [const AuthLoading(), const LogoutSuccess(token: 'token')],
    verify: (_) => verifyNever(mockUserBloc.add(any)),
  );

  blocTest<AuthBloc, AuthState>(
    'Logout error',
    build: () => AuthBloc(
      authRepository: mockAuthRepository,
      userBloc: mockUserBloc,
      authTokenProvider: mockAuthTokenProvider,
    ),
    setUp: () {
      when(mockAuthRepository.logout()).thenAnswer((_) async => throw Exception());
    },
    act: (bloc) => bloc.add(const Logout()),
    expect: () => [
      const AuthLoading(),
      LogoutError(errorMsg: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
    ],
    verify: (_) => verifyNever(mockUserBloc.add(any)),
  );

  blocTest<AuthBloc, AuthState>(
    'Auth Exception',
    build: () {
      when(
        mockAuthRepository.signIn(const AuthTestSeed().validMockAuthModel),
      ).thenAnswer((_) async => throw Exception());

      return AuthBloc(
        authRepository: mockAuthRepository,
        userBloc: mockUserBloc,
        authTokenProvider: mockAuthTokenProvider,
      );
    },
    act: (bloc) => bloc.add(Authenticate(authModel: const AuthTestSeed().validMockAuthModel)),
    expect: () => [
      const AuthLoading(),
      AuthError(errorMsg: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
    ],
    verify: (_) => verify(mockUserBloc.add(const UserEvent.resetUserData())),
  );

  blocTest<AuthBloc, AuthState>(
    'Refresh Token',
    build: () => AuthBloc(
      authRepository: mockAuthRepository,
      userBloc: mockUserBloc,
      authTokenProvider: mockAuthTokenProvider,
    ),
    setUp: () {
      when(mockAuthRepository.refreshToken(any)).thenAnswer((_) async {});
    },
    act: (bloc) => bloc.add(RefreshToken(const AuthTestSeed().refreshTokenModel)),
    expect: () => [const RefreshSuccess()],
    verify: (_) => verifyNever(mockUserBloc.add(any)),
  );

  blocTest<AuthBloc, AuthState>(
    'Refresh Token error',
    build: () => AuthBloc(
      authRepository: mockAuthRepository,
      userBloc: mockUserBloc,
      authTokenProvider: mockAuthTokenProvider,
    ),
    setUp: () {
      when(mockRouterDelegate.currentConfiguration).thenReturn(mockRouteMatchList);
      when(mockRouteMatchList.last).thenReturn(mockRouteMatch);
      when(mockRouteMatch.matchedLocation).thenReturn(Routes.rootPage.path);
      when(mockGoRouter.routerDelegate).thenReturn(mockRouterDelegate);
      when(mockAuthRepository.refreshToken(any)).thenAnswer((_) async => throw Exception());
    },
    act: (bloc) => bloc.add(RefreshToken(const AuthTestSeed().refreshTokenModel)),
    verify: (_) {
      verify(mockAuthTokenProvider.invalidateToken()).called(1);
      verifyNever(mockUserBloc.add(any));
    },
  );
}
