// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in national_skills_platform/test/features/auth/auth_bloc/auth_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i10;
import 'dart:ui' as _i15;

import 'package:bloc/bloc.dart' as _i13;
import 'package:flutter/foundation.dart' as _i7;
import 'package:flutter/material.dart' as _i5;
import 'package:go_router/src/builder.dart' as _i3;
import 'package:go_router/src/configuration.dart' as _i16;
import 'package:go_router/src/delegate.dart' as _i14;
import 'package:go_router/src/match.dart' as _i4;
import 'package:go_router/src/route.dart' as _i8;
import 'package:go_router/src/state.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i12;
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart' as _i11;
import 'package:national_skills_platform/features/auth/domain/repositories/auth_repository.dart'
    as _i9;
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserState_0 extends _i1.SmartFake implements _i2.UserState {
  _FakeUserState_0(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

class _FakeRouteBuilder_1 extends _i1.SmartFake implements _i3.RouteBuilder {
  _FakeRouteBuilder_1(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

class _FakeRouteMatchList_2 extends _i1.SmartFake implements _i4.RouteMatchList {
  _FakeRouteMatchList_2(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();
}

class _FakeGoRouterState_3 extends _i1.SmartFake implements _i6.GoRouterState {
  _FakeGoRouterState_3(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeGlobalKey_4<T extends _i5.State<_i5.StatefulWidget>> extends _i1.SmartFake
    implements _i5.GlobalKey<T> {
  _FakeGlobalKey_4(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

class _FakeWidget_5 extends _i1.SmartFake implements _i5.Widget {
  _FakeWidget_5(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();
}

class _FakeUri_6 extends _i1.SmartFake implements Uri {
  _FakeUri_6(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

class _FakeRouteMatch_7 extends _i1.SmartFake implements _i4.RouteMatch {
  _FakeRouteMatch_7(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();
}

class _FakeDiagnosticsNode_8 extends _i1.SmartFake implements _i5.DiagnosticsNode {
  _FakeDiagnosticsNode_8(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);

  @override
  String toString({
    _i7.TextTreeConfiguration? parentConfiguration,
    _i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info,
  }) =>
      super.toString();
}

class _FakeGoRoute_9 extends _i1.SmartFake implements _i8.GoRoute {
  _FakeGoRoute_9(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();
}

class _FakeValueKey_10<T> extends _i1.SmartFake implements _i5.ValueKey<T> {
  _FakeValueKey_10(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i9.AuthRepository {
  @override
  _i10.Future<void> signIn(_i11.AuthModel? authModel) => (super.noSuchMethod(
        Invocation.method(#signIn, [authModel]),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<void> refreshToken(_i11.RefreshTokenModel? refreshTokenModel) => (super.noSuchMethod(
        Invocation.method(#refreshToken, [refreshTokenModel]),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<String> logout() => (super.noSuchMethod(
        Invocation.method(#logout, []),
        returnValue: _i10.Future<String>.value(
          _i12.dummyValue<String>(this, Invocation.method(#logout, [])),
        ),
        returnValueForMissingStub: _i10.Future<String>.value(
          _i12.dummyValue<String>(this, Invocation.method(#logout, [])),
        ),
      ) as _i10.Future<String>);
}

/// A class which mocks [UserBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserBloc extends _i1.Mock implements _i2.UserBloc {
  @override
  _i2.UserState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeUserState_0(this, Invocation.getter(#state)),
        returnValueForMissingStub: _FakeUserState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.UserState);

  @override
  _i10.Stream<_i2.UserState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i10.Stream<_i2.UserState>.empty(),
        returnValueForMissingStub: _i10.Stream<_i2.UserState>.empty(),
      ) as _i10.Stream<_i2.UserState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void add(_i2.UserEvent? event) => super.noSuchMethod(
        Invocation.method(#add, [event]),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i2.UserEvent? event) => super.noSuchMethod(
        Invocation.method(#onEvent, [event]),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.UserState? state) => super.noSuchMethod(
        Invocation.method(#emit, [state]),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i2.UserEvent>(
    _i13.EventHandler<E, _i2.UserState>? handler, {
    _i13.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(#on, [handler], {#transformer: transformer}),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
    _i13.Transition<_i2.UserEvent, _i2.UserState>? transition,
  ) =>
      super.noSuchMethod(
        Invocation.method(#onTransition, [transition]),
        returnValueForMissingStub: null,
      );

  @override
  _i10.Future<void> close() => (super.noSuchMethod(
        Invocation.method(#close, []),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  void onChange(_i13.Change<_i2.UserState>? change) => super.noSuchMethod(
        Invocation.method(#onChange, [change]),
        returnValueForMissingStub: null,
      );

  @override
  void addError(Object? error, [StackTrace? stackTrace]) => super.noSuchMethod(
        Invocation.method(#addError, [error, stackTrace]),
        returnValueForMissingStub: null,
      );

  @override
  void onError(Object? error, StackTrace? stackTrace) => super.noSuchMethod(
        Invocation.method(#onError, [error, stackTrace]),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [GoRouterDelegate].
///
/// See the documentation for Mockito's code generation for more information.
class MockGoRouterDelegate extends _i1.Mock implements _i14.GoRouterDelegate {
  @override
  _i3.RouteBuilder get builder => (super.noSuchMethod(
        Invocation.getter(#builder),
        returnValue: _FakeRouteBuilder_1(this, Invocation.getter(#builder)),
        returnValueForMissingStub: _FakeRouteBuilder_1(
          this,
          Invocation.getter(#builder),
        ),
      ) as _i3.RouteBuilder);

  @override
  set builder(_i3.RouteBuilder? _builder) => super.noSuchMethod(
        Invocation.setter(#builder, _builder),
        returnValueForMissingStub: null,
      );

  @override
  bool get routerNeglect => (super.noSuchMethod(
        Invocation.getter(#routerNeglect),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i4.RouteMatchList get currentConfiguration => (super.noSuchMethod(
        Invocation.getter(#currentConfiguration),
        returnValue: _FakeRouteMatchList_2(
          this,
          Invocation.getter(#currentConfiguration),
        ),
        returnValueForMissingStub: _FakeRouteMatchList_2(
          this,
          Invocation.getter(#currentConfiguration),
        ),
      ) as _i4.RouteMatchList);

  @override
  set currentConfiguration(_i4.RouteMatchList? _currentConfiguration) => super.noSuchMethod(
        Invocation.setter(#currentConfiguration, _currentConfiguration),
        returnValueForMissingStub: null,
      );

  @override
  _i6.GoRouterState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeGoRouterState_3(this, Invocation.getter(#state)),
        returnValueForMissingStub: _FakeGoRouterState_3(
          this,
          Invocation.getter(#state),
        ),
      ) as _i6.GoRouterState);

  @override
  _i5.GlobalKey<_i5.NavigatorState> get navigatorKey => (super.noSuchMethod(
        Invocation.getter(#navigatorKey),
        returnValue: _FakeGlobalKey_4<_i5.NavigatorState>(
          this,
          Invocation.getter(#navigatorKey),
        ),
        returnValueForMissingStub: _FakeGlobalKey_4<_i5.NavigatorState>(
          this,
          Invocation.getter(#navigatorKey),
        ),
      ) as _i5.GlobalKey<_i5.NavigatorState>);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i10.Future<bool> popRoute() => (super.noSuchMethod(
        Invocation.method(#popRoute, []),
        returnValue: _i10.Future<bool>.value(false),
        returnValueForMissingStub: _i10.Future<bool>.value(false),
      ) as _i10.Future<bool>);

  @override
  bool canPop() => (super.noSuchMethod(
        Invocation.method(#canPop, []),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  void pop<T extends Object?>([T? result]) => super.noSuchMethod(
        Invocation.method(#pop, [result]),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Widget build(_i5.BuildContext? context) => (super.noSuchMethod(
        Invocation.method(#build, [context]),
        returnValue: _FakeWidget_5(
          this,
          Invocation.method(#build, [context]),
        ),
        returnValueForMissingStub: _FakeWidget_5(
          this,
          Invocation.method(#build, [context]),
        ),
      ) as _i5.Widget);

  @override
  _i10.Future<void> setNewRoutePath(_i4.RouteMatchList? configuration) => (super.noSuchMethod(
        Invocation.method(#setNewRoutePath, [configuration]),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<void> setInitialRoutePath(_i4.RouteMatchList? configuration) => (super.noSuchMethod(
        Invocation.method(#setInitialRoutePath, [configuration]),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  _i10.Future<void> setRestoredRoutePath(_i4.RouteMatchList? configuration) => (super.noSuchMethod(
        Invocation.method(#setRestoredRoutePath, [configuration]),
        returnValue: _i10.Future<void>.value(),
        returnValueForMissingStub: _i10.Future<void>.value(),
      ) as _i10.Future<void>);

  @override
  void addListener(_i15.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(#addListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i15.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(#removeListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(#dispose, []),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(#notifyListeners, []),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RouteMatchList].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockRouteMatchList extends _i1.Mock implements _i4.RouteMatchList {
  @override
  List<_i4.RouteMatchBase> get matches => (super.noSuchMethod(
        Invocation.getter(#matches),
        returnValue: <_i4.RouteMatchBase>[],
        returnValueForMissingStub: <_i4.RouteMatchBase>[],
      ) as List<_i4.RouteMatchBase>);

  @override
  Map<String, String> get pathParameters => (super.noSuchMethod(
        Invocation.getter(#pathParameters),
        returnValue: <String, String>{},
        returnValueForMissingStub: <String, String>{},
      ) as Map<String, String>);

  @override
  Uri get uri => (super.noSuchMethod(
        Invocation.getter(#uri),
        returnValue: _FakeUri_6(this, Invocation.getter(#uri)),
        returnValueForMissingStub: _FakeUri_6(
          this,
          Invocation.getter(#uri),
        ),
      ) as Uri);

  @override
  String get fullPath => (super.noSuchMethod(
        Invocation.getter(#fullPath),
        returnValue: _i12.dummyValue<String>(
          this,
          Invocation.getter(#fullPath),
        ),
        returnValueForMissingStub: _i12.dummyValue<String>(
          this,
          Invocation.getter(#fullPath),
        ),
      ) as String);

  @override
  bool get isEmpty => (super.noSuchMethod(
        Invocation.getter(#isEmpty),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isNotEmpty => (super.noSuchMethod(
        Invocation.getter(#isNotEmpty),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i4.RouteMatch get last => (super.noSuchMethod(
        Invocation.getter(#last),
        returnValue: _FakeRouteMatch_7(this, Invocation.getter(#last)),
        returnValueForMissingStub: _FakeRouteMatch_7(
          this,
          Invocation.getter(#last),
        ),
      ) as _i4.RouteMatch);

  @override
  bool get isError => (super.noSuchMethod(
        Invocation.getter(#isError),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  List<_i8.RouteBase> get routes => (super.noSuchMethod(
        Invocation.getter(#routes),
        returnValue: <_i8.RouteBase>[],
        returnValueForMissingStub: <_i8.RouteBase>[],
      ) as List<_i8.RouteBase>);

  @override
  _i4.RouteMatchList push(_i4.ImperativeRouteMatch? match) => (super.noSuchMethod(
        Invocation.method(#push, [match]),
        returnValue: _FakeRouteMatchList_2(
          this,
          Invocation.method(#push, [match]),
        ),
        returnValueForMissingStub: _FakeRouteMatchList_2(
          this,
          Invocation.method(#push, [match]),
        ),
      ) as _i4.RouteMatchList);

  @override
  _i4.RouteMatchList remove(_i4.RouteMatchBase? match) => (super.noSuchMethod(
        Invocation.method(#remove, [match]),
        returnValue: _FakeRouteMatchList_2(
          this,
          Invocation.method(#remove, [match]),
        ),
        returnValueForMissingStub: _FakeRouteMatchList_2(
          this,
          Invocation.method(#remove, [match]),
        ),
      ) as _i4.RouteMatchList);

  @override
  void visitRouteMatches(_i4.RouteMatchVisitor? visitor) => super.noSuchMethod(
        Invocation.method(#visitRouteMatches, [visitor]),
        returnValueForMissingStub: null,
      );

  @override
  _i4.RouteMatchList copyWith({
    List<_i4.RouteMatchBase>? matches,
    Uri? uri,
    Map<String, String>? pathParameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#copyWith, [], {
          #matches: matches,
          #uri: uri,
          #pathParameters: pathParameters,
        }),
        returnValue: _FakeRouteMatchList_2(
          this,
          Invocation.method(#copyWith, [], {
            #matches: matches,
            #uri: uri,
            #pathParameters: pathParameters,
          }),
        ),
        returnValueForMissingStub: _FakeRouteMatchList_2(
          this,
          Invocation.method(#copyWith, [], {
            #matches: matches,
            #uri: uri,
            #pathParameters: pathParameters,
          }),
        ),
      ) as _i4.RouteMatchList);

  @override
  void debugFillProperties(_i7.DiagnosticPropertiesBuilder? properties) => super.noSuchMethod(
        Invocation.method(#debugFillProperties, [properties]),
        returnValueForMissingStub: null,
      );

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();

  @override
  String toStringShort() => (super.noSuchMethod(
        Invocation.method(#toStringShort, []),
        returnValue: _i12.dummyValue<String>(
          this,
          Invocation.method(#toStringShort, []),
        ),
        returnValueForMissingStub: _i12.dummyValue<String>(
          this,
          Invocation.method(#toStringShort, []),
        ),
      ) as String);

  @override
  _i5.DiagnosticsNode toDiagnosticsNode({
    String? name,
    _i7.DiagnosticsTreeStyle? style,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#toDiagnosticsNode, [], {
          #name: name,
          #style: style,
        }),
        returnValue: _FakeDiagnosticsNode_8(
          this,
          Invocation.method(#toDiagnosticsNode, [], {
            #name: name,
            #style: style,
          }),
        ),
        returnValueForMissingStub: _FakeDiagnosticsNode_8(
          this,
          Invocation.method(#toDiagnosticsNode, [], {
            #name: name,
            #style: style,
          }),
        ),
      ) as _i5.DiagnosticsNode);
}

/// A class which mocks [RouteMatch].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockRouteMatch extends _i1.Mock implements _i4.RouteMatch {
  @override
  _i8.GoRoute get route => (super.noSuchMethod(
        Invocation.getter(#route),
        returnValue: _FakeGoRoute_9(this, Invocation.getter(#route)),
        returnValueForMissingStub: _FakeGoRoute_9(
          this,
          Invocation.getter(#route),
        ),
      ) as _i8.GoRoute);

  @override
  String get matchedLocation => (super.noSuchMethod(
        Invocation.getter(#matchedLocation),
        returnValue: _i12.dummyValue<String>(
          this,
          Invocation.getter(#matchedLocation),
        ),
        returnValueForMissingStub: _i12.dummyValue<String>(
          this,
          Invocation.getter(#matchedLocation),
        ),
      ) as String);

  @override
  _i5.ValueKey<String> get pageKey => (super.noSuchMethod(
        Invocation.getter(#pageKey),
        returnValue: _FakeValueKey_10<String>(
          this,
          Invocation.getter(#pageKey),
        ),
        returnValueForMissingStub: _FakeValueKey_10<String>(
          this,
          Invocation.getter(#pageKey),
        ),
      ) as _i5.ValueKey<String>);

  @override
  _i6.GoRouterState buildState(
    _i16.RouteConfiguration? configuration,
    _i4.RouteMatchList? matches,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#buildState, [configuration, matches]),
        returnValue: _FakeGoRouterState_3(
          this,
          Invocation.method(#buildState, [configuration, matches]),
        ),
        returnValueForMissingStub: _FakeGoRouterState_3(
          this,
          Invocation.method(#buildState, [configuration, matches]),
        ),
      ) as _i6.GoRouterState);

  @override
  void debugFillProperties(_i7.DiagnosticPropertiesBuilder? properties) => super.noSuchMethod(
        Invocation.method(#debugFillProperties, [properties]),
        returnValueForMissingStub: null,
      );

  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) => super.toString();

  @override
  String toStringShort() => (super.noSuchMethod(
        Invocation.method(#toStringShort, []),
        returnValue: _i12.dummyValue<String>(
          this,
          Invocation.method(#toStringShort, []),
        ),
        returnValueForMissingStub: _i12.dummyValue<String>(
          this,
          Invocation.method(#toStringShort, []),
        ),
      ) as String);

  @override
  _i5.DiagnosticsNode toDiagnosticsNode({
    String? name,
    _i7.DiagnosticsTreeStyle? style,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#toDiagnosticsNode, [], {
          #name: name,
          #style: style,
        }),
        returnValue: _FakeDiagnosticsNode_8(
          this,
          Invocation.method(#toDiagnosticsNode, [], {
            #name: name,
            #style: style,
          }),
        ),
        returnValueForMissingStub: _FakeDiagnosticsNode_8(
          this,
          Invocation.method(#toDiagnosticsNode, [], {
            #name: name,
            #style: style,
          }),
        ),
      ) as _i5.DiagnosticsNode);
}
