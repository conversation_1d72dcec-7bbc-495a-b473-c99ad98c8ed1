import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/auth/data/data_source/data_source/auth_data_source.dart';
import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';

import 'auth_data_source_test.mocks.dart';

@GenerateMocks([Dio])
void main() {
  late MockDio mockDio;
  late AuthDataSource mockAuthDataSource;
  const mockRefreshToken = 'refresh_token';
  final mockLogoutResponse = Response(
    requestOptions: RequestOptions(path: ApiConstants.logoutPath),
    data: {Constants.token: 'token', Constants.isAdmin: false},
    statusCode: Constants.statusCode200,
  );
  final mockLoginResponse = Response(
    requestOptions: RequestOptions(path: ApiConstants.signInPath),
    data: {
      Constants.accessToken: 'token',
      Constants.refreshToken: 'refresh_token',
      Constants.isAdmin: false,
    },
    statusCode: Constants.statusCode201,
  );

  setUpAll(() {
    mockDio = MockDio();
    mockAuthDataSource = AuthDataSource(dio: mockDio);
  });

  group('logout success', () {
    setUp(() {
      when(
        mockDio.post(ApiConstants.logoutPath, data: jsonEncode(mockRefreshToken)),
      ).thenAnswer((_) async => mockLogoutResponse);
    });

    test('returns token on successful logout', () async {
      expect(await mockAuthDataSource.logout(refreshToken: mockRefreshToken), 'token');
    });
  });

  group('refresh token success', () {
    setUp(() {
      const refreshTokenModel = RefreshTokenModel(refreshToken: mockRefreshToken);
      when(
        mockDio.post(ApiConstants.refreshTokenPath, data: refreshTokenModel.toJson()),
      ).thenAnswer((_) async => mockLoginResponse);
    });

    test('returns tokens on successful refresh token', () async {
      const refreshTokenModel = RefreshTokenModel(refreshToken: mockRefreshToken);
      expect(await mockAuthDataSource.refreshToken(refreshTokenModel), ('token', 'refresh_token'));
    });
  });

  group('logout exception', () {
    setUp(() {
      when(
        mockDio.post(ApiConstants.logoutPath, data: jsonEncode(mockRefreshToken)),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), statusCode: 400));
    });

    test('throws DioException on logout failure', () {
      expect(
        () async => await mockAuthDataSource.logout(refreshToken: mockRefreshToken),
        throwsA(isInstanceOf<DioException>()),
      );
    });
  });

  group('signInWithToken success', () {
    setUp(() {
      const authModel = AuthModel(
        codeVerifier: 'verifier',
        codeChallenge: 'challenge',
        token: 'token',
      );
      when(
        mockDio.post(ApiConstants.signInPath, data: authModel.toJson()),
      ).thenAnswer((_) async => mockLoginResponse);
    });

    test('returns tokens on successful sign in', () async {
      const authModel = AuthModel(
        codeVerifier: 'verifier',
        codeChallenge: 'challenge',
        token: 'token',
      );
      expect(await mockAuthDataSource.signInWithToken(authModel), ('token', 'refresh_token'));
    });
  });

  group('signInWithToken exception', () {
    setUp(() {
      const authModel = AuthModel(
        codeVerifier: 'verifier',
        codeChallenge: 'challenge',
        token: 'token',
      );
      when(
        mockDio.post(ApiConstants.signInPath, data: authModel.toJson()),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), statusCode: 400));
    });

    test('throws DioException on sign in failure', () {
      const authModel = AuthModel(
        codeVerifier: 'verifier',
        codeChallenge: 'challenge',
        token: 'token',
      );
      expect(
        () async => await mockAuthDataSource.signInWithToken(authModel),
        throwsA(isInstanceOf<DioException>()),
      );
    });
  });

  group('refreshToken exception', () {
    setUp(() {
      const refreshTokenModel = RefreshTokenModel(refreshToken: mockRefreshToken);
      when(
        mockDio.post(ApiConstants.refreshTokenPath, data: refreshTokenModel.toJson()),
      ).thenAnswer((_) async => Response(requestOptions: RequestOptions(), statusCode: 400));
    });

    test('throws DioException on refresh token failure', () {
      const refreshTokenModel = RefreshTokenModel(refreshToken: mockRefreshToken);
      expect(
        () async => await mockAuthDataSource.refreshToken(refreshTokenModel),
        throwsA(isInstanceOf<DioException>()),
      );
    });
  });
}
