import 'package:national_skills_platform/features/auth/data/model/auth_model.dart';

class AuthTestSeed {
  const AuthTestSeed();

  AuthModel get invalidMockAuthModel => const AuthModel(codeChallenge: '', codeVerifier: '');

  AuthModel get validMockAuthModel =>
      const AuthModel(codeChallenge: '', codeVerifier: '', token: '');

  RefreshTokenModel get refreshTokenModel => const RefreshTokenModel(refreshToken: 'refreshToken');
}
