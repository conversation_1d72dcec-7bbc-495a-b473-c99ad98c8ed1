// import 'package:bloc_test/bloc_test.dart';
// import 'package:get_it/get_it.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
//
// class MockAuthBloc extends MockBloc<AuthEvent, AuthState> implements AuthBloc {}
//
// //ignore: avoid_implementing_value_types
// class AuthEventFake extends Fake implements AuthEvent {}
//
// //ignore: avoid_implementing_value_types
// class AuthStateFake extends Fake implements AuthState {}
//
// late final MockAuthBloc mockAuthBloc;
//
// void setUpMockAuthBloc() {
//   registerFallbackValue(AuthEventFake());
//   registerFallbackValue(AuthStateFake());
//   mockAuthBloc = MockAuthBloc();
//
//   whenListen(
//     mockAuthBloc,
//     Stream.fromIterable([AuthStateFake()]),
//     initialState: AuthStateFake(),
//   );
// }
//
// extension InjectorAuthBlocExt on GetIt {
//   void registerAndSetUpMockAuthBloc() {
//     setUpMockAuthBloc();
//     registerFactory<AuthBloc>(() => mockAuthBloc);
//   }
// }
