import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/presentation/bloc/quiz_bloc.dart';
import 'package:national_skills_platform/features/quiz/presentation/pages/quiz_page.dart';
import 'package:national_skills_platform/features/quiz/presentation/widgets/answer_tile.dart';
import 'package:national_skills_platform/features/training_consumption/data/models/training_consumption_model.dart';
import 'package:national_skills_platform/features/training_consumption/data/params/lesson_params.dart';
import 'package:national_skills_platform/features/training_consumption/presentation/bloc/training_consumption_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_consumption/presentation/pages/training_consumption_page_test.dart';
import '../../../training_consumption/training_consumption_seed.dart';
import '../../quiz_seed.dart';

class MockQuizBloc extends MockBloc<QuizEvent, QuizState> implements QuizBloc {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final MockQuizBloc mockQuizBloc = MockQuizBloc();
  final mockTrainingConsumptionBloc = MockTrainingConsumptionBloc();
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);

  final lessonParams = LessonParams(
    isCompleted: true,
    completedLessonsInSection: 1,
    trainingConsumptionBloc: mockTrainingConsumptionBloc,
    section: trainingDetailsModel.trainingStructure!.sections.first,
    lesson: trainingDetailsModel.trainingStructure!.sections.first.lessons[3],
  );

  setUpAll(() async {
    GetIt.instance
      ..registerFactory<TrainingConsumptionBloc>(() => mockTrainingConsumptionBloc)
      ..registerFactory<QuizBloc>(() => mockQuizBloc);

    whenListen(
      mockTrainingConsumptionBloc,
      Stream<TrainingConsumptionState>.value(
        TrainingConsumptionState(
          trainingConsumptionModel: TrainingConsumptionModel.fromJson(
            tTrainingConsumptionDetailsJson,
          ),
        ),
      ),
      initialState: TrainingConsumptionState(),
    );
    //add when listen for QuizState
    whenListen(
      mockQuizBloc,
      Stream<QuizState>.value(
        QuizState(
          quiz: trainingDetailsModel.trainingStructure!.sections.first.lessons[3].quiz,
          selectedAnswers: {},
        ),
      ),
      initialState: QuizState(selectedAnswers: {}),
    );
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Quiz Page', () {
    testGoldens('quiz page', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: QuizPage(lessonParams: lessonParams, quizResult: null)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'quiz_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );

      //tap any AnswerTile
      await tester.tap(find.byType(AnswerTile).first);
      await tester.pumpAndSettle();

      if (find.text(LocaleKeys.questions_goBack.tr()).evaluate().isNotEmpty) {
        await tester.tap(find.text(LocaleKeys.questions_goBack.tr()));
      }
    });

    testGoldens('quiz results page', (tester) async {
      final quizResult = QuizResult.fromJson(tQuizResultJson);
      whenListen(
        mockQuizBloc,
        Stream<QuizState>.value(
          QuizState(
            quiz: trainingDetailsModel.trainingStructure!.sections.first.lessons[3].quiz,
            currentQuestionIndex: 10,
            selectedAnswers: {},
            quizResultsModel: quizResult,
          ),
        ),
        initialState: QuizState(selectedAnswers: {}),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: QuizPage(lessonParams: lessonParams, quizResult: quizResult)),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'quiz_results_page',
        devices: [
          ...GoldenToolkit.configuration.defaultDevices,
          Device.tabletPortrait,
          Device.iphone11,
        ],
      );
    });
  });
}
