import 'dart:math';

import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/qualification_test/domain/converters/model_converter.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/domain/repositories/quiz_repository.dart';
import 'package:national_skills_platform/features/quiz/presentation/bloc/quiz_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../catalog/shared/catalog_test_seed.dart';
import '../quiz_seed.dart';
import 'quiz_bloc_test.mocks.dart';

@GenerateMocks([QuizRepository])
void main() {
  group('QuizBloc', () {
    late QuizBloc quizBloc;
    late final MockQuizRepository mockQuizRepository = MockQuizRepository();
    final TrainingDetailsModel trainingDetailsModel = TrainingDetailsModel.fromJson(
      tTrainingDetailsJson,
    );
    final lesson = trainingDetailsModel.trainingStructure?.sections.first.lessons[3];
    final currentQuestion = lesson?.quiz?.questions.first;
    final firstAnswer = {
      currentQuestion?.id ?? '': convertAnswerToQuestionAnswerPair(
        currentQuestion?.answers.first,
        currentQuestion?.id,
        currentQuestion?.question,
      ),
    };

    setUp(() {
      quizBloc = QuizBloc(quizRepository: mockQuizRepository, random: Random(42));
    });
    tearDown(() {
      quizBloc.close();
    });
    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with quiz loaded when LoadQuizEvent is added',
      build: () => quizBloc,
      act: (bloc) => bloc.add(
        LoadQuizEvent(
          lesson: lesson,
          trainingDetailsModel: trainingDetailsModel,
          quizResult: null,
        ),
      ),
      expect: () => [QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: {})],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with updated selectedAnswers when SelectAnswerEvent is added',
      build: () => quizBloc,
      seed: () => QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: {}),
      act: (bloc) => bloc.add(
        SelectAnswerEvent(
          convertAnswerToQuestionAnswerPair(
            currentQuestion?.answers.first,
            currentQuestion?.id,
            currentQuestion?.question,
          ),
        ),
      ),
      expect: () => [
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          updatingAnswerSelection: true,
        ),
        QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: firstAnswer),
      ],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with submittingAnswer true and then false when SubmitAnswerEvent is added',
      build: () => quizBloc..trainingDetailsModel = trainingDetailsModel,
      seed: () => QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: firstAnswer),
      act: (bloc) {
        when(
          mockQuizRepository.submitAnswer(any, any, any),
        ).thenAnswer((_) async => QuizResult.fromJson(tQuizResultJson));
        bloc.add(const SubmitAnswerEvent());
      },
      expect: () => [
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          submittingAnswer: true,
        ),
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          currentQuestionIndex: 1,
          quizResultsModel: QuizResult.fromJson(tQuizResultJson),
        ),
      ],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with previous question index when NavigateToPreviousQuestionEvent is added',
      build: () => quizBloc,
      seed: () => QuizState(
        quiz: lesson?.quiz,
        lesson: lesson,
        selectedAnswers: firstAnswer,
        currentQuestionIndex: 1,
      ),
      act: (bloc) => bloc.add(const NavigateToPreviousQuestionEvent()),
      expect: () => [QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: firstAnswer)],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with submittingAnswer true and then false when SubmitAnswerEvent is added',
      build: () => quizBloc..trainingDetailsModel = trainingDetailsModel,
      seed: () => QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: firstAnswer),
      act: (bloc) {
        when(
          mockQuizRepository.submitAnswer(any, any, any),
        ).thenAnswer((_) async => throw Exception());
        bloc.add(const SubmitAnswerEvent());
      },
      expect: () => [
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          submittingAnswer: true,
        ),
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with error message when SubmitAnswerEvent is added and questionID is null',
      build: () => quizBloc..trainingDetailsModel = trainingDetailsModel,
      seed: () => QuizState(lesson: lesson, selectedAnswers: firstAnswer),
      act: (bloc) => bloc.add(const SubmitAnswerEvent()),
      expect: () => [
        QuizState(lesson: lesson, selectedAnswers: firstAnswer, submittingAnswer: true),
        QuizState(
          lesson: lesson,
          selectedAnswers: firstAnswer,
          errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<QuizBloc, QuizState>(
      'emits [QuizState] with error message when SubmitAnswerEvent is added and answerId is null',
      build: () => quizBloc..trainingDetailsModel = trainingDetailsModel,
      seed: () => QuizState(quiz: lesson?.quiz, lesson: lesson, selectedAnswers: firstAnswer),
      act: (bloc) => bloc.add(const SubmitAnswerEvent()),
      expect: () => [
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          submittingAnswer: true,
        ),
        QuizState(
          quiz: lesson?.quiz,
          lesson: lesson,
          selectedAnswers: firstAnswer,
          errorMessage: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );
  });
}
