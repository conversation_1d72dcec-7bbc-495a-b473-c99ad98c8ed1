import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/quiz/data/data_sources/quiz_datasource.dart';
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';

import '../../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../quiz_seed.dart';

void main() {
  late QuizDatasource dataSource;
  late MockDio mockDio;

  setUp(() {
    mockDio = MockDio();
    dataSource = QuizDatasource(dio: mockDio);
  });

  group('submitAnswer', () {
    const tTrainingId = 'trainingId';
    const tLessonId = 'lessonId';
    final tAnswerModel = AnswerModel(questionId: 'questionId', answerIds: ['answerId']);
    final tQuizResult = QuizResult.fromJson(tQuizResultJson);

    test('should perform a PUT request and return QuizResult', () async {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.quizAnswerSubmitPath +
                tLessonId,
          ),
          statusCode: 200,
          data: tQuizResultJson,
        ),
      );

      final result = await dataSource.submitAnswer(tTrainingId, tLessonId, tAnswerModel);

      verify(
        mockDio.put(
          ApiConstants.applicantsTrainings +
              tTrainingId +
              ApiConstants.quizAnswerSubmitPath +
              tLessonId,
          data: [tAnswerModel.toJson()],
        ),
      ).called(1);
      expect(result, equals(tQuizResult));
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.put(any, data: anyNamed('data'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(
            path: ApiConstants.applicantsTrainings +
                tTrainingId +
                ApiConstants.quizAnswerSubmitPath +
                tLessonId,
          ),
          statusCode: 404,
        ),
      );

      expect(
        () => dataSource.submitAnswer(tTrainingId, tLessonId, tAnswerModel),
        throwsA(isA<DioException>()),
      );
    });
  });
}
