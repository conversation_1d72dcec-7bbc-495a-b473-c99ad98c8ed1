// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in national_skills_platform/test/features/quiz/domain/repositories/quiz_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/quiz/data/data_sources/quiz_datasource.dart'
    as _i3;
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart' as _i5;
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeQuizResult_0 extends _i1.SmartFake implements _i2.QuizResult {
  _FakeQuizResult_0(Object parent, Invocation parentInvocation) : super(parent, parentInvocation);
}

/// A class which mocks [QuizDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockQuizDatasource extends _i1.Mock implements _i3.QuizDatasource {
  MockQuizDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.QuizResult> submitAnswer(
    String? trainingId,
    String? lessonId,
    _i5.AnswerModel? answerModel,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#submitAnswer, [
          trainingId,
          lessonId,
          answerModel,
        ]),
        returnValue: _i4.Future<_i2.QuizResult>.value(
          _FakeQuizResult_0(
            this,
            Invocation.method(#submitAnswer, [
              trainingId,
              lessonId,
              answerModel,
            ]),
          ),
        ),
      ) as _i4.Future<_i2.QuizResult>);
}
