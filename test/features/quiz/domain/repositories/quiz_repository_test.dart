import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/quiz/data/data_sources/quiz_datasource.dart';
import 'package:national_skills_platform/features/quiz/data/models/answer_model.dart';
import 'package:national_skills_platform/features/quiz/data/models/quiz_result.dart';
import 'package:national_skills_platform/features/quiz/domain/repositories/quiz_repository.dart';

import '../../quiz_seed.dart';
import 'quiz_repository_test.mocks.dart';

@GenerateMocks([QuizDatasource])
void main() {
  late MockQuizDatasource mockDataSource;
  late QuizRepository repository;

  setUp(() {
    mockDataSource = MockQuizDatasource();
    repository = QuizRepository(dataSource: mockDataSource);
  });

  group('submitAnswer', () {
    const tTrainingId = 'trainingId';
    const tLessonId = 'lessonId';
    final tAnswerModel = AnswerModel(questionId: 'questionId', answerIds: ['answerId']);

    final tQuizResult = QuizResult.fromJson(tQuizResultJson);

    test('should submit answer and return QuizResult', () async {
      when(mockDataSource.submitAnswer(any, any, any)).thenAnswer((_) async => tQuizResult);

      final result = await repository.submitAnswer(tTrainingId, tLessonId, tAnswerModel);

      verify(mockDataSource.submitAnswer(tTrainingId, tLessonId, tAnswerModel));
      expect(result, equals(tQuizResult));
    });

    test('should throw an exception when the data source throws', () {
      when(mockDataSource.submitAnswer(any, any, any)).thenThrow(Exception());

      expect(
        () => repository.submitAnswer(tTrainingId, tLessonId, tAnswerModel),
        throwsA(isA<Exception>()),
      );
    });
  });
}
