// Mocks generated by Mockito 5.4.5 from annotations
// in national_skills_platform/test/features/training_details/training_details_repository/training_details_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart'
    as _i2;
import 'package:national_skills_platform/features/course_details/training_details/data/data_sources/training_details_datasource.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTrainingDetailsModel_0 extends _i1.SmartFake implements _i2.TrainingDetailsModel {
  _FakeTrainingDetailsModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [TrainingDetailsDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingDetailsDataSource extends _i1.Mock implements _i3.TrainingDetailsDataSource {
  @override
  _i4.Future<_i2.TrainingDetailsModel> getTrainingDetails(String? courseId) => (super.noSuchMethod(
        Invocation.method(#getTrainingDetails, [courseId]),
        returnValue: _i4.Future<_i2.TrainingDetailsModel>.value(
          _FakeTrainingDetailsModel_0(
            this,
            Invocation.method(#getTrainingDetails, [courseId]),
          ),
        ),
        returnValueForMissingStub: _i4.Future<_i2.TrainingDetailsModel>.value(
          _FakeTrainingDetailsModel_0(
            this,
            Invocation.method(#getTrainingDetails, [courseId]),
          ),
        ),
      ) as _i4.Future<_i2.TrainingDetailsModel>);

  @override
  _i4.Future<void> enrollTraining(String? trainingId) => (super.noSuchMethod(
        Invocation.method(#enrollTraining, [trainingId]),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> enrollStream(String? trainingId, String? streamId) => (super.noSuchMethod(
        Invocation.method(#enrollStream, [trainingId, streamId]),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
