import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/data/data_sources/training_details_datasource.dart';

import 'package:national_skills_platform/features/course_details/training_details/domain/repositories/training_details_repository.dart';

import '../../catalog/shared/catalog_test_seed.dart';
import 'training_details_repository_test.mocks.dart';

@GenerateNiceMocks([MockSpec<TrainingDetailsDataSource>()])
void main() {
  late final MockTrainingDetailsDataSource mockDataSource;
  late final TrainingDetailsRepository repository;

  setUpAll(() {
    mockDataSource = MockTrainingDetailsDataSource();
    repository = TrainingDetailsRepository(dataSource: mockDataSource);
  });

  group('training details repository', () {
    test('should get TrainingDetailsModel from the data source', () async {
      await repository.loadTrainingDetails('id1');

      verify(mockDataSource.getTrainingDetails('id1'));
    });

    test('should get TrainingDetailsModel from the data source', () async {
      final tTrainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
      when(mockDataSource.getTrainingDetails('id1')).thenAnswer((_) async => tTrainingDetailsModel);

      final result = await repository.loadTrainingDetails('id1');

      verify(mockDataSource.getTrainingDetails('id1'));
      expect(result, equals(tTrainingDetailsModel));
    });

    const trainingId = 'id1';

    test('verify enroll training called', () async {
      await repository.enrollTraining(trainingId: trainingId);

      verify(mockDataSource.enrollTraining(trainingId));
    });

    const streamId = 'str1';

    test('verify enroll stream called', () async {
      await repository.enrollStream(trainingId: trainingId, streamId: streamId);

      verify(mockDataSource.enrollStream(trainingId, streamId));
    });
  });
}
