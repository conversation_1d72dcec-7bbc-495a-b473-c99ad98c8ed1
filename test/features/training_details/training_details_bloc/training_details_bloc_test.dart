import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/converters/date_time_converter.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_header_panel.dart';
import 'package:national_skills_platform/features/course_details/usecases/update_study_streams.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart'
    as my_learnings;
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../catalog/shared/catalog_test_seed.dart';
import '../../learning_track_details/shared/learning_track_details_test_seed.dart';
import '../../my_learnings/presentation/my_learnings_test.dart';
import '../training_details_page/training_details_page_test.mocks.dart';

void main() {
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  late final LearningTrackDetailsModel learningTrackDetailsModel;
  final UpdateStudyStreams updateAndSortStudyStreams = UpdateStudyStreams();
  late final MockTrainingDetailsRepository mockRepository;
  late final MockLearningTrackDetailsRepository mockLearningTrackDetailsRepository;
  late final MockMyLearningsBloc mockMyLearningsBloc;

  setUpAll(() {
    try {
      learningTrackDetailsModel = LearningTrackDetailsModel.fromJson(tLearningTrackDetailsJson);
    } catch (e) {
      // Create a minimal model for testing if JSON parsing fails
      learningTrackDetailsModel = const LearningTrackDetailsModel(
        id: '0c4ea5dd-5a91-482e-867d-50bf23b5d265',
        title: 'Web Design Learning Track',
        description: 'Complete web design learning track covering wireframes to prototypes',
        level: 'BEGINNER',
        status: 'PUBLISHED',
        profileImage: null,
        profileImageUrl: '',
        sector: null,
        domain: null,
        organizationName: 'Tech Academy',
        forNominationOnly: false,
        languageCode: 'en',
        trainingProviderName: 'Web Design Institute',
        trainingCount: 2,
        trainings: [],
        requirements: [],
        outcomes: [],
      );
    }

    mockRepository = MockTrainingDetailsRepository();
    mockLearningTrackDetailsRepository = MockLearningTrackDetailsRepository();
    mockMyLearningsBloc = MockMyLearningsBloc();

    // Configure the mock to return a valid state
    whenListen(
      mockMyLearningsBloc,
      Stream<my_learnings.MyLearningsState>.value(my_learnings.MyLearningsState()),
      initialState: my_learnings.MyLearningsState(),
    );
  });

  //This function used to prepare "expected" data for comparing
  bool checkStreamAvailability(StudyStream? stream) {
    if (stream == null) return false;

    final closedForEnrollmentDate = stream.closedForEnrollmentDate;
    if (closedForEnrollmentDate == null) return false;

    ///To get the current KSA time we get current UTC time and add 3 hours to it
    final nowKSA = truncateToSeconds(DateTime.now().toUtc().add(const Duration(hours: 3)));

    return (truncateToSeconds(closedForEnrollmentDate).isAfter(nowKSA)) &&
        stream.currentNumberOfParticipants < stream.maxNumberOfParticipants;
  }

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [TrainingDetailsLoading, TrainingDetailsError] when LoadTrainingDetailsEvent is added and repository throws an error',
    build: () {
      when(mockRepository.loadTrainingDetails(any)).thenAnswer((_) async => throw Exception());
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const LoadTrainingDetailsEvent(id: '1'));
    },
    expect: () => [
      const CourseDetailsState(isCourseDetailsLoading: true),
      CourseDetailsState(
        courseDetailsError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits [TrainingDetailsLoading, TrainingDetailsLoaded] when TrainingDetailsLoadEvent is added',
    build: () {
      when(mockRepository.loadTrainingDetails(any)).thenAnswer((_) async => trainingDetailsModel);

      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const LoadTrainingDetailsEvent(id: '1'));
    },
    expect: () => [
      const CourseDetailsState(isCourseDetailsLoading: true),
      CourseDetailsState(
        trainingDetailsModel: trainingDetailsModel.copyWith(
          studyStreams: trainingDetailsModel.studyStreams.where(checkStreamAvailability).toList(),
        ),
        showEnrollButton: false,
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollTrainingError when EnrollTrainingEvent is added and repository throws an error',
    build: () {
      when(
        mockRepository.enrollTraining(trainingId: ''),
      ).thenAnswer((_) async => throw Exception());
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollTrainingEvent());
    },
    expect: () => [
      const CourseDetailsState(isTrainingEnrollmentInProgress: true),
      CourseDetailsState(
        enrollTrainingError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollTrainingSuccess when EnrollTrainingEvent is added',
    build: () {
      when(
        mockRepository.enrollTraining(trainingId: ''),
      ).thenAnswer((_) async => trainingDetailsModel);
      return CourseDetailsBloc(
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollTrainingEvent());
    },
    expect: () => [const CourseDetailsState(isTrainingEnrollmentInProgress: true)],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollTrainingError when EnrollStudyStreamEvent is added and repository throws an error',
    build: () {
      when(
        mockRepository.enrollStream(trainingId: '', streamId: ''),
      ).thenAnswer((_) async => throw Exception());
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollStudyStreamEvent(''));
    },
    expect: () => [
      const CourseDetailsState(isTrainingEnrollmentInProgress: true),
      CourseDetailsState(
        enrollTrainingError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits enrollTrainingSuccess when EnrollStudyStreamEvent is added',
    build: () {
      when(
        mockRepository.enrollStream(trainingId: '', streamId: ''),
      ).thenAnswer((_) async => trainingDetailsModel);
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    act: (bloc) {
      bloc.add(const EnrollStudyStreamEvent(''));
    },
    expect: () => [const CourseDetailsState(isTrainingEnrollmentInProgress: true)],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits LoadTrainingDetailsEvent when RefreshDetailsPageEvent is added with training type',
    build: () {
      final updatedModel = trainingDetailsModel.copyWith(
        studyStreams: trainingDetailsModel.studyStreams.where(checkStreamAvailability).toList(),
      );
      when(mockRepository.loadTrainingDetails(any)).thenAnswer((_) async => updatedModel);
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    seed: () => CourseDetailsState(trainingDetailsModel: trainingDetailsModel),
    act: (bloc) {
      bloc.add(const RefreshDetailsPageEvent(CourseType.training, enrollTrainingSuccess: true));
    },
    verify: (_) {
      verify(mockRepository.loadTrainingDetails(trainingDetailsModel.id)).called(1);
    },
    wait: const Duration(milliseconds: 100),
    expect: () => [
      CourseDetailsState(
        trainingDetailsModel: trainingDetailsModel.copyWith(
          studyStreams: trainingDetailsModel.studyStreams.where(checkStreamAvailability).toList(),
        ),
        enrollTrainingSuccess: true,
        showEnrollButton: false,
      ),
    ],
  );

  blocTest<CourseDetailsBloc, CourseDetailsState>(
    'emits LoadLearningTrackDetailsEvent when RefreshDetailsPageEvent is added with learning track type',
    build: () {
      when(mockLearningTrackDetailsRepository.loadLearningTracks(any))
          .thenAnswer((_) async => learningTrackDetailsModel);
      return CourseDetailsBloc(
        trainingDetailsRepository: mockRepository,
        learningTrackDetailsRepository: mockLearningTrackDetailsRepository,
        updateAndSortStudyStreams: updateAndSortStudyStreams,
        myLearningsBloc: mockMyLearningsBloc,
      );
    },
    seed: () => CourseDetailsState(learningTrackDetailsModel: learningTrackDetailsModel),
    act: (bloc) {
      bloc.add(
        const RefreshDetailsPageEvent(CourseType.learningTrack, enrollTrainingSuccess: true),
      );
    },
    verify: (_) {
      verify(mockLearningTrackDetailsRepository.loadLearningTracks(learningTrackDetailsModel.id))
          .called(1);
    },
    wait: const Duration(milliseconds: 100),
    expect: () => [
      CourseDetailsState(
        learningTrackDetailsModel: learningTrackDetailsModel,
        enrollTrainingSuccess: true,
      ),
    ],
  );
}
