import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  const trainingStructureModel = TrainingStructureModel(
    sections: [
      Section(title: 'Section 1', lessons: [], id: '1', index: 1),
      Section(title: 'Section 2', lessons: [], id: '2', index: 2),
    ],
  );

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Training Details Widgets', () {
    testGoldens('Training Syllabus', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: const Scaffold(
            body: TrainingSyllabus(trainingStructureModel: trainingStructureModel),
          ),
        ),
        surfaceSize: const Size(350, 600),
      );
      await tester.pumpAndSettle();
      await screenMatchesGolden(tester, 'training_structure');
    });

    testGoldens('Training Overview Item', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: const Scaffold(
            body: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  TrainingOverviewItem(
                    Lesson(id: '', title: 'Lesson Title 1', index: 0, lessonType: LessonType.Slide),
                  ),
                  TrainingOverviewItem(
                    Lesson(
                      id: '1',
                      title: 'Lesson Title 2',
                      index: 0,
                      lessonType: LessonType.Slide,
                    ),
                  ),
                  TrainingOverviewItem(
                    Lesson(
                      id: '1',
                      title: 'Lesson Title 2',
                      index: 0,
                      lessonType: LessonType.Slide,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        surfaceSize: const Size(170, 145),
      );
      await tester.pumpAndSettle();
      await screenMatchesGolden(tester, 'training_overview_item');
    });

    testGoldens('Learning Track Structure', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Scaffold(
            body: SingleChildScrollView(
              child: LearningTrackSyllabus(
                [
                  TrainingContentModel(
                    id: '1',
                    profileImageUrl: '',
                    title: 'Dummy Training',
                    language: 'English',
                    level: 'Beginner',
                    duration: '1 hour',
                    status: 'Active',
                    skills: const ['Dart', 'Flutter'],
                    enrolledCount: 100,
                    createdDate: DateTime.parse('2022-01-01'),
                    lastModifiedDate: DateTime.parse('2022-01-02'),
                    organizationName: 'Organization Name',
                    trainingType: TrainingType.SelfPaced,
                    hidden: false,
                    hasFutureInPerson: false,
                    hasFutureOnline: false,
                    cities: const [],
                  ),
                ],
              ),
            ),
          ),
        ),
        surfaceSize: const Size(350, 600),
      );

      await tester.pumpAndSettle();
      await screenMatchesGolden(tester, 'learning_track_structure');
    });
  });
}
