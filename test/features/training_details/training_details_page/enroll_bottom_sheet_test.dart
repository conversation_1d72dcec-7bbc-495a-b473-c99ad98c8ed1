import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_alice/alice.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';

import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/course_components.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/enroll_bottom_sheet/change_stream_enrollment_bottom_sheet.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/widgets/enroll_bottom_sheet/enroll_course_bottom_sheet.dart';
import 'package:national_skills_platform/features/profile_page/data/models/user_model.dart';
import 'package:national_skills_platform/features/profile_page/presentation/bloc/user_bloc.dart';
import 'package:national_skills_platform/features/shared/ui_components/shared_decoration.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:video_player_platform_interface/video_player_platform_interface.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../catalog/shared/catalog_test_seed.dart';
import '../../home_page/presentation/pages/home_page_test.dart';
import '../../profile/profile_test_seed.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../mock/fake_video_platform.dart';

void main() {
  provideDummy<CourseDetailsState>(const CourseDetailsState());

  TestWidgetsFlutterBinding.ensureInitialized();
  late final MockUserBloc mockUserBloc;

  final injector = GetIt.instance;
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);

  setUpAll(() async {
    mockUserBloc = MockUserBloc();

    injector
      ..registerFactory<UserBloc>(() => mockUserBloc)
      ..registerSingleton<GoRouter>(MockGoRouter())
      ..registerSingleton<Alice>(Alice());

    VideoPlayerPlatform.instance = FakeVideoPlayerPlatform();

    ///Bloc State Stub
    whenListen(
      mockUserBloc,
      Stream<UserState>.value(
        UserState(
          isUserDataLoading: false,
          userData: UserModel.fromJson(tUserDataJson),
          isUserAvatarLoading: false,
          userAvatar: '',
        ),
      ),
      initialState: const UserState(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Enroll', () {
    testGoldens('Enroll Training', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Builder(
            builder: (context) => Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.white,
                      shape: const RoundedRectangleBorder(
                        borderRadius: SharedDecoration.borderTopLeftRight10,
                      ),
                      builder: (context) => EnrollCourseBottomSheet(
                        courseType: CourseType.training,
                        trainingDetailsModel: trainingDetailsModel,
                      ),
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));

      await multiScreenGolden(
        tester,
        'enroll_training_bottom_sheet',
        devices: [Device.tabletPortrait, Device.iphone11],
      );

      await tester.tap(find.text(LocaleKeys.trainingDetails_goToTraining.tr()));
    });

    testGoldens('Change Training Stream Enroll', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Builder(
            builder: (context) => Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    showModalBottomSheet(
                      backgroundColor: Colors.white,
                      shape: const RoundedRectangleBorder(
                        borderRadius: SharedDecoration.borderTopLeftRight10,
                      ),
                      context: context,
                      builder: (context) => ChangeStreamEnrollmentBottomSheet(enroll: () {}),
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));

      await multiScreenGolden(
        tester,
        'change_stream_enroll_bottom_sheet',
        devices: [Device.tabletPortrait, Device.iphone11],
      );
      await tester.tap(find.text(LocaleKeys.cancel.tr()));
    });

    testGoldens('Enroll Learning Track', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: Builder(
            builder: (context) => Column(
              children: [
                ElevatedButton(
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      backgroundColor: Colors.white,
                      shape: const RoundedRectangleBorder(
                        borderRadius: SharedDecoration.borderTopLeftRight10,
                      ),
                      builder: (context) => EnrollCourseBottomSheet(
                        courseType: CourseType.learningTrack,
                        trainingDetailsModel: trainingDetailsModel,
                      ),
                    );
                  },
                  child: const Text('Open LT Modal'),
                ),
              ],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.tap(find.text('Open LT Modal'));

      await multiScreenGolden(
        tester,
        'enroll_learning_track_bottom_sheet',
        devices: [Device.tabletPortrait, Device.iphone11],
      );
    });
  });
}
