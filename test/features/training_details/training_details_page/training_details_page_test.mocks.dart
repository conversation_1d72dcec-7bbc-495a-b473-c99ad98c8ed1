// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in national_skills_platform/test/features/training_details/training_details_page/training_details_page_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i11;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart'
    as _i2;
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart'
    as _i3;
import 'package:national_skills_platform/features/course_details/learning_track_details/domain/repositories/learning_track_details_repository.dart'
    as _i7;
import 'package:national_skills_platform/features/course_details/training_details/domain/repositories/training_details_repository.dart'
    as _i5;
import 'package:video_player/src/closed_caption_file.dart' as _i10;
import 'package:video_player/video_player.dart' as _i4;
import 'package:video_player_platform_interface/video_player_platform_interface.dart' as _i9;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTrainingDetailsModel_0 extends _i1.SmartFake implements _i2.TrainingDetailsModel {
  _FakeTrainingDetailsModel_0(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeLearningTrackDetailsModel_1 extends _i1.SmartFake
    implements _i3.LearningTrackDetailsModel {
  _FakeLearningTrackDetailsModel_1(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

class _FakeVideoPlayerValue_2 extends _i1.SmartFake implements _i4.VideoPlayerValue {
  _FakeVideoPlayerValue_2(Object parent, Invocation parentInvocation)
      : super(parent, parentInvocation);
}

/// A class which mocks [TrainingDetailsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTrainingDetailsRepository extends _i1.Mock implements _i5.TrainingDetailsRepository {
  @override
  _i6.Future<_i2.TrainingDetailsModel> loadTrainingDetails(String? courseId) => (super.noSuchMethod(
        Invocation.method(#loadTrainingDetails, [courseId]),
        returnValue: _i6.Future<_i2.TrainingDetailsModel>.value(
          _FakeTrainingDetailsModel_0(
            this,
            Invocation.method(#loadTrainingDetails, [courseId]),
          ),
        ),
        returnValueForMissingStub: _i6.Future<_i2.TrainingDetailsModel>.value(
          _FakeTrainingDetailsModel_0(
            this,
            Invocation.method(#loadTrainingDetails, [courseId]),
          ),
        ),
      ) as _i6.Future<_i2.TrainingDetailsModel>);

  @override
  _i6.Future<void> enrollTraining({required String? trainingId}) => (super.noSuchMethod(
        Invocation.method(#enrollTraining, [], {#trainingId: trainingId}),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> enrollStream({
    required String? trainingId,
    required String? streamId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(#enrollStream, [], {
          #trainingId: trainingId,
          #streamId: streamId,
        }),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

/// A class which mocks [LearningTrackDetailsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockLearningTrackDetailsRepository extends _i1.Mock
    implements _i7.LearningTrackDetailsRepository {
  @override
  _i6.Future<_i3.LearningTrackDetailsModel> loadLearningTracks(
    String? courseId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#loadLearningTracks, [courseId]),
        returnValue: _i6.Future<_i3.LearningTrackDetailsModel>.value(
          _FakeLearningTrackDetailsModel_1(
            this,
            Invocation.method(#loadLearningTracks, [courseId]),
          ),
        ),
        returnValueForMissingStub: _i6.Future<_i3.LearningTrackDetailsModel>.value(
          _FakeLearningTrackDetailsModel_1(
            this,
            Invocation.method(#loadLearningTracks, [courseId]),
          ),
        ),
      ) as _i6.Future<_i3.LearningTrackDetailsModel>);

  @override
  _i6.Future<void> enrollLearningTrack({required String? courseId}) => (super.noSuchMethod(
        Invocation.method(#enrollLearningTrack, [], {#courseId: courseId}),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);
}

/// A class which mocks [VideoPlayerController].
///
/// See the documentation for Mockito's code generation for more information.
class MockVideoPlayerController extends _i1.Mock implements _i4.VideoPlayerController {
  MockVideoPlayerController() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get dataSource => (super.noSuchMethod(
        Invocation.getter(#dataSource),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#dataSource),
        ),
      ) as String);

  @override
  Map<String, String> get httpHeaders => (super.noSuchMethod(
        Invocation.getter(#httpHeaders),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  _i9.DataSourceType get dataSourceType => (super.noSuchMethod(
        Invocation.getter(#dataSourceType),
        returnValue: _i9.DataSourceType.asset,
      ) as _i9.DataSourceType);

  @override
  int get textureId => (super.noSuchMethod(Invocation.getter(#textureId), returnValue: 0) as int);

  @override
  _i6.Future<Duration?> get position => (super.noSuchMethod(
        Invocation.getter(#position),
        returnValue: _i6.Future<Duration?>.value(),
      ) as _i6.Future<Duration?>);

  @override
  _i4.VideoPlayerValue get value => (super.noSuchMethod(
        Invocation.getter(#value),
        returnValue: _FakeVideoPlayerValue_2(
          this,
          Invocation.getter(#value),
        ),
      ) as _i4.VideoPlayerValue);

  @override
  set value(_i4.VideoPlayerValue? newValue) => super.noSuchMethod(
        Invocation.setter(#value, newValue),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false) as bool);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(#initialize, []),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(#dispose, []),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> play() => (super.noSuchMethod(
        Invocation.method(#play, []),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setLooping(bool? looping) => (super.noSuchMethod(
        Invocation.method(#setLooping, [looping]),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> pause() => (super.noSuchMethod(
        Invocation.method(#pause, []),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> seekTo(Duration? position) => (super.noSuchMethod(
        Invocation.method(#seekTo, [position]),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setVolume(double? volume) => (super.noSuchMethod(
        Invocation.method(#setVolume, [volume]),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> setPlaybackSpeed(double? speed) => (super.noSuchMethod(
        Invocation.method(#setPlaybackSpeed, [speed]),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void setCaptionOffset(Duration? offset) => super.noSuchMethod(
        Invocation.method(#setCaptionOffset, [offset]),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<void> setClosedCaptionFile(
    _i6.Future<_i10.ClosedCaptionFile>? closedCaptionFile,
  ) =>
      (super.noSuchMethod(
        Invocation.method(#setClosedCaptionFile, [closedCaptionFile]),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void removeListener(_i11.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(#removeListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i11.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(#addListener, [listener]),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(#notifyListeners, []),
        returnValueForMissingStub: null,
      );
}
