import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/domain/repositories/learning_track_details_repository.dart';

import 'package:national_skills_platform/features/course_details/training_details/domain/repositories/training_details_repository.dart';
import 'package:national_skills_platform/features/course_details/training_details/presentation/pages/training_details_page.dart';
import 'package:video_player/video_player.dart';
import 'package:video_player_platform_interface/video_player_platform_interface.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../catalog/shared/catalog_test_seed.dart';
import '../mock/fake_video_platform.dart';
import 'training_details_page_test.mocks.dart';

class MockTrainingDetailsBloc extends MockBloc<CourseDetailsEvent, CourseDetailsState>
    implements CourseDetailsBloc {}

@GenerateNiceMocks([
  MockSpec<TrainingDetailsRepository>(),
  MockSpec<LearningTrackDetailsRepository>(),
])
@GenerateMocks([VideoPlayerController])
void main() {
  provideDummy<CourseDetailsState>(const CourseDetailsState());

  TestWidgetsFlutterBinding.ensureInitialized();
  late final MockTrainingDetailsRepository mockTrainingDetailsRepository;
  late final MockTrainingDetailsBloc mockTrainingDetailsBloc;

  final injector = GetIt.instance;
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockAuthTokenProvider = MockAuthTokenProvider();

  setUpAll(() async {
    mockTrainingDetailsBloc = MockTrainingDetailsBloc();
    mockTrainingDetailsRepository = MockTrainingDetailsRepository();

    injector
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerFactory<CourseDetailsBloc>(() => mockTrainingDetailsBloc);

    VideoPlayerPlatform.instance = FakeVideoPlayerPlatform();

    ///Bloc State Stub
    whenListen(
      mockTrainingDetailsBloc,
      Stream<CourseDetailsState>.value(
        CourseDetailsState(trainingDetailsModel: trainingDetailsModel),
      ),
      initialState: const CourseDetailsState(),
    );

    ///Repositories stub
    when(
      mockTrainingDetailsRepository.loadTrainingDetails('1'),
    ).thenAnswer((_) => Future<TrainingDetailsModel>.value(trainingDetailsModel));

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Training Details', () {
    testGoldens('Training Structure', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const TrainingDetailsPage(trainingID: '')),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'trainings_details_page',
        devices: [
          const Device(name: 'tablet_portrait', size: Size(1024, 3150)),
          const Device(
            name: 'iphone11',
            size: Size(414, 3150),
            safeArea: EdgeInsets.only(top: 44, bottom: 34),
          ),
        ],
      );
    });
  });
}
