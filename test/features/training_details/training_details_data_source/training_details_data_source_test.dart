import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/training_details/data/data_sources/training_details_datasource.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../catalog/shared/catalog_test_seed.dart';

void main() {
  late final MockDio mockDio;
  late final TrainingDetailsDataSource dataSource;

  setUpAll(() async {
    await testEnvSetup();
    mockDio = MockDio();
    dataSource = TrainingDetailsDataSource(dio: mockDio);
    when(mockDio.options).thenReturn(BaseOptions(headers: {}));
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('TrainingDetailsDataSource test', () {
    const url = '${ApiConstants.trainingDetailsPath}/id1';

    test('should get TrainingDetailsModel from the API', () async {
      when(mockDio.get(url, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: 'any_path'),
          data: tTrainingDetailsJson,
          statusCode: 200,
        ),
      );

      final result = await dataSource.getTrainingDetails('id1');

      expect(result, equals(TrainingDetailsModel.fromJson(tTrainingDetailsJson)));
    });

    test('should throw DioException when status code is not 200 or 201', () {
      when(mockDio.get(url, options: anyNamed('options'))).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: 'any_path'),
          data: tTrainingDetailsJson,
          statusCode: 400,
        ),
      );

      expect(() => dataSource.getTrainingDetails('id1'), throwsA(isA<DioException>()));
    });

    test('should enroll successfully when status code is 200', () async {
      when(
        mockDio.post(any, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(statusCode: 200, requestOptions: RequestOptions()));

      await expectLater(dataSource.enrollTraining('123'), completes);
    });

    test('should enroll successfully when status code is 201', () async {
      when(
        mockDio.post(any, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(statusCode: 201, requestOptions: RequestOptions()));

      await expectLater(dataSource.enrollTraining('123'), completes);
    });

    test('should throw DioException when status code is not 200 or 201', () {
      when(
        mockDio.post(any, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(statusCode: 400, requestOptions: RequestOptions()));

      expect(() => dataSource.enrollTraining('123'), throwsA(isA<DioException>()));
    });

    test('should enroll stream successfully when status code is 201', () async {
      when(
        mockDio.post(any, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(statusCode: 201, requestOptions: RequestOptions()));

      await expectLater(dataSource.enrollStream('123', '12'), completes);
    });

    test('should throw DioException when status code is not 200 or 201', () {
      when(
        mockDio.post(any, data: anyNamed('data')),
      ).thenAnswer((_) async => Response(statusCode: 400, requestOptions: RequestOptions()));

      expect(() => dataSource.enrollStream('123', '12'), throwsA(isA<DioException>()));
    });
  });
}
