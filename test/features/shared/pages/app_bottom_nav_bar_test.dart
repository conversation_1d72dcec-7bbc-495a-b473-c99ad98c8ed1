import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';

class MockTrainingsBloc extends Mock implements TrainingsBloc {}

void main() {
  group('AppBottomNavBar', () {
    late MockTrainingsBloc mockTrainingsBloc;
    late GetIt getIt;

    setUp(() {
      getIt = GetIt.instance;
      mockTrainingsBloc = MockTrainingsBloc();

      if (getIt.isRegistered<TrainingsBloc>()) {
        getIt.unregister<TrainingsBloc>();
      }
      getIt.registerSingleton<TrainingsBloc>(mockTrainingsBloc);
    });

    tearDown(() {
      getIt.unregister<TrainingsBloc>();
    });

    test('should add GetTrainingsListEvent when navigating to Home tab', () {
      // Get the trainingsBloc from GetIt and add the event
      GetIt.instance<TrainingsBloc>().add(const GetTrainingsListEvent());

      // Verify that the GetTrainingsListEvent was added to the bloc
      verify(mockTrainingsBloc.add(const GetTrainingsListEvent())).called(1);
    });
  });
}
