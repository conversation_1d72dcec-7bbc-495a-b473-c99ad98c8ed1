import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/training_details_bottom_sheet.dart';
import 'package:platform/platform.dart';
import 'package:video_player_platform_interface/video_player_platform_interface.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../../training_details/mock/fake_video_platform.dart';
import '../../test_seeds/my_learnings_test_seed.dart';

class MockCourseDetailsBloc extends MockBloc<CourseDetailsEvent, CourseDetailsState>
    implements CourseDetailsBloc {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late final MockCourseDetailsBloc mockCourseDetailsBloc;

  final injector = GetIt.instance;
  final testTraining = ApplicantTraining.fromJson(tApplicantTrainingJson);
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockAuthTokenProvider = MockAuthTokenProvider();

  setUpAll(() async {
    mockCourseDetailsBloc = MockCourseDetailsBloc();

    injector
      ..registerFactory<CourseDetailsBloc>(() => mockCourseDetailsBloc)
      ..registerSingleton<AuthTokenProvider>(mockAuthTokenProvider);

    VideoPlayerPlatform.instance = FakeVideoPlayerPlatform();

    whenListen(
      mockCourseDetailsBloc,
      Stream<CourseDetailsState>.value(
        CourseDetailsState(trainingDetailsModel: trainingDetailsModel),
      ),
      initialState: const CourseDetailsState(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Training Details Bottom Sheet', () {
    testGoldens('should show training details bottom sheet', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    openTrainingDetailsBottomSheet(
                      tester.element(find.byType(ElevatedButton)),
                      testTraining,
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));
      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'training_details_bottom_sheet');
    });

    testGoldens('should show training details bottom sheet on iOS', (tester) async {
      final iosPlatform = FakePlatform(
        operatingSystem: 'ios',
        environment: {Constants.flutterTest: 'true'},
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: CupertinoScaffold(
              body: Scaffold(
                body: Center(
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () {
                        openTrainingDetailsBottomSheet(
                          context,
                          testTraining,
                          platform: iosPlatform,
                        );
                      },
                      child: const Text('Open Modal'),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));
      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'training_details_bottom_sheet_ios');
    });

    testGoldens('should show error toast when training is null', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    openTrainingDetailsBottomSheet(
                      tester.element(find.byType(ElevatedButton)),
                      null,
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));

      // Wait for toast animation to complete
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Capture the toast message in the screenshot
      await screenMatchesGolden(tester, 'training_details_bottom_sheet_error');

      // Wait for toast to dismiss to avoid timer issues
      await tester.pump(const Duration(seconds: 7));
      await tester.pumpAndSettle();
    });
  });
}
