import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mocktail/mocktail.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/course_details/bloc/course_details_bloc.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/course_details/learning_track_details/data/models/learning_track_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/learning_track_details_bottom_sheet.dart';
import 'package:platform/platform.dart';
import 'package:video_player_platform_interface/video_player_platform_interface.dart';

import '../../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../../catalog/shared/catalog_test_seed.dart';
import '../../../learning_track_details/shared/learning_track_details_test_seed.dart';
import '../../../training_details/mock/fake_video_platform.dart';
import '../../test_seeds/my_learnings_test_seed.dart';

class MockCourseDetailsBloc extends MockBloc<CourseDetailsEvent, CourseDetailsState>
    implements CourseDetailsBloc {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late final MockCourseDetailsBloc mockCourseDetailsBloc;

  final injector = GetIt.instance;
  final testLearningTrack = LearningTrackView.fromJson(tLearningTrackViewJson);
  final trainingDetailsModel = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
  final mockAuthTokenProvider = MockAuthTokenProvider();

  setUpAll(() async {
    registerFallbackValue(const LoadLearningTrackDetailsEvent(id: ''));
    debugPrint('Setting up test environment...');
    mockCourseDetailsBloc = MockCourseDetailsBloc();

    injector
      ..registerFactory<CourseDetailsBloc>(() => mockCourseDetailsBloc)
      ..registerSingleton<AuthTokenProvider>(mockAuthTokenProvider);

    VideoPlayerPlatform.instance = FakeVideoPlayerPlatform();

    debugPrint('Test learning track: ${testLearningTrack.toJson()}');
    debugPrint('Test training details: ${trainingDetailsModel.toJson()}');

    whenListen(
      mockCourseDetailsBloc,
      Stream<CourseDetailsState>.fromIterable([
        const CourseDetailsState(),
        const CourseDetailsState(isCourseDetailsLoading: true),
        CourseDetailsState(
          learningTrackDetailsModel: LearningTrackDetailsModel.fromJson(tLearningTrackDetailsJson),
          trainingDetailsModel: trainingDetailsModel,
        ),
      ]),
      initialState: const CourseDetailsState(),
    );

    // Mock bloc response to LoadLearningTrackDetailsEvent
    when(() => mockCourseDetailsBloc.add(any(that: isA<LoadLearningTrackDetailsEvent>())))
        .thenAnswer((_) {});

    debugPrint('Test environment setup complete');
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('Learning Track Details Bottom Sheet', () {
    testGoldens('should show learning track details bottom sheet', (tester) async {
      debugPrint('Starting golden test...');
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    debugPrint(
                      'Opening bottom sheet with learning track ID: ${testLearningTrack.learningTrackId}',
                    );
                    openLearningTrackDetailsBottomSheet(
                      tester.element(find.byType(ElevatedButton)),
                      testLearningTrack,
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      debugPrint('Widget built successfully');

      await tester.pumpAndSettle();
      debugPrint('Tapping Open Modal button...');
      await tester.tap(find.text('Open Modal'));
      debugPrint('Waiting for bottom sheet to appear...');
      await tester.pumpAndSettle();
      debugPrint('Bottom sheet should be visible now');

      debugPrint('Taking golden screenshot...');
      await screenMatchesGolden(tester, 'learning_track_details_bottom_sheet');
      debugPrint('Golden test complete');
    });

    testGoldens('should show learning track details bottom sheet on iOS', (tester) async {
      final iosPlatform = FakePlatform(
        operatingSystem: 'ios',
        environment: {Constants.flutterTest: 'true'},
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: CupertinoScaffold(
              body: Scaffold(
                body: Center(
                  child: Builder(
                    builder: (context) => ElevatedButton(
                      onPressed: () {
                        openLearningTrackDetailsBottomSheet(
                          context,
                          testLearningTrack,
                          platform: iosPlatform,
                        );
                      },
                      child: const Text('Open Modal'),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));
      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'learning_track_details_bottom_sheet_ios');
    });

    testGoldens('should show error toast when learning track is null', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Center(
                child: ElevatedButton(
                  onPressed: () {
                    openLearningTrackDetailsBottomSheet(
                      tester.element(find.byType(ElevatedButton)),
                      null,
                    );
                  },
                  child: const Text('Open Modal'),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));

      // Wait for toast animation to complete
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Capture the toast message in the screenshot
      await screenMatchesGolden(tester, 'learning_track_details_bottom_sheet_error');

      // Wait for toast to dismiss to avoid timer issues
      await tester.pump(const Duration(seconds: 7));
      await tester.pumpAndSettle();
    });
  });
}
