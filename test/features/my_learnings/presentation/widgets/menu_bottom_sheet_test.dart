import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/bottom_sheets/menu_bottom_sheet.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('More Menu Bottom Sheet', () {
    testGoldens('should show menu bottom sheet with correct layout', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showCupertinoModalBottomSheet(
                        context: context,
                        builder: (context) => Material(
                          child: MoreMenuBottomSheet(
                            onContinuePressed: () {},
                            onViewTrainingDetails: () {},
                          ),
                        ),
                      );
                    },
                    child: const Text('Open Modal'),
                  ),
                ),
              ),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await tester.tap(find.text('Open Modal'));
      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'more_menu_bottom_sheet');
    });

    testWidgets('should handle button taps correctly', (tester) async {
      bool continueTapped = false;
      bool viewDetailsTapped = false;

      await tester.pumpWidget(
        nspTestWrapper(
          child: MaterialApp(
            home: Scaffold(
              body: Builder(
                builder: (context) => Center(
                  child: ElevatedButton(
                    onPressed: () {
                      showCupertinoModalBottomSheet(
                        context: context,
                        builder: (context) => Material(
                          child: MoreMenuBottomSheet(
                            onContinuePressed: () {
                              continueTapped = true;
                            },
                            onViewTrainingDetails: () {
                              viewDetailsTapped = true;
                            },
                          ),
                        ),
                      );
                    },
                    child: const Text('Open Modal'),
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify initial state
      expect(continueTapped, false);
      expect(viewDetailsTapped, false);

      // Open modal
      await tester.tap(find.text('Open Modal'));
      await tester.pumpAndSettle();

      // Tap continue button
      await tester.tap(find.text(LocaleKeys.userLearnings_continue.tr()));
      await tester.pumpAndSettle();
      expect(continueTapped, true);
      expect(viewDetailsTapped, false);

      // Reset flags
      continueTapped = false;
      viewDetailsTapped = false;

      // Tap view details button
      await tester.tap(find.text(LocaleKeys.viewTrainingDetails.tr()));
      await tester.pumpAndSettle();
      expect(continueTapped, false);
      expect(viewDetailsTapped, true);
    });
  });
}
