import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/view_all_card.dart';

import '../../../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('ViewAllCard Golden Test', () {
    testGoldens('ViewAllCard widget', (tester) async {
      var onTapCalled = false;

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: ViewAllCard(
            onTap: () => onTapCalled = true,
          ),
        ),
        surfaceSize: const Size(400, 210),
      );

      await screenMatchesGolden(tester, 'view_all_card_widget');

      await tester.tap(find.byType(ViewAllCard));
      expect(onTapCalled, isTrue);
    });
  });
}
