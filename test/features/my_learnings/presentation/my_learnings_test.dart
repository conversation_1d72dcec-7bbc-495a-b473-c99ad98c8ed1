import 'package:bloc_test/bloc_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/core/utils/merge_applicant_training_lists.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/pages/my_learnings_page.dart';

import '../../../core/refresh_token_interceptor/refresh_token_interceptor.mocks.dart';
import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../auth/login_page/login_page_test.mocks.dart';
import '../my_learnings_seed.dart';

class MockMyLearningsBloc extends MockBloc<MyLearningsEvent, MyLearningsState>
    implements MyLearningsBloc {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mockAuthTokenProvider = MockAuthTokenProvider();
  final MockMyLearningsBloc mockMyLearningsBloc = MockMyLearningsBloc();
  final myLearningsModel = MyLearningsModel.fromJson(tMyLearningsJson);

  setUpAll(() async {
    GetIt.instance
      ..registerFactory<AuthBloc>(MockAuthBloc.new)
      ..registerFactory<AuthTokenProvider>(() => mockAuthTokenProvider)
      ..registerFactory<MyLearningsBloc>(() => mockMyLearningsBloc);

    whenListen(
      mockMyLearningsBloc,
      Stream<MyLearningsState>.value(
        MyLearningsState(
          myLearningsModel: myLearningsModel,
          allRecentTrainings: mergeApplicantTrainingLists(myLearningsModel),
        ),
      ),
      initialState: MyLearningsState(),
    );

    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  group('My Learnings', () {
    testGoldens('non-auth. my learnings page', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(false));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const MyLearningsPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'non_auth_my_learnings_page', devices: [Device.iphone11]);
    });

    testGoldens('authed. my learnings page', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(true));

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const MyLearningsPage()),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'my_learnings_page',
        devices: [Device.iphone11, const Device(name: 'whole screen', size: Size(414, 1750))],
      );
    });

    testGoldens('authed. no courses in my learnings page', (tester) async {
      when(mockAuthTokenProvider.authStateStream).thenAnswer((_) => Stream.value(true));
      whenListen(
        mockMyLearningsBloc,
        Stream<MyLearningsState>.value(MyLearningsState(allRecentTrainings: [])),
        initialState: MyLearningsState(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(child: const MyLearningsPage()),
        surfaceSize: const Size(414, 1250),
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'no_courses_my_learnings_page',
        devices: [Device.iphone11, const Device(name: 'whole screen', size: Size(414, 1250))],
      );
    });
  });
}
