import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/core/utils/merge_applicant_training_lists.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_cubit.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/cubit/my_learnings_filter_state.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/my_learnings_filter.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/widgets/view_all/view_all_my_learnings.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../shared_test_utils/nsp_test_wrapper.dart';
import '../../home_page/presentation/pages/home_page_test.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../my_learnings_seed.dart';

class MockMyLearningsFilterCubit extends MockCubit<MyLearningsFilterState>
    implements MyLearningsFilterCubit {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late MockMyLearningsFilterCubit mockFilterCubit;
  final MockMyLearningsBloc mockMyLearningsBloc = MockMyLearningsBloc();
  final myLearningsModel = MyLearningsModel.fromJson(tMyLearningsJson);
  final mockRouter = MockGoRouter();

  setUpAll(() async {
    SharedPreferences.setMockInitialValues({});
    await testEnvSetup();

    GetIt.instance
      ..registerFactory<MyLearningsBloc>(() => mockMyLearningsBloc)
      ..registerFactory<GoRouter>(() => mockRouter)
      ..registerFactory<MyLearningsFilterCubit>(() => mockFilterCubit);
    mockFilterCubit = MockMyLearningsFilterCubit();

    whenListen(
      mockMyLearningsBloc,
      Stream<MyLearningsState>.value(
        MyLearningsState(
          myLearningsModel: myLearningsModel,
          allRecentTrainings: mergeApplicantTrainingLists(myLearningsModel),
        ),
      ),
      initialState: MyLearningsState(),
    );
  });

  tearDownAll(() async => await testEnvTearDown());

  group('View all My Learnings', () {
    final nominatedTraining = ApplicantTraining(
      id: 'test-id',
      trainingId: 'test-training-id',
      trainingTitle: 'Test Nominated Training',
      trainingDurationMin: 1,
      trainingDurationMax: 2,
      profileImage: const ProfileImage(
        originalFilename: 'test.jpg',
        key: 'test-key',
        size: 1000,
      ),
      enrolledDate: DateTime.now(),
      passedScore: 0,
      allSections: 1,
      passedSections: 0,
      rating: 4,
      applicantTrainingStatus: ApplicantTrainingStatus.NOMINATED,
      preTrainingTestMandatory: false,
      profileImageUrl: '',
      type: TrainingType.SelfPaced,
      applicantStudyStreams: const [],
    );
    testGoldens('view all my learnings page with learning tracks', (tester) async {
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: ViewAllMyLearnings(
            learningType: LearningType.LearningTrack,
            myLearningTracks: myLearningsModel.applicantLearningTrackViewList,
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(tester, 'view_all_my_learnings_page_lt', devices: [Device.iphone11]);
    });

    testGoldens('self-paced courses in my learnings page', (tester) async {
      whenListen(
        mockMyLearningsBloc,
        Stream<MyLearningsState>.value(
          MyLearningsState(allRecentTrainings: mergeApplicantTrainingLists(myLearningsModel)),
        ),
        initialState: MyLearningsState(),
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: ViewAllMyLearnings(
            learningType: LearningType.SelfPaced,
            myTrainings: mergeApplicantTrainingLists(myLearningsModel),
          ),
        ),
        surfaceSize: const Size(414, 1250),
      );

      await tester.pumpAndSettle();
      await multiScreenGolden(
        tester,
        'view_all_my_learnings_page_self_paced',
        devices: [Device.iphone11],
      );
    });

    testGoldens('shows nominated self-paced training and more menu', (tester) async {
      whenListen(
        mockMyLearningsBloc,
        Stream<MyLearningsState>.value(
          MyLearningsState(
            allRecentTrainings: [nominatedTraining],
          ),
        ),
        initialState: MyLearningsState(),
      );

      final initialState = MyLearningsFilterState(
        allTrainings: [nominatedTraining],
        filteredTrainings: [nominatedTraining],
        selectedStatus: ApplicantTrainingStatus.NOMINATED,
        statusCounts: const {ApplicantTrainingStatus.NOMINATED: 1},
      );

      whenListen(
        mockFilterCubit,
        Stream.value(initialState),
        initialState: initialState,
      );
      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: MultiBlocProvider(
            providers: [
              BlocProvider<MyLearningsBloc>.value(value: mockMyLearningsBloc),
              BlocProvider<MyLearningsFilterCubit>.value(value: mockFilterCubit),
            ],
            child: ViewAllMyLearnings(
              learningType: LearningType.SelfPaced,
              myTrainings: [nominatedTraining],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      await tester.pumpAndSettle();

      /// Find and tap the filter button
      final filterButton = find.byType(MyLearningsFilter);
      await tester.tap(filterButton.first);
      await tester.pumpAndSettle();

      /// Find and tap the Nominated filter option
      final nominatedFilterOption =
          find.widgetWithText(ListTile, '${LocaleKeys.userLearnings_nominated.tr()} (1)');
      await tester.tap(nominatedFilterOption.first);
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'view_all_my_learnings_nominated_initial',
        devices: [Device.iphone11],
      );

      /// Find and tap the more menu button
      final moreMenuButton = find.byIcon(Icons.more_vert);
      expect(moreMenuButton, findsOneWidget, reason: 'More menu button should be visible');
      await tester.tap(moreMenuButton.first);
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'view_all_my_learnings_nominated_with_more_menu',
        devices: [Device.iphone11],
      );
      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.viewTrainingDetails.tr()));
    });

    testGoldens('shows filter options when filter button is clicked', (tester) async {
      const initialState =
          MyLearningsFilterState(statusCounts: {ApplicantTrainingStatus.ENROLLED: 5});

      whenListen(
        mockFilterCubit,
        Stream.value(initialState),
        initialState: initialState,
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: BlocProvider.value(
            value: mockFilterCubit,
            child: ViewAllMyLearnings(
              learningType: LearningType.SelfPaced,
              myTrainings: mergeApplicantTrainingLists(myLearningsModel),
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      await tester.pumpAndSettle();

      final filterByText = find.text(LocaleKeys.filterBy.tr());
      await tester.tap(filterByText.first);
      await tester.pumpAndSettle();

      await screenMatchesGolden(tester, 'view_all_my_learnings_filter_options');

      final listTile = find.byType(ListTile);
      await tester.tap(listTile.first);

      await tester.pumpAndSettle();
      await tester.tap(find.text(LocaleKeys.userLearnings_continue.tr()).first);
    });

    testGoldens('shows completion date for completed training', (tester) async {
      final completedTraining =
          myLearningsModel.applicantSelfPacedTrainingList.firstWhere((t) => t.passedScore == 100);

      final state = MyLearningsFilterState(
        allTrainings: [completedTraining],
        filteredTrainings: [completedTraining],
        selectedStatus: ApplicantTrainingStatus.COMPLETED,
        statusCounts: const {ApplicantTrainingStatus.COMPLETED: 1},
      );

      whenListen(
        mockFilterCubit,
        Stream.fromIterable([state]),
        initialState: state,
      );

      await tester.pumpWidgetBuilder(
        nspTestWrapper(
          child: BlocProvider.value(
            value: mockFilterCubit,
            child: ViewAllMyLearnings(
              learningType: LearningType.SelfPaced,
              myTrainings: [completedTraining],
            ),
          ),
        ),
        surfaceSize: Device.iphone11.size,
      );
      await tester.pumpAndSettle();

      final filterButton = find.byType(MyLearningsFilter);
      await tester.tap(filterButton.first);
      await tester.pumpAndSettle();

      final completedFilterOption =
          find.widgetWithText(ListTile, '${LocaleKeys.userLearnings_completed.tr()} (1)');
      await tester.tap(completedFilterOption.first);
      await tester.pumpAndSettle();

      await multiScreenGolden(
        tester,
        'view_all_my_learnings_completed_with_date',
        devices: [Device.iphone11],
      );
    });
  });
}
