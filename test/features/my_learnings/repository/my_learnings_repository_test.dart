import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/utils/merge_applicant_training_lists.dart';
import 'package:national_skills_platform/features/my_learnings/data/data_sources/my_learning_datasource.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';

import 'package:national_skills_platform/features/my_learnings/domain/repositories/my_learnings_repository.dart';

import '../my_learnings_seed.dart';
import 'my_learnings_repository_test.mocks.dart';

@GenerateMocks([MyLearningsDataSource])
void main() {
  late MockMyLearningsDataSource mockDataSource;
  late MyLearningsRepository repository;
  final myLearningsModel = MyLearningsModel.fromJson(tMyLearningsJson);

  setUp(() {
    mockDataSource = MockMyLearningsDataSource();
    repository = MyLearningsRepository(dataSource: mockDataSource);

    when(mockDataSource.getMyLearnings()).thenAnswer((_) async => myLearningsModel);
  });

  group('getCoursesInProgress', () {
    test(
      'should get Future<(MyLearningsModel, List<ApplicantTraining>)> from the data source',
      () async {
        final result = await repository.getMyLearnings();

        verify(mockDataSource.getMyLearnings());
        expect(result.$1, equals(myLearningsModel));
        expect(result.$2, equals(mergeApplicantTrainingLists(myLearningsModel)));
      },
    );
  });
}
