import 'package:bloc_test/bloc_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/utils/merge_applicant_training_lists.dart';
import 'package:national_skills_platform/features/catalog/presentation/bloc/trainings_bloc/trainings_bloc.dart';
import 'package:national_skills_platform/features/catalog/presentation/widgets/sort_bottom_sheet/sort_bottom_sheet.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/features/my_learnings/presentation/bloc/my_learnings_bloc.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../catalog/shared/catalog_test_seed.dart';
import '../../home_page/presentation/bloc/home_page_bloc_test.mocks.dart';
import '../../training_consumption/presentation/bloc/training_consumption_bloc_test.mocks.dart';
import '../../training_details/training_details_page/training_details_page_test.mocks.dart';
import '../my_learnings_seed.dart';

class MockTrainingsBloc extends MockBloc<TrainingsEvent, TrainingsState> implements TrainingsBloc {}

void main() {
  late MyLearningsBloc myLearningsBloc;
  late MockMyLearningsRepository mockMyLearningsRepository;
  late MockTrainingDetailsRepository mockTrainingDetailsRepository;
  late MockTrainingConsumptionRepository mockTrainingConsumptionRepository;
  late MockTrainingsBloc mockTrainingsBloc;
  late MockGoRouter mockGoRouter;

  setUp(() {
    mockMyLearningsRepository = MockMyLearningsRepository();
    mockTrainingDetailsRepository = MockTrainingDetailsRepository();
    mockTrainingConsumptionRepository = MockTrainingConsumptionRepository();
    mockTrainingsBloc = MockTrainingsBloc();
    mockGoRouter = MockGoRouter();

    GetIt.instance.registerSingleton<GoRouter>(mockGoRouter);

    myLearningsBloc = MyLearningsBloc(
      myLearningsRepository: mockMyLearningsRepository,
      trainingDetailsRepository: mockTrainingDetailsRepository,
      trainingConsumptionRepository: mockTrainingConsumptionRepository,
      trainingsBloc: mockTrainingsBloc,
    );
  });

  tearDown(() {
    GetIt.instance.reset();
  });

  group('GetAllMyLearnings', () {
    final myLearningsModel = MyLearningsModel.fromJson(tMyLearningsJson);
    final allRecentTrainings = mergeApplicantTrainingLists(myLearningsModel);

    blocTest<MyLearningsBloc, MyLearningsState>(
      'emits correct states when GetAllMyLearnings succeeds',
      build: () {
        when(
          mockMyLearningsRepository.getMyLearnings(),
        ).thenAnswer((_) async => (myLearningsModel, allRecentTrainings));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(const GetAllMyLearnings()),
      expect: () => [
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({}), isLoading: true),
        MyLearningsState(
          myLearningsModel: myLearningsModel,
          allRecentTrainings: allRecentTrainings,
        ),
      ],
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'emits correct states when GetAllMyLearnings fails',
      build: () {
        when(
          mockMyLearningsRepository.getMyLearnings(),
        ).thenAnswer((_) => Future.error(Exception('Failed to get my learnings')));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(const GetAllMyLearnings()),
      expect: () => [
        MyLearningsState(isLoading: true),
        MyLearningsState(error: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr()),
      ],
    );
  });

  group('ExploreLearningsEvent', () {
    setUp(() {
      whenListen(
        mockTrainingsBloc,
        Stream<TrainingsState>.value(
          TrainingsLoaded(
            tTrainingsModel,
            currentPage: 1,
            isLastPage: true,
            sortedBy: SortOptionsEnum.enrolledCount,
          ),
        ),
        initialState: TrainingsLoaded(
          tTrainingsModel,
          currentPage: 1,
          isLastPage: true,
          sortedBy: SortOptionsEnum.enrolledCount,
        ),
      );
    });

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle selfPaced exploration correctly',
      build: () => myLearningsBloc,
      act: (bloc) => bloc.add(const ExploreLearningsEvent(ExploreCardType.selfPaced)),
      expect: () => [],
      verify: (_) {
        expect(mockTrainingsBloc.state, isA<TrainingsLoaded>());
      },
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle onlineTraining exploration correctly',
      build: () => myLearningsBloc,
      act: (bloc) => bloc.add(const ExploreLearningsEvent(ExploreCardType.onlineTraining)),
      expect: () => [],
      verify: (_) {
        expect(mockTrainingsBloc.state, isA<TrainingsLoaded>());
      },
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle inPerson exploration correctly',
      build: () => myLearningsBloc,
      act: (bloc) => bloc.add(const ExploreLearningsEvent(ExploreCardType.inPerson)),
      expect: () => [],
      verify: (_) {
        expect(mockTrainingsBloc.state, isA<TrainingsLoaded>());
      },
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle learningTrack exploration correctly',
      build: () => myLearningsBloc,
      act: (bloc) => bloc.add(const ExploreLearningsEvent(ExploreCardType.learningTrack)),
      expect: () => [],
      verify: (_) {
        expect(mockTrainingsBloc.state, isA<TrainingsLoaded>());
      },
    );
  });

  group('EnrollTrainingEvent', () {
    final testTraining =
        MyLearningsModel.fromJson(tMyLearningsJson).applicantSelfPacedTrainingList.first;

    final testTrainingDetails = TrainingDetailsModel.fromJson(
      tTrainingDetailsJson,
    ).copyWith(id: testTraining.trainingId);

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should enroll in self-paced training successfully',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(testTraining.trainingId),
        ).thenAnswer((_) async => testTrainingDetails);
        when(
          mockTrainingDetailsRepository.enrollTraining(trainingId: testTraining.trainingId),
        ).thenAnswer((_) async {});
        when(mockMyLearningsRepository.getMyLearnings()).thenAnswer(
          (_) async => (MyLearningsModel.fromJson(tMyLearningsJson), <ApplicantTraining>[]),
        );
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(
          trainingId: testTraining.trainingId,
          trainingType: TrainingType.SelfPaced,
        ),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({})),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({}), isLoading: true),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson(tMyLearningsJson)),
      ],
      verify: (_) {
        verify(
          mockTrainingDetailsRepository.enrollTraining(trainingId: testTraining.trainingId),
        ).called(1);
      },
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle self-paced enrollment failure',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(testTraining.trainingId),
        ).thenAnswer((_) async => testTrainingDetails);
        when(
          mockTrainingDetailsRepository.enrollTraining(trainingId: testTraining.trainingId),
        ).thenThrow(Exception('Enrollment failed'));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(
          trainingId: testTraining.trainingId,
          trainingType: TrainingType.SelfPaced,
        ),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(
          enrollmentError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    final onlineTraining =
        MyLearningsModel.fromJson(tMyLearningsJson).applicantOnlineStudyTrainingList.first;

    final onlineTrainingDetails = TrainingDetailsModel.fromJson(tTrainingDetailsJson);
    final studyStream = onlineTrainingDetails.studyStreams.first;

    final onlineTrainingWithStream = onlineTrainingDetails.copyWith(
      id: onlineTraining.trainingId,
      studyStreams: [
        studyStream?.copyWith(id: onlineTraining.applicantStudyStreams.first.streamId) ??
            studyStream,
      ],
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should enroll in instructor-led training successfully',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(onlineTraining.trainingId),
        ).thenAnswer((_) async => onlineTrainingWithStream);
        when(
          mockTrainingDetailsRepository.enrollStream(
            trainingId: onlineTraining.trainingId,
            streamId: onlineTraining.applicantStudyStreams.first.streamId,
          ),
        ).thenAnswer((_) async {});
        when(mockMyLearningsRepository.getMyLearnings()).thenAnswer(
          (_) async => (MyLearningsModel.fromJson(tMyLearningsJson), <ApplicantTraining>[]),
        );
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(
          trainingId: onlineTraining.trainingId,
          trainingType: TrainingType.InstructorLed,
        ),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({})),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({}), isLoading: true),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson(tMyLearningsJson)),
      ],
      verify: (_) {
        verify(
          mockTrainingDetailsRepository.enrollStream(
            trainingId: onlineTraining.trainingId,
            streamId: onlineTraining.applicantStudyStreams.first.streamId,
          ),
        ).called(1);
      },
    );
  });

  group('EnrollTrainingEvent error cases', () {
    final testTraining = TrainingDetailsModel.fromJson(tTrainingDetailsJson);

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle enrollment error for self-paced training',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(any),
        ).thenAnswer((_) async => testTraining);
        when(
          mockTrainingDetailsRepository.enrollTraining(trainingId: testTraining.id),
        ).thenAnswer((_) async => throw Exception('Enrollment failed'));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(trainingId: testTraining.id, trainingType: TrainingType.SelfPaced),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(
          enrollmentError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle enrollment error for instructor-led training',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(any),
        ).thenAnswer((_) async => testTraining);
        when(
          mockTrainingDetailsRepository.enrollStream(
            trainingId: testTraining.id,
            streamId: testTraining.studyStreams.firstOrNull?.id,
          ),
        ).thenAnswer((_) async => throw Exception('Enrollment failed'));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(
          trainingId: testTraining.id,
          trainingType: TrainingType.InstructorLed,
        ),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(
          enrollmentError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle initial loading error for instructor-led training',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(any),
        ).thenAnswer((_) async => throw Exception('Loading failed'));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(
        EnrollTrainingEvent(
          trainingId: testTraining.id,
          trainingType: TrainingType.InstructorLed,
        ),
      ),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isEnrollmentInProgress: true,
        ),
        MyLearningsState(
          enrollmentError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );
  });

  group('ContinueTrainingEvent', () {
    final testTraining =
        MyLearningsModel.fromJson(tMyLearningsJson).applicantSelfPacedTrainingList.first;

    final testTrainingDetails = TrainingDetailsModel.fromJson(
      tTrainingDetailsJson,
    ).copyWith(id: testTraining.trainingId);

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle continue training successfully',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(testTraining.trainingId),
        ).thenAnswer((_) async => testTrainingDetails);
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(ContinueTrainingEvent(trainingId: testTraining.trainingId)),
      expect: () => [
        MyLearningsState(
          myLearningsModel: MyLearningsModel.fromJson({}),
          isContinueInProgress: true,
        ),
        MyLearningsState(myLearningsModel: MyLearningsModel.fromJson({})),
      ],
      verify: (_) {
        verify(
          mockTrainingDetailsRepository.loadTrainingDetails(testTraining.trainingId),
        ).called(1);
      },
    );

    blocTest<MyLearningsBloc, MyLearningsState>(
      'should handle continue training failure',
      build: () {
        when(
          mockTrainingDetailsRepository.loadTrainingDetails(testTraining.trainingId),
        ).thenAnswer((_) => Future.error(Exception('Failed to load training details')));
        return myLearningsBloc;
      },
      act: (bloc) => bloc.add(ContinueTrainingEvent(trainingId: testTraining.trainingId)),
      expect: () => [
        MyLearningsState(isContinueInProgress: true),
        MyLearningsState(
          continueError: LocaleKeys.somethingWentWrongPage_somethingWentWrong.tr(),
        ),
      ],
    );
  });
}
