const tMyLearningsJson = {
  "applicantSelfPacedTrainingList": [
    {
      "id": "f26ced8a-f30b-4370-b463-7ba658a00e45",
      "trainingId": "7a214967-d38b-406c-a19e-2edd2bf2e0c5",
      "trainingTitle": "Algorithms",
      "trainingDurationMin": 1,
      "trainingDurationMax": 36,
      "profileImage": {
        "originalFilename": "images.jpg",
        "key": "images/df8a83cc-8a68-4390-81f3-0b9a01c0e6e6/2024-11-12T09:40:11.389792529",
        "size": 20760,
      },
      "completedDate": "2025-01-06T22:17:27.561557606",
      "enrolledDate": "2025-01-06T22:13:29.140327",
      "passedScore": 25,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "COMPLETED",
      "certificateId": 590507,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/df8a83cc-8a68-4390-81f3-0b9a01c0e6e6/2024-11-12T09%3A40%3A11.389792529?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T121559Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=5580f3663dd6ff26a4b20b3bb7e02bdded20061cea39d2e12985c4a5ceb3faba",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "61421ad9-1b74-42d2-b63d-5e77108496f6",
      "trainingId": "04a79e25-0c48-4a29-b0c5-9aae74b421e2",
      "trainingTitle": "test error validation training",
      "trainingDurationMin": 5,
      "trainingDurationMax": 12,
      "profileImage": {
        "originalFilename": "mainPageWallpaper-df164e0f.png",
        "key": "images/04a79e25-0c48-4a29-b0c5-9aae74b421e2/2024-11-14T16:40:25.517134631",
        "size": 1129286,
      },
      "completedDate": null,
      "enrolledDate": "2025-01-06T21:17:58.150938",
      "passedScore": 0,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/04a79e25-0c48-4a29-b0c5-9aae74b421e2/2024-11-14T16%3A40%3A25.517134631?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=bc88f039075ed71a5200d7daed89848cdc4ade7bd4100630527cfc2172b82b68",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "d4065717-ceee-471b-99de-cdda54f340e0",
      "trainingId": "fc5a2a62-7b57-4963-9f7a-2052e168a13c",
      "trainingTitle": "Mastering Coffee From Bean to Brew",
      "trainingDurationMin": 1,
      "trainingDurationMax": 1,
      "profileImage": {
        "originalFilename": "AdobeStock_159183621update.jpg",
        "key": "images/2292d63a-1347-437f-8d4e-10cc080a9904/2024-11-20T14:10:52.861640772",
        "size": 1037192,
      },
      "completedDate": "2025-01-14T07:31:41.619995415",
      "enrolledDate": "2025-01-06T21:14:28.941487",
      "passedScore": 30,
      "allSections": 3,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "COMPLETED",
      "certificateId": 590505,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/2292d63a-1347-437f-8d4e-10cc080a9904/2024-11-20T14%3A10%3A52.861640772?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T121559Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=25ceb162ec2a77abb800f702f729ddc14aa0e11b5ae53b0530cd7cdc4ab235e5",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "65790265-aba9-4a77-8356-f40724042967",
      "trainingId": "ba196890-1c1e-41f1-bc8e-01b9df28649b",
      "trainingTitle": "long long lessons titles",
      "trainingDurationMin": 1,
      "trainingDurationMax": 1,
      "profileImage": {
        "originalFilename": "abstract_picture_vibrant_colors.jpg",
        "key": "images/ba196890-1c1e-41f1-bc8e-01b9df28649b/2024-11-19T12:43:21.308040198",
        "size": 4201,
      },
      "completedDate": "2024-12-22T18:24:46.890115957",
      "enrolledDate": "2024-12-22T18:00:51.590664",
      "passedScore": 100,
      "allSections": 1,
      "passedSections": 1,
      "rating": 4.3,
      "applicantTrainingStatus": "COMPLETED",
      "certificateId": 590464,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/ba196890-1c1e-41f1-bc8e-01b9df28649b/2024-11-19T12%3A43%3A21.308040198?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=ac4093095a16a72d2574f97799e0b516722d5be7de0c2d8a0ce5469f55ba44b9",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "201272d5-9cbe-4468-a6c1-f6eb2d039b48",
      "trainingId": "e14e9811-806e-426b-a832-19fc4f83aa36",
      "trainingTitle": "Tea and Cheese Pairing",
      "trainingDurationMin": 10,
      "trainingDurationMax": 15,
      "profileImage": {
        "originalFilename": "How-to-Pair-Tea-with-Cheese.jpg",
        "key": "images/e14e9811-806e-426b-a832-19fc4f83aa36/2024-11-20T17:03:32.918870220",
        "size": 236086,
      },
      "completedDate": "2024-12-23T09:25:56.654678021",
      "enrolledDate": "2024-12-22T17:58:36.375366",
      "passedScore": 100,
      "allSections": 2,
      "passedSections": 2,
      "rating": 4.3,
      "applicantTrainingStatus": "COMPLETED",
      "certificateId": 590463,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/e14e9811-806e-426b-a832-19fc4f83aa36/2024-11-20T17%3A03%3A32.918870220?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T114012Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=636e0575d6a7d00e3da635b362f6a48179f4485929d6b3eb39a8084646f1e9fd",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "769ef005-a4f4-4953-9d46-8539eaed66e2",
      "trainingId": "356f2a4e-2dca-442a-bbd7-02124136599d",
      "trainingTitle": "for learning track 3",
      "trainingDurationMin": 1,
      "trainingDurationMax": 31,
      "profileImage": {
        "originalFilename": "Screenshot 2024-10-16 092604.png",
        "key": "images/422d7c37-e3cc-4455-b009-cc2238ef16ef/2024-12-04T14:34:39.693618820",
        "size": 1310029,
      },
      "completedDate": "2024-12-15T14:28:16.555939556",
      "enrolledDate": "2024-12-15T14:28:13.037019",
      "passedScore": 100,
      "allSections": 1,
      "passedSections": 1,
      "rating": 4.3,
      "applicantTrainingStatus": "COMPLETED",
      "certificateId": 570231,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/422d7c37-e3cc-4455-b009-cc2238ef16ef/2024-12-04T14%3A34%3A39.693618820?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=55a97dd39923596f684b0c549594d914d65569e73ab877e3043fb1a132632f41",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
    {
      "id": "2d7f0cf0-1080-4506-a669-98c64a9ddcc3",
      "trainingId": "a10f98d4-e6d1-46da-bdf4-25ca4d5fa501",
      "trainingTitle": "Employee Rights and Responsibilities",
      "trainingDurationMin": 1,
      "trainingDurationMax": 38,
      "profileImage": {
        "originalFilename": "70058.jpg",
        "key": "images/a10f98d4-e6d1-46da-bdf4-25ca4d5fa501/2024-11-20T12:37:33.622653867",
        "size": 52941,
      },
      "completedDate": null,
      "enrolledDate": "2024-12-15T14:27:49.823334",
      "passedScore": 0,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/a10f98d4-e6d1-46da-bdf4-25ca4d5fa501/2024-11-20T12%3A37%3A33.622653867?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=b544688e5f5af6930a0c7b357554b1ebf909555c736f046e3090c683f5d0dbde",
      "preTestPassed": null,
      "type": "SELF_PACED",
      "applicantStudyStreams": [],
    },
  ],
  "applicantOnlineStudyTrainingList": [
    {
      "id": "a9ce38bd-032f-4ec5-9af1-8ffe95440a77",
      "trainingId": "ee67fd93-3af8-4f19-b470-1c8d649e07c7",
      "trainingTitle": "Instructor-led training demo",
      "trainingDurationMin": 1,
      "trainingDurationMax": 95,
      "profileImage": {
        "originalFilename": "361190720_237610342493790_974195986237830552_n.jpg",
        "key": "images/ee67fd93-3af8-4f19-b470-1c8d649e07c7/1832766231435550",
        "size": 151073,
      },
      "completedDate": null,
      "enrolledDate": "2025-01-13T17:56:24.034397",
      "passedScore": 0,
      "allSections": null,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/ee67fd93-3af8-4f19-b470-1c8d649e07c7/1832766231435550?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=7214821eb61d5d4f55ae8f29e1382d05818bfebf123797680db50f1a0ae52210",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "96fc471f-565f-4ab2-af92-b4ae1c07b5fe",
          "status": "DISABLED",
          "startDate": "2025-01-14T00:00:00",
          "endDate": "2025-01-28T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": {
            "id": null,
            "trainingId": null,
            "status": "ENROLLED",
            "progress": [],
            "passedScore": 0,
            "allSections": null,
            "passedSections": 0,
            "completedDate": null,
            "enrolledDate": null,
            "certificateId": null,
            "preQualificationTest": null,
            "postQualificationTest": {
              "id": "3696c35a-021f-494b-b64c-d7e71127f2e5",
              "randomized": true,
              "questions": [
                {
                  "id": "2ada53b7-001a-4458-af38-809711032762",
                  "lastModifiedDate": "2025-01-13T17:18:39.283885274",
                  "questionType": "RADIO",
                  "question": "New Question",
                  "answers": [
                    {
                      "id": "faff8774-a2c5-40e6-a262-ae54c59c4de4",
                      "answer": "correct",
                      "correct": null,
                      "index": 1,
                    },
                    {
                      "id": "912c2023-0747-483d-a6a1-8eea5678fdf8",
                      "answer": "wrong",
                      "correct": null,
                      "index": 2,
                    },
                  ],
                  "index": null,
                },
              ],
              "title": "Post-Test title",
              "description": "<p>Post-Test title</p>",
              "minimumScore": 1,
              "timeLimit": null,
              "mandatory": true,
              "finalScore": 0,
              "applicantAnswers": [],
              "resolutionDate": null,
              "firstAttempt": true,
              "applicantSavedAnswers": {},
            },
            "preTestPassed": null,
            "historyOfTests": [],
          },
        },
        {
          "streamId": "e57c7ebd-8797-4ffd-b518-cb6961b8e174",
          "status": "ENROLLED",
          "startDate": "2025-01-16T00:00:00",
          "endDate": "2025-01-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": {
            "id": null,
            "trainingId": null,
            "status": "ENROLLED",
            "progress": [],
            "passedScore": 0,
            "allSections": null,
            "passedSections": 0,
            "completedDate": null,
            "enrolledDate": null,
            "certificateId": null,
            "preQualificationTest": null,
            "postQualificationTest": {
              "id": "3696c35a-021f-494b-b64c-d7e71127f2e5",
              "randomized": true,
              "questions": [
                {
                  "id": "2ada53b7-001a-4458-af38-809711032762",
                  "lastModifiedDate": "2025-01-13T17:18:39.283885274",
                  "questionType": "RADIO",
                  "question": "New Question",
                  "answers": [
                    {
                      "id": "faff8774-a2c5-40e6-a262-ae54c59c4de4",
                      "answer": "correct",
                      "correct": null,
                      "index": 1,
                    },
                    {
                      "id": "912c2023-0747-483d-a6a1-8eea5678fdf8",
                      "answer": "wrong",
                      "correct": null,
                      "index": 2,
                    },
                  ],
                  "index": null,
                },
              ],
              "title": "Post-Test title",
              "description": "<p>Post-Test title</p>",
              "minimumScore": 1,
              "timeLimit": null,
              "mandatory": true,
              "finalScore": 0,
              "applicantAnswers": [],
              "resolutionDate": null,
              "firstAttempt": true,
              "applicantSavedAnswers": {},
            },
            "preTestPassed": null,
            "historyOfTests": [],
          },
        },
        {
          "streamId": "430c9cbd-1406-44ba-ab3d-207cab3182b5",
          "status": "DISABLED",
          "startDate": "2025-01-14T00:00:00",
          "endDate": "2025-01-23T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "5ccbeac2-be97-4284-93bb-93dcbd90ddc5",
      "trainingId": "718c6bb8-d204-4a0e-925d-f908f5240d87",
      "trainingTitle": "New test training",
      "trainingDurationMin": 6,
      "trainingDurationMax": 13,
      "profileImage": {
        "originalFilename": "Odessa.jpg",
        "key": "images/718c6bb8-d204-4a0e-925d-f908f5240d87/1299238726426395",
        "size": 141345,
      },
      "completedDate": null,
      "enrolledDate": "2025-01-02T15:10:22.208802",
      "passedScore": 0,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/718c6bb8-d204-4a0e-925d-f908f5240d87/1299238726426395?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=6447fc98c51fe19fdb3cf17df9eaec78f05fbdd9f4b9a213f6540e9fb46f5866",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "bc594716-10bf-4906-9d66-14620a0f8473",
          "status": "ENROLLED",
          "startDate": "2025-01-05T00:00:00",
          "endDate": "2025-01-18T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "8bf28cc5-6238-4957-bd84-f5e535ff4ff9",
      "trainingId": "628046e0-2455-4da9-bbf7-3d3de574e2b3",
      "trainingTitle": "LT 7004452525",
      "trainingDurationMin": 1,
      "trainingDurationMax": 1,
      "profileImage": {
        "originalFilename": "Odessa.jpg",
        "key": "images/628046e0-2455-4da9-bbf7-3d3de574e2b3/1300009466991108",
        "size": 140974,
      },
      "completedDate": null,
      "enrolledDate": "2024-12-24T13:16:22.495664",
      "passedScore": 50,
      "allSections": 1,
      "passedSections": 1,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/628046e0-2455-4da9-bbf7-3d3de574e2b3/1300009466991108?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=7d528424e512c454d31f801ad68d5440a3db472b2fb74b729e349023710f79ea",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "96cf9674-5e3e-4fbb-a574-4206d49bbb71",
          "status": "ENROLLED",
          "startDate": "2024-12-22T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "96489b69-02c7-4a9c-95bd-134e1c4fc4e5",
      "trainingId": "aa26b8b4-3ff0-4cb4-bd52-cdf866f18c8c",
      "trainingTitle": "wwww",
      "trainingDurationMin": 1,
      "trainingDurationMax": 56,
      "profileImage": {
        "originalFilename": "modern-home-office-setup-with-smartphone-and-laptop-picjumbo-com.jpg",
        "key": "images/6e592bd6-4944-4337-bd48-eacebb1c83c9/2024-12-02T16:32:50.132144842",
        "size": 681370,
      },
      "completedDate": null,
      "enrolledDate": "2024-12-16T12:33:16.716697",
      "passedScore": 0,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/6e592bd6-4944-4337-bd48-eacebb1c83c9/2024-12-02T16%3A32%3A50.132144842?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=62c3243bc16dc35326cf4d5bcb42fb74bf8a692811b7467b8f29fdeed45c4241",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "0a36198e-f5b4-4ca2-bfef-ff1b687cd81c",
          "status": "ENROLLED",
          "startDate": "2024-12-26T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "a41e2510-28cb-41e2-bd6b-014d4c5e4a45",
      "trainingId": "3379b3ca-11a7-43e9-9e9a-bb63ddc126c6",
      "trainingTitle": "testsstststs",
      "trainingDurationMin": 1,
      "trainingDurationMax": 50,
      "profileImage": {
        "originalFilename": "Screenshot 2024-10-16 092604.png",
        "key": "images/d005fd7a-2e00-42f4-ac7a-83016b51a682/2024-11-26T13:20:42.311500353",
        "size": 1310029,
      },
      "completedDate": null,
      "enrolledDate": "2024-12-11T11:52:25.859402",
      "passedScore": 0,
      "allSections": 2,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/d005fd7a-2e00-42f4-ac7a-83016b51a682/2024-11-26T13%3A20%3A42.311500353?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=63a1d927109d8644a5f89e890da09b8602e29ea3d4f1db83a49ed22371eefee6",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "825f0f39-a1ce-4d5e-8896-0436a9c32ba9",
          "status": "ENROLLED",
          "startDate": "2024-12-10T00:00:00",
          "endDate": "2024-12-13T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "95b0873b-a268-4554-9613-22c4566b8c43",
      "trainingId": "d005fd7a-2e00-42f4-ac7a-83016b51a682",
      "trainingTitle": "testsstststs",
      "trainingDurationMin": 1,
      "trainingDurationMax": 50,
      "profileImage": {
        "originalFilename": "Screenshot 2024-10-16 092604.png",
        "key": "images/d005fd7a-2e00-42f4-ac7a-83016b51a682/2024-11-26T13:20:42.311500353",
        "size": 1310029,
      },
      "completedDate": null,
      "enrolledDate": "2024-11-26T13:59:35.263455",
      "passedScore": 60,
      "allSections": null,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/d005fd7a-2e00-42f4-ac7a-83016b51a682/2024-11-26T13%3A20%3A42.311500353?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=63a1d927109d8644a5f89e890da09b8602e29ea3d4f1db83a49ed22371eefee6",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "825f0f39-a1ce-4d5e-8896-0436a9c32ba9",
          "status": "ENROLLED",
          "startDate": "2024-12-10T00:00:00",
          "endDate": "2024-12-13T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
    {
      "id": "ce9c5c2b-359b-4fe5-a4e0-8686ec2dc4a2",
      "trainingId": "1eec6de9-884d-476d-b8f9-838c1c36433e",
      "trainingTitle": "Test reccuring",
      "trainingDurationMin": 1,
      "trainingDurationMax": 1,
      "profileImage": {
        "originalFilename": "abstract_picture_vibrant_colors.jpg",
        "key": "images/1eec6de9-884d-476d-b8f9-838c1c36433e/2024-11-20T17:18:39.927948539",
        "size": 2579,
      },
      "completedDate": null,
      "enrolledDate": "2024-11-20T17:21:28.523458",
      "passedScore": 0,
      "allSections": null,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/1eec6de9-884d-476d-b8f9-838c1c36433e/2024-11-20T17%3A18%3A39.927948539?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=8be2cddb7f381b22de1327b00cc2ffe54ab9faff82ca7eb28b926cc5fa67fab0",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "d4eaebe7-efee-4475-956a-edd15e80cb99",
          "status": "DISABLED",
          "startDate": "2024-11-23T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
        {
          "streamId": "d4eaebe7-efee-4475-956a-edd15e80cb99",
          "status": "ENROLLED",
          "startDate": "2024-11-23T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": {
            "id": null,
            "trainingId": null,
            "status": "ENROLLED",
            "progress": [],
            "passedScore": 0,
            "allSections": null,
            "passedSections": 0,
            "completedDate": null,
            "enrolledDate": null,
            "certificateId": null,
            "preQualificationTest": null,
            "postQualificationTest": {
              "id": "d27d1db0-43fc-436e-b8ef-17cfe26636ea",
              "randomized": false,
              "questions": [
                {
                  "id": "2bb1642e-3e90-4ac2-9e7b-0e4aa91a66de",
                  "lastModifiedDate": "2024-11-20T17:19:37.676061921",
                  "questionType": "RADIO",
                  "question": "New Question",
                  "answers": [
                    {
                      "id": "673532ed-3db0-4a71-a15c-55d99202459a",
                      "answer": "correct",
                      "correct": null,
                      "index": 1,
                    },
                    {
                      "id": "5ff0e5f9-b125-485d-9d13-1b1c9a753868",
                      "answer": "incorrecrt",
                      "correct": null,
                      "index": 2,
                    },
                  ],
                  "index": null,
                },
              ],
              "title": "Title",
              "description": "<p>grgr</p>",
              "minimumScore": 70,
              "timeLimit": null,
              "mandatory": true,
              "finalScore": 0,
              "applicantAnswers": [],
              "resolutionDate": null,
              "firstAttempt": true,
              "applicantSavedAnswers": {},
            },
            "preTestPassed": null,
            "historyOfTests": [],
          },
        },
        {
          "streamId": "45013a51-0f3d-437d-bebd-429076d7a5f4",
          "status": "DISABLED",
          "startDate": "2024-11-30T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": {
            "id": null,
            "trainingId": null,
            "status": "ENROLLED",
            "progress": [],
            "passedScore": 0,
            "allSections": null,
            "passedSections": 0,
            "completedDate": null,
            "enrolledDate": null,
            "certificateId": null,
            "preQualificationTest": null,
            "postQualificationTest": {
              "id": "d27d1db0-43fc-436e-b8ef-17cfe26636ea",
              "randomized": false,
              "questions": [
                {
                  "id": "2bb1642e-3e90-4ac2-9e7b-0e4aa91a66de",
                  "lastModifiedDate": "2024-11-20T17:19:37.676061921",
                  "questionType": "RADIO",
                  "question": "New Question",
                  "answers": [
                    {
                      "id": "673532ed-3db0-4a71-a15c-55d99202459a",
                      "answer": "correct",
                      "correct": null,
                      "index": 1,
                    },
                    {
                      "id": "5ff0e5f9-b125-485d-9d13-1b1c9a753868",
                      "answer": "incorrecrt",
                      "correct": null,
                      "index": 2,
                    },
                  ],
                  "index": null,
                },
              ],
              "title": "Title",
              "description": "<p>grgr</p>",
              "minimumScore": 70,
              "timeLimit": null,
              "mandatory": true,
              "finalScore": 0,
              "applicantAnswers": [],
              "resolutionDate": null,
              "firstAttempt": true,
              "applicantSavedAnswers": {},
            },
            "preTestPassed": null,
            "historyOfTests": [],
          },
        },
      ],
    },
  ],
  "applicantInPersonStudyTrainingList": [
    {
      "id": "96489b69-02c7-4a9c-95bd-134e1c4fc4e5",
      "trainingId": "aa26b8b4-3ff0-4cb4-bd52-cdf866f18c8c",
      "trainingTitle": "wwww",
      "trainingDurationMin": 1,
      "trainingDurationMax": 56,
      "profileImage": {
        "originalFilename": "modern-home-office-setup-with-smartphone-and-laptop-picjumbo-com.jpg",
        "key": "images/6e592bd6-4944-4337-bd48-eacebb1c83c9/2024-12-02T16:32:50.132144842",
        "size": 681370,
      },
      "completedDate": null,
      "enrolledDate": "2024-12-16T12:33:16.716697",
      "passedScore": 0,
      "allSections": 1,
      "passedSections": 0,
      "rating": 4.3,
      "applicantTrainingStatus": "ENROLLED",
      "certificateId": null,
      "preTrainingTestMandatory": false,
      "profileImageUrl":
          "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/6e592bd6-4944-4337-bd48-eacebb1c83c9/2024-12-02T16%3A32%3A50.132144842?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=62c3243bc16dc35326cf4d5bcb42fb74bf8a692811b7467b8f29fdeed45c4241",
      "preTestPassed": null,
      "type": "INSTRUCTOR_LED",
      "applicantStudyStreams": [
        {
          "streamId": "0a36198e-f5b4-4ca2-bfef-ff1b687cd81c",
          "status": "ENROLLED",
          "startDate": "2024-12-26T00:00:00",
          "endDate": "2024-12-31T23:59:59",
          "nextSessionDate": null,
          "cancelled": false,
          "cancellationDate": null,
          "applicantTrainingDto": null,
        },
      ],
    },
  ],
  "applicantLearningTrackViewList": [
    {
      "id": "37d77eb5-c72a-492a-8935-c4d042ff98a6",
      "learningTrackId": "821b58d8-729e-43a4-af69-396f5f4663ea",
      "learningTrackTitle": "test hide expred /emnded ",
      "status": "ENROLLED",
      "applicantTrainings": [
        {
          "id": "22b494dc-a615-4d21-b14c-f242bf7bfa10",
          "trainingId": "422d7c37-e3cc-4455-b009-cc2238ef16ef",
          "trainingTitle": "for learning track 3",
          "trainingDurationMin": 1,
          "trainingDurationMax": 31,
          "profileImage": {
            "originalFilename": "Screenshot 2024-10-16 092604.png",
            "key": "images/422d7c37-e3cc-4455-b009-cc2238ef16ef/2024-12-04T14:34:39.693618820",
            "size": 1310029,
          },
          "completedDate": null,
          "enrolledDate": "2024-12-04T14:55:58.954339",
          "passedScore": 0,
          "allSections": 1,
          "passedSections": 0,
          "rating": 4.3,
          "applicantTrainingStatus": "ENROLLED",
          "certificateId": null,
          "preTrainingTestMandatory": false,
          "profileImageUrl":
              "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/422d7c37-e3cc-4455-b009-cc2238ef16ef/2024-12-04T14%3A34%3A39.693618820?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=55a97dd39923596f684b0c549594d914d65569e73ab877e3043fb1a132632f41",
          "preTestPassed": null,
          "type": "SELF_PACED",
          "applicantStudyStreams": [],
        },
        {
          "id": "b4f379a7-fbe6-4b55-9189-08d56a85e523",
          "trainingId": "e883c9a0-cbec-48bc-8c70-e76f44166164",
          "trainingTitle": "for learning track 2",
          "trainingDurationMin": 1,
          "trainingDurationMax": 1,
          "profileImage": {
            "originalFilename": "Screenshot 2024-10-16 093529.png",
            "key": "images/e883c9a0-cbec-48bc-8c70-e76f44166164/2024-12-04T14:31:47.410818511",
            "size": 427714,
          },
          "completedDate": "2024-12-04T14:56:00.197196081",
          "enrolledDate": "2024-12-04T14:55:58.995019",
          "passedScore": 100,
          "allSections": 1,
          "passedSections": 1,
          "rating": 4.3,
          "applicantTrainingStatus": "COMPLETED",
          "certificateId": 570183,
          "preTrainingTestMandatory": false,
          "profileImageUrl":
              "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/e883c9a0-cbec-48bc-8c70-e76f44166164/2024-12-04T14%3A31%3A47.410818511?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=9bc7e57e9160cb06bd720a743ad66f0910dd91859c88d2d18e6a43d40b1561f8",
          "preTestPassed": null,
          "type": "SELF_PACED",
          "applicantStudyStreams": [],
        },
      ],
      "passedScore": 50,
      "allTrainings": 2,
      "passedTrainings": 1,
      "completedDate": null,
      "enrolledDate": "2024-12-04T14:55:58.893764",
      "certificateId": 570181,
    },
    {
      "id": "0cf1cb3f-7dfe-4ed5-bf94-6298ab3f4915",
      "learningTrackId": "f0e253e2-f14a-4c4e-8f58-c3b2781afe61",
      "learningTrackTitle": "TEST PG VIEW UPDATED",
      "status": "ENROLLED",
      "applicantTrainings": [
        {
          "id": "007dbfa1-acdb-462a-8e31-b58d7e72661b",
          "trainingId": "9b9b64ba-f124-4420-900c-ba38ef855b5d",
          "trainingTitle": "ENGLISH TITLE COPY",
          "trainingDurationMin": 27,
          "trainingDurationMax": 71,
          "profileImage": {
            "originalFilename": "Screenshot 2024-11-06 at 5.35.58 PM.png",
            "key": "images/001aa715-ce2f-4033-a3a9-6b133f0bcaf5/2024-11-06T20:39:06.742654001",
            "size": 153644,
          },
          "completedDate": null,
          "enrolledDate": "2024-11-12T11:01:27.325588",
          "passedScore": 0,
          "allSections": 2,
          "passedSections": 0,
          "rating": 4.3,
          "applicantTrainingStatus": "ENROLLED",
          "certificateId": null,
          "preTrainingTestMandatory": false,
          "profileImageUrl":
              "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/001aa715-ce2f-4033-a3a9-6b133f0bcaf5/2024-11-06T20%3A39%3A06.742654001?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=ac5896d78426175d8733db3cda48ba9427a937b613f4580101dfc32fa8008fad",
          "preTestPassed": null,
          "type": "SELF_PACED",
          "applicantStudyStreams": [],
        },
        {
          "id": "518c04a3-d6d0-4860-812c-b9bb44b2d853",
          "trainingId": "8421cbe6-271f-42ec-b5f0-38a1b543f1b8",
          "trainingTitle": "Test progress training 1",
          "trainingDurationMin": 1,
          "trainingDurationMax": 1,
          "profileImage": {
            "originalFilename": "abstract_picture_vibrant_colors.jpg",
            "key": "images/8421cbe6-271f-42ec-b5f0-38a1b543f1b8/2024-11-07T11:48:20.599924969",
            "size": 9161,
          },
          "completedDate": null,
          "enrolledDate": "2024-11-12T11:01:27.29865",
          "passedScore": 40,
          "allSections": 1,
          "passedSections": 0,
          "rating": 4.3,
          "applicantTrainingStatus": "ENROLLED",
          "certificateId": null,
          "preTrainingTestMandatory": false,
          "profileImageUrl":
              "https://axyoc73bayij.compat.objectstorage.me-jeddah-1.oraclecloud.com/stage-nsp-api/images/8421cbe6-271f-42ec-b5f0-38a1b543f1b8/2024-11-07T11%3A48%3A20.599924969?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20250117T133518Z&X-Amz-SignedHeaders=host&X-Amz-Credential=60db557a07af925e8f49ccc518485854a589f3de%2F20250117%2Fme-jeddah-1%2Fs3%2Faws4_request&X-Amz-Expires=18000&X-Amz-Signature=af1b79233318d3cef24a6d72d321563652d491c82542fb818964a469534d3681",
          "preTestPassed": null,
          "type": "SELF_PACED",
          "applicantStudyStreams": [],
        },
      ],
      "passedScore": 0,
      "allTrainings": 2,
      "passedTrainings": 0,
      "completedDate": null,
      "enrolledDate": "2024-11-12T11:01:27.249688",
      "certificateId": 545355,
    },
  ],
};
