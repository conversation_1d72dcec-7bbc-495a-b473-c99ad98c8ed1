final tApplicantTrainingJson = {
  'id': '1234',
  'trainingId': '5678',
  'trainingTitle': 'Test Training',
  'trainingDurationMin': 60,
  'trainingDurationMax': 120,
  'profileImage': {'originalFilename': 'test.jpg', 'key': 'test_key', 'size': 1024},
  'enrolledDate': '2025-02-01T00:00:00.000Z',
  'completedDate': null,
  'passedScore': 80,
  'allSections': 10,
  'passedSections': 5,
  'rating': 4,
  'applicantTrainingStatus': 'ENROLLED',
  'certificateId': null,
  'preTrainingTestMandatory': false,
  'profileImageUrl': 'https://example.com/image.jpg',
  'preTestPassed': null,
  'type': 'SelfPaced',
  'applicantStudyStreams': [],
};

final tLearningTrackViewJson = {
  'id': '1234',
  'learningTrackId': '5678',
  'learningTrackTitle': 'Test Learning Track',
  'status': 'ENROLLED',
  'applicantTrainings': [],
  'passedScore': 80,
  'allTrainings': 10,
  'passedTrainings': 5,
  'completedDate': null,
  'enrolledDate': '2025-02-01T00:00:00.000Z',
  'certificateId': 12345,
};
