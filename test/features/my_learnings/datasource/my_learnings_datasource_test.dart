import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/shared.dart';
import 'package:national_skills_platform/features/my_learnings/data/data_sources/my_learning_datasource.dart';

import '../../auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../my_learnings_seed.dart';

void main() {
  late MyLearningsDataSource dataSource;
  late MockDio mockDio;

  setUp(() {
    mockDio = MockDio();
    dataSource = MyLearningsDataSource(dio: mockDio);
  });

  group('get courses in progress', () {
    test('should perform a GET request', () async {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.currentCoursesAll),
          statusCode: 200,
          data: tMyLearningsJson,
        ),
      );

      await dataSource.getMyLearnings();

      verify(mockDio.get(ApiConstants.currentCoursesAll)).called(1);
    });

    test('should throw a DioException when the response code is not 200 or 201', () {
      when(mockDio.get(any)).thenAnswer(
        (_) async => Response(
          requestOptions: RequestOptions(path: ApiConstants.currentCoursesAll),
          statusCode: 404,
        ),
      );

      expect(() => dataSource.getMyLearnings(), throwsA(isA<DioException>()));
    });
  });
}
