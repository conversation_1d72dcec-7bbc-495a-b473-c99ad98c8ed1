import 'package:flutter_alice/alice.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';

import 'token_provider_test.mocks.dart';

@GenerateNiceMocks([MockSpec<FlutterSecureStorage>(onMissingStub: OnMissingStub.returnDefault)])
void main() {
  late MockFlutterSecureStorage mockSecureStorage;
  late AuthTokenProvider authTokenProvider;

  setUpAll(() {
    final injector = GetIt.instance;
    mockSecureStorage = MockFlutterSecureStorage();
    authTokenProvider = AuthTokenProvider(secureStorage: mockSecureStorage);
    injector
      ..registerSingleton<FlutterSecureStorage>(mockSecureStorage)
      ..registerSingleton<Alice>(Alice());
  });

  group('AuthTokenProvider Tests', () {
    test('getToken returns a cached token if available', () async {
      when(mockSecureStorage.read(key: anyNamed('key'))).thenAnswer((_) async => 'mock_token');

      final token = await authTokenProvider.getToken();
      expect(token, 'mock_token');

      verify(mockSecureStorage.read(key: SecureStorageKeys.token)).called(
        2,
      ); //it's been called twice because init() function calling once, and getToken calling once
    });

    test('getRefreshToken returns a cached refresh token if available', () async {
      when(
        mockSecureStorage.read(key: anyNamed('key')),
      ).thenAnswer((_) async => 'mock_refresh_token');

      final refreshToken = await authTokenProvider.getRefreshToken();
      expect(refreshToken, 'mock_refresh_token');
      verify(mockSecureStorage.read(key: SecureStorageKeys.refreshToken)).called(1);
    });

    test('invalidateToken clears tokens and calls deleteAll on secure storage', () async {
      when(mockSecureStorage.read(key: SecureStorageKeys.token)).thenAnswer((_) async => 'token');
      when(
        mockSecureStorage.read(key: SecureStorageKeys.refreshToken),
      ).thenAnswer((_) async => 'refreshToken');

      await authTokenProvider.updateTokens('token', 'refreshToken');

      await authTokenProvider.invalidateToken();

      when(mockSecureStorage.read(key: SecureStorageKeys.token)).thenAnswer((_) async => null);
      when(
        mockSecureStorage.read(key: SecureStorageKeys.refreshToken),
      ).thenAnswer((_) async => null);

      expect(await authTokenProvider.getToken(), null);
      expect(await authTokenProvider.getRefreshToken(), null);

      verify(mockSecureStorage.deleteAll()).called(1);
    });

    test('updateTokens updates tokens and writes them to secure storage', () async {
      await authTokenProvider.updateTokens('new_token', 'new_refresh_token');

      expect(await authTokenProvider.getToken(), 'new_token');
      expect(await authTokenProvider.getRefreshToken(), 'new_refresh_token');

      verify(mockSecureStorage.write(key: SecureStorageKeys.token, value: 'new_token')).called(1);
      verify(
        mockSecureStorage.write(key: SecureStorageKeys.refreshToken, value: 'new_refresh_token'),
      ).called(1);
    });
  });
}
