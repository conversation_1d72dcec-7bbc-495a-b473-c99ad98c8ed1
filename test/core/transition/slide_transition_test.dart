import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/shared/transitions/slide_in_transition.dart';

void main() {
  testWidgets('SlideTransitionPage transitions correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Navigator(
          pages: [SlideBottomToTopTransition(child: Container(color: Colors.blueGrey))],
          onDidRemovePage: (result) => result.canPop,
        ),
      ),
    );

    expect(find.byType(Container), findsOneWidget);

    SlideTransition slideTransition = tester.firstWidget<SlideTransition>(
      find.byType(SlideTransition),
    );

    expect(slideTransition.position.value, Offset.zero);

    await tester.pump();

    await tester.pumpAndSettle();

    slideTransition = tester.firstWidget<SlideTransition>(find.byType(SlideTransition));
    expect(slideTransition.position.value, Offset.zero);
  });
}
