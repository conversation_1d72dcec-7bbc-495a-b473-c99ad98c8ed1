import 'package:flutter/material.dart';
import 'package:flutter_styled_toast/flutter_styled_toast.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:national_skills_platform/features/shared/ui_components/toast_messages.dart';

import '../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() async {
    await testEnvSetup();
  });

  tearDown(() async {
    await testEnvTearDown();
  });

  testGoldens('showAppToast displays error toast', (tester) async {
    await tester.pumpWidgetBuilder(
      nspTestWrapper(
        child: Builder(
          builder: (context) {
            return Scaffold(
              body: Align(
                alignment: Alignment.bottomCenter,
                child: ElevatedButton(
                  onPressed: () {
                    showAppToast(context, title: 'Error', message: 'This is an error message');
                  },
                  child: const Text('Show Error Toast'),
                ),
              ),
            );
          },
        ),
      ),
      surfaceSize: const Size(414, 150),
    );

    await tester.tap(find.text('Show Error Toast'));
    await tester.pumpAndSettle();

    await screenMatchesGolden(tester, 'show_error_toast');
    dismissAllToast();
  });

  testGoldens('showAppToast displays warning toast', (tester) async {
    await tester.pumpWidgetBuilder(
      nspTestWrapper(
        child: Builder(
          builder: (context) {
            return Scaffold(
              body: Align(
                alignment: Alignment.bottomCenter,
                child: ElevatedButton(
                  onPressed: () {
                    showAppToast(
                      context,
                      title: 'Warning',
                      message: 'This is a warning message',
                      type: ToastType.warning,
                    );
                  },
                  child: const Text('Show Warning Toast'),
                ),
              ),
            );
          },
        ),
      ),
      surfaceSize: const Size(414, 150),
    );

    await tester.tap(find.text('Show Warning Toast'));
    await tester.pumpAndSettle();

    await screenMatchesGolden(tester, 'show_warning_toast');
    dismissAllToast();
  });

  testGoldens('showAppToast displays success toast', (tester) async {
    await tester.pumpWidgetBuilder(
      nspTestWrapper(
        child: Builder(
          builder: (context) {
            return Scaffold(
              body: Align(
                alignment: Alignment.bottomCenter,
                child: ElevatedButton(
                  onPressed: () {
                    showAppToast(
                      context,
                      title: 'Success',
                      message: 'This is a success message',
                      type: ToastType.success,
                    );
                  },
                  child: const Text('Show Success Toast'),
                ),
              ),
            );
          },
        ),
      ),
      surfaceSize: const Size(414, 150),
    );

    await tester.tap(find.text('Show Success Toast'));
    await tester.pumpAndSettle();

    await screenMatchesGolden(tester, 'show_success_toast');
    dismissAllToast();
  });
}
