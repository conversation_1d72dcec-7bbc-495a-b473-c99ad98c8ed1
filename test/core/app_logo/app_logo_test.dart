import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/shared/ui_components/app_logo.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('AppLogo has a logo', (tester) async {
    await tester.pumpWidget(const MaterialApp(home: AppLogo()));

    final logoFinder = find.bySemanticsLabel('Logo');

    expect(logoFinder, findsOneWidget);
  });
}
