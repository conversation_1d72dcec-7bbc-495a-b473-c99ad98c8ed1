import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/shared/error_handler.dart';

void main() {
  group('errorHandler', () {
    test('calls onSuccess when future completes successfully', () async {
      final future = Future.value('Success');
      var onSuccessCalled = false;

      await future.errorHandler(
        onSuccess: (result) async {
          onSuccessCalled = true;
        },
        onError: (error) {},
      );

      expect(onSuccessCalled, true);
    });
  });

  test('calls onError when future completes with an error', () async {
    final future = Future.error('Error');
    var onErrorCalled = false;

    await future.errorHandler(
      onSuccess: (result) async {},
      onError: (error) {
        onErrorCalled = true;
      },
    );

    expect(onErrorCalled, true);
  });
}
