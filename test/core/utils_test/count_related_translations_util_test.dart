import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/features/catalog/utils/count_related_translations_util.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../shared_test_utils/nsp_test_wrapper.dart';

void main() {
  group('Count Related Translations Util', () {
    setUpAll(() async {
      await testEnvSetup();
    });

    tearDown(() async {
      await testEnvTearDown();
    });

    test('getTotalResult returns correct translation for 1 result', () {
      expect(getTotalResult(1), equals(LocaleKeys.totalResults1.tr()));
    });

    test('getTotalResult returns correct translation for 2 results', () {
      expect(getTotalResult(2), equals(LocaleKeys.totalResults2.tr()));
    });

    test('getTotalResult returns correct translation for 5 results', () {
      expect(getTotalResult(5), equals(LocaleKeys.totalResults2to11.tr()));
    });

    test('getTotalResult returns correct translation for 15 results', () {
      expect(getTotalResult(15), equals(LocaleKeys.totalResults.tr()));
    });

    test('getItemsSelectedTranslation returns correct translation for 1 item selected', () {
      expect(getItemsSelectedTranslation(1), equals(LocaleKeys.itemsSelected1.tr()));
    });

    test('getItemsSelectedTranslation returns correct translation for 2 items selected', () {
      expect(getItemsSelectedTranslation(2), equals(LocaleKeys.itemsSelected2.tr()));
    });

    test('getItemsSelectedTranslation returns correct translation for 5 items selected', () {
      expect(getItemsSelectedTranslation(5), equals(LocaleKeys.itemsSelected3to10.tr()));
    });

    test('getItemsSelectedTranslation returns correct translation for 15 items selected', () {
      expect(getItemsSelectedTranslation(15), equals(LocaleKeys.itemsSelected11.tr()));
    });
  });
}
