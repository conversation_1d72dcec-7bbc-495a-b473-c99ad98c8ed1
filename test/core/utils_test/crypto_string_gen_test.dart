import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/utils/random_string_generator.dart';

void main() {
  group('generateCryptoRandomString', () {
    test('returns a string of the correct length', () {
      const length = 64;
      final result = generateCryptoRandomString();

      expect(result.length, equals(length));
    });

    test('returns a string containing only valid characters', () {
      final result = generateCryptoRandomString();

      // The valid characters for the generated string
      const validChars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

      for (var i = 0; i < result.length; i++) {
        expect(validChars.contains(result[i]), isTrue);
      }
    });
  });
}
