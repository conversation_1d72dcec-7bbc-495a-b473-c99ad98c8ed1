import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/utils/code_challange_generator.dart';

void main() {
  group('generateCodeChallenge', () {
    test('returns correct code challenge for given code verifier', () {
      const codeVerifier = 'randomCodeVerifier';
      final codeChallenge = generateCodeChallenge(codeVerifier);

      // The expected result is a base64url-encoded SHA-256 hash of the code verifier,
      // with any trailing '=' characters removed.
      final expectedCodeChallenge =
          base64Url.encode(sha256.convert(utf8.encode(codeVerifier)).bytes).replaceAll('=', '');

      expect(codeChallenge, equals(expectedCodeChallenge));
    });
  });
}
