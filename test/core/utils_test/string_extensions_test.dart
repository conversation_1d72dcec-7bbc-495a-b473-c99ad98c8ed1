import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/shared/extensions/string_extensions.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

void main() {
  group('TagRemoval', () {
    test('removeTags should remove HTML tags from a string', () {
      const stringWithTags = '<p>Hello, <b>World</b>!</p>';

      final result = stringWithTags.removeHtmlElements();

      expect(result, 'Hello, World!');
    });
  });

  group('languageCodeToName tests', () {
    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    test('returns English name for "en" language code', () {
      const languageCode = 'en';
      expect(languageCode.languageCodeToName(), LocaleKeys.languages_english_en.tr());
    });

    test('returns Arabic name for "ar" language code', () {
      const languageCode = 'ar';
      expect(languageCode.languageCodeToName(), LocaleKeys.languages_arabic_ar.tr());
    });

    test('returns "Unknown language" for unknown language code', () {
      const languageCode = 'de';
      expect(languageCode.languageCodeToName(), 'Unknown language');
    });
  });
}
