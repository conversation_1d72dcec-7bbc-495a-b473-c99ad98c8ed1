import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:national_skills_platform/core/shared/converters/training_type_string.dart';
import 'package:national_skills_platform/features/catalog/data/models/trainings_model.dart';
import 'package:national_skills_platform/features/course_details/data/models/training_details_model.dart';
import 'package:national_skills_platform/features/my_learnings/data/models/my_learnings_model.dart';
import 'package:national_skills_platform/generated/locale_keys.g.dart';

import '../../features/catalog/shared/catalog_test_seed.dart';

void main() {
  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  group('trainingTypeString', () {
    final baseTraining = TrainingDetailsModel.fromJson(tTrainingDetailsJson);

    test('should return training when type is none', () {
      final training = baseTraining.copyWith(type: TrainingType.none);
      expect(trainingTypeString(training), LocaleKeys.training.tr());
    });

    test('should return self_paced when type is SelfPaced', () {
      final training = baseTraining.copyWith(type: TrainingType.SelfPaced);
      expect(trainingTypeString(training), LocaleKeys.self_paced.tr());
    });

    test('should return online when type is InstructorLed and has future online', () {
      final training = baseTraining.copyWith(
        type: TrainingType.InstructorLed,
        hasFutureOnline: true,
        hasFutureInPerson: false,
      );
      expect(trainingTypeString(training), LocaleKeys.trainingDetails_online.tr());
    });

    test('should return in_person when type is InstructorLed and has future in person', () {
      final training = baseTraining.copyWith(
        type: TrainingType.InstructorLed,
        hasFutureOnline: false,
        hasFutureInPerson: true,
      );
      expect(trainingTypeString(training), LocaleKeys.in_person.tr());
    });

    test('should return online_in_person when type is InstructorLed and has no future sessions',
        () {
      final training = baseTraining.copyWith(
        type: TrainingType.InstructorLed,
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(trainingTypeString(training), LocaleKeys.online_in_person.tr());
    });

    test('should return training when training is null', () {
      expect(trainingTypeString(null), LocaleKeys.training.tr());
    });
  });

  group('applicantTrainingTypeString', () {
    test('should return self_paced for SelfPaced type', () {
      expect(applicantTrainingTypeString(LearningType.SelfPaced), LocaleKeys.self_paced.tr());
    });

    test('should return online for OnlineStudy type', () {
      expect(
        applicantTrainingTypeString(LearningType.OnlineStudy),
        LocaleKeys.trainingDetails_online.tr(),
      );
    });

    test('should return in_person for InPersonStudy type', () {
      expect(applicantTrainingTypeString(LearningType.InPersonStudy), LocaleKeys.in_person.tr());
    });

    test('should return training for LearningTrack type', () {
      expect(applicantTrainingTypeString(LearningType.LearningTrack), LocaleKeys.training.tr());
    });

    test('should return learningTracks_title for null type', () {
      expect(applicantTrainingTypeString(null), LocaleKeys.learningTracks_title.tr());
    });
  });

  group('trainingContentTypeString', () {
    final baseTraining = TrainingContentModel.fromJson(tTrainingDetailsJson);

    test('should return training when type is none', () {
      final training = baseTraining.copyWith(trainingType: TrainingType.none);
      expect(trainingContentTypeString(training), LocaleKeys.training.tr());
    });

    test('should return self_paced when type is SelfPaced', () {
      final training = baseTraining.copyWith(trainingType: TrainingType.SelfPaced);
      expect(trainingContentTypeString(training), LocaleKeys.self_paced.tr());
    });

    test('should return online when type is InstructorLed and has future online', () {
      final training = baseTraining.copyWith(
        trainingType: TrainingType.InstructorLed,
        hasFutureOnline: true,
        hasFutureInPerson: false,
      );
      expect(trainingContentTypeString(training), LocaleKeys.trainingDetails_online.tr());
    });

    test('should return in_person when type is InstructorLed and has future in person', () {
      final training = baseTraining.copyWith(
        trainingType: TrainingType.InstructorLed,
        hasFutureOnline: false,
        hasFutureInPerson: true,
      );
      expect(trainingContentTypeString(training), LocaleKeys.in_person.tr());
    });

    test('should return online_in_person when type is InstructorLed and has no future sessions',
        () {
      final training = baseTraining.copyWith(
        trainingType: TrainingType.InstructorLed,
        hasFutureOnline: false,
        hasFutureInPerson: false,
      );
      expect(trainingContentTypeString(training), LocaleKeys.online_in_person.tr());
    });

    test('should return training when training is null', () {
      expect(trainingContentTypeString(null), LocaleKeys.training.tr());
    });
  });
}
