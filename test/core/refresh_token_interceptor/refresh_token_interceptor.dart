import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:national_skills_platform/core/shared/token_provider/refresh_token_interceptor.dart';
import 'package:national_skills_platform/core/shared/token_provider/token_provider.dart';
import 'package:national_skills_platform/features/auth/presentation/bloc/auth_bloc.dart';

import '../../features/auth/auth_data_source/auth_data_source_test.mocks.dart';
import '../../features/auth/login_page/login_page_test.mocks.dart';
import 'refresh_token_interceptor.mocks.dart';

@GenerateNiceMocks([MockSpec<AuthTokenProvider>()])
void main() {
  late MockAuthTokenProvider mockAuthTokenProvider;
  late MockAuthBloc mockAuthBloc;
  late RefreshTokenInterceptor refreshTokenInterceptor;
  late DioException dioException401;
  late DioException dioException403;
  late DioException dioException500;

  setUp(() {
    mockAuthTokenProvider = MockAuthTokenProvider();
    mockAuthBloc = MockAuthBloc();
    refreshTokenInterceptor = RefreshTokenInterceptor(
      mockAuthTokenProvider,
      mockAuthBloc,
      MockDio(),
    );

    dioException401 = DioException(
      requestOptions: RequestOptions(path: '/test'),
      response: Response(statusCode: 401, requestOptions: RequestOptions(path: '/test')),
    );
    dioException403 = DioException(
      requestOptions: RequestOptions(path: '/test'),
      response: Response(statusCode: 403, requestOptions: RequestOptions(path: '/test')),
    );
    dioException500 = DioException(
      requestOptions: RequestOptions(path: '/test'),
      response: Response(statusCode: 500, requestOptions: RequestOptions(path: '/test')),
    );
  });

  setUpAll(() {
    GetIt.instance.registerFactory<AuthBloc>(() => mockAuthBloc);
  });

  test('Triggers token refresh on 401/403 with valid tokens', () async {
    when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => 'accessToken');
    when(mockAuthTokenProvider.getRefreshToken()).thenAnswer((_) async => 'refreshToken');

    await refreshTokenInterceptor.onError(dioException401, ErrorInterceptorHandler());
    verify(mockAuthBloc.add(any)).called(1);

    await refreshTokenInterceptor.onError(dioException403, ErrorInterceptorHandler());
    verify(mockAuthBloc.add(any)).called(1);
  });

  test('Does not trigger token refresh on 401/403 with null tokens', () async {
    when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => null);
    when(mockAuthTokenProvider.getRefreshToken()).thenAnswer((_) async => null);

    await refreshTokenInterceptor.onError(dioException401, ErrorInterceptorHandler());
    await refreshTokenInterceptor.onError(dioException403, ErrorInterceptorHandler());
    verifyNever(mockAuthBloc.add(any));
  });

  test('Does not handle other status codes', () async {
    when(mockAuthTokenProvider.getToken()).thenAnswer((_) async => 'accessToken');
    when(mockAuthTokenProvider.getRefreshToken()).thenAnswer((_) async => 'refreshToken');

    await refreshTokenInterceptor.onError(dioException500, ErrorInterceptorHandler());
    verifyNever(mockAuthBloc.add(any));
  });
}
