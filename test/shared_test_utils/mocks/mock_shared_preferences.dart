import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockPathProviderPlatform extends Mock
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  static String kTemporaryPath = 'temporaryPath${DateTime.now().millisecondsSinceEpoch}';
  static String kApplicationSupportPath =
      'applicationSupportPath${DateTime.now().millisecondsSinceEpoch}';
  static String kDownloadsPath = 'downloadsPath${DateTime.now().millisecondsSinceEpoch}';
  static String kLibraryPath = 'libraryPath${DateTime.now().millisecondsSinceEpoch}';
  static String kApplicationDocumentsPath =
      'applicationDocumentsPath${DateTime.now().millisecondsSinceEpoch}';
  static String kExternalCachePath = 'externalCachePath${DateTime.now().millisecondsSinceEpoch}';
  static String kExternalStoragePath =
      'externalStoragePath${DateTime.now().millisecondsSinceEpoch}';

  static List<String> directories = [
    kTemporaryPath,
    kApplicationSupportPath,
    kDownloadsPath,
    kLibraryPath,
    kApplicationDocumentsPath,
    kExternalCachePath,
    kExternalStoragePath,
  ];

  static Future<void> cleanUpTestPaths() async {
    for (final path in directories) {
      try {
        final directory = Directory(path);
        if (directory.existsSync()) {
          await directory.delete(recursive: true);
        }
      } catch (e) {
        // Ignore errors during cleanup
      }
    }
  }

  @override
  Future<String> getTemporaryPath() async {
    return kTemporaryPath;
  }

  @override
  Future<String> getApplicationSupportPath() async {
    return kApplicationSupportPath;
  }

  @override
  Future<String> getLibraryPath() async {
    return kLibraryPath;
  }

  @override
  Future<String> getApplicationDocumentsPath() async {
    return kApplicationDocumentsPath;
  }

  @override
  Future<String> getExternalStoragePath() async {
    return kExternalStoragePath;
  }

  @override
  Future<List<String>> getExternalCachePaths() async {
    return <String>[kExternalCachePath];
  }

  @override
  Future<String> getDownloadsPath() async {
    return kDownloadsPath;
  }
}
