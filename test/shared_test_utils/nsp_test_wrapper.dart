import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:national_skills_platform/core/shared/constants.dart';
import 'package:national_skills_platform/core/theme/theme.dart';
import 'package:national_skills_platform/generated/codegen_loader.g.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'mocks/mock_shared_preferences.dart';

Future<void> testEnvSetup() async {
  await loadAppFonts();

  PathProviderPlatform.instance = MockPathProviderPlatform();
  SharedPreferences.setMockInitialValues({
    'locale': 'en',
    'countryCode': '',
  });
  await Hive.initFlutter();
  await Hive.openBox(HiveKeys.hiveNspStorage);
  EasyLocalization.logger.enableLevels = [];
  await EasyLocalization.ensureInitialized();
}

Future<void> testEnvTearDown() async {
  await MockPathProviderPlatform.cleanUpTestPaths();
}

Widget nspTestWrapper({required Widget child}) {
  return EasyLocalization(
    supportedLocales: const [Locale(Constants.localeEN), Locale(Constants.localeAR)],
    path: AssetsPath.translationPath,
    fallbackLocale: const Locale(Constants.localeEN),
    startLocale: const Locale(Constants.localeEN),
    assetLoader: const CodegenLoader(),
    child: Builder(
      builder: (context) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: const Locale(Constants.localeEN), // Force English locale
          theme: AppTheme().light(),
          home: child,
        );
      },
    ),
  );
}
