{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Demo",
            "request": "launch",
            "type": "dart",
            "flutterMode" : "debug",
            "args": [
                "--flavor=demo",
                "--dart-define-from-file=env/demo.json"
            ]
        },
        {
            "name": "Stage",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor=stage",
                "--dart-define-from-file=env/stage.json"
            ]
        },
        {
            "name": "UAT",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor=uat",
                "--dart-define-from-file=env/uat.json"
            ]
        },
        {
            "name": "Prod",
            "request": "launch",
            "type": "dart",
            "args": [
                "--flavor=prod",
                "--dart-define-from-file=env/prod.json"
            ]
        }
    ]
}