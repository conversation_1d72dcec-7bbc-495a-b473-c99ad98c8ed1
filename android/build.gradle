buildscript {
    ext.kotlin_version = '1.9.22'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        // check only for "com.android.library" to not modify
        // your "app" subproject. All plugins will have "com.android.library" plugin, and only your app "com.android.application"
        // Change your application's namespace in main build.gradle and in main android block.

        if (project.plugins.hasPlugin("com.android.library")) {
            project.android {
                if (namespace == null) {
                    namespace project.group
                }
            }
        }
    }
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
