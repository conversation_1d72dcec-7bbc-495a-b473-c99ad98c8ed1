plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

// Determine if local signing should be used
def useLocalSigning = localProperties.getProperty('localSigning', 'false').toBoolean()

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key/key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.takamolholding.NSP"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    flavorDimensions "env"
    productFlavors {
        demo {
            dimension "env"
            versionNameSuffix "-demo"
            resValue "string", "app_name", "SP Demo"
        }
        stage {
            dimension "env"
            resValue "string", "app_name", "Skills Portal Stage"
            versionNameSuffix "-stage"
        }
        uat {
            dimension "env"
            resValue "string", "app_name", "Skills Portal UAT"
            versionNameSuffix "-uat"
        }
        prod {
            dimension "env"
            resValue "string", "app_name", "Skills Portal"
        }
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.takamolholding.NSP"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion 34
        compileSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
    signingConfigs {
        release {
            storeFile file("../../android.keystore")
            storePassword System.getenv("ANDROID_KEYSTORE_PASSWORD")
            keyAlias System.getenv("ANDROID_KEYSTORE_ALIAS")
            keyPassword System.getenv("ANDROID_KEYSTORE_PASSWORD")
        }
        localRelease {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            // Use the appropriate signing config based on the local property
            signingConfig useLocalSigning ? signingConfigs.localRelease : signingConfigs.release
        }
    }
}

flutter {
    source '../..'
}

dependencies {}
