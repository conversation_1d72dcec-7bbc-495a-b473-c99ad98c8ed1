name: SonarCube Analysis

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  sonar-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Shallow clones should be disabled for better analysis

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.29.3'  # Use your Flutter version
        channel: 'stable'

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'

    - name: Cache Flutter dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.pub-cache
          **/.dart_tool
        key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: |
          ${{ runner.os }}-flutter-

    - name: Prepare project for analysis
      run: |
        chmod +x scripts/prepare-sonar-analysis.sh
        ./scripts/prepare-sonar-analysis.sh

    - name: Run tests with coverage
      run: |
        flutter test --coverage
        
    - name: Convert coverage to LCOV format
      run: |
        # Ensure coverage is in the right format
        if [ -f "coverage/lcov.info" ]; then
          echo "Coverage file found"
        else
          echo "No coverage file found, creating empty one"
          mkdir -p coverage
          touch coverage/lcov.info
        fi

    - name: SonarCube Scan
      uses: sonarqube-quality-gate-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      with:
        projectBaseDir: .
        args: >
          -Dsonar.projectKey=${{ secrets.SONAR_PROJECT_KEY }}
          -Dsonar.organization=${{ secrets.SONAR_ORGANIZATION }}
          -Dsonar.host.url=${{ secrets.SONAR_HOST_URL }}
          -Dsonar.login=${{ secrets.SONAR_TOKEN }}
          -Dsonar.dart.coverage.reportPaths=coverage/lcov.info
          -Dsonar.sources=lib
          -Dsonar.tests=test
          -Dsonar.exclusions=**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart
          -Dsonar.coverage.exclusions=**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart,**/*_test.dart
