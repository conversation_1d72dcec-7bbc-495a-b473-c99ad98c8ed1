# National Skills Platform
nsp mobile application

## Project and its structure
This repo is the project for the NSP app written in flutter.

- `core` folder containing the components needed by almost all features
- `features` folder containing specific main app features
- `injection_container` folder containing the dependency injection setup
- `generated` folder contains generated files of localization

---

## Architecture
The project use some third party library:

* [get_it](https://pub.dev/packages/get_it) as a service locator for the Inversion of Control
  with [injectable](https://pub.dev/packages/injectable).
* [go_router](https://pub.dev/packages/go_router) for handling navigation and routing in a
  declarative way.
* [flutter_bloc](https://pub.dev/packages/flutter_bloc) as a state management
  pattern. [Here](https://bloclibrary.dev/#/) the home page of bloc state management library.

> Tips:
>
>  we use the [Bloc plugin](https://plugins.jetbrains.com/plugin/12129-bloc) for Android Studio to
> generate boilerplate code for BLoC state management.

In general we suggest to develop and scaffold your code following
the [Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html).

---

## Development setup and IDE configurations
Install flutter SDK following [these](https://flutter.dev/docs/get-started/install) steps.
Find there also how to set-up Android Emulator or iOS Simulator for launching the app.

- In project we use leftHook git manager for (local githooks) configuring commands that run on
  different git actions (commit, push, etc).
- Do not change analysis_options.yaml or githook rules without discussing it with the team.

#### For this project we are using length limit of the code equals `--length-limit=100`:
- For VS Code it settings up automatically.
- For Android Studio you should set up manually: "Android Studio" -> "Settings" -> "Editor" -> "Code
  Style" -> "Dart" set "Line Length" to 100
- If you are using different an IDE please add your own settings to code base or description here
  how other can configure the IDE.

> Tips:
>
> We enable inside the android studio preferences -> Language & Frameworks -> Flutter this useful
> options:
> - Format code on save
> - Organize imports on save

#### Naming commits:
All commit message should follow the [conventional commit](https://www.conventionalcommits.org/en/v1.0.0/) rules.

And we accepted as a rule to add ticket number to the commit every commit name e.g: “fix: IPA-0000 commit message”

## Tests
Do always cover with unit tests all your new code, even small functions, especially converters,
regEx related functions and other
utils type of functions.

Also write bloc tests for each bloc you create.
In order to create bloc tests you can use the official Dart
Package [https://pub.dev/packages/bloc_test].
You may need to use another official Dart package [https://pub.dev/packages/mockito] with you bloc
tests
in order to generate some code.

### Golden tests
- Always create golden tests, if you've created new page or new UI component (BottomSheet,
  AlertDialog etc.)
- If generated image of some specific page is getting too big in height, it has to be divided into
  smaller
  pieces, otherwise it becomes difficult to identify the broken parts of affected UI during code
  review.

### Test Coverage
To get test coverage and to see which part of code is/isn't covered with tests, run commands below:

- flutter test --coverage && remove_from_coverage -f coverage/lcov.info -r '\.g\.dart$' && genhtml coverage/lcov.info -o coverage/html
- open coverage/html/index.html

### Localization
After updating json files in `assets/translations` run the command below to regenerate/update the dart files:
```flutter pub run easy_localization:generate --source-dir assets/translations && flutter pub run easy_localization:generate -f keys -o locale_keys.g.dart --source-dir assets/translations```

### Upgrading Flutter
After upgrading flutter version, you should change .variables.gitlab-ci.yml file to the new version of flutter so that CI also uses the same version.

### In case if ios build starts failing due podfile.lock mismatch
Try running `bundle exec pod update` and `pod update`

### Releasing UAT
Before releasing UAT, go to file called .release and update the version: ```release: nsp-mobile-apps.X.Y.Z``` push this to your MR, then merge it to "stage" branch and you can release UAT (using CI manual-job).

#### Rules for MR

1. In your ticket on the right side under "Details" section, click "create branch" and there you can
   get command to create a new branch, this is important because it will link this branch to that
   ticket on Jira.
2. Don't forget to write a tests to new code and check coverage with the command mentioned above.
3. Then pull latest changes from demo branch into your branch and
   a) run the lint (flutter analyze)
   b) update golden tests to check if there are any changes in the UI and nothing is broken (flutter test --update-goldens)
   c) run tests (flutter test) scripts, to make sure there are no errors and also run the app on
   both platforms.
4. Also run the formatter: "dart format lib --line-length=100 --set-exit-if-changed" and "dart
   format test --line-length=100 --set-exit-if-changed" (especially in case you have run the
   build runner). If you run the lint formatter before the build runner, the newly generated .gr
   files won't be formatted, and your MR will fail because of that.
5. If there were changes merged into main which causes your MR to not be able to merge automatically
   anymore, merge with main again locally and go through the process from step 1.
6. Devs which request approval of their MRs should first wait until the pipeline has passed. You
   won't get approved if the pipeline is still running.




NSP Documentation:

# Technical Documentation: National Skills Platform (NSP) Flutter App

## 1. Introduction

This document provides a technical overview of the National Skills Platform (NSP) Flutter mobile application. The application allows users (primarily trainees) to access training materials, manage their learning journey, browse course catalogs, interact with platform features like quizzes and qualification tests, and manage their profile information. It serves as the primary mobile interface for the NSP ecosystem.

**Key Purpose:** To deliver a seamless, mobile-first experience for the National Skills Platform, empowering users to engage with learning content and platform features anytime, anywhere.

## 2. Architecture & Design Patterns

The application employs a modern, layered architecture focused on separation of concerns, testability, and maintainability.

*   **State Management:** **BLoC (Business Logic Component)** pattern using the `flutter_bloc` package is the core state management solution.
    *   **Reactive Approach:** Facilitates handling asynchronous operations and UI updates based on state changes.
    *   **Feature-Scoped BLoCs:** Each feature module typically encapsulates its state logic within dedicated BLoC/Cubit classes (e.g., `AuthBloc`, `CatalogBloc`, `UserBloc`).
    *   **Event/State Flow:** UI triggers actions by adding Events to BLoCs, which process logic and emit new States, causing the UI to rebuild reactively.
    *   **Debugging:** `AppBlocObserver` logs BLoC events, transitions, and errors during development for easier troubleshooting.
*   **Dependency Injection (DI):** **GetIt** service locator combined with the **Injectable** code generator manages dependencies.
    *   **Loose Coupling:** Decouples classes, making components easier to test and replace.
    *   **Configuration:** Setup is centralized in `injection_container/injection_container.dart` and `injection_container/register_module.dart`. Annotations (`@injectable`, `@singleton`, `@lazySingleton`) control dependency lifecycles and registration.
*   **Layered Architecture:** Code within feature modules generally adheres to:
    *   **Presentation Layer:** (`presentation/`) UI elements (Pages, Widgets) and Presentation Logic Controllers (BLoCs/Cubits). Responsible for displaying data and handling user input.
    *   **Domain Layer:** (`domain/`) Core business logic, Use Cases (orchestrating actions), and abstract Repository interfaces defining data contracts. Entity definitions might also reside here if distinct from data models.
    *   **Data Layer:** (`data/`) Concrete Repository implementations, Data Sources (interacting with APIs, local storage), and Data Models (DTOs, often generated with Freezed/JsonSerializable) representing data structures.
*   **Routing:** Declarative navigation managed by **GoRouter**.
    *   **Centralized Configuration:** Routes defined in `router.dart`.
    *   **Type Safety:** Uses enums (`Routes`) for route names.
    *   **Deep Linking & Parameters:** Supports path parameters, query parameters (`RouterQueryConstants`), and passing complex objects (`extra`).
    *   **Shell Routes:** Implements the main bottom navigation bar (`AppBottomNavBar`) using `StatefulShellRoute`, preserving navigation state within each tab.
    *   **Custom Transitions:** Utilizes `CustomTransitionPage` (`SlideBottomToTopTransition`, `MultiTransition`) for tailored screen transition animations.
*   **API Communication:** Handled via the **Dio** package.
    *   **Abstraction:** Data Sources encapsulate direct API calls. Repositories provide a clean interface for the domain/presentation layers.
    *   **Interceptors:** Centralize cross-cutting concerns:
        *   Token Injection (`Authorization` header).
        *   Token Refresh (`RefreshTokenInterceptor`) using `AuthBloc`.
        *   Request/Response Logging (`Alice` in debug).
        *   Automatic Retries (`RetryInterceptor` via `dio_smart_retry`).
        *   Common Headers (`X-Current-Role`, `X-Request-Id`).
*   **Data Modeling:**
    *   **Freezed:** Generates immutable data classes, state classes, and unions, reducing boilerplate and enhancing type safety.
    *   **JsonSerializable:** Used with Freezed for seamless Dart object <-> JSON conversion.
*   **Feature-Driven Structure:** The `lib/features` directory organizes the codebase by application functionality, promoting modularity and team collaboration.

## 3. Project Structure Explained

The project follows a standard Flutter layout, enhanced with a feature-first organization within the `lib` directory.

```
.
├── android/                       # Android Native Project
│   ├── app/                       # Main Android application module
│   │   ├── build.gradle           # App-level build configuration
│   │   └── src/
│   │       ├── main/
│   │       │   ├── AndroidManifest.xml
│   │       │   ├── java/ / kotlin/
│   │       │   └── res/
│   │       └── ...                # debug/profile manifests
│   ├── build.gradle               # Top-level Android build script
│   ├── settings.gradle            # Android project settings
│   └── ...                        # Other Gradle files
│
├── assets/                        # Static Application Assets
│   ├── icons/                     # Application icons
│   │   └── bottom_nav_bar_icons/
│   ├── images/                    # General images
│   ├── translations/              # Localization files
│   └── vector_images/             # Scalable Vector Graphics
│
├── ios/                           # iOS Native Project
│   └── ...                        # Xcode project files
│
├── lib/                           # Main Dart/Flutter Codebase
│   ├── core/                      # Foundational Code
│   │   ├── environment/
│   │   │   └── environment_configs.dart
│   │   ├── shared/
│   │   │   ├── constants.dart
│   │   │   ├── converters/
│   │   │   ├── extensions/
│   │   │   ├── token_provider/
│   │   │   │   ├── token_provider.dart
│   │   │   │   └── refresh_token_interceptor.dart
│   │   │   ├── error_handler.dart
│   │   │   └── app_print.dart
│   │   ├── theme/
│   │   │   ├── app_colors.dart
│   │   │   ├── app_text_theme.dart
│   │   │   └── app_theme.dart
│   │   └── utils/
│   │       └── bloc_observer.dart
│   │
│   ├── features/                  # Application Feature Modules
│   │   ├── auth/                  # Authentication
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── catalog/               # Course Catalog
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── course_details/        # Course Details
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   ├── presentation/
│   │   │   ├── learning_track_details/
│   │   │   └── training_details/
│   │   ├── home/                  # Home Screen
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── my_learnings/          # My Learnings
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── profile_page/          # User Profile
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── qualification_test/    # Tests
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── quiz/                  # Quizzes
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── search/                # Search
│   │   │   ├── data/
│   │   │   ├── domain/
│   │   │   └── presentation/
│   │   ├── shared/                # Shared Components
│   │   │   ├── pages/
│   │   │   ├── transitions/
│   │   │   └── ui_components/
│   │   └── training_consumption/  # Course Taking
│   │       ├── data/
│   │       ├── domain/
│   │       └── presentation/
│   │
│   ├── injection_container/       # Dependency Injection
│   │   ├── injection_container.dart
│   │   ├── injection_container.config.dart
│   │   └── register_module.dart
│   │
│   ├── main.dart                  # App Entry Point
│   ├── root_page.dart             # Initial Routing
│   └── router.dart                # GoRouter Config
│
├── test/                          # Automated Tests
│   └── ...                        # Test files
│
├── pubspec.yaml                   # Project dependencies
├── pubspec.lock                   # Locked versions
└── ...                            # Other project files
```


## 4. Core Features & Modules

*   **Authentication (`auth`):** Manages user sign-in (Qiwa WebView flow), sign-out, token handling (secure storage, refresh), and initial app language selection. Uses `AuthBloc`.
*   **Catalog (`catalog`):** Enables users to browse, filter (by type, duration, language, sector, provider, location, skill level), and sort Trainings and Learning Tracks. Uses `TrainingsBloc`, `LearningTracksBloc`, various `FilterBlocs` (e.g., `SectorFilterBloc`).
*   **Course Details (`course_details`):** Displays comprehensive information about selected Trainings or Learning Tracks, including description, requirements, outcomes, syllabus, and enrollment options. Handles enrollment actions. Uses `CourseDetailsBloc`. Contains specific logic for `learning_track_details` and `training_details`.
*   **Home (`home`):** Serves as the main dashboard, showing a welcome message, potentially recent activities, popular courses, and featured sectors. Uses `HomePageBloc`.
*   **My Learnings (`my_learnings`):** Lists the user's enrolled, nominated, and completed Trainings and Learning Tracks. Allows filtering by status. Uses `MyLearningsBloc` for data fetching and `MyLearningsFilterCubit` for client-side filtering/sorting.
*   **Profile (`profile_page`):** Displays user details (name, email, phone, etc.), allows profile picture upload (gallery pick, crop) and deletion, and initiates logout. Uses `UserBloc`.
*   **Qualification Test (`qualification_test`):** Manages mandatory pre- and post-training assessments. Presents questions, records answers, submits results, displays scores, and handles retakes. Uses `QualificationTestBloc`.
*   **Quiz (`quiz`):** Handles quizzes embedded within lessons. Similar flow to Qualification Tests but typically tied to specific lesson content. Uses `QuizBloc`.
*   **Search (`search`):** Provides an interface for users to search the course catalog. Displays suggestions and allows navigation to full results. Uses `SearchBloc`.
*   **Shared (`shared`):** Contains common UI widgets (`AppButton`, `AppInput`), base pages (`AppBottomNavBar`), custom navigation transitions, and utilities used across multiple features.
*   **Training Consumption (`training_consumption`):** Manages the in-progress learning experience. Displays lesson content (Article, Video, File, Slide), handles navigation between lessons/meetings, manages lesson completion state, and integrates with Quizzes/Qualification Tests. Uses `TrainingConsumptionBloc` and navigation mixins.

## 5. Core Utilities (`lib/core`)

*   **Environment (`EnvironmentConfigs`):** Manages environment-specific settings like API URLs and Client IDs loaded via `--dart-define`. Crucial for switching between `stage`, `prod`, etc.
*   **Shared Utilities (`shared`):**
    *   **Constants:** Centralized definitions for API endpoints (`ApiConstants`), asset paths (`AssetsPath`), status codes, keys, locales, etc. Prevents magic strings/numbers.
    *   **Converters:** Functions for transforming data types (e.g., `DateTime` formatting, `String` to `Enum`, Bytes to MB).
    *   **Extensions:** Utility methods added to existing classes (e.g., `String.removeHtmlElements()`).
    *   **`AuthTokenProvider`:** Securely stores/retrieves JWT tokens using `FlutterSecureStorage`. Exposes a `Stream` (`authStateStream`) to react to login/logout changes. Central to managing authenticated state.
    *   **`ErrorHandler`:** An extension on `Future` providing a standardized way to handle exceptions (especially `DioException`) from async operations, reducing boilerplate in BLoCs.
    *   **`AppPrint`:** A `kDebugMode`-aware logging function.
*   **Theme (`theme`):**
    *   **`AppColors`:** Defines the app's specific color palette.
    *   **`AppTextTheme`:** Provides `TextStyle` definitions accessible via `context.textTheme`. Includes logic to switch between `Noto Sans` (EN) and `Tajawal` (AR) fonts based on locale. Offers convenient style extensions (`.bold`, `.semiBold`, `.greyPrimary`, etc.).
    *   **`AppTheme`:** Configures the global `ThemeData`.
*   **Utilities (`utils`):** Contains miscellaneous helper functions like PKCE code challenge generation (`code_challange_generator.dart`) and random string generation (`random_string_generator.dart`).

## 6. State Management (BLoC)

*   **Implementation:** `flutter_bloc` is used consistently across features.
*   **Structure:** Events (`_event.dart`) define actions/inputs. States (`_state.dart`, often using `Freezed`) represent immutable UI states. BLoCs (`_bloc.dart`) contain the business logic to transform events into states.
*   **UI Binding:** `BlocProvider` (often via DI) makes BLoCs available. `BlocBuilder`, `BlocListener`, `BlocConsumer` rebuild UI or trigger side effects (like showing toasts, navigation) based on state changes.
*   **Observation:** `AppBlocObserver` logs events, transitions, and errors in debug builds, aiding development.

## 7. Routing (GoRouter)

*   **Configuration:** Centralized in `router.dart`, defining routes using `GoRoute` and `StatefulShellRoute`.
*   **Bottom Navigation:** `StatefulShellRoute.indexedStack` manages the main tabs (Home, Catalog, My Learnings, Profile), preserving the state of each tab's navigation stack. `AppBottomNavBar` acts as the shell UI.
*   **Navigation:** Standard `pushNamed`, `go`, `pop` methods are used. `popUntil` and `popAllRoutes` extensions provide more control over the navigation stack.
*   **Data Passing:** Route parameters (`/path/:id`), query parameters (`/path?tab=...`), and the `extra` object are used for passing data between routes.
*   **Transitions:** Custom page transitions (`SlideBottomToTopTransition`, `MultiTransition`) enhance the user experience for specific navigations (e.g., modals, sequential lesson flow). `MultiTransition` supports directionality based on forward/backward navigation context (`LessonNavigationType`).

## 8. API Communication (Dio)

*   **Client:** `Dio` instance configured in `RegisterModule` and managed by `GetIt`.
*   **Layering:**
    *   **Data Sources:** Feature-specific classes (e.g., `TrainingsDataSource`, `UserDataSource`) directly use the `Dio` client to make HTTP requests.
    *   **Repositories:** Abstract data access (e.g., `TrainingsRepository`, `UserRepository`). They interact with Data Sources and might include caching logic (e.g., `SectorRepository`, `TrainingProvidersRepository`).
*   **Models:** Defined in `data/models` directories, using `Freezed` and `JsonSerializable` for immutability and JSON conversion.
*   **Interceptors:**
    *   `InterceptorsWrapper` (custom): Adds `Authorization: Bearer` token from `AuthTokenProvider`, `X-Current-Role`, and `X-Request-Id` headers to outgoing requests.
    *   `RefreshTokenInterceptor`: Custom interceptor that catches 401/403 errors, triggers a token refresh via `AuthBloc`, and retries the failed request upon success. Crucial for session management.
    *   `RetryInterceptor` (`dio_smart_retry`): Automatically retries failed `GET` requests based on network conditions or specific status codes (custom evaluator `_retryOnlyGetRequestsEvaluator`).
    *   `Alice`: In debug builds, logs detailed HTTP traffic, accessible via notification/shake.

## 9. Native Integration & Plugins

*   **Core Functionality:** Relies on numerous plugins for device features and common tasks.
*   **Key Plugins:**
    *   **UI/UX:** `flutter_native_splash`, `modal_bottom_sheet`, `timelines_plus`, `shimmer`, `skeletonizer`, `pull_to_refresh_flutter3`, `smooth_page_indicator`, `flutter_styled_toast`.
    *   **State/DI:** `flutter_bloc`, `get_it`, `injectable`.
    *   **Data/Network:** `dio`, `dio_smart_retry`, `flutter_secure_storage`, `hive`, `hive_flutter`.
    *   **Device/OS:** `path_provider`, `image_picker`, `share_plus`, `url_launcher`, `video_player`/`chewie`, `pdfrx`, `open_filex`, `background_downloader`, `platform`.
    *   **Utilities:** `equatable`, `freezed`, `json_serializable`, `easy_localization`, `crypto`, `uuid`, `sentry_flutter`.
    *   **Media:** `cached_network_image`, `flutter_svg`, `custom_image_crop`.
    *   **Web Integration:** `webview_flutter` (for Qiwa).
*   **Android:**
    *   **Flavors:** Configured in `android/app/build.gradle` for different environments (`demo`, `stage`, `uat`, `prod`), allowing different app names and potentially configurations per environment.
    *   **Permissions:** `INTERNET` permission requested in `AndroidManifest.xml`.
    *   **Queries:** Intents declared for interacting with browser, SMS, phone apps.
*   **iOS:** (Details limited by excluded files) Expected configurations in `Info.plist` (permissions) and dependency management via CocoaPods (`Podfile`).

## 10. UI & Theme

*   **Foundation:** Defined in `lib/core/theme`. Uses `AppColors`, `AppTextTheme` (with LTR/RTL font handling), and `AppTheme`.
*   **Reusability:** Common widgets in `lib/features/shared/ui_components` ensure consistency (buttons, inputs, lists, loading indicators, etc.).
*   **Localization:** `easy_localization` used for multi-language support (AR, EN). Translations stored in `assets/translations`. Keys generated in `locale_keys.g.dart`.
*   **Responsiveness:** Primarily targets mobile portrait mode, enforced via `SystemChrome.setPreferredOrientations`.

## 11. Build & Deployment

*   **Environments:** Build flavors (Android) and `--dart-define` provide environment-specific configurations (`EnvironmentConfigs.dart`).
*   **Android Signing:** `android/app/build.gradle` supports signing via secure environment variables or a local `key.properties` file for release builds.

## 12. Noteworthy Points & Potential Improvements

*   **iOS Configuration:** Details regarding iOS-specific setup (permissions in `Info.plist`, Podfile dependencies) are missing due to excluded files.
*   **Testing Strategy:** The `test` directory exists, but contents are excluded. A full understanding requires visibility into unit (`mockito`, `bloc_test`), widget (`golden_toolkit`), and integration tests.
*   **Qiwa WebView:** The `webview_flutter` dependency for Qiwa authentication might introduce UI/UX inconsistencies across platforms and requires careful maintenance. Native SDKs or other methods could be considered alternatives if available.
*   **Background Downloads:** `background_downloader` adds complexity in managing download states, progress, errors, and file system interactions. Robust error handling and user feedback are crucial.
*   **Arabic Pluralization:** The custom `plural_translations.dart` suggests a potential limitation or past issue with `easy_localization`'s built-in pluralization for Arabic. This might need revisiting.
*   **State Synchronization:** With multiple BLoCs (e.g., `MyLearningsBloc`, `CourseDetailsBloc`, `AuthBloc`), ensuring data consistency after actions like enrollment or logout requires careful coordination (e.g., refreshing data in related BLoCs).
*   **Error Granularity:** While `ErrorHandler` and Sentry exist, specific UI states for different types of errors (network vs. server vs. validation) within BLoCs could enhance the user experience.
*   **PTET Completion:** The Post-training evaluation Test (PTET) completion has been enhanced to handle edge cases where API errors might occur during the final question submission. The system now ensures trainees can complete the test even if there are backend issues, with proper state updates in both the `QualificationTestBloc` and `TrainingConsumptionBloc`.