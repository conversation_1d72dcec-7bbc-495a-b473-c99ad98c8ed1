include: package:lint/strict.yaml

analyzer:
  exclude:
    - '**.freezed.dart'
    - '**.g.dart'
    - '**.gr.dart'
    - '**.auth_bloc_mock.dart'
    - '**.mocks.dart'
    - '**.injectable.dart'

  strong-mode:
    implicit-casts: true

  language:
    strict-casts: false
    strict-inference: false
    strict-raw-types: false

  errors:
    # https://github.com/rrousselGit/freezed/issues/488
    invalid_annotation_target: ignore

prefer-const-border-radius: true

linter:
  rules:
    always_put_control_body_on_new_line: false
    flutter_style_todos: false
    avoid_multiple_declarations_per_line: false
    cascade_invocations: true
    sort_pub_dependencies: false
    constant_identifier_names: false
    depend_on_referenced_packages: false
    avoid_dynamic_calls: true
    omit_local_variable_types: false
    avoid_annotating_with_dynamic: true
  # avoid_print: false  # Uncomment to disable the `avoid_print` rule
  # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
