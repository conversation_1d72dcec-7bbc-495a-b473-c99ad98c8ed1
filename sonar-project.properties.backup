sonar.projectKey=takamol_nsp_nsp-mobile-app_8286a789-bc62-4cb4-9cdf-60b4436d0fa4
sonar.projectName=nsp-mobile-app

# Source and test directories
sonar.sources=lib
sonar.tests=test

# Language configuration
sonar.language=dart
sonar.sourceEncoding=UTF-8

# Dart-specific configuration
sonar.dart.analyzer.file=analysis_options.yaml
sonar.dart.coverage.reportPaths=coverage/lcov.info

# File patterns
sonar.inclusions=**/*.dart,pubspec.yaml
sonar.exclusions=**/*_test.dart,**/generated/**,**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart,**/*.config.dart,**/generated_plugin_registrant.dart,**/.dart_tool/**,**/build/**,**/.pub-cache/**

# Coverage exclusions
sonar.coverage.exclusions=**/*_test.dart,**/generated/**,**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart,**/*.config.dart,**/generated_plugin_registrant.dart

# Additional Dart configuration
sonar.lang.patterns.dart=**/*.dart

# Working directory (important for dependency resolution)
sonar.working.directory=.

# Flutter SDK configuration
# Set the Flutter SDK path - update this to your actual Flutter installation path
# Examples:
# macOS/Linux: /Users/<USER>/flutter or /home/<USER>/flutter
# Windows: C:\flutter
sonar.dart.sdk=/Users/<USER>/fvm/versions/3.29.3

# Pub cache path (optional but recommended)
sonar.dart.pub.cache=/Users/<USER>/.pub-cache

# Scanner properties for better dependency resolution
sonar.scanner.force-deprecated-java-version=false
