name: national_skills_platform
description: "National Skills Platform"

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.2.0+90

environment:
  sdk: ^3.6.0

scripts:
  gen: flutter pub run build_runner build --delete-conflicting-outputs
  pre: git hook run pre-commit
  ios-update: cd ios && pod install --repo-update
  xcode-open: open ios/Runner.xcworkspace

dependencies:
  flutter:
    sdk: flutter

  get_it: ^8.0.2
  go_router: ^15.1.1
  flutter_bloc: ^9.1.1
  path_provider: ^2.0.0
  equatable: ^2.0.5
  injectable: ^2.3.2
  freezed_annotation: ^3.0.0
  sentry_flutter: ^8.14.2
  easy_localization: ^3.0.7

  flutter_secure_storage: ^9.0.0
  cached_network_image: ^3.3.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0


  loading_overlay_pro: ^2.1.1
  flutter_svg: ^2.1.0
  flutter_native_splash: ^2.4.4
  modal_bottom_sheet: ^3.0.0
  timelines_plus: ^1.0.3
  shimmer: ^3.0.0
  flutter_html: ^3.0.0-alpha.3
  skeletonizer: ^1.4.2
  pull_to_refresh_flutter3: ^2.0.2
  smooth_page_indicator: ^1.0.0+2
  flutter_styled_toast: ^2.2.1

  dio: ^5.4.0
  webview_flutter: ^4.11.0

  crypto: ^3.0.3
  dio_smart_retry: ^7.0.1
  json_annotation: ^4.9.0
  share_plus: ^11.0.0
  chewie: ^1.11.3
  uuid: ^4.4.2
  pdfrx: ^1.0.103
  image_picker: ^1.1.2
  open_filex: ^4.6.0
  background_downloader: ^9.2.1
  custom_image_crop:
    git:
      url: https://github.com/icapps/flutter-custom-image-crop.git
      ref: 5a863748965ecb2a8b57b698dacf9861bde610be
  platform: ^3.1.6

dependency_overrides:
  http: ^1.2.2
  webview_flutter_wkwebview: 3.20.0 #https://github.com/flutter/flutter/issues/162437

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.8
  lint: ^2.3.0
  injectable_generator: ^2.7.0
  json_serializable: ^6.9.5
  freezed: ^3.0.6
  flutter_alice: ^2.0.1

  #test
  mockito: ^5.4.4
  bloc_test: ^10.0.0
  golden_toolkit: ^0.15.0
  shared_preferences: ^2.4.6
  remove_from_coverage: ^2.0.0
  plugin_platform_interface: ^2.1.8
  url_launcher_platform_interface: ^2.3.2

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/translations/
    - assets/fonts/
    - assets/images/
    - assets/vector_images/
    - assets/icons/
    - assets/icons/bottom_nav_bar_icons/
    - assets/icons/course_details/

  fonts:
    - family: Noto Sans
      fonts:
        - asset: assets/fonts/Noto-Sans/NotoSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/Noto-Sans/NotoSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Noto-Sans/NotoSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/Noto-Sans/NotoSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/Noto-Sans/NotoSans-Light.ttf
          weight: 300

    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal/Tajawal-ExtraBold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal/Tajawal-Bold.ttf
          weight: 600
        - asset: assets/fonts/Tajawal/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal/Tajawal-Regular.ttf
          weight: 400
        - asset: assets/fonts/Tajawal/Tajawal-Light.ttf
          weight: 300
