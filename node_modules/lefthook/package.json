{"name": "lefthook", "version": "1.10.1", "description": "Simple git hooks manager", "repository": {"type": "git", "url": "git+https://github.com/evilmartians/lefthook.git"}, "main": "bin/index.js", "bin": {"lefthook": "bin/index.js"}, "keywords": ["git", "hook", "manager"], "author": "mrexox", "license": "MIT", "bugs": {"url": "https://github.com/evilmartians/lefthook/issues", "email": "<EMAIL>"}, "homepage": "https://github.com/evilmartians/lefthook#readme", "optionalDependencies": {"lefthook-darwin-arm64": "1.10.1", "lefthook-darwin-x64": "1.10.1", "lefthook-linux-arm64": "1.10.1", "lefthook-linux-x64": "1.10.1", "lefthook-freebsd-arm64": "1.10.1", "lefthook-freebsd-x64": "1.10.1", "lefthook-openbsd-arm64": "1.10.1", "lefthook-openbsd-x64": "1.10.1", "lefthook-windows-arm64": "1.10.1", "lefthook-windows-x64": "1.10.1"}, "scripts": {"postinstall": "node postinstall.js"}}