# Refer to Lefthook documentation for detailed configuration:
# https://github.com/evilmartians/lefthook/blob/master/docs/configuration.md

pre-commit:
  parallel: true
  commands:
    project-formatting:
      tags: [code formatting]
      run: |
        dart format lib --line-length=100 --set-exit-if-changed
        dart format test --line-length=100 --set-exit-if-changed
    block-main-commit:
      run: sh node_modules/lefthook/pre-push/block_main_push.sh

pre-push:
  parallel: true
  commands:
    flutter-analyzer:
      tags: [error and warning analyzer]
      run: flutter analyze
    project-formatting:
      tags: [code formatting]
      run: |
        dart format lib --line-length=100 --set-exit-if-changed
        dart format test --line-length=100 --set-exit-if-changed
#    mandatory-cocoapods-update:
#      tags: [updating cocoapods]
#      run: |
#        cd ios
#        pod repo update
#        pod update
    block-main-push:
      run: sh node_modules/lefthook/pre-push/block_main_push.sh

#other examples:
# pre-rebase:
#   parallel: true
#   commands:
#     eslint:
#       glob: "*.{js,ts,jsx,tsx}"
#       run: yarn eslint {staged_files}
#     rubocop:
#       tags: backend style
#       glob: "*.rb"
#       exclude: '(^|/)(application|routes)\.rb$'
#       run: bundle exec rubocop --force-exclusion {all_files}
#     govet:
#       tags: backend style
#       files: git ls-files -m
#       glob: "*.go"
#       run: go vet {files}
#   scripts:
#     "hello.js":
#       runner: node
#     "any.go":
#       runner: go run
