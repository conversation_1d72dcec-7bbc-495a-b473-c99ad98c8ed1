#!/bin/bash

# Script to run SonarCube analysis locally
# Make sure you have SonarQube server running and sonar-scanner installed

set -e

echo "🔍 Running local SonarCube analysis..."

# Check if sonar-scanner is installed
if ! command -v sonar-scanner &> /dev/null; then
    echo "❌ sonar-scanner is not installed"
    echo "Please install sonar-scanner:"
    echo "  macOS: brew install sonar-scanner"
    echo "  Linux: Download from https://docs.sonarqube.org/latest/analysis/scan/sonarscanner/"
    exit 1
fi

# Prepare the project
echo "📋 Preparing project for analysis..."
./scripts/prepare-sonar-analysis.sh

# Run tests with coverage (optional but recommended)
echo "🧪 Running tests with coverage..."
flutter test --coverage || echo "⚠️  Tests failed or no tests found, continuing with analysis..."

# Ensure coverage directory exists
mkdir -p coverage
if [ ! -f "coverage/lcov.info" ]; then
    echo "ℹ️  No coverage file found, creating empty one"
    touch coverage/lcov.info
fi

# Check if SonarQube server is running (optional check)
SONAR_HOST_URL=${SONAR_HOST_URL:-"http://localhost:9000"}
echo "🌐 Checking SonarQube server at $SONAR_HOST_URL..."

if curl -s "$SONAR_HOST_URL/api/system/status" > /dev/null 2>&1; then
    echo "✅ SonarQube server is running"
else
    echo "⚠️  Cannot reach SonarQube server at $SONAR_HOST_URL"
    echo "Make sure SonarQube is running or set SONAR_HOST_URL environment variable"
fi

# Run SonarQube analysis
echo "🚀 Starting SonarQube analysis..."

# You can override these with environment variables
SONAR_PROJECT_KEY=${SONAR_PROJECT_KEY:-"nsp-mobile-app"}
SONAR_PROJECT_NAME=${SONAR_PROJECT_NAME:-"NSP Mobile App"}
SONAR_PROJECT_VERSION=${SONAR_PROJECT_VERSION:-"1.0"}

sonar-scanner \
  -Dsonar.projectKey="$SONAR_PROJECT_KEY" \
  -Dsonar.projectName="$SONAR_PROJECT_NAME" \
  -Dsonar.projectVersion="$SONAR_PROJECT_VERSION" \
  -Dsonar.sources=lib \
  -Dsonar.tests=test \
  -Dsonar.language=dart \
  -Dsonar.sourceEncoding=UTF-8 \
  -Dsonar.dart.analyzer.file=analysis_options.yaml \
  -Dsonar.dart.coverage.reportPaths=coverage/lcov.info \
  -Dsonar.inclusions="**/*.dart,pubspec.yaml" \
  -Dsonar.exclusions="**/*_test.dart,**/generated/**,**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart,**/*.config.dart,**/generated_plugin_registrant.dart,**/.dart_tool/**,**/build/**,**/.pub-cache/**" \
  -Dsonar.coverage.exclusions="**/*_test.dart,**/generated/**,**/*.g.dart,**/*.freezed.dart,**/*.mocks.dart,**/*.config.dart,**/generated_plugin_registrant.dart" \
  -Dsonar.working.directory=. \
  ${SONAR_EXTRA_ARGS:-}

echo "✅ SonarQube analysis completed!"
echo "📊 Check the results at: $SONAR_HOST_URL/dashboard?id=$SONAR_PROJECT_KEY"
