#!/bin/bash

# <PERSON>ript to automatically detect and configure Flutter SDK path for SonarQube

set -e

echo "🔍 Detecting Flutter SDK path..."

# Function to detect Flutter SDK path
detect_flutter_sdk() {
    # Method 1: Check if flutter command is available and get its path
    if command -v flutter &> /dev/null; then
        FLUTTER_COMMAND_PATH=$(which flutter)
        echo "Found Flutter command at: $FLUTTER_COMMAND_PATH"
        
        # Get the real path (resolve symlinks)
        FLUTTER_REAL_PATH=$(readlink -f "$FLUTTER_COMMAND_PATH" 2>/dev/null || realpath "$FLUTTER_COMMAND_PATH" 2>/dev/null || echo "$FLUTTER_COMMAND_PATH")
        
        # Extract SDK path (remove /bin/flutter from the end)
        FLUTTER_SDK_PATH=$(dirname "$(dirname "$FLUTTER_REAL_PATH")")
        
        # Verify this is actually a Flutter SDK directory
        if [ -f "$FLUTTER_SDK_PATH/bin/flutter" ] && [ -d "$FLUTTER_SDK_PATH/packages" ]; then
            echo "✅ Flutter SDK detected at: $FLUTTER_SDK_PATH"
            return 0
        fi
    fi
    
    # Method 2: Check common Flutter installation paths
    COMMON_PATHS=(
        "$HOME/flutter"
        "$HOME/fvm/default"
        "$HOME/fvm/versions/stable"
        "$HOME/development/flutter"
        "/usr/local/flutter"
        "/opt/flutter"
        "C:/flutter"
        "C:/src/flutter"
    )
    
    for path in "${COMMON_PATHS[@]}"; do
        if [ -f "$path/bin/flutter" ] && [ -d "$path/packages" ]; then
            FLUTTER_SDK_PATH="$path"
            echo "✅ Flutter SDK found at: $FLUTTER_SDK_PATH"
            return 0
        fi
    done
    
    # Method 3: Check FVM versions
    if [ -d "$HOME/fvm/versions" ]; then
        echo "🔍 Checking FVM versions..."
        for version_dir in "$HOME/fvm/versions"/*; do
            if [ -f "$version_dir/bin/flutter" ] && [ -d "$version_dir/packages" ]; then
                FLUTTER_SDK_PATH="$version_dir"
                echo "✅ Flutter SDK found in FVM: $FLUTTER_SDK_PATH"
                return 0
            fi
        done
    fi
    
    echo "❌ Flutter SDK not found in common locations"
    return 1
}

# Function to detect pub cache path
detect_pub_cache() {
    # Default pub cache locations
    if [ -d "$HOME/.pub-cache" ]; then
        PUB_CACHE_PATH="$HOME/.pub-cache"
    elif [ -d "$APPDATA/Pub/Cache" ]; then  # Windows
        PUB_CACHE_PATH="$APPDATA/Pub/Cache"
    else
        # Try to get from Flutter
        if command -v flutter &> /dev/null; then
            PUB_CACHE_PATH=$(flutter pub cache dir 2>/dev/null || echo "$HOME/.pub-cache")
        else
            PUB_CACHE_PATH="$HOME/.pub-cache"
        fi
    fi
    
    echo "📦 Pub cache path: $PUB_CACHE_PATH"
}

# Function to update sonar-project.properties
update_sonar_config() {
    local flutter_sdk="$1"
    local pub_cache="$2"
    local config_file="sonar-project.properties"
    
    if [ ! -f "$config_file" ]; then
        echo "❌ $config_file not found"
        return 1
    fi
    
    echo "📝 Updating $config_file with detected paths..."
    
    # Create a backup
    cp "$config_file" "$config_file.backup"
    
    # Update Flutter SDK path
    if grep -q "sonar.dart.sdk=" "$config_file"; then
        # Replace existing line
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|^sonar.dart.sdk=.*|sonar.dart.sdk=$flutter_sdk|" "$config_file"
        else
            # Linux
            sed -i "s|^sonar.dart.sdk=.*|sonar.dart.sdk=$flutter_sdk|" "$config_file"
        fi
    else
        # Add new line
        echo "sonar.dart.sdk=$flutter_sdk" >> "$config_file"
    fi
    
    # Update pub cache path
    if grep -q "sonar.dart.pub.cache=" "$config_file"; then
        # Replace existing line
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|^sonar.dart.pub.cache=.*|sonar.dart.pub.cache=$pub_cache|" "$config_file"
        else
            # Linux
            sed -i "s|^sonar.dart.pub.cache=.*|sonar.dart.pub.cache=$pub_cache|" "$config_file"
        fi
    else
        # Add new line
        echo "sonar.dart.pub.cache=$pub_cache" >> "$config_file"
    fi
    
    echo "✅ Configuration updated successfully"
}

# Main execution
main() {
    echo "🚀 Configuring Flutter SDK for SonarQube analysis..."
    
    # Detect Flutter SDK
    if detect_flutter_sdk; then
        echo "Flutter SDK Path: $FLUTTER_SDK_PATH"
    else
        echo "❌ Could not detect Flutter SDK automatically"
        echo "Please manually set the Flutter SDK path in sonar-project.properties:"
        echo "sonar.dart.sdk=/path/to/your/flutter/sdk"
        exit 1
    fi
    
    # Detect pub cache
    detect_pub_cache
    
    # Update configuration
    update_sonar_config "$FLUTTER_SDK_PATH" "$PUB_CACHE_PATH"
    
    # Verify Flutter version
    if [ -f "$FLUTTER_SDK_PATH/bin/flutter" ]; then
        echo "📋 Flutter version information:"
        "$FLUTTER_SDK_PATH/bin/flutter" --version | head -n 1
    fi
    
    echo "🎉 Flutter SDK configuration complete!"
    echo "Updated sonar-project.properties with:"
    echo "  Flutter SDK: $FLUTTER_SDK_PATH"
    echo "  Pub Cache: $PUB_CACHE_PATH"
}

# Run main function
main "$@"
