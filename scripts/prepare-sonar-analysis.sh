#!/bin/bash

# Script to prepare Flutter project for SonarCube analysis
# This ensures all dependencies and generated files are available

set -e  # Exit on any error

echo "🚀 Preparing Flutter project for SonarCube analysis..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    echo "Please install Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Check if we're in a Flutter project
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml not found. Are you in a Flutter project directory?"
    exit 1
fi

echo "✅ Flutter found: $(flutter --version | head -n 1)"

# Configure Flutter SDK path for SonarQube
echo "⚙️  Configuring Flutter SDK path for SonarQube..."
if [ -f "scripts/configure-flutter-sdk.sh" ]; then
    ./scripts/configure-flutter-sdk.sh
else
    echo "⚠️  Flutter SDK configuration script not found, using default paths"
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Generate code (if build_runner is used)
if grep -q "build_runner" pubspec.yaml; then
    echo "🔧 Running code generation..."
    flutter pub run build_runner build --delete-conflicting-outputs
else
    echo "ℹ️  No build_runner found, skipping code generation"
fi

# Generate localization files (if easy_localization is used)
if grep -q "easy_localization" pubspec.yaml; then
    echo "🌐 Generating localization files..."
    # This ensures locale files are generated
    flutter pub run easy_localization:generate -S assets/translations -O lib/generated
fi

# Verify analysis passes
echo "🔍 Running Flutter analysis to verify project state..."
flutter analyze

if [ $? -eq 0 ]; then
    echo "✅ Flutter analysis passed - project is ready for SonarCube analysis"
else
    echo "❌ Flutter analysis failed - please fix issues before running SonarCube"
    exit 1
fi

# Create coverage directory if it doesn't exist
mkdir -p coverage

echo "🎉 Project preparation complete!"
echo "You can now run SonarCube analysis."
