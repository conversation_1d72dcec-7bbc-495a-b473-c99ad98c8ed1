{"devDependencies": {"lefthook": "^1.10.1"}, "scripts": {"prepare": "flutter --version || (curl -O https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.29.0-stable.tar.xz && tar -xf flutter_linux_3.29.0-stable.tar.xz -C /opt/ && ln -s /opt/flutter/bin/flutter /usr/bin/flutter && flutter doctor)", "lint": "flutter analyze", "test:unit": "flutter test", "build": "flutter build appbundle"}}